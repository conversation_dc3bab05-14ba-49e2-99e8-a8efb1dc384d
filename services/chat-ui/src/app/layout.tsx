'use client';

import { Geist, <PERSON>eist_Mono } from "next/font/google";
import { SWRConfig } from 'swr';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/AuthContext';
import "./globals.css";

// Create QueryClient instance for TanStack Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10000, // 10s - Balance between freshness and performance
      gcTime: 5 * 60 * 1000, // 5min - Cache retention (renamed from cacheTime in v4)
      retry: 2, // Fewer retries for faster feedback in supervisor context
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 15000), // Faster retry ceiling
      refetchOnWindowFocus: true, // Auto-refresh when supervisor returns to tab
      refetchOnReconnect: true,   // Auto-refresh on network reconnection
    },
  },
});

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Note: metadata moved to page-level or separate metadata file

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <QueryClientProvider client={queryClient}>
          <SWRConfig
            value={{
              refreshInterval: 0, // Disable automatic polling by default
              revalidateOnFocus: false, // Disable revalidation on window focus by default
              errorRetryCount: 3,
              errorRetryInterval: 5000,
            }}
          >
            <AuthProvider>
              {children}
            </AuthProvider>
          </SWRConfig>
        </QueryClientProvider>
      </body>
    </html>
  );
}
