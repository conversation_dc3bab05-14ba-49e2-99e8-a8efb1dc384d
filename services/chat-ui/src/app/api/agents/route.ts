import { NextRequest, NextResponse } from 'next/server';

// Chat Realtime API URL (server-side, secure)
const CHAT_REALTIME_API = process.env.CHAT_REALTIME_API_URL || 'http://localhost:3003/api';

export async function GET(request: NextRequest) {
  try {
    // Delegate to Chat Realtime API for hybrid agent data (Supabase + Firebase)
    console.log('🔄 Delegating to Chat Realtime API for agent data...');
    
    const response = await fetch(`${CHAT_REALTIME_API}/agents`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Chat Realtime API error: ${response.status} ${response.statusText}`);
      return NextResponse.json({ 
        success: false, 
        error: { message: `Failed to fetch from Chat Realtime API: ${response.statusText}` } 
      }, { status: response.status });
    }

    const data = await response.json();
    
    console.log(`📋 Agents API: Successfully delegated to Chat Realtime`);
    
    return NextResponse.json(data);

  } catch (error) {
    console.error('Error in /api/agents:', error);
    return NextResponse.json({ 
      success: false, 
      error: { message: 'Internal server error' } 
    }, { status: 500 });
  }
}