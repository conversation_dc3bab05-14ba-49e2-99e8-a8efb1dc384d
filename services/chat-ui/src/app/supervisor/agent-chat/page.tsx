'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { MessageSquare, Users } from 'lucide-react';

export default function AgentChatPage() {
  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Chat con Agentes">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <MessageSquare className="h-12 w-12 text-gray-400" />
              <Users className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900">Chat Directo con Agentes</h3>
            <p className="mt-2 text-sm text-gray-500">
              Comunicación directa y privada con agentes para coordinación y soporte.
            </p>
            <div className="mt-6 text-xs text-gray-400">
              <p>🔧 En desarrollo - Próximamente disponible</p>
            </div>
          </div>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}