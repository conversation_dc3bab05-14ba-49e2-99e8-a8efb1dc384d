'use client';

import { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Users, 
  RefreshCw, 
  Settings, 
  Eye,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertTriangle,
  UserCheck,
  Calendar,
  Filter,
  HelpCircle
} from 'lucide-react';
import { useSupervisorQueries as useSupervisorDashboard } from '@/hooks/supervisor/useSupervisorQueries';
import { AgentStatus, AgentStatusSummary } from '@/types/api';

export default function AgentsPage() {
  const { dashboardData, loading, error, refreshDashboard } = useSupervisorDashboard();
  const [selectedFilter, setSelectedFilter] = useState<'all' | AgentStatus>('all');

  const handleRefresh = async () => {
    await refreshDashboard();
  };

  const handleAuthorizeAgent = (agentId: string) => {
    // TODO: Implement authorization approval
    console.log('Authorize agent:', agentId);
  };

  const handleViewAgent = (agentId: string) => {
    // TODO: Navigate to agent detail view
    console.log('View agent:', agentId);
  };

  const handleChatAgent = (agentId: string) => {
    // TODO: Open chat with agent
    console.log('Chat with agent:', agentId);
  };

  const getStatusBadge = (status: AgentStatus) => {
    switch (status) {
      case 'available':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Disponible</Badge>;
      case 'busy':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Ocupado</Badge>;
      case 'away':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Ausente</Badge>;
    }
  };

  const getStatusDuration = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    }
    return `${minutes}m`;
  };

  const getAvatarColor = (status: AgentStatus) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-700';
      case 'busy':
        return 'bg-amber-100 text-amber-700';
      case 'away':
        return 'bg-gray-100 text-gray-700';
    }
  };

  const agents = dashboardData?.agents || [];
  const summary = dashboardData?.summary;
  const pendingAuthorizations = dashboardData?.pendingAuthorizations || [];

  // Filter agents
  const filteredAgents = selectedFilter === 'all' 
    ? agents 
    : agents.filter(agent => agent.currentStatus === selectedFilter);

  const actions = (
    <div className="flex items-center space-x-3">
      <Button variant="outline" onClick={handleRefresh} disabled={loading}>
        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
        Actualizar
      </Button>
      <Button variant="outline">
        <Settings className="h-4 w-4 mr-2" />
        Configurar
      </Button>
    </div>
  );

  if (error) {
    return (
      <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
        <SupervisorLayout title="Estado de Agentes" actions={actions}>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2 text-red-600">
                <AlertTriangle className="h-5 w-5" />
                <span>Error cargando datos de agentes: {error}</span>
              </div>
            </CardContent>
          </Card>
        </SupervisorLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Estado de Agentes" actions={actions}>
        <div className="space-y-6">
          {/* Summary Panel */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-400" />
                    <CardTitle className="text-sm font-medium text-gray-600">
                      Total Agentes
                    </CardTitle>
                  </div>
                  <div className="relative group">
                    <HelpCircle className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600" />
                    {/* Tooltip */}
                    <div className="absolute top-full mt-2 hidden group-hover:block z-[60] w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-xl border border-gray-700 right-0">
                      Número total de agentes registrados en el sistema y asignados a tu supervisión. Incluye agentes en todos los estados.
                      <div className="absolute bottom-full w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 right-4"></div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="h-8 w-8 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    summary?.totalAgents ?? 0
                  )}
                </div>
                <p className="text-xs text-gray-500">agentes registrados</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <CardTitle className="text-sm font-medium text-gray-600">
                      Disponibles
                    </CardTitle>
                  </div>
                  <div className="relative group">
                    <HelpCircle className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600" />
                    {/* Tooltip */}
                    <div className="absolute top-full mt-2 hidden group-hover:block z-[60] w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-xl border border-gray-700 right-0">
                      Agentes actualmente disponibles para recibir nuevas conversaciones. Pueden aceptar asignaciones automáticas del sistema.
                      <div className="absolute bottom-full w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 right-4"></div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {loading ? (
                    <div className="h-8 w-8 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    summary?.available ?? 0
                  )}
                </div>
                <p className="text-xs text-gray-500">pueden recibir chats</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-amber-500" />
                    <CardTitle className="text-sm font-medium text-gray-600">
                      Ocupados
                    </CardTitle>
                  </div>
                  <div className="relative group">
                    <HelpCircle className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600" />
                    {/* Tooltip */}
                    <div className="absolute top-full mt-2 hidden group-hover:block z-[60] w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-xl border border-gray-700 right-0">
                      Agentes en estado ocupado con autorización del supervisor. No reciben nuevas asignaciones hasta cambiar de estado.
                      <div className="absolute bottom-full w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 right-4"></div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">
                  {loading ? (
                    <div className="h-8 w-8 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    summary?.busy ?? 0
                  )}
                </div>
                <p className="text-xs text-gray-500">con autorización</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <UserCheck className="h-4 w-4 text-blue-500" />
                    <CardTitle className="text-sm font-medium text-gray-600">
                      Pendientes
                    </CardTitle>
                  </div>
                  <div className="relative group">
                    <HelpCircle className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600" />
                    {/* Tooltip */}
                    <div className="absolute top-full mt-2 hidden group-hover:block z-[60] w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-xl border border-gray-700 right-0">
                      Solicitudes de agentes para cambiar a estado ocupado que requieren tu autorización como supervisor.
                      <div className="absolute bottom-full w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 right-4"></div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  {loading ? (
                    <div className="h-8 w-8 bg-gray-300 rounded animate-pulse" />
                  ) : (
                    pendingAuthorizations.length
                  )}
                </div>
                <p className="text-xs text-gray-500">autorizaciones</p>
              </CardContent>
            </Card>
          </div>

          {/* Filters */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-400" />
                  <CardTitle className="text-sm font-medium">Filtros</CardTitle>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant={selectedFilter === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFilter('all')}
                >
                  Todos ({agents.length})
                </Button>
                <Button
                  variant={selectedFilter === 'available' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFilter('available')}
                >
                  Disponibles ({summary?.available ?? 0})
                </Button>
                <Button
                  variant={selectedFilter === 'busy' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFilter('busy')}
                >
                  Ocupados ({summary?.busy ?? 0})
                </Button>
                <Button
                  variant={selectedFilter === 'away' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedFilter('away')}
                >
                  Ausentes ({summary?.away ?? 0})
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Agents Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Agentes del Equipo</span>
                {loading && (
                  <div className="h-4 w-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="flex items-center space-x-4 p-4">
                      <div className="h-10 w-10 bg-gray-300 rounded-full animate-pulse" />
                      <div className="flex-1 space-y-2">
                        <div className="h-4 w-32 bg-gray-300 rounded animate-pulse" />
                        <div className="h-3 w-48 bg-gray-300 rounded animate-pulse" />
                      </div>
                      <div className="h-6 w-20 bg-gray-300 rounded animate-pulse" />
                    </div>
                  ))}
                </div>
              ) : filteredAgents.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h3 className="text-lg font-medium mb-2">No hay agentes</h3>
                  <p className="text-sm">No se encontraron agentes con el filtro seleccionado.</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <div className="space-y-0 divide-y divide-gray-100">
                    {filteredAgents.map((agent) => {
                      const pendingAuth = pendingAuthorizations.find(auth => auth.agentId === agent.agentId);
                      
                      return (
                        <div key={agent.agentId} className="p-4 grid grid-cols-6 gap-4 items-center hover:bg-gray-50">
                          {/* Agent Info */}
                          <div className="col-span-2 flex items-center space-x-3">
                            <Avatar className="h-10 w-10">
                              <AvatarFallback className={getAvatarColor(agent.currentStatus)}>
                                {agent.agentName.split(' ').map(n => n[0]).join('').toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <h4 className="font-semibold text-gray-900">{agent.agentName}</h4>
                              <p className="text-sm text-gray-500">
                                {getStatusDuration(agent.currentStatusDuration)} en estado actual
                              </p>
                            </div>
                          </div>

                          {/* Status */}
                          <div className="flex flex-col space-y-1">
                            {getStatusBadge(agent.currentStatus)}
                            {pendingAuth && (
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                Solicitud pendiente
                              </Badge>
                            )}
                          </div>

                          {/* Workload Conversations */}
                          <div className="text-center">
                            {agent.hasActiveChatsThatNeedAction ? (
                              <div className="flex flex-col items-center text-amber-600">
                                <AlertTriangle className="h-4 w-4 mb-1" />
                                <div className="font-medium text-base">{agent.workloadConversations || 0}</div>
                                <span className="text-xs">requieren atención</span>
                              </div>
                            ) : (
                              <div className="text-sm text-gray-600">
                                <MessageSquare className="h-4 w-4 mx-auto mb-1" />
                                <div className="font-medium text-base">{agent.workloadConversations || 0}</div>
                                <span>carga de trabajo</span>
                              </div>
                            )}
                            {/* Status breakdown on hover */}
                            {agent.workloadConversations > 0 && (
                              <div className="text-xs text-gray-500 mt-1">
                                {agent.conversationsByStatus.active > 0 && `${agent.conversationsByStatus.active} activas`}
                                {agent.conversationsByStatus.supervised > 0 && `, ${agent.conversationsByStatus.supervised} sup.`}
                                {agent.conversationsByStatus.escalated > 0 && `, ${agent.conversationsByStatus.escalated} esc.`}
                              </div>
                            )}
                          </div>

                          {/* Today's Conversations */}
                          <div className="text-center text-sm text-gray-600">
                            <Calendar className="h-4 w-4 mx-auto mb-1" />
                            <div className="font-medium text-base">{agent.totalConversationsToday || 0}</div>
                            <span>asignadas hoy</span>
                          </div>

                          {/* Actions */}
                          <div className="flex flex-wrap gap-1">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewAgent(agent.agentId)}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              Ver
                            </Button>
                            
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleChatAgent(agent.agentId)}
                            >
                              <MessageSquare className="h-3 w-3 mr-1" />
                              Chat
                            </Button>

                            {pendingAuth && (
                              <Button
                                variant="default"
                                size="sm"
                                onClick={() => handleAuthorizeAgent(agent.agentId)}
                                className="bg-blue-600 hover:bg-blue-700"
                              >
                                <UserCheck className="h-3 w-3 mr-1" />
                                Autorizar
                              </Button>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}