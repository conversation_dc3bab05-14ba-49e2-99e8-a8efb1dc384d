'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { ClipboardList, RefreshCw, Settings } from 'lucide-react';

export default function QueuePage() {
  const actions = (
    <>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <Settings className="h-4 w-4 mr-2" />
        ⚙️ Configurar Cola
      </button>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <RefreshCw className="h-4 w-4 mr-2" />
        🔄 Actualizar
      </button>
    </>
  );

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Cola de Supervisión" actions={actions}>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Tareas Pendientes de Supervisión</h3>
          </div>
          
          <div className="p-4 space-y-4">
            {/* Queue Item 1 */}
            <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">Juan P. - Transferencia Pendiente</h4>
                  <p className="text-sm text-gray-600">Cliente requiere especialista técnico</p>
                </div>
                <div className="text-xs text-gray-500">Hace 5 minutos</div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Cliente:</strong> María González</p>
                <p><strong>Problema:</strong> Configuración servidor dedicado</p>
                <p><strong>Destino:</strong> Departamento Técnico Senior</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700">
                  👁️ Revisar Chat
                </button>
                <button className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700">
                  ✓ Aprobar Transferencia
                </button>
              </div>
            </div>

            {/* Queue Item 2 */}
            <div className="p-4 border border-amber-200 bg-amber-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">Sistema - Revisión Automática</h4>
                  <p className="text-sm text-gray-600">Conversación marcada para supervisión</p>
                </div>
                <div className="text-xs text-gray-500">Hace 12 minutos</div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Agente:</strong> Ana Martínez</p>
                <p><strong>Cliente:</strong> Carlos Ruiz</p>
                <p><strong>Motivo:</strong> Cliente insatisfecho con respuesta</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700">
                  👁️ Ver Conversación
                </button>
                <button className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700">
                  ✓ Marcar como Revisada
                </button>
              </div>
            </div>

            {/* Queue Item 3 */}
            <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">Luis G. - Intervención Requerida</h4>
                  <p className="text-sm text-gray-600">Solicita intervención en chat complejo</p>
                </div>
                <div className="text-xs text-gray-500">Hace 3 minutos</div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Cliente:</strong> Empresa TechCorp</p>
                <p><strong>Problema:</strong> Migración crítica de datos</p>
                <p><strong>Prioridad:</strong> Alta - Cliente Enterprise</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded hover:bg-red-700">
                  🚨 Intervenir Ahora
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700">
                  👁️ Ver Chat
                </button>
              </div>
            </div>
          </div>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}