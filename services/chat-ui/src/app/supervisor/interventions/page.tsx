'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { Eye, RefreshCw, Settings } from 'lucide-react';

export default function InterventionsPage() {
  const actions = (
    <>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <Settings className="h-4 w-4 mr-2" />
        Configurar
      </button>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <RefreshCw className="h-4 w-4 mr-2" />
        Actualizar
      </button>
    </>
  );

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Intervenciones Activas" actions={actions}>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Supervisión en Tiempo Real</h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-sm text-green-600">3 activas</span>
              </div>
            </div>
          </div>
          
          <div className="p-4 space-y-4">
            {/* Active Intervention 1 */}
            <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">👁️ Observación - Juan Pérez</h4>
                  <p className="text-sm text-gray-600">Cliente: María González</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded">OBSERVANDO</span>
                  <div className="text-xs text-gray-500">15:23</div>
                </div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Motivo:</strong> Consulta técnica compleja - servidor dedicado</p>
                <p><strong>Duración:</strong> 8 minutos</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700">
                  👁️ Ver Chat
                </button>
                <button className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700">
                  💬 Unirse a Conversación
                </button>
                <button className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded hover:bg-gray-700">
                  ✓ Finalizar Observación
                </button>
              </div>
            </div>

            {/* Active Intervention 2 */}
            <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">💬 Participando - Ana Martínez</h4>
                  <p className="text-sm text-gray-600">Cliente: TechCorp Enterprise</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800 rounded">PARTICIPANDO</span>
                  <div className="text-xs text-gray-500">15:18</div>
                </div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Motivo:</strong> Cliente insatisfecho - escalación crítica</p>
                <p><strong>Duración:</strong> 13 minutos</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700">
                  💬 Continuar Chat
                </button>
                <button className="px-4 py-2 bg-green-600 text-white text-white text-sm font-medium rounded hover:bg-green-700">
                  ✓ Resolver y Cerrar
                </button>
              </div>
            </div>

            {/* Active Intervention 3 */}
            <div className="p-4 border border-amber-200 bg-amber-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">👁️ Observación - Luis García</h4>
                  <p className="text-sm text-gray-600">Cliente: Carlos Mendoza</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="px-2 py-1 text-xs font-semibold bg-amber-100 text-amber-800 rounded">OBSERVANDO</span>
                  <div className="text-xs text-gray-500">15:25</div>
                </div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Motivo:</strong> Entrenamiento agente nuevo</p>
                <p><strong>Duración:</strong> 6 minutos</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700">
                  👁️ Ver Chat
                </button>
                <button className="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded hover:bg-gray-700">
                  ✓ Finalizar Observación
                </button>
              </div>
            </div>
          </div>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}