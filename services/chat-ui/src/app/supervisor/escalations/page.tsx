'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { EscalationsPanel } from '@/components/supervisor/dashboard/EscalationsPanel';
import { AlertTriangle, RefreshCw, Filter } from 'lucide-react';

export default function EscalationsPage() {
  const actions = (
    <>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <Filter className="h-4 w-4 mr-2" />
        Filtros
      </button>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <RefreshCw className="h-4 w-4 mr-2" />
        🔄 Actualizar
      </button>
    </>
  );

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Gestión de Escalaciones" actions={actions}>
        <div className="space-y-6">
          {/* Priority Escalations */}
          <div className="flex items-center gap-2 mb-6">
            <AlertTriangle className="h-5 w-5 text-red-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Escalaciones Prioritarias
            </h2>
            <div className="text-xs px-2 py-1 bg-red-100 text-red-800 rounded-full">
              Auto-refresh 30s
            </div>
          </div>
          
          <EscalationsPanel />
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}