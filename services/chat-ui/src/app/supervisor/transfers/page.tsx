'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { RefreshCw, Filter } from 'lucide-react';

export default function TransfersPage() {
  const actions = (
    <>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <Filter className="h-4 w-4 mr-2" />
        Filtros
      </button>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <RefreshCw className="h-4 w-4 mr-2" />
        Actualizar
      </button>
    </>
  );

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Gestión de Transferencias" actions={actions}>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 text-center">
            <RefreshCw className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">Historial de Transferencias</h3>
            <p className="mt-2 text-sm text-gray-500">
              Monitoreo y análisis de todas las transferencias entre agentes y departamentos.
            </p>
            <div className="mt-6 text-xs text-gray-400">
              <p>🔧 En desarrollo - Próximamente disponible</p>
            </div>
          </div>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}