'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { PieChart, Download, Calendar } from 'lucide-react';

export default function ReportsPage() {
  const actions = (
    <>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <Calendar className="h-4 w-4 mr-2" />
        📅 Período
      </button>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <Download className="h-4 w-4 mr-2" />
        📄 Generar Reporte
      </button>
    </>
  );

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Reportes y Análisis" actions={actions}>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-6 text-center">
            <PieChart className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">Generador de Reportes</h3>
            <p className="mt-2 text-sm text-gray-500">
              Reportes personalizables con datos históricos, análisis comparativo y exportación a PDF/Excel.
            </p>
            <div className="mt-6 text-xs text-gray-400">
              <p>🔧 En desarrollo - Próximamente disponible</p>
            </div>
          </div>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}