'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { StatsCards } from '@/components/supervisor/dashboard/StatsCards';
import { DailyMetricsPanel } from '@/components/supervisor/dashboard/DailyMetricsPanel';
import { CapacityPanel } from '@/components/supervisor/dashboard/CapacityPanel';
import { PendingActionsPanel } from '@/components/supervisor/dashboard/PendingActionsPanel';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Settings, AlertCircle, ChevronDown, Clock, User, MessageCircle, RefreshCw } from 'lucide-react';
import { useState } from 'react';
import { useCrisisInterventionsQuery as useCrisisInterventions } from '@/hooks/supervisor/useCrisisInterventionsQuery';

export default function SupervisorPage() {
  const [showCrisisDropdown, setShowCrisisDropdown] = useState(false);
  const { crisisInterventions, loading: crisisLoading, refreshCrisisInterventions } = useCrisisInterventions();
  
  // Crisis interventions are already filtered for immediate supervisor action
  const crisisChats = crisisInterventions.slice(0, 10); // Limit to 10 most critical

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-600';
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const handleCrisisIntervention = (chatId: string) => {
    console.log(`😨 CRISIS: Supervisor entrando al chat: ${chatId}`);
    // Navigate to agent view with this specific conversation for direct participation
    window.open(`/agent?conversation=${chatId}`, '_blank');
    setShowCrisisDropdown(false);
  };

  const handleRefreshCrisis = async () => {
    await refreshCrisisInterventions();
  };

  const actions = (
    <>
      <Button variant="outline" size="sm">
        <Settings className="h-4 w-4 mr-2" />
        Configuración
      </Button>
      
      {/* Crisis Intervention Dropdown */}
      <div className="relative">
        <Button 
          variant="destructive" 
          size="sm"
          onClick={() => setShowCrisisDropdown(!showCrisisDropdown)}
          className="flex items-center gap-2"
          disabled={crisisLoading}
        >
          <AlertCircle className="h-4 w-4" />
          🚨 Crisis Inmediata
          {crisisChats.length > 0 && (
            <Badge variant="secondary" className="bg-white text-red-600 text-xs ml-1">
              {crisisChats.length}
            </Badge>
          )}
          <ChevronDown className="h-3 w-3" />
        </Button>

        {showCrisisDropdown && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-[48]" 
              onClick={() => setShowCrisisDropdown(false)}
            />
            
            {/* Dropdown */}
            <div className="absolute top-full right-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-[49] max-h-80 overflow-y-auto">
              <div className="p-3 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-sm">😨 Crisis - Intervención Inmediata</h3>
                  <div className="flex items-center gap-2">
                    <Badge variant="destructive" className="text-xs">
                      {crisisChats.length} crisis
                    </Badge>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={handleRefreshCrisis}
                      disabled={crisisLoading}
                      className="h-6 w-6 p-0"
                    >
                      <RefreshCw className={`h-3 w-3 ${crisisLoading ? 'animate-spin' : ''}`} />
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="max-h-64 overflow-y-auto">
                {crisisLoading ? (
                  <div className="p-6 text-center">
                    <RefreshCw className="h-4 w-4 animate-spin mx-auto mb-2" />
                    <div className="text-xs text-gray-500">Buscando crisis...</div>
                  </div>
                ) : crisisChats.length === 0 ? (
                  <div className="p-6 text-center">
                    <div className="text-sm text-gray-600 mb-1">✅ Sin crisis activas</div>
                    <div className="text-xs text-gray-500">No hay situaciones críticas que requieran tu intervención directa</div>
                  </div>
                ) : (
                  crisisChats.map((crisis) => (
                    <div 
                      key={crisis.id}
                      className="p-3 border-b border-gray-50 hover:bg-red-50 cursor-pointer border-l-4 border-l-red-500"
                      onClick={() => handleCrisisIntervention(crisis.id)}
                    >
                      <div className="space-y-2">
                        {/* Header */}
                        <div className="flex items-center justify-between">
                          <div className="font-medium text-sm text-red-800">{crisis.customerName}</div>
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-red-600 animate-pulse"></div>
                            <span className="text-xs text-gray-500">{crisis.channel}</span>
                          </div>
                        </div>
                        
                        {/* Crisis Issue */}
                        <div className="text-xs text-red-700 font-medium">{crisis.issue}</div>
                        
                        {/* Agent & Time */}
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <User className="h-3 w-3" />
                            {crisis.agentName || 'Sin asignar'}
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3 text-red-500" />
                              {crisis.waitTime}
                            </div>
                            <div className="flex items-center gap-1">
                              <MessageCircle className="h-3 w-3" />
                              {crisis.id.slice(-6)}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
              
              {/* Footer */}
              <div className="p-3 border-t border-gray-100 bg-red-50">
                <div className="text-xs text-red-700 mb-2 font-medium">
                  💡 Al hacer clic entrarás al chat como agente para resolver la crisis
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full text-xs"
                  onClick={() => {
                    setShowCrisisDropdown(false);
                    window.location.href = '/supervisor/interventions';
                  }}
                >
                  Ver Gestión de Recursos →
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Dashboard de Supervisión" actions={actions}>
        {/* Stats Overview */}
        <StatsCards />

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mt-8">
          {/* Daily Metrics Panel */}
          <div className="lg:col-span-2">
            <DailyMetricsPanel />
          </div>

          {/* Right Panel */}
          <div className="space-y-6">
            <CapacityPanel />
            <PendingActionsPanel />
          </div>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}