'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { SupervisorLayout } from '@/components/supervisor/layout/SupervisorLayout';
import { UserCheck, RefreshCw, Filter } from 'lucide-react';

export default function AuthorizationsPage() {
  const actions = (
    <>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <Filter className="h-4 w-4 mr-2" />
        Filtros
      </button>
      <button className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
        <RefreshCw className="h-4 w-4 mr-2" />
        Actualizar
      </button>
    </>
  );

  return (
    <ProtectedRoute allowedRoles={['supervisor', 'admin']}>
      <SupervisorLayout title="Autorizaciones Pendientes" actions={actions}>
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Solicitudes de Autorización</h3>
          </div>
          
          <div className="p-4 space-y-4">
            {/* Authorization Request 1 */}
            <div className="p-4 border border-amber-200 bg-amber-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">Ana Martínez - Cambio de Estado</h4>
                  <p className="text-sm text-gray-600">Solicita cambio de "busy" a "away" - Descanso</p>
                </div>
                <div className="text-xs text-gray-500">Hace 2 minutos</div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Motivo:</strong> Descanso programado de 15 minutos</p>
                <p><strong>Conversaciones activas:</strong> 4 chats asignados</p>
                <p><strong>Estado actual:</strong> busy (5/5 chats)</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700">
                  ✓ Aprobar
                </button>
                <button className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded hover:bg-red-700">
                  ✗ Rechazar
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700">
                  👁️ Ver Conversaciones
                </button>
              </div>
            </div>

            {/* Authorization Request 2 */}
            <div className="p-4 border border-gray-200 bg-gray-50 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">Luis García - Extensión de Turno</h4>
                  <p className="text-sm text-gray-600">Solicita autorización para turno extra</p>
                </div>
                <div className="text-xs text-gray-500">Hace 8 minutos</div>
              </div>
              
              <div className="text-sm text-gray-700 mb-4">
                <p><strong>Motivo:</strong> Cubrir ausencia de compañero</p>
                <p><strong>Tiempo solicitado:</strong> 2 horas extra</p>
                <p><strong>Turno actual:</strong> Noche (22:00 - 06:00)</p>
              </div>
              
              <div className="flex space-x-3">
                <button className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded hover:bg-green-700">
                  ✓ Aprobar
                </button>
                <button className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded hover:bg-red-700">
                  ✗ Rechazar
                </button>
              </div>
            </div>
          </div>
        </div>
      </SupervisorLayout>
    </ProtectedRoute>
  );
}