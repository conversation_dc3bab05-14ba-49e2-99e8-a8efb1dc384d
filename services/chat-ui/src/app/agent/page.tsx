'use client';

import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { AgentInterface } from '@/components/agent/AgentInterface';
import { FixedAdaptivePollingDebug } from '@/components/debug/FixedAdaptivePollingDebug';

export default function AgentPage() {
  return (
    <ProtectedRoute allowedRoles={['agent', 'admin']}>
      <AgentInterface />
      
      {/* Debug component - only in development */}
      {process.env.NODE_ENV === 'development' && (
        <FixedAdaptivePollingDebug />
      )}
    </ProtectedRoute>
  );
}