'use client';

import { Loader2 } from 'lucide-react';
import { useAuthRedirect } from '@/hooks/useAuthRedirect';
import { ContinuumLogo } from '@/components/ui/ContinuumLogo';

export default function Home() {
  const { loading } = useAuthRedirect({
    redirectTo: '/login' // Redirect to login if no user, or to role-based page if user exists
  });

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="flex flex-col items-center space-y-6">
        <ContinuumLogo width={200} height={80} />
        <div className="flex flex-col items-center space-y-2">
          <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
          <p className="text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    </div>
  );
}
