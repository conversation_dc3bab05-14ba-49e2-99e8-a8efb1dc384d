import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface UseAuthRedirectOptions {
  allowedRoles?: string[];
  redirectTo?: string;
  enabled?: boolean;
}

/**
 * Centralized authentication redirect logic
 * Handles all redirects based on auth state and user role
 */
export function useAuthRedirect(options: UseAuthRedirectOptions = {}) {
  const { 
    allowedRoles, 
    redirectTo = '/login',
    enabled = true 
  } = options;
  
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!enabled || loading) return;

    const currentPath = typeof window !== 'undefined' ? window.location.pathname : '';

    // No user -> redirect to login (unless already on login page)
    if (!user) {
      if (currentPath !== redirectTo) {
        router.push(redirectTo);
      }
      return;
    }

    // User exists - determine target dashboard
    const targetPath = user.role === 'admin' ? '/admin'
                     : user.role === 'supervisor' ? '/supervisor'
                     : '/agent';

    // Check role permissions for protected routes
    if (allowedRoles && !allowedRoles.includes(user.role)) {
      // User doesn't have permission for this route, redirect to their dashboard
      if (currentPath !== targetPath) {
        router.push(targetPath);
      }
      return;
    }
    
    // If user is authenticated and on login page or home page, redirect to dashboard
    if (user && (currentPath === '/login' || currentPath === '/')) {
      router.push(targetPath);
      return;
    }
    
  }, [user, loading, allowedRoles, redirectTo, enabled, router]);

  return {
    user,
    loading,
    isAllowed: user && (!allowedRoles || allowedRoles.includes(user.role))
  };
}