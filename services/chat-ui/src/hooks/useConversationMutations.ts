'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { mutate } from 'swr';
import chatRealtimeAPI from '@/services/api';
import { Conversation, ConversationStatus } from '@/types';
import { useAdaptivePolling } from './useAdaptivePolling';

interface OptimisticConversationUpdate {
  conversationId: string;
  updates: Partial<Conversation>;
  previousState?: Conversation;
}

/**
 * TanStack Query mutations for conversation operations with optimistic updates
 * Extends optimistic patterns beyond message bubbles to conversation lists
 * 
 * Provides immediate UI feedback for:
 * - Status changes (active → transferring → pending_acceptance)
 * - Assignment changes (agentId updates)
 * - Unread count decrements
 * - Last message updates
 * - Priority escalations
 */
export function useConversationMutations() {
  const queryClient = useQueryClient();
  const { boostPolling } = useAdaptivePolling();

  /**
   * Optimistic conversation status update
   * Updates conversation list immediately before API call
   */
  const updateConversationStatusMutation = useMutation({
    mutationFn: async ({
      conversationId,
      status,
      metadata
    }: {
      conversationId: string;
      status: string;
      metadata?: any;
    }) => {
      // API call will vary based on status type
      if (status === 'transferring') {
        return await chatRealtimeAPI.transferConversation(conversationId, metadata);
      }
      // Add other status update endpoints as needed
      throw new Error(`Status update not implemented: ${status}`);
    },

    onMutate: async ({ conversationId, status, metadata }) => {
      // Cancel outgoing refetches to prevent race conditions
      await queryClient.cancelQueries({ queryKey: ['conversations'] });

      // Get current conversation data
      const previousConversations = queryClient.getQueryData(['conversations']) as any;
      
      // Create optimistic update
      const optimisticUpdates: Partial<Conversation> = {
        status: status as ConversationStatus,
        updatedAt: new Date().toISOString(),
        ...(status === 'transferring' && {
          transfer: {
            status: 'pending',
            requestedAt: new Date().toISOString(),
            fromAgentId: metadata?.fromAgentId,
            toAgentId: metadata?.toAgentId,
            reason: metadata?.reason
          }
        })
      };

      // Apply optimistic update to TanStack Query cache
      queryClient.setQueryData(['conversations'], (old: any) => {
        if (!old?.data?.conversations) return old;
        
        return {
          ...old,
          data: {
            ...old.data,
            conversations: old.data.conversations.map((conv: Conversation) =>
              conv.id === conversationId 
                ? { ...conv, ...optimisticUpdates }
                : conv
            )
          }
        };
      });

      // Also update SWR cache for coordination
      mutate(['conversations'], (old: any) => {
        if (!old?.data?.conversations) return old;
        return {
          ...old,
          data: {
            ...old.data,
            conversations: old.data.conversations.map((conv: Conversation) =>
              conv.id === conversationId 
                ? { ...conv, ...optimisticUpdates }
                : conv
            )
          }
        };
      }, { revalidate: false });

      return { previousConversations, conversationId, optimisticUpdates };
    },

    onError: (error, { conversationId }, context) => {
      // Rollback optimistic updates
      if (context?.previousConversations) {
        queryClient.setQueryData(['conversations'], context.previousConversations);
        mutate(['conversations'], context.previousConversations, { revalidate: false });
      }
      console.error('Conversation status update failed:', error);
    },

    onSuccess: (result, { conversationId, status }, context) => {
      // Replace optimistic update with real data
      if (result.success && result.data) {
        queryClient.setQueryData(['conversations'], (old: any) => {
          if (!old?.data?.conversations) return old;
          
          return {
            ...old,
            data: {
              ...old.data,
              conversations: old.data.conversations.map((conv: Conversation) =>
                conv.id === conversationId 
                  ? { ...conv, ...(result.data || {}) }
                  : conv
              )
            }
          };
        });

        // Update SWR cache with real data
        mutate(['conversations'], (old: any) => {
          if (!old?.data?.conversations) return old;
          return {
            ...old,
            data: {
              ...old.data,
              conversations: old.data.conversations.map((conv: Conversation) =>
                conv.id === conversationId 
                  ? { ...conv, ...(result.data || {}) }
                  : conv
              )
            }
          };
        }, { revalidate: false });
      }

      // Boost polling for immediate feedback on status changes
      boostPolling(`status_change_${status}`, 8000);
    },

    onSettled: (data, error, { conversationId }) => {
      // Always revalidate to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      
      setTimeout(() => {
        mutate(['conversations']);
      }, 100);
    },

    retry: 1,
    retryDelay: 1000,
  });

  /**
   * Optimistic unread count update
   * Immediately decrements unread count when conversation is selected
   */
  const markConversationReadMutation = useMutation({
    mutationFn: async (conversationId: string) => {
      return await chatRealtimeAPI.markConversationAsRead(conversationId);
    },

    onMutate: async (conversationId: string) => {
      await queryClient.cancelQueries({ queryKey: ['conversations'] });

      const previousConversations = queryClient.getQueryData(['conversations']) as any;

      // Optimistically set unread count to 0
      const optimisticUpdate = { unreadCount: 0 };

      // Update TanStack Query cache
      queryClient.setQueryData(['conversations'], (old: any) => {
        if (!old?.data?.conversations) return old;
        
        return {
          ...old,
          data: {
            ...old.data,
            conversations: old.data.conversations.map((conv: Conversation) =>
              conv.id === conversationId 
                ? { ...conv, ...optimisticUpdate }
                : conv
            )
          }
        };
      });

      // Update SWR cache
      mutate(['conversations'], (old: any) => {
        if (!old?.data?.conversations) return old;
        return {
          ...old,
          data: {
            ...old.data,
            conversations: old.data.conversations.map((conv: Conversation) =>
              conv.id === conversationId 
                ? { ...conv, ...optimisticUpdate }
                : conv
            )
          }
        };
      }, { revalidate: false });

      return { previousConversations, conversationId };
    },

    onError: (error, conversationId, context) => {
      // Rollback on failure
      if (context?.previousConversations) {
        queryClient.setQueryData(['conversations'], context.previousConversations);
        mutate(['conversations'], context.previousConversations, { revalidate: false });
      }
    },

    // No specific onSuccess needed - the optimistic update is usually correct
    // and will be overwritten by next polling cycle
  });

  /**
   * Optimistic conversation assignment update
   * Shows immediate agent assignment changes in conversation list
   */
  const updateConversationAssignmentMutation = useMutation({
    mutationFn: async ({
      conversationId,
      agentId,
      assignmentType
    }: {
      conversationId: string;
      agentId: string;
      assignmentType: 'assign' | 'unassign' | 'transfer';
    }) => {
      // Different API calls based on assignment type
      if (assignmentType === 'transfer') {
        return await chatRealtimeAPI.transferConversation(conversationId, { targetAgentId: agentId, reason: "Agent reassignment" });
      }
      // Add other assignment endpoints as needed
      throw new Error(`Assignment type not implemented: ${assignmentType}`);
    },

    onMutate: async ({ conversationId, agentId, assignmentType }) => {
      await queryClient.cancelQueries({ queryKey: ['conversations'] });

      const previousConversations = queryClient.getQueryData(['conversations']) as any;

      // Create optimistic assignment update
      const optimisticUpdates: Partial<Conversation> = {
        assignedAgent: assignmentType === 'unassign' ? null : { id: agentId },
        status: assignmentType === 'assign' ? 'active' : 'transferring',
        updatedAt: new Date().toISOString()
      };

      // Apply optimistic updates to both caches
      queryClient.setQueryData(['conversations'], (old: any) => {
        if (!old?.data?.conversations) return old;
        
        return {
          ...old,
          data: {
            ...old.data,
            conversations: old.data.conversations.map((conv: Conversation) =>
              conv.id === conversationId 
                ? { ...conv, ...optimisticUpdates }
                : conv
            )
          }
        };
      });

      mutate(['conversations'], (old: any) => {
        if (!old?.data?.conversations) return old;
        return {
          ...old,
          data: {
            ...old.data,
            conversations: old.data.conversations.map((conv: Conversation) =>
              conv.id === conversationId 
                ? { ...conv, ...optimisticUpdates }
                : conv
            )
          }
        };
      }, { revalidate: false });

      return { previousConversations };
    },

    onError: (error, variables, context) => {
      if (context?.previousConversations) {
        queryClient.setQueryData(['conversations'], context.previousConversations);
        mutate(['conversations'], context.previousConversations, { revalidate: false });
      }
      console.error('Assignment update failed:', error);
    },

    onSuccess: (result, { conversationId, assignmentType }) => {
      // Boost polling after assignment changes
      boostPolling(`assignment_${assignmentType}`, 6000);
    },

    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
      setTimeout(() => mutate(['conversations']), 100);
    },

    retry: 1,
  });

  // TODO: Priority updates - endpoint not implemented in backend yet
  // When chatRealtimeAPI.updateConversationPriority() is available, uncomment:
  // const updateConversationPriorityMutation = useMutation({ ... });

  /**
   * Optimistic local filtering
   * Provides instant filter changes without API calls for better UX
   */
  const applyOptimisticFilter = (
    conversations: Conversation[], 
    filter: 'all' | 'active' | 'pending' | 'transferring'
  ) => {
    if (filter === 'all') return conversations;
    
    return conversations.filter(conv => {
      switch (filter) {
        case 'active':
          return conv.status === 'active' || conv.status === 'supervised';
        case 'pending':
          return conv.status === 'pending' || conv.status === 'pending_acceptance';
        case 'transferring':
          return conv.status === 'transferring' || conv.status === 'escalated';
        default:
          return true;
      }
    });
  };

  return {
    // Status changes (transferring, escalated, closed, etc.)
    updateStatus: updateConversationStatusMutation.mutate,
    updateStatusAsync: updateConversationStatusMutation.mutateAsync,
    isUpdatingStatus: updateConversationStatusMutation.isPending,
    statusError: updateConversationStatusMutation.error,

    // Read status (unread count management)
    markAsRead: markConversationReadMutation.mutate,
    markAsReadAsync: markConversationReadMutation.mutateAsync,
    isMarkingRead: markConversationReadMutation.isPending,
    markReadError: markConversationReadMutation.error,

    // Assignment changes (agent transfers, assignments)
    updateAssignment: updateConversationAssignmentMutation.mutate,
    updateAssignmentAsync: updateConversationAssignmentMutation.mutateAsync,
    isUpdatingAssignment: updateConversationAssignmentMutation.isPending,
    assignmentError: updateConversationAssignmentMutation.error,

    // Priority changes - TODO: implement when backend endpoint available
    // updatePriority: updateConversationPriorityMutation.mutate,
    // updatePriorityAsync: updateConversationPriorityMutation.mutateAsync,
    // isUpdatingPriority: updateConversationPriorityMutation.isPending,
    // priorityError: updateConversationPriorityMutation.error,

    // Utility functions
    applyOptimisticFilter,

    // Reset all mutations
    reset: () => {
      updateConversationStatusMutation.reset();
      markConversationReadMutation.reset();
      updateConversationAssignmentMutation.reset();
      // updateConversationPriorityMutation.reset(); // TODO: uncomment when implemented
    }
  };
}

export default useConversationMutations;