// Hook for managing agents
'use client';

import useSWR, { mutate } from 'swr';
import { useState, useCallback } from 'react';
import chatRealtimeAPI from '@/services/api';
import { Agent, UpdateAgentStatusRequest } from '@/types';

// Agents list hook
export function useAgents(filters?: {status?: string, department?: string}) {
  const { data, error, isLoading, mutate } = useSWR(
    ['agents', filters],
    () => chatRealtimeAPI.getAgents(filters),
    {
      refreshInterval: 10000, // Refresh every 10 seconds
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
    }
  );

  return {
    agents: data?.data?.agents || [],
    loading: isLoading,
    error,
    refresh: mutate,
  };
}

// Single agent hook
export function useAgent(agentId: string | null) {
  const { data, error, isLoading, mutate } = useSWR(
    agentId ? ['agent', agentId] : null,
    () => agentId ? chatRealtimeAPI.getAgentDetails(agentId) : null,
    {
      refreshInterval: 5000, // Refresh every 5 seconds for current agent
      revalidateOnFocus: true,
    }
  );

  return {
    agent: data?.data,
    loading: isLoading,
    error,
    refresh: mutate,
  };
}

// Agent actions hook
export function useAgentActions() {
  const [loading, setLoading] = useState<Record<string, boolean>>({});

  const setActionLoading = (action: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [action]: isLoading }));
  };

  const updateStatus = useCallback(async (
    agentId: string,
    statusData: UpdateAgentStatusRequest
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('updateStatus', true);
      const response = await chatRealtimeAPI.updateAgentStatus(agentId, statusData);
      
      if (response.success) {
        // Refresh agent data
        mutate(['agent', agentId]);
        mutate(['agents']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('updateStatus', false);
    }
  }, []);

  const setCapacity = useCallback(async (
    agentId: string,
    maxConcurrentChats: number
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('setCapacity', true);
      const response = await chatRealtimeAPI.setAgentCapacity(agentId, maxConcurrentChats);
      
      if (response.success) {
        mutate(['agent', agentId]);
        mutate(['agents']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('setCapacity', false);
    }
  }, []);

  return {
    loading,
    updateStatus,
    setCapacity,
  };
}