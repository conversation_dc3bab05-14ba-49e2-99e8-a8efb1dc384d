/**
 * Adaptive Polling Hook
 * 
 * Intelligent polling intervals that adapt to:
 * - Agent activity (active/idle)
 * - System load (number of concurrent conversations)
 * - Context awareness (unread messages, typing)
 * - Time-based patterns (business hours vs off-hours)
 * 
 * Features:
 * - Automatic scaling during peak loads
 * - Battery-friendly polling during idle times
 * - Context-aware acceleration when needed
 * - Debug metrics for optimization
 */

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import chatRealtimeAPI from '@/services/api';

// System load context
interface SystemLoadContext {
  activeAgents: number;
  totalConversations: number;
  averageResponseTime: number;
  systemLoad: 'low' | 'medium' | 'high' | 'critical';
  lastUpdated: Date;
}

// Agent activity context
interface AgentContext {
  isActive: boolean;           // User interacting in last 2 minutes
  isTyping: boolean;           // Currently typing/in input focus
  activeConversationId: string | null;
  unreadCount: number;
  lastActivityAt: Date;
}

// Polling configuration based on context
interface PollingConfig {
  conversationsInterval: number;
  messagesInterval: number;
  systemCheckInterval: number;
  reason: string;             // Why this interval was chosen
}

// Hook for detecting system load
function useSystemLoad() {
  const [systemLoad, setSystemLoad] = useState<SystemLoadContext>({
    activeAgents: 0,
    totalConversations: 0,
    averageResponseTime: 0,
    systemLoad: 'low',
    lastUpdated: new Date()
  });
  
  const loadCheckRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch system metrics from Chat Realtime API
  const checkSystemLoad = useCallback(async () => {
    try {
      // Get system status from Chat Realtime Service
      const statusResponse = await chatRealtimeAPI.getSystemStatus();
      
      if (statusResponse.success && statusResponse.data?.metrics) {
        const { 
          activeAgents, 
          activeConversations, 
          avgResponseTime 
        } = statusResponse.data.metrics;
        
        // Determine system load level based on active conversations
        let loadLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
        
        if (activeConversations > 25) {
          loadLevel = 'critical';
        } else if (activeConversations > 15) {
          loadLevel = 'high';
        } else if (activeConversations > 8) {
          loadLevel = 'medium';
        }
        
        setSystemLoad({
          activeAgents,
          totalConversations: activeConversations,
          averageResponseTime: avgResponseTime,
          systemLoad: loadLevel,
          lastUpdated: new Date()
        });
      }
    } catch (error) {
      console.warn('System load check failed, using fallback:', error);
      // Fallback: assume medium load if we can't check
      setSystemLoad(prev => ({
        ...prev,
        systemLoad: 'medium',
        lastUpdated: new Date()
      }));
    }
  }, []);

  // Check system load periodically - FIXED: Remove checkSystemLoad from deps
  useEffect(() => {
    checkSystemLoad(); // Initial check
    
    // Check every 30 seconds
    loadCheckRef.current = setInterval(() => {
      checkSystemLoad();
    }, 30000);
    
    return () => {
      if (loadCheckRef.current) {
        clearInterval(loadCheckRef.current);
      }
    };
  }, []); // Empty deps - only run once

  return {
    ...systemLoad,
    forceRefresh: checkSystemLoad
  };
}

// Hook for detecting agent activity
function useAgentActivity(conversationId: string | null) {
  const [agentContext, setAgentContext] = useState<AgentContext>({
    isActive: false,
    isTyping: false,
    activeConversationId: conversationId,
    unreadCount: 0,
    lastActivityAt: new Date()
  });

  const activityTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const { user } = useAuth();

  // Track user activity
  const trackActivity = useCallback(() => {
    setAgentContext(prev => ({
      ...prev,
      isActive: true,
      lastActivityAt: new Date()
    }));

    // Set inactive after 2 minutes of no activity
    if (activityTimeoutRef.current) {
      clearTimeout(activityTimeoutRef.current);
    }
    
    activityTimeoutRef.current = setTimeout(() => {
      setAgentContext(prev => ({
        ...prev,
        isActive: false
      }));
    }, 120000); // 2 minutes
  }, []);

  // Track typing activity
  const setTyping = useCallback((isTyping: boolean) => {
    setAgentContext(prev => ({
      ...prev,
      isTyping,
      isActive: true,
      lastActivityAt: new Date()
    }));
    
    if (isTyping) {
      trackActivity(); // Reset activity timer when typing
    }
  }, [trackActivity]);

  // Track conversation changes - FIXED: Remove trackActivity from deps
  useEffect(() => {
    setAgentContext(prev => ({
      ...prev,
      activeConversationId: conversationId
    }));
    
    if (conversationId) {
      trackActivity(); // Activity when switching conversations
    }
  }, [conversationId]); // Only depend on conversationId

  // Setup activity listeners - FIXED: Remove trackActivity from deps  
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => trackActivity();
    
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
      
      if (activityTimeoutRef.current) {
        clearTimeout(activityTimeoutRef.current);
      }
    };
  }, []); // Empty deps - only setup once

  return {
    ...agentContext,
    setTyping,
    trackActivity
  };
}

// Main adaptive polling hook
export function useAdaptivePolling(conversationId: string | null = null) {
  const systemLoad = useSystemLoad();
  const agentContext = useAgentActivity(conversationId);
  
  // Calculate optimal polling intervals
  const getPollingConfig = useCallback((): PollingConfig => {
    const {
      isActive,
      isTyping,
      activeConversationId,
      unreadCount
    } = agentContext;
    
    const {
      systemLoad: loadLevel,
      totalConversations
    } = systemLoad;

    // Business hours check (8 AM - 8 PM local time)
    const now = new Date();
    const hour = now.getHours();
    const isBusinessHours = hour >= 8 && hour <= 20;
    
    // Base intervals (in milliseconds)
    let conversationsInterval = 30000; // 30s default
    let messagesInterval = 10000;      // 10s default
    let reason = 'default';

    // Critical system load - aggressive polling
    if (loadLevel === 'critical') {
      conversationsInterval = isActive ? 3000 : 8000;   // 3s active, 8s idle
      messagesInterval = isActive ? 2000 : 5000;        // 2s active, 5s idle
      reason = 'critical_load_detected';
    }
    // High system load
    else if (loadLevel === 'high') {
      conversationsInterval = isActive ? 5000 : 12000;  // 5s active, 12s idle
      messagesInterval = isActive ? 3000 : 7000;        // 3s active, 7s idle
      reason = 'high_load_detected';
    }
    // Medium system load
    else if (loadLevel === 'medium') {
      conversationsInterval = isActive ? 8000 : 20000;  // 8s active, 20s idle
      messagesInterval = isActive ? 5000 : 10000;       // 5s active, 10s idle
      reason = 'medium_load_detected';
    }
    // Low system load - normal intervals
    else {
      conversationsInterval = isActive ? 10000 : 30000; // 10s active, 30s idle
      messagesInterval = isActive ? 5000 : 15000;       // 5s active, 15s idle
      reason = 'low_load_normal';
    }

    // Boost for active conversation with unread messages
    if (activeConversationId && (isTyping || unreadCount > 0)) {
      conversationsInterval = Math.min(conversationsInterval, 5000); // Max 5s
      messagesInterval = Math.min(messagesInterval, 3000);           // Max 3s
      reason += '_active_conversation';
    }

    // Reduce polling outside business hours for battery saving
    if (!isBusinessHours && !isActive) {
      conversationsInterval = Math.max(conversationsInterval, 60000); // Min 60s
      messagesInterval = Math.max(messagesInterval, 30000);           // Min 30s
      reason += '_off_hours';
    }

    return {
      conversationsInterval,
      messagesInterval,
      systemCheckInterval: 30000, // Always 30s for system checks
      reason
    };
  }, [agentContext, systemLoad]);

  // Current polling configuration
  const [currentConfig, setCurrentConfig] = useState<PollingConfig>(getPollingConfig());

  // Update config when context changes - with debouncing to prevent loops
  useEffect(() => {
    const newConfig = getPollingConfig();
    
    // Only update if intervals actually changed (prevent unnecessary re-renders)
    if (
      newConfig.conversationsInterval !== currentConfig.conversationsInterval ||
      newConfig.messagesInterval !== currentConfig.messagesInterval ||
      newConfig.reason !== currentConfig.reason
    ) {
      // Debounce config updates to prevent rapid changes
      const timeoutId = setTimeout(() => {
        console.log('🔄 Adaptive polling updated:', {
          conversations: `${currentConfig.conversationsInterval}ms → ${newConfig.conversationsInterval}ms`,
          messages: `${currentConfig.messagesInterval}ms → ${newConfig.messagesInterval}ms`,
          reason: newConfig.reason,
          systemLoad: systemLoad.systemLoad,
          isActive: agentContext.isActive
        });
        
        setCurrentConfig(newConfig);
      }, 100); // 100ms debounce
      
      return () => clearTimeout(timeoutId);
    }
  }, [
    // Only depend on primitive values to prevent reference equality issues
    agentContext.isActive,
    agentContext.isTyping,
    agentContext.unreadCount,
    systemLoad.systemLoad,
    systemLoad.totalConversations,
    currentConfig.conversationsInterval,
    currentConfig.messagesInterval,
    currentConfig.reason
  ]);

  // Add boost mechanism for post-action scenarios (like transfers)
  const boostPolling = useCallback((reason: string, durationMs: number = 10000) => {
    console.log(`🚀 Adaptive polling BOOST: ${reason} for ${durationMs}ms`);
    
    const boostConfig = {
      conversationsInterval: 2000,  // 2s aggressive polling
      messagesInterval: 1000,       // 1s aggressive polling  
      systemCheckInterval: 5000,    // 5s system checks
      reason: `boost_${reason}`
    };
    
    setCurrentConfig(boostConfig);
    
    // Reset to normal after duration
    setTimeout(() => {
      const normalConfig = getPollingConfig();
      setCurrentConfig(normalConfig);
      console.log(`🔄 Adaptive polling boost ended, returning to: ${normalConfig.reason}`);
    }, durationMs);
  }, [getPollingConfig]);

  return {
    // Current intervals
    conversationsInterval: currentConfig.conversationsInterval,
    messagesInterval: currentConfig.messagesInterval,
    systemCheckInterval: currentConfig.systemCheckInterval,
    
    // Context information
    systemLoad: systemLoad.systemLoad,
    totalConversations: systemLoad.totalConversations,
    isAgentActive: agentContext.isActive,
    isTyping: agentContext.isTyping,
    
    // Manual controls
    setTyping: agentContext.setTyping,
    trackActivity: agentContext.trackActivity,
    forceSystemRefresh: systemLoad.forceRefresh,
    boostPolling, // 🚀 NEW: Temporary aggressive polling
    
    // Debug info
    debugInfo: {
      reason: currentConfig.reason,
      agentContext,
      systemLoad,
      lastConfigUpdate: new Date()
    }
  };
}

/**
 * Debug hook for monitoring adaptive polling behavior
 * Useful for optimization and troubleshooting
 */
export function useAdaptivePollingDebug() {
  const adaptivePolling = useAdaptivePolling();
  const [history, setHistory] = useState<{
    timestamp: Date;
    config: PollingConfig;
    context: any;
  }[]>([]);
  
  const lastConfigRef = useRef<string>('');

  // Track polling changes - only when config actually changes
  useEffect(() => {
    const configKey = `${adaptivePolling.conversationsInterval}-${adaptivePolling.messagesInterval}-${adaptivePolling.debugInfo.reason}`;
    
    // Only add to history if config actually changed
    if (configKey !== lastConfigRef.current) {
      lastConfigRef.current = configKey;
      
      setHistory(prev => [
        ...prev.slice(-19), // Keep last 20 entries
        {
          timestamp: new Date(),
          config: {
            conversationsInterval: adaptivePolling.conversationsInterval,
            messagesInterval: adaptivePolling.messagesInterval,
            systemCheckInterval: adaptivePolling.systemCheckInterval,
            reason: adaptivePolling.debugInfo.reason
          },
          context: {
            systemLoad: adaptivePolling.systemLoad,
            totalConversations: adaptivePolling.totalConversations,
            isActive: adaptivePolling.isAgentActive,
            isTyping: adaptivePolling.isTyping
          }
        }
      ]);
    }
  }, [
    // Only track specific values that indicate config changes
    adaptivePolling.conversationsInterval,
    adaptivePolling.messagesInterval,
    adaptivePolling.debugInfo.reason
  ]);

  // Simple calculations without useMemo to prevent hook issues
  const averageConversationsInterval = history.length > 0 
    ? history.reduce((sum, h) => sum + h.config.conversationsInterval, 0) / history.length
    : 0;
  
  const averageMessagesInterval = history.length > 0
    ? history.reduce((sum, h) => sum + h.config.messagesInterval, 0) / history.length  
    : 0;

  return {
    ...adaptivePolling,
    history,
    averageConversationsInterval,
    averageMessagesInterval,
    configChanges: history.length
  };
}