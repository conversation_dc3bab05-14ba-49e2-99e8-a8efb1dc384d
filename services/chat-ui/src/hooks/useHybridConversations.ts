/**
 * Hybrid Conversation Hooks
 * 
 * Smart hooks that automatically switch between real-time and REST API:
 * - Real-time when Firebase is connected
 * - Fallback to existing SWR hooks when disconnected
 * - Seamless switching without user awareness
 * - Zero breaking changes to existing components
 */

import { useState, useEffect, useMemo } from 'react';
import { useRealtimeMessages, useRealtimeConversations, useConnectionState } from './useFirebaseRealtime';
import { useMessages as useSWRMessages, useConversations as useSWRConversations, useConversation as useSWRConversation } from './useConversations';
import type { Message, Conversation, ConversationFilters } from '@/types';

/**
 * Hybrid Messages Hook
 * Uses real-time when connected, falls back to SWR polling when disconnected
 */
export function useHybridMessages(conversationId: string | null, limit?: number) {
  const { isConnected } = useConnectionState();
  
  // Real-time hook
  const realtimeResult = useRealtimeMessages(conversationId);
  
  // SWR fallback hook  
  const swrResult = useSWRMessages(conversationId, limit);
  
  // Smart switching logic
  const result = useMemo(() => {
    if (isConnected && !realtimeResult.error) {
      // Use real-time data when available
      return {
        messages: realtimeResult.messages,
        count: realtimeResult.messages.length,
        loading: realtimeResult.loading,
        error: realtimeResult.error,
        refresh: realtimeResult.refresh,
        // Additional metadata
        isRealtime: true,
        connectionState: realtimeResult.connectionState
      };
    } else {
      // Fallback to SWR polling
      return {
        messages: swrResult.messages,
        count: swrResult.count,
        loading: swrResult.loading,
        error: swrResult.error,
        refresh: swrResult.refresh,
        // Additional metadata
        isRealtime: false,
        connectionState: 'polling' as const
      };
    }
  }, [
    isConnected,
    realtimeResult.messages,
    realtimeResult.loading,
    realtimeResult.error,
    realtimeResult.refresh,
    realtimeResult.connectionState,
    swrResult.messages,
    swrResult.count,
    swrResult.loading,
    swrResult.error,
    swrResult.refresh
  ]);

  return result;
}

/**
 * Hybrid Conversations Hook
 * Uses real-time when connected, falls back to SWR polling when disconnected
 */
export function useHybridConversations(filters?: ConversationFilters, agentId?: string) {
  const { isConnected } = useConnectionState();
  
  // Real-time hook (only if agentId provided)
  const realtimeResult = useRealtimeConversations(agentId || null);
  
  // SWR fallback hook
  const swrResult = useSWRConversations(filters);
  
  // Smart switching logic
  const result = useMemo(() => {
    if (isConnected && agentId && !realtimeResult.error && realtimeResult.conversations.length > 0) {
      // Use real-time data when available, has data, and agentId provided
      return {
        conversations: realtimeResult.conversations,
        total: realtimeResult.conversations.length,
        loading: realtimeResult.loading,
        error: realtimeResult.error,
        refresh: () => {
          // Enhanced refresh for real-time with SWR backup
          realtimeResult.refresh();
          swrResult.refresh(); // Also refresh SWR cache
          console.log('🔄 Hybrid refresh: real-time + SWR backup');
        },
        // Additional metadata
        isRealtime: true,
        connectionState: realtimeResult.connectionState
      };
    } else {
      // Fallback to SWR polling
      return {
        conversations: swrResult.conversations,
        total: swrResult.total,
        loading: swrResult.loading,
        error: swrResult.error,
        refresh: () => {
          // Enhanced refresh for SWR with real-time backup
          swrResult.refresh();
          if (agentId) realtimeResult.refresh(); // Also refresh real-time if agent available
          console.log('🔄 Hybrid refresh: SWR + real-time backup');
        },
        // Additional metadata - honest status
        isRealtime: false,
        connectionState: 'polling' as const
      };
    }
  }, [
    isConnected,
    agentId,
    realtimeResult.conversations,
    realtimeResult.loading,
    realtimeResult.error,
    realtimeResult.refresh,
    realtimeResult.connectionState,
    swrResult.conversations,
    swrResult.total,
    swrResult.loading,
    swrResult.error,
    swrResult.refresh
  ]);

  return result;
}

/**
 * Hybrid Single Conversation Hook
 * Always uses SWR for single conversation details (complex queries)
 * But provides connection state awareness
 */
export function useHybridConversation(conversationId: string | null) {
  const { connectionState } = useConnectionState();
  
  // Use existing SWR hook for single conversation
  const swrResult = useSWRConversation(conversationId);
  
  // Add connection state metadata
  return {
    ...swrResult,
    isRealtime: false, // Single conversation always uses REST API
    connectionState
  };
}

/**
 * Connection-Aware Hook
 * Provides connection state and manual controls for any component
 */
export function useConnectionAware() {
  const { 
    connectionState, 
    isConnected, 
    isPolling, 
    isReconnecting, 
    isOffline,
    forceReconnect 
  } = useConnectionState();
  
  const [lastConnectedAt, setLastConnectedAt] = useState<Date | null>(null);
  const [connectionHistory, setConnectionHistory] = useState<{
    state: string;
    timestamp: Date;
  }[]>([]);

  // Track connection changes
  useEffect(() => {
    if (isConnected && !lastConnectedAt) {
      setLastConnectedAt(new Date());
    }
    
    // Add to connection history
    setConnectionHistory(prev => [
      ...prev.slice(-9), // Keep last 10 entries
      {
        state: connectionState,
        timestamp: new Date()
      }
    ]);
  }, [connectionState, isConnected, lastConnectedAt]);

  return {
    // Current state
    connectionState,
    isConnected,
    isPolling,
    isReconnecting,
    isOffline,
    
    // Actions
    forceReconnect,
    
    // History
    lastConnectedAt,
    connectionHistory,
    
    // Helpers
    getConnectionQuality: () => {
      const recentConnections = connectionHistory.slice(-5);
      const connectedCount = recentConnections.filter(h => h.state === 'connected').length;
      
      if (connectedCount >= 4) return 'excellent';
      if (connectedCount >= 2) return 'good'; 
      if (connectedCount >= 1) return 'poor';
      return 'offline';
    }
  };
}

/**
 * Debug Hook for Development
 * Provides detailed information about hybrid switching behavior
 */
export function useHybridDebug(conversationId?: string) {
  const { connectionState, isConnected } = useConnectionState();
  const [switchEvents, setSwitchEvents] = useState<{
    from: string;
    to: string;
    timestamp: Date;
    reason: string;
  }[]>([]);

  useEffect(() => {
    // Track switching events
    setSwitchEvents(prev => [
      ...prev.slice(-19), // Keep last 20 events
      {
        from: prev[prev.length - 1]?.to || 'unknown',
        to: isConnected ? 'realtime' : 'polling',
        timestamp: new Date(),
        reason: isConnected ? 'Firebase connected' : 'Firebase disconnected'
      }
    ]);
  }, [isConnected]);

  return {
    connectionState,
    isConnected,
    switchEvents,
    conversationId,
    debugInfo: {
      totalSwitches: switchEvents.length,
      realtimeUptime: switchEvents.filter(e => e.to === 'realtime').length,
      pollingUptime: switchEvents.filter(e => e.to === 'polling').length,
      lastSwitch: switchEvents[switchEvents.length - 1]
    }
  };
}