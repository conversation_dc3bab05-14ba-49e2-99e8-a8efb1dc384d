// Hook for managing conversations with S<PERSON>
'use client';

import useS<PERSON>, { mutate } from 'swr';
import { useState, useCallback } from 'react';
import chatRealtimeAPI from '@/services/api';
import { useAdaptivePolling } from './useAdaptivePolling';
import { 
  Conversation, 
  Message, 
  ConversationFilters,
  SendMessageRequest,
  TransferConversationRequest,
  AddNoteRequest
} from '@/types';

// SWR fetchers
const fetcher = {
  conversations: (filters?: ConversationFilters) => 
    chatRealtimeAPI.getConversations(filters),
    
  conversation: (id: string) => 
    chatRealtimeAPI.getConversation(id),
    
  messages: (conversationId: string, limit?: number) => 
    chatRealtimeAPI.getMessages(conversationId, limit),
};

// Conversations list hook - with adaptive polling
export function useConversations(filters?: ConversationFilters) {
  const { conversationsInterval } = useAdaptivePolling();
  
  const { data, error, isLoading, mutate } = useSWR(
    ['conversations', filters],
    () => fetcher.conversations(filters),
    {
      refreshInterval: conversationsInterval, // Adaptive polling interval
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      revalidateOnMount: true,
    }
  );

  return {
    conversations: data?.data?.conversations || [],
    total: data?.data?.total || 0,
    loading: isLoading,
    error, // Show real errors - no fallbacks
    refresh: mutate,
  };
}

// Single conversation hook - with adaptive polling
export function useConversation(conversationId: string | null) {
  const { conversationsInterval } = useAdaptivePolling(conversationId);
  
  const { data, error, isLoading, mutate } = useSWR(
    conversationId ? ['conversation', conversationId] : null,
    () => conversationId ? fetcher.conversation(conversationId) : null,
    {
      refreshInterval: Math.min(conversationsInterval, 10000), // Adaptive but max 10s for active conversation
      revalidateOnFocus: true,
    }
  );

  return {
    conversation: data?.data,
    loading: isLoading,
    error,
    refresh: mutate,
  };
}

// Messages hook - with adaptive polling
export function useMessages(conversationId: string | null, limit?: number) {
  const { messagesInterval } = useAdaptivePolling(conversationId);
  
  const { data, error, isLoading, mutate } = useSWR(
    conversationId ? ['messages', conversationId, limit] : null,
    () => conversationId ? fetcher.messages(conversationId, limit) : null,
    {
      refreshInterval: messagesInterval, // Adaptive polling interval
      revalidateOnFocus: true,
    }
  );

  // DEBUG: Removed console.log to prevent infinite loops
  // This was causing Maximum update depth exceeded errors

  return {
    messages: data?.data?.messages || [],
    count: data?.data?.count || 0,
    loading: isLoading,
    error,
    refresh: mutate,
  };
}

// Conversation actions hook
export function useConversationActions() {
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const { boostPolling } = useAdaptivePolling(); // 🚀 Access to polling boost

  const setActionLoading = (action: string, isLoading: boolean) => {
    setLoading(prev => ({ ...prev, [action]: isLoading }));
  };

  const sendMessage = useCallback(async (
    conversationId: string, 
    messageData: SendMessageRequest
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('sendMessage', true);
      const response = await chatRealtimeAPI.sendMessage(conversationId, messageData);
      
      if (response.success) {
        // Refresh messages
        mutate(['messages', conversationId]);
        // Refresh conversations list to update last message
        mutate(['conversations']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('sendMessage', false);
    }
  }, []);

  const transferConversation = useCallback(async (
    conversationId: string, 
    transferData: TransferConversationRequest
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('transfer', true);
      const response = await chatRealtimeAPI.transferConversation(conversationId, transferData);
      
      if (response.success) {
        // 🚨 AGGRESSIVE CACHE INVALIDATION for transfers
        
        // 1. Clear specific conversation cache 
        mutate(['conversation', conversationId], undefined, { revalidate: true });
        
        // 2. Clear ALL conversations cache with multiple patterns
        mutate(['conversations'], undefined, { revalidate: true });
        mutate(['conversations', undefined], undefined, { revalidate: true });
        mutate((key) => key && Array.isArray(key) && key[0] === 'conversations', undefined, { revalidate: true });
        
        // 3. Force immediate revalidation with timeout bypass
        setTimeout(() => {
          mutate(['conversation', conversationId]);
          mutate(['conversations']);
        }, 100);
        
        // 4. Third wave for stubborn cache
        setTimeout(() => {
          mutate(['conversations']);
        }, 1000);
        
        // 5. 🚀 BOOST ADAPTIVE POLLING for immediate updates
        boostPolling('transfer_completed', 15000); // 15s aggressive polling
        
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('transfer', false);
    }
  }, []);

  const acceptTransfer = useCallback(async (
    conversationId: string,
    agentId: string
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('acceptTransfer', true);
      const response = await chatRealtimeAPI.acceptTransfer(conversationId, agentId);
      
      if (response.success) {
        mutate(['conversation', conversationId]);
        mutate(['conversations']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('acceptTransfer', false);
    }
  }, []);

  const rejectTransfer = useCallback(async (
    conversationId: string,
    agentId: string,
    reason: string
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('rejectTransfer', true);
      const response = await chatRealtimeAPI.rejectTransfer(conversationId, agentId, reason);
      
      if (response.success) {
        mutate(['conversation', conversationId]);
        mutate(['conversations']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('rejectTransfer', false);
    }
  }, []);

  const cancelTransfer = useCallback(async (
    conversationId: string,
    agentId: string,
    reason?: string
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('cancelTransfer', true);
      const response = await chatRealtimeAPI.cancelTransfer(conversationId, agentId, reason);
      
      if (response.success) {
        mutate(['conversation', conversationId]);
        mutate(['conversations']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('cancelTransfer', false);
    }
  }, []);

  const escalateConversation = useCallback(async (
    conversationId: string,
    reason: string
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('escalate', true);
      const response = await chatRealtimeAPI.escalateConversation(conversationId, reason);
      
      if (response.success) {
        mutate(['conversation', conversationId]);
        mutate(['conversations']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('escalate', false);
    }
  }, []);

  const closeConversation = useCallback(async (
    conversationId: string,
    reason?: string
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('close', true);
      const response = await chatRealtimeAPI.closeConversation(conversationId, reason);
      
      if (response.success) {
        mutate(['conversation', conversationId]);
        mutate(['conversations']);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('close', false);
    }
  }, []);


  const addNote = useCallback(async (
    conversationId: string,
    noteData: AddNoteRequest
  ): Promise<{success: boolean, error?: string}> => {
    try {
      setActionLoading('addNote', true);
      const response = await chatRealtimeAPI.addNote(conversationId, noteData);
      
      if (response.success) {
        mutate(['conversation', conversationId]);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message };
    } catch (error: any) {
      return { success: false, error: error.message };
    } finally {
      setActionLoading('addNote', false);
    }
  }, []);

  return {
    loading,
    sendMessage,
    transferConversation,
    acceptTransfer,
    rejectTransfer,
    cancelTransfer,
    escalateConversation,
    closeConversation,
    addNote,
  };
}