'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Conversation } from '@/types';

interface AnimationState {
  newConversations: Set<string>;
  updatedConversations: Set<string>;
  removedConversations: Set<string>;
}

/**
 * Hook for optimistic conversation list animations
 * Provides smooth transitions for conversation state changes
 */
export function useOptimisticConversationAnimations(conversations: Conversation[]) {
  const [animationState, setAnimationState] = useState<AnimationState>({
    newConversations: new Set(),
    updatedConversations: new Set(),
    removedConversations: new Set()
  });

  const previousConversationsRef = useRef<Map<string, Conversation>>(new Map());
  const animationTimeoutRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Track conversation changes for animations
  useEffect(() => {
    const currentMap = new Map(conversations.map(conv => [conv.id, conv]));
    const previousMap = previousConversationsRef.current;

    // Detect new conversations
    const newIds = new Set<string>();
    currentMap.forEach((conv, id) => {
      if (!previousMap.has(id)) {
        newIds.add(id);
      }
    });

    // Detect updated conversations (status, priority, unread count changes)
    const updatedIds = new Set<string>();
    currentMap.forEach((conv, id) => {
      const prevConv = previousMap.get(id);
      if (prevConv) {
        const hasChanged = (
          prevConv.status !== conv.status ||
          prevConv.priority !== conv.priority ||
          prevConv.unreadCount !== conv.unreadCount ||
          prevConv.assignedAgent?.id !== conv.assignedAgent?.id
        );
        
        if (hasChanged) {
          updatedIds.add(id);
        }
      }
    });

    // Detect removed conversations
    const removedIds = new Set<string>();
    previousMap.forEach((conv, id) => {
      if (!currentMap.has(id)) {
        removedIds.add(id);
      }
    });

    // Update animation state
    if (newIds.size > 0 || updatedIds.size > 0 || removedIds.size > 0) {
      setAnimationState({
        newConversations: newIds,
        updatedConversations: updatedIds,
        removedConversations: removedIds
      });

      // Clear animation states after animation duration
      [...newIds, ...updatedIds].forEach(id => {
        // Clear any existing timeout
        const existingTimeout = animationTimeoutRef.current.get(id);
        if (existingTimeout) clearTimeout(existingTimeout);

        // Set new timeout
        const timeout = setTimeout(() => {
          setAnimationState(prev => ({
            newConversations: new Set([...prev.newConversations].filter(convId => convId !== id)),
            updatedConversations: new Set([...prev.updatedConversations].filter(convId => convId !== id)),
            removedConversations: prev.removedConversations
          }));
          animationTimeoutRef.current.delete(id);
        }, 2000); // Animation duration

        animationTimeoutRef.current.set(id, timeout);
      });
    }

    // Update previous conversations reference
    previousConversationsRef.current = currentMap;

    // Cleanup function
    return () => {
      animationTimeoutRef.current.forEach(timeout => clearTimeout(timeout));
      animationTimeoutRef.current.clear();
    };
  }, [conversations]);

  // Get animation class for a conversation
  const getAnimationClass = useCallback((conversationId: string): string => {
    if (animationState.newConversations.has(conversationId)) {
      return 'animate-in slide-in-from-top-2 fade-in-0 duration-500';
    }
    if (animationState.updatedConversations.has(conversationId)) {
      return 'animate-pulse bg-blue-50 border-blue-200 duration-1000';
    }
    if (animationState.removedConversations.has(conversationId)) {
      return 'animate-out slide-out-to-right-2 fade-out-0 duration-300';
    }
    return '';
  }, [animationState]);

  // Check if conversation should show "NEW" badge
  const isNewConversation = useCallback((conversationId: string): boolean => {
    return animationState.newConversations.has(conversationId);
  }, [animationState.newConversations]);

  // Check if conversation was recently updated
  const isRecentlyUpdated = useCallback((conversationId: string): boolean => {
    return animationState.updatedConversations.has(conversationId);
  }, [animationState.updatedConversations]);

  return {
    getAnimationClass,
    isNewConversation,
    isRecentlyUpdated,
    hasAnimations: animationState.newConversations.size > 0 || 
                  animationState.updatedConversations.size > 0 ||
                  animationState.removedConversations.size > 0
  };
}

export default useOptimisticConversationAnimations;