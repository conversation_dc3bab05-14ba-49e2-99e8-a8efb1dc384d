'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { mutate } from 'swr'; // For SWR cache coordination
import chatRealtimeAPI from '@/services/api';
import { SendMessageRequest, Message } from '@/types';
import { useAdaptivePolling } from './useAdaptivePolling';

interface OptimisticMessage extends Message {
  status: 'sending' | 'sent' | 'failed';
  isOptimistic?: boolean;
}

/**
 * TanStack Query mutations for message sending with optimistic updates
 * Integrates with existing SWR + Adaptive Polling architecture
 * 
 * Key improvements over manual implementation:
 * - Automatic optimistic UI updates
 * - Auto-rollback on failure  
 * - Race condition protection
 * - Built-in retry logic
 * - Coordinated cache updates between TanStack + SWR
 */
export function useMessageMutations() {
  const queryClient = useQueryClient();
  const { boostPolling } = useAdaptivePolling();

  const sendMessageMutation = useMutation({
    mutationFn: async ({ 
      conversationId, 
      messageData 
    }: { 
      conversationId: string; 
      messageData: SendMessageRequest; 
    }) => {
      const response = await chatRealtimeAPI.sendMessage(conversationId, messageData);
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to send message');
      }
      
      return response.data;
    },

    onMutate: async ({ conversationId, messageData }) => {
      // 🚫 Cancel any outgoing refetches to prevent race conditions
      await queryClient.cancelQueries({ queryKey: ['messages', conversationId] });

      // 📸 Snapshot the previous messages for rollback
      const previousMessages = queryClient.getQueryData(['messages', conversationId]) as Message[] | undefined;

      // ✨ Create optimistic message that appears immediately
      const optimisticMessage: OptimisticMessage = {
        id: `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        conversationId,
        senderId: messageData.senderId,
        senderType: messageData.senderType,
        content: messageData.content,
        type: messageData.type || 'text',
        timestamp: new Date().toISOString(),
        status: 'sending',
        isOptimistic: true,
        senderName: messageData.senderName,
        metadata: messageData.metadata || {}
      };

      // 🚀 Immediately add optimistic message to TanStack Query cache
      queryClient.setQueryData(['messages', conversationId], (old: Message[] | undefined) => {
        return [...(old || []), optimisticMessage];
      });

      // 🔄 Also update SWR cache for coordination with existing hooks
      mutate(['messages', conversationId], (old: any) => {
        if (!old?.data?.messages) return old;
        return {
          ...old,
          data: {
            ...old.data,
            messages: [...old.data.messages, optimisticMessage],
            count: (old.data.count || 0) + 1
          }
        };
      }, { revalidate: false });

      // 💪 Boost adaptive polling for immediate feedback
      boostPolling('message_sent', 5000); // 5s aggressive polling after send

      return { previousMessages, optimisticMessage };
    },

    onError: (error, { conversationId }, context) => {
      // 🔄 Rollback: Remove the optimistic message from TanStack Query
      queryClient.setQueryData(['messages', conversationId], context?.previousMessages);

      // 🔄 Rollback: Remove the optimistic message from SWR cache
      mutate(['messages', conversationId], (old: any) => {
        if (!old?.data?.messages) return old;
        return {
          ...old,
          data: {
            ...old.data,
            messages: old.data.messages.filter((msg: Message) => 
              !(msg as OptimisticMessage).isOptimistic
            ),
            count: Math.max((old.data.count || 1) - 1, 0)
          }
        };
      }, { revalidate: false });

      console.error('Message send failed:', error);
    },

    onSuccess: (realMessage, { conversationId }, context) => {
      // 🎯 Replace optimistic message with real message from server
      queryClient.setQueryData(['messages', conversationId], (old: Message[] | undefined) => {
        if (!old) return [realMessage];
        
        return old.map(msg => {
          const optMsg = msg as OptimisticMessage;
          return optMsg.isOptimistic && optMsg.id === context?.optimisticMessage.id 
            ? realMessage 
            : msg;
        });
      });

      // 🔄 Update SWR cache with real message
      mutate(['messages', conversationId], (old: any) => {
        if (!old?.data?.messages) return old;
        return {
          ...old,
          data: {
            ...old.data,
            messages: old.data.messages.map((msg: Message) => {
              const optMsg = msg as OptimisticMessage;
              return optMsg.isOptimistic && optMsg.id === context?.optimisticMessage.id 
                ? realMessage 
                : msg;
            })
          }
        };
      }, { revalidate: false });

      // 🔄 Update conversations list to show latest message  
      mutate(['conversations'], undefined, { revalidate: true });
    },

    onSettled: (data, error, { conversationId }) => {
      // 🧹 Cleanup: Always revalidate to ensure data consistency
      queryClient.invalidateQueries({ queryKey: ['messages', conversationId] });
      
      // 🔄 Revalidate SWR to stay in sync
      setTimeout(() => {
        mutate(['messages', conversationId]);
        mutate(['conversations']);
      }, 100);
    },

    // Retry configuration for message sending
    retry: 2,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 5000),
  });

  return {
    sendMessage: sendMessageMutation.mutate,
    sendMessageAsync: sendMessageMutation.mutateAsync,
    isLoading: sendMessageMutation.isPending,
    error: sendMessageMutation.error,
    reset: sendMessageMutation.reset,
  };
}

export default useMessageMutations;