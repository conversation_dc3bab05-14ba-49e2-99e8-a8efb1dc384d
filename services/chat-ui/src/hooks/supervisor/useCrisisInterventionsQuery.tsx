'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { normalizePriority, getLastActivityMinutes, isPriorityUrgent, validateConversationsResponse, Priority } from '@/utils/typeGuards';

interface CrisisIntervention {
  id: string;
  customerId: string;
  customerName: string;
  agentId?: string;
  agentName?: string;
  channel: string;
  status: string;
  priority: Priority;
  issue: string;
  waitTime: string;
  lastActivityAt: string;
  metadata?: {
    escalationReason?: string;
    transferCount?: number;
    responseDelayMinutes?: number;
  };
}

interface UseCrisisInterventionsReturn {
  crisisInterventions: CrisisIntervention[];
  loading: boolean;
  error: string | null;
  refreshCrisisInterventions: () => Promise<void>;
}

/**
 * TanStack Query hook for crisis interventions
 * Replaces the setInterval-based useCrisisInterventions with proper caching
 */
export function useCrisisInterventionsQuery(): UseCrisisInterventionsReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Crisis interventions query with 15-second refresh (more frequent for crisis)
  const crisisQuery = useQuery({
    queryKey: ['supervisor', 'crisis-interventions'],
    queryFn: async () => {
      if (!user) {
        throw new Error('Usuario no autenticado');
      }

      // Import API service dynamically
      const { default: chatRealtimeAPI } = await import('@/services/api');

      // Get conversations with critical statuses that need IMMEDIATE supervisor participation
      const response = await chatRealtimeAPI.getConversations({
        status: ['escalated', 'active', 'transferring'],
        limit: 50
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Error fetching crisis interventions');
      }

      if (!validateConversationsResponse(response.data)) {
        console.warn('Invalid conversations data structure:', response.data);
        return [];
      }
      
      const conversationsData = response.data;
      const conversations = conversationsData.conversations;
      
      // Filter for CRISIS SITUATIONS requiring immediate supervisor participation
      const crisisChats: CrisisIntervention[] = conversations
        .filter((conv: any) => {
          const lastActivityMinutes = getLastActivityMinutes(conv.lastActivityAt);
            
          // CRISIS CRITERIA - Supervisor must enter and participate
          const isCrisis = (
            // Explicitly escalated conversations
            conv.status === 'escalated' ||
            // Critical/Urgent priority with significant delays
            (isPriorityUrgent(conv.priority) && lastActivityMinutes > 3) ||
            // Multiple failed transfers (customer is frustrated)
            (conv.metadata?.transferCount || 0) > 2 ||
            // Very long delays on any priority (customer likely upset)
            lastActivityMinutes > 15
          );
          
          return isCrisis;
        })
        .map((conv: any) => {
          const lastActivityMinutes = getLastActivityMinutes(conv.lastActivityAt);
          const normalizedPriority = normalizePriority(conv.priority);
            
          // Determine crisis reason
          let issue = '🚨 Crisis requiere intervención inmediata';
          if (conv.status === 'escalated') {
            issue = `🔥 Escalado: ${conv.metadata?.escalationReason || 'Requiere supervisor'}`;
          } else if ((conv.metadata?.transferCount || 0) > 2) {
            issue = `💥 ${conv.metadata?.transferCount} transferencias fallidas`;
          } else if (lastActivityMinutes > 15) {
            issue = `⏰ Sin respuesta ${lastActivityMinutes} min - Cliente esperando`;
          } else if (isPriorityUrgent(conv.priority)) {
            issue = `🔴 Cliente ${normalizedPriority.toUpperCase()} - demora crítica`;
          }

          return {
            id: conv.id,
            customerId: conv.customerId,
            customerName: conv.customer?.name || conv.metadata?.customerName || 'Cliente Anónimo',
            agentId: conv.agentId,
            agentName: conv.assignedAgent?.name || 'Sin asignar',
            channel: conv.channel?.toUpperCase() || 'UNKNOWN',
            status: conv.status,
            priority: normalizedPriority,
            issue,
            waitTime: `${lastActivityMinutes} min`,
            lastActivityAt: conv.lastActivityAt,
            metadata: conv.metadata
          };
        })
        .sort((a, b) => {
          // Sort by crisis severity: escalated first, then priority, then wait time
          if (a.status === 'escalated' && b.status !== 'escalated') return -1;
          if (b.status === 'escalated' && a.status !== 'escalated') return 1;
          
          const priorityOrder = { critical: 5, urgent: 4, high: 3, medium: 2, low: 1 };
          const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder] || 1;
          const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder] || 1;
          
          if (aPriority !== bPriority) {
            return bPriority - aPriority;
          }
          
          const aWait = parseInt(a.waitTime.split(' ')[0]);
          const bWait = parseInt(b.waitTime.split(' ')[0]);
          return bWait - aWait;
        });

      return crisisChats;
    },
    enabled: !!user, // Only run query when user is authenticated
    refetchInterval: 15000, // 15 seconds for crisis situations (more frequent)
    staleTime: 5000, // Data considered fresh for only 5s (crisis needs fresh data)
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
  });

  // Manual refresh function
  const refreshCrisisInterventions = async () => {
    await queryClient.invalidateQueries({ queryKey: ['supervisor', 'crisis-interventions'] });
  };

  return {
    crisisInterventions: crisisQuery.data ?? [],
    loading: crisisQuery.isLoading,
    error: crisisQuery.error ? (crisisQuery.error as Error).message : null,
    refreshCrisisInterventions,
  };
}

export default useCrisisInterventionsQuery;