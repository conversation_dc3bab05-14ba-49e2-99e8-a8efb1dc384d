'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { isPriorityHigh, validateConversationsResponse } from '@/utils/typeGuards';

interface ResourceIssue {
  type: 'agent_unavailable_with_chats' | 'unassigned_priority_chat' | 'overloaded_agent' | 'department_bottleneck';
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  affectedChats: number;
  agentName?: string;
  agentId?: string;
  department?: string;
  actionRequired: string;
}

interface UseResourceManagementReturn {
  resourceIssues: ResourceIssue[];
  totalIssues: number;
  loading: boolean;
  error: string | null;
  refreshResourceManagement: () => Promise<void>;
}

/**
 * TanStack Query hook for resource management data
 * Replaces the problematic useResourceManagement hook
 */
export function useResourceManagementQuery(): UseResourceManagementReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();


  // Resource management query with 30-second refresh
  const resourceQuery = useQuery({
    queryKey: ['supervisor', 'resource-management'],
    queryFn: async () => {
      if (!user) {
        throw new Error('Usuario no autenticado');
      }

      // Import API service dynamically
      const { default: chatRealtimeAPI } = await import('@/services/api');

      // Get conversations and agent data (exclude 'escalated' status - those are handled by useEscalations)
      const [conversationsResponse, dashboardResponse] = await Promise.all([
        chatRealtimeAPI.getConversations({
          status: ['active', 'pending_acceptance', 'transferring'], // Note: 'escalated' excluded intentionally
          limit: 100
        }),
        chatRealtimeAPI.getSupervisorDashboard()
      ]);

      if (!conversationsResponse.success || !dashboardResponse.success) {
        throw new Error('Error fetching resource management data');
      }

      if (!validateConversationsResponse(conversationsResponse.data)) {
        console.warn('Invalid conversations data structure:', conversationsResponse.data);
        return [];
      }
      
      const conversationsData = conversationsResponse.data;
      const dashboardData = dashboardResponse.data;

      const conversations = conversationsData.conversations;
      const issues: ResourceIssue[] = [];

      // 1. Agents away/busy but have active chats
      const agentWithChatsMap = new Map<string, { agent: any, chats: any[] }>();
      
      conversations.forEach(conv => {
        if (conv.agentId && conv.status === 'active') {
          if (!agentWithChatsMap.has(conv.agentId)) {
            agentWithChatsMap.set(conv.agentId, {
              agent: conv.assignedAgent,
              chats: []
            });
          }
          agentWithChatsMap.get(conv.agentId)?.chats.push(conv);
        }
      });

      // Check if agents are unavailable but have chats
      const agentStatuses = dashboardData?.agents || [];
      agentStatuses.forEach((agent: any) => {
        if ((agent.status === 'away' || agent.status === 'busy') && agentWithChatsMap.has(agent.id)) {
          const agentData = agentWithChatsMap.get(agent.id);
          if (agentData && agentData.chats.length > 0) {
            issues.push({
              type: 'agent_unavailable_with_chats',
              id: `agent-${agent.id}`,
              title: `${agent.name} no disponible con chats`,
              description: `Agente marcado como ${agent.status} pero tiene ${agentData.chats.length} conversación(es) activa(s)`,
              priority: agentData.chats.length > 2 ? 'high' : 'medium',
              affectedChats: agentData.chats.length,
              agentName: agent.name,
              agentId: agent.id,
              actionRequired: 'Reasignar conversaciones o cambiar estado del agente'
            });
          }
        }
      });

      // 2. High/Critical priority chats without agent assignment
      const unassignedPriorityChats = conversations.filter(conv => 
        !conv.agentId && isPriorityHigh(conv.priority)
      );

      if (unassignedPriorityChats.length > 0) {
        issues.push({
          type: 'unassigned_priority_chat',
          id: 'unassigned-priority',
          title: `${unassignedPriorityChats.length} chats prioritarios sin asignar`,
          description: 'Conversaciones de alta prioridad esperando asignación de agente',
          priority: 'high',
          affectedChats: unassignedPriorityChats.length,
          actionRequired: 'Asignar agentes disponibles o escalar a más agentes'
        });
      }

      // 3. Overloaded agents (more than expected concurrent chats)
      const MAX_CONCURRENT_CHATS = 3;
      agentWithChatsMap.forEach((agentData, agentId) => {
        if (agentData.chats.length > MAX_CONCURRENT_CHATS) {
          issues.push({
            type: 'overloaded_agent',
            id: `overload-${agentId}`,
            title: `${agentData.agent?.name || 'Agente'} sobrecargado`,
            description: `Manejando ${agentData.chats.length} chats (límite: ${MAX_CONCURRENT_CHATS})`,
            priority: agentData.chats.length > 5 ? 'high' : 'medium',
            affectedChats: agentData.chats.length,
            agentName: agentData.agent?.name,
            agentId: agentId,
            actionRequired: 'Redistribuir chats o asignar agente de apoyo'
          });
        }
      });

      // 4. Department bottlenecks (many chats pending in same department)
      const departmentChats = new Map<string, any[]>();
      conversations
        .filter(conv => conv.status === 'pending_acceptance')
        .forEach(conv => {
          const dept = 'general'; // TODO: Add department to ConversationMetadata type
          if (!departmentChats.has(dept)) {
            departmentChats.set(dept, []);
          }
          departmentChats.get(dept)?.push(conv);
        });

      departmentChats.forEach((chats, department) => {
        if (chats.length > 5) {
          issues.push({
            type: 'department_bottleneck',
            id: `dept-${department}`,
            title: `Cuello de botella en ${department}`,
            description: `${chats.length} chats pendientes de aceptación`,
            priority: chats.length > 10 ? 'high' : 'medium',
            affectedChats: chats.length,
            department: department,
            actionRequired: 'Asignar más agentes al departamento o redistribuir carga'
          });
        }
      });

      // Sort by priority and affected chats
      issues.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.priority];
        const bPriority = priorityOrder[b.priority];
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority;
        }
        
        return b.affectedChats - a.affectedChats;
      });

      return issues;
    },
    enabled: !!user, // Only run query when user is authenticated
    refetchInterval: 30000, // 30 seconds
    staleTime: 15000, // Data considered fresh for 15s
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
  });

  // Manual refresh function
  const refreshResourceManagement = async () => {
    await queryClient.invalidateQueries({ queryKey: ['supervisor', 'resource-management'] });
  };

  return {
    resourceIssues: resourceQuery.data ?? [],
    totalIssues: resourceQuery.data?.length ?? 0,
    loading: resourceQuery.isLoading,
    error: resourceQuery.error ? (resourceQuery.error as Error).message : null,
    refreshResourceManagement,
  };
}

export default useResourceManagementQuery;