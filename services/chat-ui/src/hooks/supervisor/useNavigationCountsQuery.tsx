'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';

interface NavigationCounts {
  escalations: number;
  interventions: number;
  authorizations: number;
  criticalIssues: number;
  totalAlerts: number;
}

interface UseNavigationCountsReturn {
  counts: NavigationCounts;
  loading: boolean;
  error: string | null;
  refreshCounts: () => Promise<void>;
}

/**
 * TanStack Query hook for navigation counts (badges in sidebar)
 * Replaces the problematic useNavigationCounts hook
 */
export function useNavigationCountsQuery(): UseNavigationCountsReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Navigation counts query with 20-second refresh (frequent for alerts)
  const countsQuery = useQuery({
    queryKey: ['supervisor', 'navigation-counts'],
    queryFn: async () => {
      if (!user) {
        throw new Error('Usuario no autenticado');
      }

      // Import API service dynamically
      const { default: chatRealtimeAPI } = await import('@/services/api');

      try {
        // Fetch data from multiple endpoints in parallel for navigation counts
        const [
          escalationsResponse,
          dashboardResponse,
          conversationsResponse
        ] = await Promise.all([
          chatRealtimeAPI.getEscalatedConversations().catch(() => ({ success: false, data: { escalations: [] } })),
          chatRealtimeAPI.getSupervisorDashboard().catch(() => ({ success: false, data: { pendingAuthorizations: [] } })),
          chatRealtimeAPI.getConversations({ 
            status: ['active', 'escalated', 'transferring'], 
            limit: 50 
          }).catch(() => ({ success: false, data: { conversations: [] } }))
        ]);

        // Extract escalations count
        const escalationsData = escalationsResponse.success 
          ? (Array.isArray(escalationsResponse.data) 
              ? escalationsResponse.data 
              : escalationsResponse.data?.escalations || [])
          : [];
        const escalationsCount = escalationsData.length;

        // Extract authorization requests count
        const pendingAuthorizations = dashboardResponse.success 
          ? (dashboardResponse.data?.pendingAuthorizations || [])
          : [];
        const authorizationsCount = pendingAuthorizations.length;

        // Calculate interventions (conversations that need supervisor attention)
        const conversations = conversationsResponse.success
          ? (conversationsResponse.data?.conversations || [])
          : [];
        
        const interventionsCount = conversations.filter((conv: any) => {
          const lastActivityMinutes = conv.lastActivityAt 
            ? Math.floor((Date.now() - new Date(conv.lastActivityAt).getTime()) / (1000 * 60))
            : 0;
            
          // Count as intervention if needs supervisor attention
          return (
            conv.status === 'escalated' ||
            lastActivityMinutes > 10 ||
            (conv.metadata?.transferCount || 0) > 2 ||
            (['high', 'urgent', 'critical'].includes(conv.priority) && lastActivityMinutes > 5)
          );
        }).length;

        // Calculate critical issues (urgent escalations + long delays)
        const criticalIssues = conversations.filter((conv: any) => {
          const lastActivityMinutes = conv.lastActivityAt 
            ? Math.floor((Date.now() - new Date(conv.lastActivityAt).getTime()) / (1000 * 60))
            : 0;
            
          return (
            conv.status === 'escalated' ||
            (['urgent', 'critical'].includes(conv.priority) && lastActivityMinutes > 3) ||
            lastActivityMinutes > 15
          );
        }).length;

        const totalAlerts = escalationsCount + authorizationsCount + criticalIssues;

        return {
          escalations: escalationsCount,
          interventions: interventionsCount,
          authorizations: authorizationsCount,
          criticalIssues,
          totalAlerts
        } as NavigationCounts;

      } catch (error) {
        console.warn('Error fetching navigation counts:', error);
        // Return zero counts on error to prevent UI breakage
        return {
          escalations: 0,
          interventions: 0,
          authorizations: 0,
          criticalIssues: 0,
          totalAlerts: 0
        } as NavigationCounts;
      }
    },
    enabled: !!user, // Only run query when user is authenticated
    refetchInterval: 20000, // 20 seconds (more frequent for alerts)
    staleTime: 10000, // Data considered fresh for 10s
    retry: 2, // Fewer retries for counts (not critical)
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 5000),
  });

  // Manual refresh function
  const refreshCounts = async () => {
    await queryClient.invalidateQueries({ queryKey: ['supervisor', 'navigation-counts'] });
  };

  return {
    counts: countsQuery.data ?? {
      escalations: 0,
      interventions: 0, 
      authorizations: 0,
      criticalIssues: 0,
      totalAlerts: 0
    },
    loading: countsQuery.isLoading,
    error: countsQuery.error ? (countsQuery.error as Error).message : null,
    refreshCounts,
  };
}

export default useNavigationCountsQuery;