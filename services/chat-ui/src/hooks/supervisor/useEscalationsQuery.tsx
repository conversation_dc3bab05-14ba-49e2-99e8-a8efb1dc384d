'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';

interface EscalatedConversation {
  id: string;
  customer: {
    id: string;
    name: string;
    phone: string;
  };
  status: string;
  channel: string;
  priority: number | string;
  assignedAgentId?: string;
  escalation?: {
    isEscalated: boolean;
    escalatedAt: number;
    escalatedBy: string;
    reason: string;
    level: number;
    priority: string;
  };
  metadata?: {
    escalationTimestamp?: number;
    messageCount?: number;
    isEscalated?: boolean;
  };
}

interface UseEscalationsReturn {
  escalations: EscalatedConversation[];
  loading: boolean;
  error: string | null;
  resolving: Record<string, boolean>;
  resolveEscalation: (conversationId: string, reason?: string) => Promise<void>;
  refreshEscalations: () => Promise<void>;
}

/**
 * TanStack Query hook for escalations management
 * Replaces the problematic useEscalations hook
 */
export function useEscalationsQuery(): UseEscalationsReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();


  // Escalations query with 30-second refresh
  const escalationsQuery = useQuery({
    queryKey: ['supervisor', 'escalations'],
    queryFn: async () => {
      if (!user) {
        throw new Error('Usuario no autenticado');
      }

      // Import API service dynamically to avoid circular dependencies
      const { default: chatRealtimeAPI } = await import('@/services/api');

      // Fetch escalated conversations using API service
      const response = await chatRealtimeAPI.getEscalatedConversations();
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Error fetching escalations');
      }

      // Extract escalations from API response
      const responseData = response.data as {escalations: any[]} | any[];
      const escalationsData = Array.isArray(responseData) ? responseData : responseData?.escalations || [];
      
      return escalationsData as EscalatedConversation[];
    },
    enabled: !!user, // Only run query when user is authenticated
    refetchInterval: 30000, // 30 seconds
    staleTime: 15000, // Data considered fresh for 15s
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
  });

  // Resolve escalation function (can be enhanced with useMutation for optimistic updates later)
  const resolveEscalation = async (conversationId: string, reason = 'Resuelto por supervisor') => {
    if (!user) {
      throw new Error('Usuario no autenticado');
    }

    try {
      // Import API service dynamically to avoid circular dependencies
      const { default: chatRealtimeAPI } = await import('@/services/api');

      // Resolve escalation using API service
      const response = await chatRealtimeAPI.resolveEscalation(conversationId, {
        reason,
        resolvedBy: user.id, // Use agent ID instead of Firebase UID
      });
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Error resolviendo escalación');
      }

      // Invalidate escalations query to refetch updated data
      await queryClient.invalidateQueries({ queryKey: ['supervisor', 'escalations'] });
      
      // Show success feedback (could be replaced with toast)
      console.log(`Escalación ${conversationId} resuelta correctamente`);
      
    } catch (error) {
      console.error('Error resolving escalation:', error);
      throw error;
    }
  };

  // Manual refresh function
  const refreshEscalations = async () => {
    await queryClient.invalidateQueries({ queryKey: ['supervisor', 'escalations'] });
  };

  return {
    escalations: escalationsQuery.data ?? [],
    loading: escalationsQuery.isLoading,
    error: escalationsQuery.error ? (escalationsQuery.error as Error).message : null,
    resolving: {}, // TODO: Implement with useMutation for per-escalation loading states
    resolveEscalation,
    refreshEscalations,
  };
}

export default useEscalationsQuery;