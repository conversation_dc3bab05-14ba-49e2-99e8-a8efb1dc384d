'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { SupervisorDashboardData } from '@/types/api';

interface UseSupervisorQueriesReturn {
  dashboardData: SupervisorDashboardData | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refreshDashboard: () => Promise<void>;
}

/**
 * TanStack Query-based supervisor data hook
 * Replaces the problematic useInterval-based hooks with proper caching and background refetch
 * 
 * Benefits:
 * - No memory leaks from setInterval
 * - Automatic background refetch
 * - Intelligent caching and deduplication
 * - Built-in error handling and retry logic
 */
export function useSupervisorQueries(): UseSupervisorQueriesReturn {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Dashboard query with 30-second refresh interval
  const dashboardQuery = useQuery({
    queryKey: ['supervisor', 'dashboard'],
    queryFn: async () => {
      if (!user) {
        throw new Error('Usuario no autenticado');
      }

      console.log('🔄 Making supervisor dashboard request at', new Date().toISOString());

      // Import API service dynamically to avoid circular dependencies
      const { default: chatRealtimeAPI } = await import('@/services/api');
      const response = await chatRealtimeAPI.getSupervisorDashboard();
      
      if (!response.success) {
        throw new Error(response.error?.message || 'Error fetching dashboard data');
      }

      console.log('✅ Dashboard data received:', { 
        availableAgents: response.data?.summary?.available,
        totalAgents: response.data?.summary?.totalAgents 
      });

      return response.data as SupervisorDashboardData;
    },
    enabled: !!user, // Only run query when user is authenticated
    refetchInterval: 30000, // 30 seconds - same as original hook
    staleTime: 2000, // Data considered fresh for 2s (reduced for development)
    refetchOnWindowFocus: true, // Refetch when switching tabs/windows
    retry: 3,
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000),
  });

  // Manual refresh function
  const refreshDashboard = async () => {
    await queryClient.invalidateQueries({ queryKey: ['supervisor', 'dashboard'] });
  };

  return {
    dashboardData: dashboardQuery.data ?? null,
    loading: dashboardQuery.isLoading,
    error: dashboardQuery.error ? (dashboardQuery.error as Error).message : null,
    lastUpdated: dashboardQuery.dataUpdatedAt ? new Date(dashboardQuery.dataUpdatedAt) : null,
    refreshDashboard,
  };
}

export default useSupervisorQueries;