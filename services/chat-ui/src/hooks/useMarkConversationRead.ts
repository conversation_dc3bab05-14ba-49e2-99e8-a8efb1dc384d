/**
 * Hook for marking conversations as read with optimistic updates
 * Provides instant UI feedback while syncing with backend
 */

import { useCallback } from 'react';
import { mutate } from 'swr';
import { useAuth } from '@/contexts/AuthContext';
import type { Conversation } from '@/types';

const CHAT_REALTIME_API_BASE = process.env.NEXT_PUBLIC_CHAT_REALTIME_URL || 'http://localhost:3003';

export function useMarkConversationRead() {
  const { user } = useAuth();

  const markAsRead = useCallback(async (conversationId: string): Promise<void> => {
    if (!user?.id) {
      console.warn('Cannot mark conversation as read: user not authenticated');
      return;
    }

    try {
      // 1. Optimistic update: immediately set unreadCount to 0
      const optimisticUpdate = (data: any) => {
        if (!data) return data;
        
        console.log(`🔍 Optimistic update - data structure:`, {
          hasConversation: !!data.conversation,
          hasDataConversations: !!data.data?.conversations,
          hasDirectConversations: !!data.conversations,
          conversationId
        });

        // Update single conversation cache if exists
        if (data.conversation) {
          return {
            ...data,
            conversation: {
              ...data.conversation,
              unreadCount: 0
            }
          };
        }

        // Update conversations list cache (API structure: data.data.conversations)
        if (data.data?.conversations) {
          return {
            ...data,
            data: {
              ...data.data,
              conversations: data.data.conversations.map((conv: Conversation) =>
                conv.id === conversationId 
                  ? { ...conv, unreadCount: 0 }
                  : conv
              )
            }
          };
        }

        // Fallback: direct conversations array (for Firebase real-time)
        if (data.conversations) {
          return {
            ...data,
            conversations: data.conversations.map((conv: Conversation) =>
              conv.id === conversationId 
                ? { ...conv, unreadCount: 0 }
                : conv
            )
          };
        }

        return data;
      };

      // Apply optimistic updates to all relevant SWR caches
      mutate(
        (key) => {
          // Handle array keys like ['conversations', filters]
          if (Array.isArray(key) && key[0] === 'conversations') {
            return true;
          }
          // Handle string keys that include 'conversation'
          if (typeof key === 'string' && key.includes('conversation')) {
            return true;
          }
          return false;
        },
        optimisticUpdate,
        { revalidate: false }
      );

      console.log(`⚡ Optimistic update: conversation ${conversationId} marked as read`);

      // 2. Backend sync: call the API to persist the change
      const response = await fetch(`${CHAT_REALTIME_API_BASE}/api/conversations/${conversationId}/read`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id
        })
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error?.message || 'Failed to mark conversation as read');
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error?.message || 'API returned error');
      }

      console.log(`✅ Backend sync: conversation ${conversationId} marked as read successfully`);

      // 3. No need to revalidate - optimistic update is sufficient
      // The backend sync already happened, so the data is consistent
      console.log(`🎯 Optimistic update completed successfully for conversation ${conversationId}`);

    } catch (error) {
      console.error('❌ Failed to mark conversation as read:', error);

      // 4. Only revalidate on error to restore correct state
      // This will fetch fresh data from backend and undo the optimistic update
      mutate(
        (key) => {
          if (Array.isArray(key) && key[0] === 'conversations') {
            return true;
          }
          if (typeof key === 'string' && key.includes('conversation')) {
            return true;
          }
          return false;
        },
        undefined,
        { revalidate: true }
      );

      console.log(`🔄 Rolled back optimistic update for conversation ${conversationId} due to error`);
      // Don't throw error to avoid breaking UI - the revalidation restores correct state
    }
  }, [user?.id]);

  return {
    markAsRead,
    isAuthenticated: !!user?.id
  };
}