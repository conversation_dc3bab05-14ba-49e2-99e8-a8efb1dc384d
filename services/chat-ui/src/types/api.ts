// Types for Chat Realtime Service API
// Based on /services/chat-realtime/src/types.ts

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
  timestamp: number;
}

// Conversation Types
export interface Conversation {
  id: string;
  customerId: string;
  agentId?: string;
  supervisorId?: string;
  status: ConversationStatus;
  priority?: Priority;
  channel: Channel;
  department: string;
  metadata: ConversationMetadata;
  createdAt: string;
  updatedAt: string;
  lastActivityAt: string;
  assignedAt?: string;
  closedAt?: string;
  // UI extension fields
  customer?: Customer;
  assignedAgent?: any; // For useFirebaseRealtime compatibility
  lastMessage?: Message;
  unreadCount?: number;
  tags?: string[]; // For useFirebaseRealtime compatibility
}

export type ConversationStatus = 
  | 'pending' 
  | 'active' 
  | 'transferring' 
  | 'pending_acceptance'
  | 'supervised' 
  | 'escalated' 
  | 'closed';

export type Priority = 'low' | 'medium' | 'high' | 'urgent';

export type Channel = 'whatsapp' | 'web_chat' | 'sms' | 'email' | 'phone';

export interface ConversationMetadata {
  customerName: string;
  customerPhone?: string;
  customerEmail?: string;
  customerAvatar?: string; // For ConversationList compatibility
  totalMessages?: number; // For ConversationList compatibility
  subject?: string;
  tags?: string[];
  routingInfo?: RoutingInfo;
  transferHistory?: TransferRecord[];
  supervisionHistory?: SupervisionRecord[];
}

export interface RoutingInfo {
  assignedDepartment: string;
  aiAnalysisAttempts: number;
  aiAnalysisHistory: AIAnalysisAttempt[];
  departmentAssignedAt: string;
}

export interface AIAnalysisAttempt {
  attempt: number;
  input: string;
  result: string;
  confidence?: number;
  timestamp: string;
}

export interface TransferRecord {
  id: string;
  fromAgentId?: string;
  toAgentId?: string;
  reason: string;
  status: 'pending' | 'accepted' | 'rejected';
  transferredAt: string;
  acceptedAt?: string;
  rejectedAt?: string;
}

export interface SupervisionRecord {
  id: string;
  supervisorId: string;
  mode: 'observe' | 'participate';
  startedAt: string;
  endedAt?: string;
  reason?: string;
}

// Customer Types  
export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  avatar?: string;
  location?: string;
  joinDate?: string;
  totalConversations?: number;
  tags?: string[];
}

// Message Types
export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: 'customer' | 'agent' | 'bot' | 'system' | 'supervisor';
  content: string;
  type: MessageType;
  timestamp: string;
  readBy?: Record<string, string>; // userId -> readAt timestamp
  senderName?: string; // For ChatArea compatibility
  metadata?: MessageMetadata;
}

export type MessageType = 'text' | 'image' | 'file' | 'system' | 'note';

export interface MessageMetadata {
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  systemAction?: string;
  noteCategory?: string;
  noteVisibility?: 'private' | 'team' | 'supervisor';
  tempId?: string; // For optimistic UI compatibility
}

// Agent Types
export interface Agent {
  id: string;
  name: string;
  email: string;
  role: AgentRole;
  status: AgentStatusDetails; // Changed: now complex object from Firebase
  agent_type: 'human' | 'bot' | 'supervisor'; // NEW: For filtering bot vs human metrics
  departments: string[];
  capacity: AgentCapacity;
  organizationId: string;
  metadata: AgentMetadata;
  createdAt: string;
  lastActiveAt: string;
}

export type AgentRole = 'agent' | 'supervisor' | 'admin';

export type AgentStatus = 
  | 'available' 
  | 'busy' 
  | 'away';

// NEW: Complex status structure returned by Firebase-hybrid API
export interface AgentStatusDetails {
  availability: AgentStatus;
  isAvailable: boolean;
  canReceiveTransfers: boolean;
  currentSessions: number;
  maxSessions: number;
  lastActivityAt: number | null;
}

export interface AgentCapacity {
  maxConcurrentChats: number;
  currentChatCount: number;
  utilization: number; // 0-100%
}

export interface AgentMetadata {
  skills: string[];
  languages: string[];
  timezone: string;
  workingHours?: {
    start: string;
    end: string;
    days: string[];
  };
}

// Typing Indicators
export interface TypingIndicator {
  userId: string;
  userType: 'customer' | 'agent' | 'supervisor';
  isTyping: boolean;
  timestamp: string;
}

// Note Types
export interface ConversationNote {
  id: string;
  conversationId: string;
  authorId: string;
  content: string;
  category?: NoteCategory;
  priority?: Priority;
  visibility?: NoteVisibility;
  createdAt: string;
  updatedAt?: string;
}

export type NoteCategory = 
  | 'general' 
  | 'technical' 
  | 'commercial' 
  | 'follow_up' 
  | 'escalation'
  | 'coaching';

export type NoteVisibility = 'private' | 'team' | 'supervisor' | 'public';

// Analytics Types
export interface ConversationAnalytics {
  conversationId: string;
  metrics: {
    duration: number; // seconds
    messageCount: number;
    responseTime: {
      first: number; // seconds
      average: number; // seconds
    };
    transferCount: number;
    escalationCount: number;
    supervisionTime: number; // seconds
  };
  satisfaction?: {
    score: number;
    feedback?: string;
  };
}

// System Status Types
export interface SystemStatus {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  version: string;
  uptime: number;
  connections: {
    firebase: boolean;
    redis?: boolean;
    database?: boolean;
  };
  metrics: {
    activeConversations: number;
    activeAgents: number;
    queueLength: number;
    avgResponseTime: number;
  };
  timestamp: number;
}

// Queue Types
export interface ConversationQueue {
  department: string;
  conversations: QueuedConversation[];
  metrics: {
    totalCount: number;
    avgWaitTime: number;
    longestWaitTime: number;
  };
}

export interface QueuedConversation {
  conversationId: string;
  priority?: Priority;
  waitTime: number;
  customerName: string;
  channel: Channel;
  subject?: string;
}

// Request/Response Types for specific endpoints
export interface CreateConversationRequest {
  customerId: string;
  channel: Channel;
  department: string;
  priority?: Priority;
  metadata: Partial<ConversationMetadata>;
}

export interface SendMessageRequest {
  content: string;
  type?: MessageType;
  senderId: string;
  senderType: 'customer' | 'agent' | 'bot' | 'system' | 'supervisor';
  senderName?: string; // For ChatArea compatibility
  metadata?: MessageMetadata;
}

export interface TransferConversationRequest {
  targetAgentId: string;
  reason: string;
  priority?: Priority;
}

export interface UpdateAgentStatusRequest {
  status: AgentStatus;
  reason?: string;
  estimatedDuration?: number; // for breaks, lunch, etc.
  supervisorAuthorizationId?: string; // Required for 'busy' status
}

export interface SuperviseConversationRequest {
  supervisorId: string;
  mode: 'observe' | 'participate';
  reason?: string;
}

export interface AddNoteRequest {
  content: string;
  category?: NoteCategory;
  priority?: Priority;
  visibility?: NoteVisibility;
  authorId?: string; // For CustomerPanel compatibility
  authorName?: string; // For CustomerPanel compatibility
  type?: string; // For CustomerPanel compatibility
}

export interface TransferConversationRequest {
  targetAgentId: string;
  reason: string;
}

// Filter types for listing conversations
export interface ConversationFilters {
  status?: ConversationStatus | ConversationStatus[]; // Allow single status or array
  agentId?: string;
  supervisorId?: string;
  department?: string;
  priority?: Priority[];
  channel?: Channel[];
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
  limit?: number;
  offset?: number;
}

// Agent Status Management Interfaces
export interface AgentStatusHistory {
  id: string;
  agentId: string;
  status: AgentStatus;
  previousStatus: AgentStatus;
  reason?: string;
  supervisorAuthorizationId?: string;
  startTime: string;
  endTime?: string;
  duration?: number; // seconds
  createdAt: string;
}

export interface SupervisorAuthorization {
  id: string;
  supervisorId: string;
  supervisorName?: string;
  agentId: string;
  agentName?: string;
  authorizedStatus: 'busy'; // Only busy requires authorization for now
  reason: string;
  maxDuration?: number; // seconds, null = no limit
  createdAt: string;
  usedAt?: string;
  expiresAt?: string;
  isUsed: boolean;
  isExpired: boolean;
}

// New Request/Response types
export interface CreateSupervisionAuthorizationRequest {
  agentId: string;
  authorizedStatus: 'busy';
  reason: string;
  maxDuration?: number; // seconds
  expiresAt?: string; // ISO string
}

export interface AgentStatusSummary {
  agentId: string;
  agentName: string;
  currentStatus: AgentStatus;
  currentStatusDuration: number; // seconds
  todaysStatusHistory: AgentStatusHistory[];
  activeAuthorizations: SupervisorAuthorization[];
  hasActiveChatsThatNeedAction?: boolean; // For non-available agents
  workloadConversations: number; // Total workload (all non-closed conversations)
  conversationsByStatus: {
    active: number;
    supervised: number;
    escalated: number;
    pending_acceptance: number;
    transferring: number;
  };
  totalConversationsToday: number; // Total conversations assigned today
}

// Supervisor Dashboard Data - matches backend response exactly
export interface SupervisorDashboardData {
  agents: AgentStatusSummary[];
  pendingAuthorizations: SupervisorAuthorization[];
  todaysTotalByStatus: Record<AgentStatus, number>;
  summary: {
    totalAgents: number;
    available: number;
    busy: number;
    away: number;
    withActiveChatActions: number;
    totalWorkloadConversations: number; // All non-closed conversations across agents
    totalConversationsToday: number; // Total conversations assigned today
    systemConversationsByStatus: {
      active?: number;
      supervised?: number;
      escalated?: number;
      pending_acceptance?: number;
      transferring?: number;
    };
    avgResponseTimeMinutes: number; // Real calculated response time from message timestamps
    responseTimeTrend?: {
      direction: 'up' | 'down' | 'neutral';
      differenceMinutes: string;
    };
    workloadTrend?: {
      direction: 'up' | 'down' | 'neutral';
      difference: string;
    };
    completedTrend?: {
      direction: 'up' | 'down' | 'neutral';
      difference: string;
    };
    escalationsTrend?: {
      direction: 'up' | 'down' | 'neutral';
      difference: string;
    };
  };
}
