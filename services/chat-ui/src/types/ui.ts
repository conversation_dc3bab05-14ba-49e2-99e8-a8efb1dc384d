// UI-specific types for Chat UI application

import { Agent, Conversation, Message, ConversationNote } from './api';

// User session types
export interface UserSession {
  user: Agent;
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
}

// Application state types
export interface AppState {
  user: Agent | null;
  isAuthenticated: boolean;
  activeConversations: Conversation[];
  selectedConversationId: string | null;
  ui: UIState;
}

export interface UIState {
  sidebarOpen: boolean;
  contextPanelOpen: boolean;
  activeTab: 'profile' | 'notes' | 'knowledge';
  chatListFilter: ChatListFilter;
  isTyping: boolean;
  cannedResponsesOpen: boolean;
  notifications: UINotification[];
}

export interface ChatListFilter {
  status: 'all' | 'assigned' | 'unassigned' | 'pending' | 'active';
  department?: string;
  priority?: string;
  search?: string;
}

// UI Components props types
export interface ChatListItemProps {
  conversation: Conversation;
  isActive: boolean;
  onClick: (conversationId: string) => void;
  unreadCount: number;
}

export interface MessageItemProps {
  message: Message;
  showSender: boolean;
  isOwn: boolean;
  onReadStatusChange?: (messageId: string) => void;
}

export interface AgentStatusSelectorProps {
  currentStatus: Agent['status'];
  onStatusChange: (status: Agent['status']) => void;
  disabled?: boolean;
}

export interface ConversationHeaderProps {
  conversation: Conversation;
  onTransfer: () => void;
  onEscalate: () => void;
  onClose: () => void;
  onTogglePanel: () => void;
}

export interface ContextPanelProps {
  conversation: Conversation;
  isOpen: boolean;
  onClose: () => void;
  activeTab: UIState['activeTab'];
  onTabChange: (tab: UIState['activeTab']) => void;
}

// Notification types
export interface UINotification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: number;
  read: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// Form types
export interface TransferDialogForm {
  targetAgentId: string;
  reason: string;
  urgent: boolean;
}

export interface NoteForm {
  content: string;
  category: ConversationNote['category'];
  priority: ConversationNote['priority'];
  visibility: ConversationNote['visibility'];
}

export interface MessageForm {
  content: string;
  type: Message['type'];
  attachments?: File[];
}

// Canned responses
export interface CannedResponse {
  id: string;
  category: string;
  title: string;
  content: string;
  variables?: string[]; // For template variables like {customerName}
  department?: string;
  tags?: string[];
}

// Knowledge base
export interface KnowledgeArticle {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  category: string;
  tags: string[];
  relevanceScore?: number; // For search results
  canInsert: boolean;
}

// Dashboard types (for supervisor view)
export interface DashboardMetrics {
  activeConversations: number;
  waitingConversations: number;
  availableAgents: number;
  busyAgents: number;
  avgResponseTime: number;
  avgResolutionTime: number;
  satisfactionScore: number;
  todayVolume: number;
}

export interface AgentPerformance {
  agent: Agent;
  metrics: {
    conversationsHandled: number;
    avgResponseTime: number;
    avgResolutionTime: number;
    transferRate: number;
    satisfactionScore: number;
    utilization: number;
  };
}

// Real-time updates
export interface RealtimeUpdate {
  type: 'message' | 'conversation_update' | 'agent_status' | 'typing' | 'notification';
  payload: any;
  timestamp: number;
}

// Error handling
export interface UIError {
  id: string;
  type: 'network' | 'validation' | 'auth' | 'server' | 'unknown';
  message: string;
  details?: any;
  timestamp: number;
  recovered: boolean;
}

// Loading states
export interface LoadingState {
  conversations: boolean;
  messages: boolean;
  agents: boolean;
  transfer: boolean;
  notes: boolean;
  sending: boolean;
}

// Theme types
export interface Theme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    foreground: string;
    muted: string;
  };
}

// Keyboard shortcuts
export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  shift?: boolean;
  alt?: boolean;
  action: () => void;
  description: string;
}

