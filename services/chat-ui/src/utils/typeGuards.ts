/**
 * Type guards and utility functions for safe API data access
 */

export type Priority = 'low' | 'medium' | 'high' | 'urgent' | 'critical';

/**
 * Safely normalizes priority values from mixed string/number inputs
 */
export const normalizePriority = (priority: unknown): Priority => {
  const priorityMap: Record<string | number, Priority> = {
    1: 'low', 
    2: 'medium', 
    3: 'high', 
    4: 'urgent', 
    5: 'critical',
    'critical': 'critical',
    'urgent': 'urgent',
    'high': 'high',
    'medium': 'medium',
    'low': 'low'
  };
  
  const key = String(priority || 'medium').toLowerCase();
  return priorityMap[key] || 'medium';
};

/**
 * Safely calculates minutes since last activity with error handling
 */
export const getLastActivityMinutes = (lastActivityAt: string | undefined): number => {
  if (!lastActivityAt) return 0;
  
  try {
    const time = new Date(lastActivityAt).getTime();
    return isNaN(time) ? 0 : Math.floor((Date.now() - time) / (1000 * 60));
  } catch {
    return 0;
  }
};

/**
 * Safe property access with fallback
 */
export const safeGet = <T>(obj: any, path: string, defaultValue?: T): T | undefined => {
  try {
    return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
  } catch {
    return defaultValue;
  }
};

/**
 * Validates conversations response structure
 */
export const validateConversationsResponse = (data: unknown): data is { conversations: any[], total: number } => {
  return data !== null && 
         typeof data === 'object' && 
         'conversations' in data && 
         Array.isArray((data as any).conversations);
};

/**
 * Checks if a priority level is considered urgent/critical
 */
export const isPriorityUrgent = (priority: unknown): boolean => {
  const normalizedPriority = normalizePriority(priority);
  return ['urgent', 'critical'].includes(normalizedPriority);
};

/**
 * Checks if a priority level is high or above
 */
export const isPriorityHigh = (priority: unknown): boolean => {
  const normalizedPriority = normalizePriority(priority);
  return ['high', 'urgent', 'critical'].includes(normalizedPriority);
};