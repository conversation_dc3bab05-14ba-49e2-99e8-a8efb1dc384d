/**
 * CX System - Debug Tools
 * 
 * Sistema completo de herramientas de debug para Chat UI
 * Implementa window.cxDebug según especificación de CLAUDE.md
 */

interface CacheState {
  conversations: any[];
  messages: Record<string, any[]>;
  agents: any[];
  authToken: string | null;
  userProfile: any | null;
  connectionState: 'online' | 'offline' | 'reconnecting';
  pendingActions: any[];
  lastSync: string | null;
}

interface PendingAction {
  id: string;
  type: 'send_message' | 'update_status' | 'sync_conversation';
  payload: any;
  timestamp: string;
  retries: number;
}

class CXDebugTools {
  private offlineSimulated = false;
  private originalFetch: typeof fetch;
  private originalXHR: typeof XMLHttpRequest;

  constructor() {
    // Only initialize on client-side
    if (typeof window !== 'undefined') {
      this.originalFetch = window.fetch.bind(window);
      this.originalXHR = window.XMLHttpRequest;
      this.initializeDebugTools();
    } else {
      // Server-side safe defaults
      this.originalFetch = null as any;
      this.originalXHR = null as any;
    }
  }

  private initializeDebugTools() {
    console.log('🛠️  CX Debug Tools initialized');
    console.log('Available tools: window.cxDebug');
    console.log('Type: cxDebug.help() for usage information');
  }

  /**
   * Muestra ayuda con todos los comandos disponibles
   */
  help() {
    console.log(`
🛠️  CX Debug Tools - Available Commands:

📋 Cache Management:
   cxDebug.clearCache()      - Clear all cached data
   cxDebug.showCacheState()  - Show current cache state
   cxDebug.exportCache()     - Export cache as JSON

🔄 Synchronization:
   cxDebug.forceSync()       - Force synchronization with Firebase
   cxDebug.resetConnection() - Reset Firebase connection
   cxDebug.getConnectionInfo() - Get connection status

📱 Offline Testing:
   cxDebug.simulateOffline() - Simulate offline mode
   cxDebug.simulateOnline()  - Return to online mode
   cxDebug.isOfflineSimulated() - Check if offline is simulated

📄 Pending Actions:
   cxDebug.showPendingQueue() - Show pending actions queue
   cxDebug.clearPendingQueue() - Clear all pending actions
   cxDebug.retryPendingActions() - Retry all pending actions

🔧 System Info:
   cxDebug.getSystemInfo()   - Get system information
   cxDebug.runDiagnostics()  - Run system diagnostics
   cxDebug.exportLogs()      - Export recent logs

💾 Data Management:
   cxDebug.seedTestData()    - Load test conversations
   cxDebug.resetToDefaults() - Reset to clean state
    `);
  }

  /**
   * Clear all cached data from localStorage
   */
  clearCache() {
    console.log('🗑️  Clearing all cached data...');
    
    const keysToRemove = [
      'cx-conversations-cache',
      'cx-messages-cache', 
      'cx-agents-cache',
      'cx-user-profile-cache',
      'cx-connection-state',
      'cx-pending-actions',
      'cx-last-sync',
      'supabase-auth-token'
    ];
    
    let clearedCount = 0;
    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        clearedCount++;
      }
    });
    
    // También limpiar sessionStorage
    sessionStorage.clear();
    
    console.log(`✅ Cleared ${clearedCount} cache entries`);
    console.log('💡 Reload the page to see fresh data');
    
    return { clearedKeys: clearedCount };
  }

  /**
   * Show current state of all cached data
   */
  showCacheState(): CacheState {
    console.log('🔍 Current Cache State:');
    
    const state: CacheState = {
      conversations: this.getCachedData('cx-conversations-cache') || [],
      messages: this.getCachedData('cx-messages-cache') || {},
      agents: this.getCachedData('cx-agents-cache') || [],
      authToken: localStorage.getItem('supabase-auth-token'),
      userProfile: this.getCachedData('cx-user-profile-cache'),
      connectionState: (this.getCachedData('cx-connection-state') as any) || 'online',
      pendingActions: this.getCachedData('cx-pending-actions') || [],
      lastSync: localStorage.getItem('cx-last-sync')
    };
    
    console.table({
      'Conversations': state.conversations.length,
      'Message Threads': Object.keys(state.messages).length,
      'Agents Cached': state.agents.length,
      'Auth Token': state.authToken ? '✅ Present' : '❌ Missing',
      'User Profile': state.userProfile ? '✅ Loaded' : '❌ Missing',
      'Connection': state.connectionState,
      'Pending Actions': state.pendingActions.length,
      'Last Sync': state.lastSync || 'Never'
    });
    
    return state;
  }

  /**
   * Export cache data as downloadable JSON
   */
  exportCache() {
    const cacheState = this.showCacheState();
    const dataStr = JSON.stringify(cacheState, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `cx-cache-export-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
    
    console.log('📥 Cache data exported as JSON file');
    return cacheState;
  }

  /**
   * Force synchronization with Firebase
   */
  forceSync() {
    console.log('🔄 Forcing synchronization with Firebase...');
    
    // Trigger re-fetch of conversations
    window.dispatchEvent(new CustomEvent('cx:force-sync', {
      detail: { timestamp: Date.now() }
    }));
    
    // Clear sync timestamp to force refresh
    localStorage.removeItem('cx-last-sync');
    
    console.log('✅ Sync triggered - check Network tab for API calls');
    console.log('💡 If no API calls appear, check Firebase connection');
  }

  /**
   * Reset Firebase connection
   */
  resetConnection() {
    console.log('🔌 Resetting Firebase connection...');
    
    // Trigger connection reset event
    window.dispatchEvent(new CustomEvent('cx:reset-connection', {
      detail: { timestamp: Date.now() }
    }));
    
    localStorage.setItem('cx-connection-state', 'reconnecting');
    
    console.log('✅ Connection reset triggered');
    console.log('💡 Watch for reconnection in Network tab');
  }

  /**
   * Get detailed connection information
   */
  getConnectionInfo() {
    const logsProxy = (window as any).__cxLogsProxy;
    const info = {
      online: navigator.onLine,
      connectionState: this.getCachedData('cx-connection-state') || 'unknown',
      lastSync: localStorage.getItem('cx-last-sync'),
      offlineSimulated: this.offlineSimulated,
      logsProxyStatus: logsProxy ? logsProxy.getStatus() : 'not_available',
      logsProxyEnabled: logsProxy ? logsProxy.isEnabled() : false,
      firebaseConnected: 'unknown' // This would need to be set by Firebase service
    };
    
    console.log('🔍 Connection Information:');
    console.table(info);
    
    return info;
  }

  /**
   * Simulate offline mode for testing
   */
  simulateOffline() {
    if (this.offlineSimulated) {
      console.log('⚠️  Offline mode already simulated');
      return;
    }
    
    console.log('📱 Simulating offline mode...');
    this.offlineSimulated = true;
    
    // Override fetch to reject
    window.fetch = (...args) => {
      console.log('🚫 [OFFLINE SIM] Fetch blocked:', args[0]);
      return Promise.reject(new Error('Simulated offline - fetch blocked'));
    };
    
    // Override XMLHttpRequest
    (window as any).XMLHttpRequest = class {
      open() { throw new Error('Simulated offline - XHR blocked'); }
      send() { throw new Error('Simulated offline - XHR blocked'); }
    };
    
    // Trigger offline events
    window.dispatchEvent(new Event('offline'));
    localStorage.setItem('cx-connection-state', 'offline');
    
    console.log('✅ Offline mode activated');
    console.log('💡 Use cxDebug.simulateOnline() to restore');
  }

  /**
   * Return to online mode
   */
  simulateOnline() {
    if (!this.offlineSimulated) {
      console.log('⚠️  Already in online mode');
      return;
    }
    
    console.log('📱 Returning to online mode...');
    this.offlineSimulated = false;
    
    // Restore original methods
    window.fetch = this.originalFetch;
    window.XMLHttpRequest = this.originalXHR;
    
    // Trigger online events
    window.dispatchEvent(new Event('online'));
    localStorage.setItem('cx-connection-state', 'online');
    
    console.log('✅ Online mode restored');
    console.log('💡 Triggering automatic sync...');
    this.forceSync();
  }

  /**
   * Check if offline mode is currently simulated
   */
  isOfflineSimulated(): boolean {
    return this.offlineSimulated;
  }

  /**
   * Show pending actions queue
   */
  showPendingQueue(): PendingAction[] {
    const pendingActions = this.getCachedData('cx-pending-actions') || [];
    
    console.log(`📄 Pending Actions Queue (${pendingActions.length} items):`);
    
    if (pendingActions.length === 0) {
      console.log('✅ No pending actions');
      return [];
    }
    
    console.table(pendingActions.map((action: any) =>  ({
      ID: action.id,
      Type: action.type,
      Retries: action.retries,
      Age: this.getTimeAgo(action.timestamp)
    })));
    
    return pendingActions;
  }

  /**
   * Clear all pending actions
   */
  clearPendingQueue() {
    localStorage.removeItem('cx-pending-actions');
    console.log('🗑️  Pending actions queue cleared');
  }

  /**
   * Retry all pending actions
   */
  retryPendingActions() {
    const pendingActions = this.getCachedData('cx-pending-actions') || [];
    
    if (pendingActions.length === 0) {
      console.log('ℹ️  No pending actions to retry');
      return;
    }
    
    console.log(`🔄 Retrying ${pendingActions.length} pending actions...`);
    
    // Trigger retry event
    window.dispatchEvent(new CustomEvent('cx:retry-pending', {
      detail: { actions: pendingActions }
    }));
    
    console.log('✅ Retry triggered - watch Network tab for API calls');
  }

  /**
   * Get comprehensive system information
   */
  getSystemInfo() {
    const info = {
      userAgent: navigator.userAgent,
      url: window.location.href,
      viewport: `${window.innerWidth}x${window.innerHeight}`,
      localStorage: `${this.getStorageSize('localStorage')} KB`,
      sessionStorage: `${this.getStorageSize('sessionStorage')} KB`,
      cacheState: this.showCacheState(),
      connectionInfo: this.getConnectionInfo(),
      timestamp: new Date().toISOString()
    };
    
    console.log('🖥️  System Information:');
    console.log(info);
    
    return info;
  }

  /**
   * Run comprehensive system diagnostics
   */
  runDiagnostics() {
    console.log('🔧 Running CX System Diagnostics...');
    
    const diagnostics = {
      cache: this.diagnoseCacheHealth(),
      connection: this.diagnoseConnection(),
      storage: this.diagnoseStorage(),
      performance: this.diagnosePerformance()
    };
    
    console.log('📊 Diagnostics Complete:');
    console.table(diagnostics);
    
    return diagnostics;
  }

  /**
   * Load test data for development
   */
  seedTestData() {
    console.log('🌱 Seeding test data...');
    
    // This would integrate with your test data creation
    const testData = {
      conversations: [
        { id: 'test-1', customer: { name: 'Test Customer 1' }, status: 'active' },
        { id: 'test-2', customer: { name: 'Test Customer 2' }, status: 'pending' }
      ],
      messages: {
        'test-1': [
          { id: 'msg-1', content: 'Test message', type: 'customer' },
          { id: 'msg-2', content: 'Test response', type: 'agent' }
        ]
      }
    };
    
    localStorage.setItem('cx-conversations-cache', JSON.stringify(testData.conversations));
    localStorage.setItem('cx-messages-cache', JSON.stringify(testData.messages));
    localStorage.setItem('cx-last-sync', new Date().toISOString());
    
    console.log('✅ Test data loaded');
    console.log('💡 Reload the page to see test data');
  }

  /**
   * Reset to clean state
   */
  resetToDefaults() {
    console.log('🔄 Resetting to defaults...');
    
    this.clearCache();
    this.simulateOnline();
    this.clearPendingQueue();
    
    console.log('✅ Reset complete');
    console.log('💡 Reload the page for fresh start');
  }

  // Helper methods
  private getCachedData(key: string): any {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch {
      return null;
    }
  }

  private getStorageSize(storageType: 'localStorage' | 'sessionStorage'): number {
    let total = 0;
    const storage = window[storageType];
    for (let key in storage) {
      if (storage.hasOwnProperty(key)) {
        total += storage[key].length + key.length;
      }
    }
    return Math.round(total / 1024 * 100) / 100; // KB
  }

  private getTimeAgo(timestamp: string): string {
    const diff = Date.now() - new Date(timestamp).getTime();
    const minutes = Math.floor(diff / 60000);
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  }

  private diagnoseCacheHealth() {
    const state = this.showCacheState();
    return {
      conversations: state.conversations.length > 0 ? '✅ OK' : '⚠️ Empty',
      auth: state.authToken ? '✅ OK' : '❌ Missing',
      sync: state.lastSync ? '✅ OK' : '⚠️ Never synced'
    };
  }

  private diagnoseConnection() {
    const logsProxy = (window as any).__cxLogsProxy;
    let logsProxyStatus = '❌ Not Available';
    
    if (logsProxy) {
      const status = logsProxy.getStatus();
      switch (status) {
        case 'connected': logsProxyStatus = '✅ Connected'; break;
        case 'disabled': logsProxyStatus = '⚠️ Disabled (Optional)'; break;
        case 'connecting': logsProxyStatus = '🔄 Connecting'; break;
        case 'disconnected': logsProxyStatus = '❌ Disconnected'; break;
      }
    }

    return {
      online: navigator.onLine ? '✅ Online' : '❌ Offline',
      simulated: this.offlineSimulated ? '⚠️ Simulated Offline' : '✅ Normal',
      logsProxy: logsProxyStatus
    };
  }

  private diagnoseStorage() {
    return {
      localStorage: this.getStorageSize('localStorage') < 5000 ? '✅ OK' : '⚠️ Large',
      sessionStorage: this.getStorageSize('sessionStorage') < 1000 ? '✅ OK' : '⚠️ Large'
    };
  }

  private diagnosePerformance() {
    const perf = performance.now();
    return {
      pageLoad: performance.timing ? '✅ Available' : '❌ Unavailable',
      memory: (performance as any).memory ? '✅ Available' : '❌ Unavailable',
      responseTime: `${Math.round(perf)}ms`
    };
  }
}

// Initialize and expose globally only on client side
let cxDebug: CXDebugTools | null = null;

if (typeof window !== 'undefined') {
  cxDebug = new CXDebugTools();
  (window as any).cxDebug = cxDebug;
  console.log('🛠️  CX Debug Tools ready - type: cxDebug.help()');
}

export default cxDebug;