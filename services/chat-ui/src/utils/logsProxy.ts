/**
 * CX System - Browser Logs WebSocket Proxy Client
 * 
 * Sobreescribe console methods para enviar logs a servidor WebSocket
 * Permite debugging remoto y logging persistente
 */

interface LogMessage {
  level: string;
  message?: string;
  args: any[];
  timestamp: string;
  url: string;
  userAgent: string;
}

class LogsWebSocketProxy {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3; // Reduced attempts
  private reconnectDelay = 1000; // Reduced delay
  private logQueue: LogMessage[] = [];
  private isDisabled = false; // Flag to disable proxy completely
  private healthCheckTimeout: number | null = null;
  private originalConsole: {
    log: typeof console.log;
    warn: typeof console.warn;
    error: typeof console.error;
    info: typeof console.info;
    debug: typeof console.debug;
  };

  constructor(private wsUrl: string = 'ws://localhost:3008') {
    // Guardar métodos originales de console
    this.originalConsole = {
      log: console.log.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      info: console.info.bind(console),
      debug: console.debug.bind(console)
    };

    this.init();
  }

  private init() {
    // Perform initial health check
    this.performHealthCheck().then((healthy) => {
      if (healthy) {
        this.connect();
        this.overrideConsoleMethods();
      } else {
        // Logs proxy not available - disable completely
        this.disableProxy();
      }
    });
    
    // Auto-reconectar en caso de pérdida de conexión
    window.addEventListener('beforeunload', () => {
      if (this.socket) {
        this.socket.close();
      }
      if (this.healthCheckTimeout) {
        clearTimeout(this.healthCheckTimeout);
      }
    });
  }

  private connect() {
    try {
      this.socket = new WebSocket(this.wsUrl);
      
      this.socket.onopen = () => {
        // Silently connected to logs proxy - this is optional debugging
        this.reconnectAttempts = 0;
        this.flushLogQueue();
      };
      
      this.socket.onclose = (event) => {
        // Silently handle disconnection - logs proxy is optional
        this.attemptReconnect();
      };
      
      this.socket.onerror = (error) => {
        // Silently handle WebSocket errors when logs proxy is not available
        // This is optional debugging functionality
      };
      
      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'connection') {
            // Logs proxy ready - operating silently
          }
        } catch (err) {
          // Silently handle parsing errors from optional logs proxy
        }
      };
      
    } catch (error) {
      // Silently handle connection errors - logs proxy is optional
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.isDisabled) {
      // Proxy is disabled, don't attempt reconnect
      return;
    }

    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      // Silently attempt reconnection - logs proxy is optional
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      // After max attempts, disable proxy completely
      this.disableProxy();
    }
  }

  private async performHealthCheck(): Promise<boolean> {
    try {
      // Quick WebSocket connection test
      const testSocket = new WebSocket(this.wsUrl);
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          testSocket.close();
          resolve(false);
        }, 2000); // 2 second timeout

        testSocket.onopen = () => {
          clearTimeout(timeout);
          testSocket.close();
          resolve(true);
        };

        testSocket.onerror = () => {
          clearTimeout(timeout);
          resolve(false);
        };
      });
    } catch (error) {
      return false;
    }
  }

  private disableProxy() {
    this.isDisabled = true;
    
    // Close any existing connection
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    // Clear any pending timeouts
    if (this.healthCheckTimeout) {
      clearTimeout(this.healthCheckTimeout);
    }
    
    // Clear log queue to prevent memory issues
    this.logQueue = [];
    
    // Do NOT override console methods when disabled
    // Leave original console.log/warn/error intact
  }

  private flushLogQueue() {
    while (this.logQueue.length > 0) {
      const log = this.logQueue.shift();
      if (log) {
        this.sendLog(log);
      }
    }
  }

  private sendLog(logData: LogMessage) {
    // Don't send logs if proxy is disabled
    if (this.isDisabled) {
      return;
    }

    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      try {
        this.socket.send(JSON.stringify(logData));
      } catch (error) {
        // Prevent recursive logging errors - use silent error logging
        if (typeof error === 'object' && error !== null) {
          // Silent error - don't log to prevent infinite loops
        }
        // Re-queue the log for retry only if not disabled
        if (!this.isDisabled) {
          this.logQueue.push(logData);
        }
      }
    } else if (!this.isDisabled) {
      // Queue the log for when connection is restored
      this.logQueue.push(logData);
      
      // Limit queue size to prevent memory issues
      if (this.logQueue.length > 100) {
        this.logQueue.shift(); // Remove oldest log
      }
    }
  }

  private createLogMessage(level: string, args: any[]): LogMessage {
    return {
      level,
      args,
      message: args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' '),
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };
  }

  private overrideConsoleMethods() {
    // Don't override console methods if proxy is disabled
    if (this.isDisabled) {
      return;
    }

    const methods = ['log', 'warn', 'error', 'info', 'debug'] as const;
    
    methods.forEach(method => {
      console[method] = (...args: any[]) => {
        // Prevent infinite loops for React errors
        const firstArg = args[0];
        const isReactError = typeof firstArg === 'string' && 
          (firstArg.includes('Maximum update depth') || 
           firstArg.includes('Cannot update a component while rendering'));
        
        // Llamar al método original primero
        this.originalConsole[method](...args);
        
        // Don't send React infinite loop errors or send anything if disabled
        if (!isReactError && !this.isDisabled) {
          const logMessage = this.createLogMessage(method, args);
          this.sendLog(logMessage);
        }
      };
    });
  }

  // Métodos públicos para control manual
  public disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }

  public reconnect() {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }

  public getQueueSize(): number {
    return this.logQueue.length;
  }

  public isConnected(): boolean {
    return !this.isDisabled && this.socket?.readyState === WebSocket.OPEN;
  }

  public isEnabled(): boolean {
    return !this.isDisabled;
  }

  public getStatus(): string {
    if (this.isDisabled) return 'disabled';
    if (this.socket?.readyState === WebSocket.OPEN) return 'connected';
    if (this.socket?.readyState === WebSocket.CONNECTING) return 'connecting';
    return 'disconnected';
  }

  // Restaurar console methods originales
  public restore() {
    Object.entries(this.originalConsole).forEach(([method, fn]) => {
      (console as any)[method] = fn;
    });
    this.disconnect();
  }
}

// Auto-inicializar solo en desarrollo y solo si no existe
let logsProxy: LogsWebSocketProxy | null = null;

if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  try {
    // Evitar múltiples instancias
    if (!(window as any).__cxLogsProxy) {
      logsProxy = new LogsWebSocketProxy();
      (window as any).__cxLogsProxy = logsProxy;
    } else {
      logsProxy = (window as any).__cxLogsProxy;
    }
  } catch (error) {
    // If logs proxy fails to initialize, silently continue without it
    console.warn('⚠️  Logs proxy failed to initialize - continuing without remote logging');
    logsProxy = null;
  }
}

export default logsProxy;