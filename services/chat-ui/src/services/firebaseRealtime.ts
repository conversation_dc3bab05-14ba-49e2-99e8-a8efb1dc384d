/**
 * Firebase Real-time Connection Manager
 * 
 * Handles WebSocket connections to Firebase Realtime Database with:
 * - Automatic fallback to polling when disconnected
 * - Smart reconnection with exponential backoff  
 * - Clean state management and event emission
 * - Zero breaking changes to existing APIs
 */

import { initializeApp } from 'firebase/app';
import { getDatabase, ref, onValue, off, connectDatabaseEmulator, goOnline, goOffline } from 'firebase/database';
import type { Database, DatabaseReference, Unsubscribe } from 'firebase/database';

// Connection states
export type ConnectionState = 'connected' | 'polling' | 'reconnecting' | 'offline';

// Event types for real-time updates
export type RealtimeEvent = 
  | 'message_added'
  | 'conversation_updated' 
  | 'agent_status_changed'
  | 'typing_started'
  | 'typing_stopped';

// Event data structure
export interface RealtimeEventData {
  type: RealtimeEvent;
  conversationId?: string;
  agentId?: string;
  data: any;
  timestamp: string;
}

// Listener callback type
export type RealtimeListener = (data: RealtimeEventData) => void;

// Firebase configuration from environment (browser-side, public)
const projectId = process.env.NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_ID || 'cx-system-469120';
const emulatorHost = '127.0.0.1:9000';
const firebaseNamespace = `${projectId}-default-rtdb`;

const firebaseConfig = {
  projectId: projectId,
  databaseURL: `http://${emulatorHost}?ns=${firebaseNamespace}`,
};

class FirebaseRealtimeManager {
  private app: any;
  private database: Database | null = null;
  private connectionState: ConnectionState = 'offline';
  private listeners: Map<string, RealtimeListener> = new Map();
  private subscriptions: Map<string, Unsubscribe> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectTimeouts = [1000, 2000, 4000, 8000, 15000, 30000]; // Exponential backoff
  private reconnectTimer: NodeJS.Timeout | null = null;
  private connectionCheckTimer: NodeJS.Timeout | null = null;
  private stateChangeListeners: ((state: ConnectionState) => void)[] = [];

  constructor() {
    this.initialize();
  }

  /**
   * Initialize Firebase connection
   */
  private async initialize() {
    try {
      // Initialize Firebase app
      this.app = initializeApp(firebaseConfig);
      this.database = getDatabase(this.app);

      // Connect to emulator in development
      if (process.env.NODE_ENV === 'development' && !(this.database as any)._delegate?._repoInternal) {
        connectDatabaseEmulator(this.database, '127.0.0.1', 9000);
      }

      // Setup connection monitoring
      this.setupConnectionMonitoring();
      
      console.log('🔥 Firebase Realtime Manager initialized');
    } catch (error) {
      console.error('❌ Firebase initialization failed:', error);
      this.setConnectionState('offline');
    }
  }

  /**
   * Monitor Firebase connection state
   */
  private setupConnectionMonitoring() {
    if (!this.database) return;

    const connectedRef = ref(this.database, '.info/connected');
    
    onValue(connectedRef, (snapshot: any) => {
      const connected = snapshot.val();
      
      if (connected) {
        console.log('🟢 Firebase connected');
        this.setConnectionState('connected');
        this.reconnectAttempts = 0;
        this.clearReconnectTimer();
      } else {
        console.log('🔴 Firebase disconnected');
        this.setConnectionState('polling');
        this.startReconnectionAttempts();
      }
    });

    // Health check every 30 seconds during polling
    this.startConnectionHealthCheck();
  }

  /**
   * Start periodic health checks during polling mode
   */
  private startConnectionHealthCheck() {
    this.connectionCheckTimer = setInterval(() => {
      if (this.connectionState === 'polling') {
        this.attemptReconnection();
      }
    }, 30000);
  }

  /**
   * Start reconnection attempts with exponential backoff
   */
  private startReconnectionAttempts() {
    if (this.reconnectTimer) return; // Already attempting

    const attemptReconnection = () => {
      if (this.connectionState === 'connected') return;

      this.setConnectionState('reconnecting');
      this.attemptReconnection();

      // Schedule next attempt
      const timeoutIndex = Math.min(this.reconnectAttempts, this.reconnectTimeouts.length - 1);
      const timeout = this.reconnectTimeouts[timeoutIndex];
      
      this.reconnectTimer = setTimeout(() => {
        this.reconnectAttempts++;
        this.reconnectTimer = null;
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          attemptReconnection();
        } else {
          console.log('🔴 Max reconnection attempts reached');
          this.setConnectionState('offline');
        }
      }, timeout);
    };

    attemptReconnection();
  }

  /**
   * Attempt to reconnect to Firebase
   */
  private async attemptReconnection() {
    if (!this.database) return;

    try {
      // Force online state
      goOnline(this.database);
      
      // Test connection with a simple read
      const testRef = ref(this.database, '.info/serverTimeOffset');
      const testPromise = new Promise((resolve, reject) => {
        let unsubscribe: (() => void) | null = null;
        
        unsubscribe = onValue(testRef, 
          (snapshot: any) => {
            if (unsubscribe) unsubscribe();
            resolve(snapshot.val());
          },
          (error: any) => {
            if (unsubscribe) unsubscribe();
            reject(error);
          }
        );
        
        // Timeout after 5 seconds
        setTimeout(() => {
          if (unsubscribe) unsubscribe();
          reject(new Error('Connection test timeout'));
        }, 5000);
      });

      await testPromise;
      console.log('🟢 Reconnection successful');
      
    } catch (error) {
      console.log('🟡 Reconnection attempt failed:', String(error));
      this.setConnectionState('polling');
    }
  }

  /**
   * Clear reconnection timer
   */
  private clearReconnectTimer() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Set connection state and notify listeners
   */
  private setConnectionState(state: ConnectionState) {
    if (this.connectionState !== state) {
      console.log(`🔄 Connection state: ${this.connectionState} → ${state}`);
      this.connectionState = state;
      
      // Notify state change listeners
      this.stateChangeListeners.forEach(listener => {
        try {
          listener(state);
        } catch (error) {
          console.error('Error in state change listener:', error);
        }
      });
    }
  }

  /**
   * Subscribe to real-time updates for a Firebase path
   */
  public subscribe(path: string, callback: RealtimeListener): string {
    const listenerId = `${path}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.listeners.set(listenerId, callback);

    // Only setup Firebase listener if connected
    if (this.connectionState === 'connected' && this.database) {
      this.setupFirebaseListener(path, listenerId, callback);
    }

    return listenerId;
  }

  /**
   * Setup Firebase real-time listener
   */
  private setupFirebaseListener(path: string, listenerId: string, callback: RealtimeListener) {
    if (!this.database) return;

    const firebaseRef = ref(this.database, path);
    
    const unsubscribe = onValue(firebaseRef, 
      (snapshot: any) => {
        try {
          const data = snapshot.val();
          if (data) {
            // Determine event type based on path
            let eventType: RealtimeEvent = 'conversation_updated';
            
            if (path.includes('/messages')) {
              eventType = 'message_added';
            } else if (path.includes('/agents')) {
              eventType = 'agent_status_changed';
            } else if (path.includes('/typing')) {
              eventType = 'typing_started';
            }

            const eventData: RealtimeEventData = {
              type: eventType,
              data,
              timestamp: new Date().toISOString()
            };

            callback(eventData);
          }
        } catch (error) {
          console.error('Error processing Firebase data:', error);
        }
      },
      (error: any) => {
        console.error(`Firebase listener error for ${path}:`, error);
        this.setConnectionState('polling');
      }
    );

    this.subscriptions.set(listenerId, unsubscribe);
  }

  /**
   * Unsubscribe from real-time updates
   */
  public unsubscribe(listenerId: string) {
    // Remove callback
    this.listeners.delete(listenerId);
    
    // Remove Firebase subscription
    const unsubscribe = this.subscriptions.get(listenerId);
    if (unsubscribe) {
      unsubscribe();
      this.subscriptions.delete(listenerId);
    }
  }

  /**
   * Subscribe to connection state changes
   */
  public onConnectionStateChange(callback: (state: ConnectionState) => void) {
    this.stateChangeListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.stateChangeListeners.indexOf(callback);
      if (index > -1) {
        this.stateChangeListeners.splice(index, 1);
      }
    };
  }

  /**
   * Get current connection state
   */
  public getConnectionState(): ConnectionState {
    return this.connectionState;
  }

  /**
   * Check if real-time is available
   */
  public isRealtimeAvailable(): boolean {
    return this.connectionState === 'connected';
  }

  /**
   * Force reconnection attempt
   */
  public async forceReconnect() {
    console.log('🔄 Force reconnection requested');
    this.reconnectAttempts = 0;
    this.clearReconnectTimer();
    
    if (this.connectionState !== 'connected') {
      this.setConnectionState('reconnecting');
      await this.attemptReconnection();
    }
  }

  /**
   * Cleanup - unsubscribe all listeners
   */
  public cleanup() {
    // Clear timers
    this.clearReconnectTimer();
    if (this.connectionCheckTimer) {
      clearInterval(this.connectionCheckTimer);
    }

    // Unsubscribe all Firebase listeners
    this.subscriptions.forEach(unsubscribe => {
      try {
        unsubscribe();
      } catch (error) {
        console.error('Error unsubscribing:', error);
      }
    });

    // Clear maps
    this.listeners.clear();
    this.subscriptions.clear();
    this.stateChangeListeners = [];

    // Go offline
    if (this.database) {
      goOffline(this.database);
    }

    console.log('🧹 Firebase Realtime Manager cleaned up');
  }
}

// Singleton instance
const firebaseRealtimeManager = new FirebaseRealtimeManager();

export default firebaseRealtimeManager;