// Supabase client and auth service
import { createClient, SupabaseClient, User } from '@supabase/supabase-js';
import { Agent, UserSession } from '@/types';
import { AgentStatus } from '@/types/api';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Create Supabase client with session persistence
const supabase: SupabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

class AuthService {
  private supabase: SupabaseClient;

  constructor() {
    this.supabase = supabase;
  }

  // Authentication methods
  async signIn(email: string, password: string): Promise<{user: User, session: any, error: any}> {
    const { data, error } = await this.supabase.auth.signInWithPassword({
      email,
      password,
    });

    return {
      user: data.user!,
      session: data.session,
      error
    };
  }

  async signOut(): Promise<{error: any}> {
    // Clear cache keys
    const keysToRemove = [
      'supabase-auth-token',
      `sb-${process.env.NEXT_PUBLIC_SUPABASE_PROJECT_ID}-auth-token`,
      'cx-user-session',
      'cx-conversations-cache',
      'cx-messages-cache',
      'cx-agents-cache'
    ];
    
    keysToRemove.forEach(key => {
      if (key && localStorage.getItem(key)) {
        localStorage.removeItem(key);
      }
    });
    
    // Clear any Supabase session storage keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('sb-') && key.includes('auth-token')) {
        localStorage.removeItem(key);
      }
    }
    
    const { error } = await this.supabase.auth.signOut();
    
    if (error) {
      console.error('Supabase signOut error:', error);
    }
    
    return { error };
  }

  async getSession(): Promise<{data: any, error: any}> {
    try {
      console.log('📡 Supabase getSession() starting...', { 
        hasUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        hasKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY 
      });
      const result = await this.supabase.auth.getSession();
      console.log('✅ Supabase getSession() completed');
      return result;
    } catch (error) {
      console.error('❌ Supabase getSession() failed:', error);
      throw error;
    }
  }

  async getUser(): Promise<{data: any, error: any}> {
    return await this.supabase.auth.getUser();
  }

  // Agent profile methods
  async getAgentProfile(userEmail: string): Promise<{data: Agent | null, error: any}> {
    try {
      const { data, error } = await this.supabase
        .from('agents')
        .select(`
          id,
          name,
          email,
          role,
          max_concurrent_sessions,
          skills,
          organization_id,
          is_active,
          created_at,
          updated_at
        `)
        .eq('email', userEmail)
        .single();

      if (error) {
        console.error('Error fetching agent profile:', error);
        return { data: null, error };
      }

      // Transform database format to our Agent type
      const agent: Agent = {
        id: data.id,
        name: data.name,
        email: data.email,
        role: data.role || 'agent', // Default to 'agent' if not set
        status: {
          availability: 'available',
          isAvailable: true,
          canReceiveTransfers: true,
          currentSessions: 0,
          maxSessions: data.max_concurrent_sessions || 5,
          lastActivityAt: null
        }, // Default status structure, will be updated from Firebase real-time status
        departments: [], // TODO: Get from agent_departments table
        capacity: {
          maxConcurrentChats: data.max_concurrent_sessions || 5,
          currentChatCount: 0, // Will be updated from Firebase real-time data
          utilization: 0 // Will be calculated from Firebase real-time data
        },
        organizationId: data.organization_id,
        metadata: {
          skills: data.skills || [],
          languages: ['en'], // Default for now
          timezone: 'UTC' // Default for now
        },
        createdAt: data.created_at,
        lastActiveAt: data.updated_at || data.created_at // Use updated_at as proxy for last activity
      };

      return { data: agent, error: null };
    } catch (error) {
      console.error('Error in getAgentProfile:', error);
      return { data: null, error };
    }
  }

  async updateAgentLastActive(userEmail: string): Promise<{error: any}> {
    const { error } = await this.supabase
      .from('agents')
      .update({ 
        updated_at: new Date().toISOString()
        // Note: status is now managed in Firebase real-time, not in Supabase
      })
      .eq('email', userEmail);

    return { error };
  }

  async updateAgentStatus(agentId: string, status: AgentStatus, reason?: string, supervisorAuthorizationId?: string): Promise<{error: any}> {
    try {
      // Use the API service to update status (this calls the backend properly)
      const { default: chatRealtimeAPI } = await import('./api');
      
      const apiResponse = await chatRealtimeAPI.updateAgentStatus(agentId, {
        status,
        reason,
        supervisorAuthorizationId
      });

      if (!apiResponse.success) {
        return { error: apiResponse.error };
      }

      console.log('✅ Agent status updated successfully via API:', status);
      return { error: null };

    } catch (error) {
      console.error('Error updating agent status:', error);
      return { error };
    }
  }

  // Session management
  async createUserSession(user: User, session: any): Promise<UserSession> {
    // Get agent profile using email instead of ID for compatibility
    const { data: agent } = await this.getAgentProfile(user.email!);
    
    if (!agent) {
      throw new Error('Agent profile not found');
    }

    // Update last active time
    await this.updateAgentLastActive(user.email!);

    return {
      user: agent,
      accessToken: session.access_token,
      refreshToken: session.refresh_token,
      expiresAt: session.expires_at * 1000, // Convert to milliseconds
    };
  }

  // Auth state listener
  onAuthStateChange(callback: (event: string, session: any) => void) {
    return this.supabase.auth.onAuthStateChange(callback);
  }

  // Utility methods
  isSessionValid(session: UserSession | null): boolean {
    if (!session) return false;
    return Date.now() < session.expiresAt;
  }

  async refreshSession(): Promise<{data: any, error: any}> {
    return await this.supabase.auth.refreshSession();
  }
}

// Simple singleton - one instance only
const authService = new AuthService();

export { supabase, authService };
export type { UserSession };