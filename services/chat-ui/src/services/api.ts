// API Service for Chat Realtime Service
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { 
  ApiResponse,
  Conversation, 
  Message, 
  Agent, 
  ConversationNote,
  ConversationAnalytics,
  SystemStatus,
  ConversationQueue,
  CreateConversationRequest,
  SendMessageRequest,
  TransferConversationRequest,
  UpdateAgentStatusRequest,
  SuperviseConversationRequest,
  AddNoteRequest,
  ConversationFilters,
  CreateSupervisionAuthorizationRequest,
  SupervisorAuthorization,
  AgentStatusHistory,
  SupervisorDashboardData
} from '@/types';

class ChatRealtimeAPI {
  private client: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_CHAT_REALTIME_URL || 'http://localhost:3003';
    
    this.client = axios.create({
      baseURL: `${this.baseURL}/api`,
      timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || '30000'),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Setup interceptors
    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - add auth token if available
    this.client.interceptors.request.use(async (config) => {
      try {
        // Get token from Supabase client (the correct way)
        const { supabase } = await import('./supabase');
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session?.access_token) {
          config.headers.Authorization = `Bearer ${session.access_token}`;
        }
      } catch (error) {
        console.error('API: Error getting Supabase session:', error);
      }
      
      return config;
    });

    // Response interceptor - handle common errors
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Handle auth errors - let Supabase handle token cleanup
          console.log('API: 401 error, signing out via Supabase');
          try {
            const { authService } = await import('./supabase');
            await authService.signOut();
            window.location.href = '/login';
          } catch (signOutError) {
            console.error('Error during sign out:', signOutError);
            window.location.href = '/login';
          }
        } else if (error.response?.status === 403) {
          // Handle permission errors - valid token but insufficient permissions
          console.error('Access denied:', error.response?.data?.message || 'Insufficient permissions');
          // Don't logout - token is valid, just no permission for this action
        }
        return Promise.reject(error);
      }
    );
  }

  // Health & Status
  async getHealth(): Promise<ApiResponse> {
    const response = await this.client.get('/health');
    return response.data;
  }

  async getSystemStatus(): Promise<ApiResponse<SystemStatus>> {
    const response = await this.client.get('/status');
    return response.data;
  }

  // Conversations
  async getConversations(filters?: ConversationFilters): Promise<ApiResponse<{conversations: Conversation[], total: number}>> {
    const response = await this.client.get('/conversations', { params: filters });
    return response.data;
  }

  async getConversation(id: string): Promise<ApiResponse<Conversation>> {
    const response = await this.client.get(`/conversations/${id}`);
    return response.data;
  }

  async createConversation(data: CreateConversationRequest): Promise<ApiResponse<Conversation>> {
    const response = await this.client.post('/conversations', data);
    return response.data;
  }

  async updateConversationStatus(id: string, status: Conversation['status']): Promise<ApiResponse> {
    const response = await this.client.put(`/conversations/${id}/status`, { status });
    return response.data;
  }

  async closeConversation(id: string, reason?: string): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${id}/close`, { reason });
    return response.data;
  }


  // Messages
  async getMessages(conversationId: string, limit?: number): Promise<ApiResponse<{messages: Message[], count: number}>> {
    const response = await this.client.get(`/conversations/${conversationId}/messages`, { 
      params: { limit } 
    });
    return response.data;
  }

  async sendMessage(conversationId: string, data: SendMessageRequest): Promise<ApiResponse<Message>> {
    const response = await this.client.post(`/conversations/${conversationId}/messages`, data);
    return response.data;
  }

  async markMessageAsRead(conversationId: string, messageId: string, userId: string): Promise<ApiResponse> {
    const response = await this.client.put(`/conversations/${conversationId}/messages/${messageId}/read`, {
      userId
    });
    return response.data;
  }

  // Typing Indicators
  async setTypingIndicator(conversationId: string, userId: string, userType: string, isTyping: boolean): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/typing`, {
      userId,
      userType,
      isTyping
    });
    return response.data;
  }

  async getTypingIndicators(conversationId: string): Promise<ApiResponse<{indicators: any[]}>> {
    const response = await this.client.get(`/conversations/${conversationId}/typing`);
    return response.data;
  }

  // Transfers & Escalation
  async transferConversation(conversationId: string, data: TransferConversationRequest): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/transfer`, data);
    return response.data;
  }

  async markConversationAsRead(conversationId: string, userId?: string): Promise<ApiResponse> {
    // Get userId from auth context if not provided
    let agentUserId = userId;
    if (!agentUserId) {
      try {
        const { supabase } = await import('./supabase');
        const { data: { user } } = await supabase.auth.getUser();
        agentUserId = user?.id;
      } catch (error) {
        console.warn('Could not get user ID for markConversationAsRead:', error);
      }
    }

    const response = await this.client.put(`/conversations/${conversationId}/read`, {
      userId: agentUserId
    });
    return response.data;
  }
  async acceptTransfer(conversationId: string, agentId: string): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/accept-transfer`, {
      agentId
    });
    return response.data;
  }

  async rejectTransfer(conversationId: string, agentId: string, reason: string): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/reject-transfer`, {
      agentId,
      reason
    });
    return response.data;
  }

  async cancelTransfer(conversationId: string, agentId: string, reason?: string): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/cancel-transfer`, {
      agentId,
      reason
    });
    return response.data;
  }

  async escalateConversation(conversationId: string, reason: string): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/escalate`, {
      reason
    });
    return response.data;
  }

  // Agent Management
  async getAgents(filters?: {status?: string, department?: string}): Promise<ApiResponse<{agents: Agent[]}>> {
    const response = await this.client.get('/agents', { params: filters });
    return response.data;
  }

  async getAgentDetails(agentId: string): Promise<ApiResponse<Agent>> {
    const response = await this.client.get(`/agents/${agentId}`);
    return response.data;
  }

  async updateAgentStatus(agentId: string, data: UpdateAgentStatusRequest): Promise<ApiResponse<Agent>> {
    const response = await this.client.put(`/agents/${agentId}/status`, data);
    return response.data;
  }

  async setAgentCapacity(agentId: string, maxConcurrentChats: number): Promise<ApiResponse> {
    const response = await this.client.put(`/agents/${agentId}/capacity`, {
      maxConcurrentChats
    });
    return response.data;
  }

  // Supervision
  async superviseConversation(conversationId: string, data: SuperviseConversationRequest): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/supervise`, data);
    return response.data;
  }

  async endSupervision(conversationId: string, supervisorId: string): Promise<ApiResponse> {
    const response = await this.client.delete(`/conversations/${conversationId}/supervise`, {
      data: { supervisorId }
    });
    return response.data;
  }

  async getSupervisionStatus(conversationId: string): Promise<ApiResponse> {
    const response = await this.client.get(`/conversations/${conversationId}/supervision/status`);
    return response.data;
  }

  async getSupervisedConversations(supervisorId: string, filters?: any): Promise<ApiResponse> {
    const response = await this.client.get('/conversations/supervised', {
      params: { supervisorId, ...filters }
    });
    return response.data;
  }

  async sendCoachingMessage(conversationId: string, message: string, supervisorId: string): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/supervise/coach`, {
      message,
      supervisorId
    });
    return response.data;
  }

  // Notes
  async addNote(conversationId: string, data: AddNoteRequest): Promise<ApiResponse<ConversationNote>> {
    const response = await this.client.post(`/conversations/${conversationId}/notes`, data);
    return response.data;
  }

  // Export & Analytics
  async exportConversation(conversationId: string, format: 'json' | 'pdf' | 'html' | 'csv' = 'json'): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/export`, {
      format
    });
    return response.data;
  }

  async getConversationAnalytics(filters?: any): Promise<ApiResponse<ConversationAnalytics[]>> {
    const response = await this.client.get('/analytics/conversations', { params: filters });
    return response.data;
  }

  async getConversationHistory(conversationId: string): Promise<ApiResponse> {
    const response = await this.client.get(`/conversations/${conversationId}/history`);
    return response.data;
  }

  async getConversationMetrics(conversationId: string): Promise<ApiResponse> {
    const response = await this.client.get(`/conversations/${conversationId}/metrics`);
    return response.data;
  }

  // Queues
  async getAllQueues(): Promise<ApiResponse<ConversationQueue[]>> {
    const response = await this.client.get('/queues');
    return response.data;
  }

  async getQueueByDepartment(departmentId: string): Promise<ApiResponse<ConversationQueue>> {
    const response = await this.client.get(`/queues/${departmentId}`);
    return response.data;
  }

  // Agents
  async getAvailableAgents(): Promise<ApiResponse<{ agents: Agent[] }>> {
    const response = await this.client.get('/agents/available');
    return response.data;
  }

  // Supervisor Authorization Endpoints
  async createSupervisorAuthorization(data: CreateSupervisionAuthorizationRequest): Promise<ApiResponse<SupervisorAuthorization>> {
    const response = await this.client.post('/supervisor/authorizations', data);
    return response.data;
  }

  async getAgentStatusHistory(agentId: string, queryParams?: {limit?: number, offset?: number, days?: number}): Promise<ApiResponse<AgentStatusHistory[]>> {
    const response = await this.client.get(`/agents/${agentId}/status/history`, { params: queryParams });
    return response.data;
  }

  async getSupervisorDashboard(): Promise<ApiResponse<SupervisorDashboardData>> {
    const response = await this.client.get('/supervisor/dashboard');
    return response.data;
  }

  async getEscalatedConversations(filters?: {priority?: string, level?: number, limit?: number}): Promise<ApiResponse<{escalations: any[]}>> {
    const response = await this.client.get('/supervisor/escalations', { params: filters });
    return response.data;
  }

  async resolveEscalation(conversationId: string, data: {reason: string, resolvedBy: string}): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/resolve-escalation`, data);
    return response.data;
  }

  // Assignment
  async assignAgent(conversationId: string, agentId: string, priority?: string): Promise<ApiResponse> {
    const response = await this.client.post(`/conversations/${conversationId}/assign`, {
      agentId,
      priority
    });
    return response.data;
  }

  async unassignAgent(conversationId: string, reason: string): Promise<ApiResponse> {
    const response = await this.client.delete(`/conversations/${conversationId}/assign`, {
      data: { reason }
    });
    return response.data;
  }
}

// Create singleton instance
const chatRealtimeAPI = new ChatRealtimeAPI();

export default chatRealtimeAPI;
export { ChatRealtimeAPI };