'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { authService } from '@/services/supabase';
import { Agent, UserSession } from '@/types';

interface AuthContextType {
  user: Agent | null;
  session: UserSession | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<Agent | null>(null);
  const [session, setSession] = useState<UserSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAuth();

    // Listen to auth state changes
    const { data: { subscription } } = authService.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session) {
        try {
          const userSession = await authService.createUserSession(session.user, session);
          setUser(userSession.user);
          setSession(userSession);
          setLoading(false);
        } catch (error) {
          console.error('Error creating user session:', error);
          setUser(null);
          setSession(null);
          setLoading(false);
        }
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setSession(null);
      }
    });

    return () => subscription?.unsubscribe?.();
  }, []);

  const checkAuth = async () => {
    try {
      console.log('🔍 Checking authentication state...');
      
      // Shorter timeout for faster UX in development (2s is even faster)
      const timeoutPromise = new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Auth check timeout (2s)')), 2000)
      );
      
      console.log('📡 Calling authService.getSession()...');
      const authPromise = authService.getSession();
      const result = await Promise.race([authPromise, timeoutPromise]);
      const { data: sessionData } = result;
      
      console.log('📋 Session data received:', { hasSession: !!sessionData.session });
      
      if (sessionData.session) {
        console.log('👤 Creating user session...');
        const userSession = await authService.createUserSession(
          sessionData.session.user,
          sessionData.session
        );
        setUser(userSession.user);
        setSession(userSession);
        console.log('✅ User authenticated:', userSession.user.name);
      } else {
        console.log('❌ No valid session found');
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('timeout')) {
        console.warn('⚠️ Auth check timed out - Supabase may be slow or unreachable');
      } else {
        console.error('❌ Error checking auth:', error);
      }
      // Clear any corrupted session data on error
      try {
        localStorage.removeItem('supabase.auth.token');
        sessionStorage.clear();
      } catch (storageError) {
        console.warn('Could not clear storage:', storageError);
      }
    } finally {
      console.log('🔄 Setting loading to false');
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { user: authUser, session: authSession, error } = await authService.signIn(email, password);
      
      if (error) {
        return { success: false, error: error.message };
      }

      const userSession = await authService.createUserSession(authUser, authSession);
      setUser(userSession.user);
      setSession(userSession);

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { success: false, error: errorMessage };
    }
  };

  const signOut = async () => {
    try {
      await authService.signOut();
      setUser(null);
      setSession(null);
      
      // Force redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const value = {
    user,
    session,
    loading,
    signIn,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}