'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ContinuumLogo } from '@/components/ui/ContinuumLogo';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger, 
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { useNavigationCountsQuery as useNavigationCounts } from '@/hooks/supervisor/useNavigationCountsQuery';
import { 
  Shield, 
  Users, 
  MessageSquare, 
  AlertTriangle, 
  UserCheck, 
  ClipboardList, 
  RefreshCw, 
  Eye, 
  BarChart3, 
  <PERSON><PERSON>hart, 
  Settings, 
  AlertCircle, 
  Bell,
  LogOut
} from 'lucide-react';

interface NavItem {
  href: string;
  icon: React.ComponentType<any>;
  label: string;
  badge?: number | string;
  badgeType?: 'default' | 'warning' | 'success' | 'danger';
}

interface NavSection {
  title: string;
  items: NavItem[];
}

// Static navigation sections - dynamic data will be added per instance
const navigationSections: Omit<NavSection, 'items'>[] = [
  { title: 'Dashboard' },
  { title: 'Gestión' },
  { title: 'Supervisión' },
  { title: 'Analytics' }
];

interface SupervisorLayoutProps {
  children: React.ReactNode;
  title?: string;
  actions?: React.ReactNode;
}

export function SupervisorLayout({ children, title, actions }: SupervisorLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, signOut } = useAuth();
  const [notificationCount] = useState(3); // TODO: Get from real data
  const { counts, loading: countsLoading } = useNavigationCounts();


  const handleLogout = async () => {
    try {
      await signOut();
      // signOut already handles redirect to /login in AuthContext
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const getBadgeVariant = (badgeType?: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
    switch (badgeType) {
      case 'danger':
        return 'destructive';
      case 'warning':
        return 'secondary';
      case 'success':
        return 'default';
      default:
        return 'default';
    }
  };

  // Build navigation sections with dynamic data
  const dynamicNavigationSections: NavSection[] = [
    {
      title: 'Dashboard',
      items: [
        { href: '/supervisor', icon: BarChart3, label: 'Vista General' },
        { href: '/supervisor/agents', icon: Users, label: 'Agentes' },
        { href: '/supervisor/conversations', icon: MessageSquare, label: 'Conversaciones' },
      ]
    },
    {
      title: 'Gestión',
      items: [
        { 
          href: '/supervisor/escalations', 
          icon: AlertTriangle, 
          label: 'Escalaciones', 
          badge: countsLoading ? undefined : counts.escalations,
          badgeType: counts.escalations > 0 ? 'destructive' : 'outline'
        },
        { 
          href: '/supervisor/authorizations', 
          icon: UserCheck, 
          label: 'Autorizaciones', 
          badge: countsLoading ? undefined : counts.authorizations,
          badgeType: counts.authorizations > 0 ? 'secondary' : 'outline'
        },
        { 
          href: '/supervisor/queue', 
          icon: ClipboardList, 
          label: 'Cola de Supervisión', 
          badge: countsLoading ? undefined : counts.criticalIssues,
          badgeType: counts.criticalIssues > 0 ? 'destructive' : 'outline'
        },
        { href: '/supervisor/transfers', icon: RefreshCw, label: 'Transferencias' },
      ]
    },
    {
      title: 'Supervisión',
      items: [
        { 
          href: '/supervisor/interventions', 
          icon: Eye, 
          label: 'Intervenciones Activas', 
          badge: countsLoading ? undefined : counts.interventions,
          badgeType: counts.interventions > 0 ? 'destructive' : 'outline'
        },
        { href: '/supervisor/agent-chat', icon: MessageSquare, label: 'Chat con Agentes' },
      ]
    },
    {
      title: 'Analytics',
      items: [
        { href: '/supervisor/metrics', icon: BarChart3, label: 'Métricas' },
        { href: '/supervisor/reports', icon: PieChart, label: 'Reportes' },
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 grid grid-cols-[280px_1fr] grid-rows-[60px_1fr]">
      {/* Header */}
      <header className="col-span-2 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <ContinuumLogo width={120} height={40} className="text-lg" />
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative">
                <Bell className="h-5 w-5" />
                {notificationCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                  >
                    {notificationCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <div className="p-4 border-b">
                <h3 className="font-semibold">Notificaciones</h3>
              </div>
              <div className="max-h-64 overflow-y-auto">
                <DropdownMenuItem className="flex items-start space-x-3 p-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium">Nueva escalación crítica</p>
                    <p className="text-xs text-muted-foreground">Cliente TechCorp - hace 5 min</p>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="flex items-start space-x-3 p-3">
                  <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium">Autorización pendiente</p>
                    <p className="text-xs text-muted-foreground">Ana M. solicita pausa - hace 12 min</p>
                  </div>
                </DropdownMenuItem>
                <DropdownMenuItem className="flex items-start space-x-3 p-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1 space-y-1">
                    <p className="text-sm font-medium">Transferencia completada</p>
                    <p className="text-xs text-muted-foreground">Juan P. → Soporte Técnico - hace 20 min</p>
                  </div>
                </DropdownMenuItem>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <Button variant="ghost" className="w-full justify-start h-auto p-2">
                  Ver todas las notificaciones
                </Button>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 h-auto p-1.5">
                <Avatar className="h-8 w-8">
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {user?.name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'MS'}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm font-medium hidden sm:inline">
                  {user?.name || 'Supervisor'}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <div className="px-2 py-2 border-b">
                <p className="text-sm font-medium">{user?.name || 'Supervisor'}</p>
                <p className="text-xs text-muted-foreground">{user?.email || '<EMAIL>'}</p>
              </div>
              <DropdownMenuItem onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Cerrar Sesión
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      {/* Sidebar */}
      <aside className="bg-white border-r border-gray-200 py-6 overflow-y-auto">
        <nav className="space-y-8">
          {dynamicNavigationSections.map((section) => (
            <div key={section.title}>
              <div className="px-6 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
                {section.title}
              </div>
              <div className="space-y-1">
                {section.items.map((item) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href;
                  
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={`
                        flex items-center justify-between px-6 py-3 text-sm font-medium transition-colors
                        ${isActive 
                          ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' 
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }
                      `}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="h-5 w-5" />
                        <span>{item.label}</span>
                      </div>
                      {item.badge && (
                        <Badge variant={getBadgeVariant(item.badgeType)} className="text-xs">
                          {item.badge}
                        </Badge>
                      )}
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </nav>
      </aside>

      {/* Main Content */}
      <main className="overflow-y-auto">
        <div className="p-6">
          {/* Content Header */}
          {title && (
            <div className="flex items-center justify-between mb-8">
              <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
              {actions && (
                <div className="flex items-center space-x-3">
                  {actions}
                </div>
              )}
            </div>
          )}
          
          {/* Page Content */}
          {children}
        </div>
      </main>
    </div>
  );
}