'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  MessageCircle, 
  Clock, 
  Star,
  CheckCircle,
  UserCheck,
  AlertTriangle,
  HelpCircle
} from 'lucide-react';
import { useSupervisorQueries as useSupervisorDashboard } from '@/hooks/supervisor/useSupervisorQueries';
import { useEscalationsQuery as useEscalations } from '@/hooks/supervisor/useEscalationsQuery';

interface MetricCardProps {
  title: string;
  value: string | number;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  icon: React.ReactNode;
  color: 'green' | 'blue' | 'orange' | 'red' | 'gray';
  tooltip: string;
  loading?: boolean;
}

const MetricCard = ({ title, value, trend, trendValue, icon, color, tooltip, loading = false }: MetricCardProps) => {
  const colorClasses = {
    green: 'border-green-200 bg-green-50 text-green-900',
    blue: 'border-blue-200 bg-blue-50 text-blue-900',
    orange: 'border-orange-200 bg-orange-50 text-orange-900',
    red: 'border-red-200 bg-red-50 text-red-900',
    gray: 'border-gray-200 bg-gray-50 text-gray-900'
  };

  const iconColorClasses = {
    green: 'text-green-600',
    blue: 'text-blue-600', 
    orange: 'text-orange-600',
    red: 'text-red-600',
    gray: 'text-gray-600'
  };

  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp className="h-3 w-3 text-green-600" />;
    if (trend === 'down') return <TrendingDown className="h-3 w-3 text-red-600" />;
    return null;
  };

  return (
    <Card className={`${colorClasses[color]} border-l-4 relative`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={iconColorClasses[color]}>
              {icon}
            </div>
            <CardTitle className="text-sm font-medium">
              {title}
            </CardTitle>
          </div>
          <div className="relative group">
            <HelpCircle className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600" />
            {/* Tooltip */}
            <div className="absolute top-full mt-2 hidden group-hover:block z-[60] w-64 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-xl border border-gray-700 right-0">
              {tooltip}
              {/* Arrow pointing up */}
              <div className="absolute bottom-full w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 right-4"></div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-xl font-bold">
            {loading ? (
              <div className="h-8 w-16 bg-gray-300 rounded animate-pulse" />
            ) : (
              value
            )}
          </div>
          
          {trend && trendValue && !loading && (
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon()}
              <span className={trend === 'up' ? 'text-green-600' : 'text-red-600'}>
                {trendValue}
              </span>
              <span className="text-gray-500">vs ayer</span>
            </div>
          )}
          
          {loading && (
            <div className="h-4 w-20 bg-gray-300 rounded animate-pulse" />
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export function DailyMetricsPanel() {
  const { dashboardData, loading } = useSupervisorDashboard();
  const { escalations } = useEscalations();

  // Don't show full skeleton, just individual loading states

  const summary = dashboardData?.summary;
  const totalEscalations = escalations?.length ?? 0;
  
  // Real data from backend - now shows total workload, not just "active"
  const workloadConversations = summary?.totalWorkloadConversations ?? 0;
  const totalConversationsToday = summary?.totalConversationsToday ?? 0;
  const statusBreakdown = summary?.systemConversationsByStatus ?? {};
  
  // Real analytics data from backend
  const avgResponseTimeMinutes = summary?.avgResponseTimeMinutes ?? 0;
  const avgResponseTime = avgResponseTimeMinutes > 0 ? avgResponseTimeMinutes.toFixed(1) : "0.0";
  const responseTimeTrend = summary?.responseTimeTrend;
  const workloadTrend = summary?.workloadTrend;
  const completedTrend = summary?.completedTrend;
  const escalationsTrend = summary?.escalationsTrend;
  
  // TODO: Still need real analytics data for satisfaction
  const satisfactionScore = 4.2; // TODO: Get real data from analytics
  const completedConversationsToday = Math.max(0, totalConversationsToday - workloadConversations); // Estimate completed as total minus workload
  
  // Build breakdown tooltip
  const breakdownParts = [];
  if ((statusBreakdown.active ?? 0) > 0) breakdownParts.push(`${statusBreakdown.active} activas`);
  if ((statusBreakdown.supervised ?? 0) > 0) breakdownParts.push(`${statusBreakdown.supervised} supervisadas`);
  if ((statusBreakdown.escalated ?? 0) > 0) breakdownParts.push(`${statusBreakdown.escalated} escaladas`);
  if ((statusBreakdown.pending_acceptance ?? 0) > 0) breakdownParts.push(`${statusBreakdown.pending_acceptance} pendientes`);
  if ((statusBreakdown.transferring ?? 0) > 0) breakdownParts.push(`${statusBreakdown.transferring} transfiriendo`);
  
  const breakdownText = breakdownParts.length > 0 ? ` (${breakdownParts.join(', ')})` : '';

  const metricsData = [
    {
      title: 'Carga de Trabajo',
      value: workloadConversations,
      icon: <MessageCircle className="h-4 w-4" />,
      color: 'blue' as const,
      trend: workloadTrend?.direction || 'neutral',
      trendValue: workloadTrend?.difference || '0',
      tooltip: `Total de conversaciones que requieren atención de los agentes. Incluye: activas, supervisadas, escaladas, pendientes y transferencias${breakdownText}.`
    },
    {
      title: 'Completadas Hoy',
      value: completedConversationsToday,
      icon: <CheckCircle className="h-4 w-4" />,
      color: 'green' as const,
      trend: completedTrend?.direction || 'neutral',
      trendValue: completedTrend?.difference || '0',
      tooltip: 'Conversaciones completadas hoy comparado con ayer. Basado en conversaciones cerradas en el día.'
    },
    {
      title: 'Tiempo Respuesta',
      value: `${avgResponseTime}min`,
      icon: <Clock className="h-4 w-4" />,
      color: (avgResponseTime < "3" ? 'green' : avgResponseTime < "5" ? 'orange' : 'red') as any,
      trend: responseTimeTrend?.direction || 'neutral',
      trendValue: responseTimeTrend?.differenceMinutes || '0.0min',
      tooltip: 'Tiempo promedio que tardan los agentes en responder al primer mensaje del cliente. Meta: <3 minutos. Medido desde que el cliente envía mensaje hasta que agente responde.'
    },
    {
      title: 'Satisfacción',
      value: `${satisfactionScore}/5`,
      icon: <Star className="h-4 w-4" />,
      color: (satisfactionScore >= 4.5 ? 'green' : satisfactionScore >= 4 ? 'blue' : satisfactionScore >= 3 ? 'orange' : 'red') as any,
      trend: 'up' as const,
      trendValue: '+0.2',
      tooltip: 'Puntuación promedio de satisfacción del cliente basada en encuestas post-chat. Escala 1-5 estrellas. Se mide automáticamente después de conversaciones resueltas.'
    },
    {
      title: 'Escalaciones Totales',
      value: totalEscalations,
      icon: <AlertTriangle className="h-4 w-4" />,
      color: (totalEscalations === 0 ? 'green' : totalEscalations < 3 ? 'orange' : 'red') as any,
      trend: escalationsTrend?.direction || 'neutral',
      trendValue: escalationsTrend?.difference || '0',
      tooltip: 'Número total de conversaciones escaladas hoy comparado con ayer. Incluye escalaciones manuales por agentes y automáticas por el sistema.'
    },
  ];

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center gap-2 mb-6">
        <Clock className="h-5 w-5 text-blue-600" />
        <h2 className="text-lg font-semibold text-gray-900">
          Métricas del Día
        </h2>
        <Badge variant="outline" className="text-xs">
          At-a-glance
        </Badge>
      </div>
      
      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {metricsData.map((metric, index) => (
          <MetricCard
            key={index}
            title={metric.title}
            value={metric.value}
            trend={metric.trend}
            trendValue={metric.trendValue}
            icon={metric.icon}
            color={metric.color}
            tooltip={metric.tooltip}
            loading={loading}
          />
        ))}
      </div>

      {/* Quick Insights */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="h-2 w-2 bg-blue-500 rounded-full mt-2"></div>
            <div>
              <h4 className="text-sm font-medium text-blue-900 mb-1">
                💡 Insight del Día
              </h4>
              <div className="text-xs text-blue-800">
                {loading ? (
                  <div className="space-y-1">
                    <div className="h-3 w-64 bg-blue-300 rounded animate-pulse"></div>
                    <div className="h-3 w-48 bg-blue-300 rounded animate-pulse"></div>
                  </div>
                ) : (
                  <div>
                    {totalEscalations === 0 
                      ? "Excelente día - cero escalaciones. El equipo está manejando bien las conversaciones."
                      : totalEscalations < 3
                        ? `${totalEscalations} escalaciones hoy - dentro del rango normal. Monitorear patrones.`
                        : `${totalEscalations} escalaciones - por encima del promedio. Revisar causas comunes.`
                    }
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}