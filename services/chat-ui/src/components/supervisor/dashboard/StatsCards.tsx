'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  Users, 
  Eye, 
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  HelpCircle,
  ExternalLink
} from 'lucide-react';
import { useSupervisorQueries as useSupervisorDashboard } from '@/hooks/supervisor/useSupervisorQueries';
import { useEscalationsQuery as useEscalations } from '@/hooks/supervisor/useEscalationsQuery';
import { useResourceManagementQuery as useResourceManagement } from '@/hooks/supervisor/useResourceManagementQuery';
import { isPriorityUrgent } from '@/utils/typeGuards';
import { useRouter } from 'next/navigation';

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color: 'red' | 'orange' | 'green' | 'blue' | 'gray';
  loading?: boolean;
  tooltip?: string;
  isRightSide?: boolean;
  actionButton?: {
    label: string;
    href: string;
    show: boolean;
  };
}

const StatCard = ({ 
  title, 
  value, 
  description, 
  icon, 
  trend, 
  trendValue, 
  color,
  loading,
  tooltip,
  isRightSide = false,
  actionButton
}: StatCardProps) => {
  const router = useRouter();
  const colorClasses = {
    red: 'border-red-200 bg-red-50 text-red-900',
    orange: 'border-orange-200 bg-orange-50 text-orange-900', 
    green: 'border-green-200 bg-green-50 text-green-900',
    blue: 'border-blue-200 bg-blue-50 text-blue-900',
    gray: 'border-gray-200 bg-gray-50 text-gray-900'
  };

  const iconColorClasses = {
    red: 'text-red-600',
    orange: 'text-orange-600',
    green: 'text-green-600', 
    blue: 'text-blue-600',
    gray: 'text-gray-600'
  };

  const getTrendIcon = () => {
    if (trend === 'up') return <TrendingUp className="h-3 w-3 text-green-600" />;
    if (trend === 'down') return <TrendingDown className="h-3 w-3 text-red-600" />;
    return <Minus className="h-3 w-3 text-gray-400" />;
  };

  return (
    <Card className={`${colorClasses[color]} border-l-4 overflow-visible`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className={iconColorClasses[color]}>
              {icon}
            </div>
            <CardTitle className="text-sm font-medium">
              {title}
            </CardTitle>
          </div>
          {tooltip && (
            <div className="relative group">
              <button className="p-1 rounded-full hover:bg-gray-100 transition-colors">
                <HelpCircle className="h-3.5 w-3.5 text-gray-400 hover:text-gray-600" />
              </button>
              {/* Tooltip positioned to avoid cutoff */}
              <div className={`absolute top-full mt-2 hidden group-hover:block z-[60] w-72 p-3 bg-gray-900 text-white text-xs rounded-lg shadow-xl border border-gray-700 ${
                isRightSide 
                  ? 'right-0 md:right-0 lg:right-0' 
                  : 'left-1/2 transform -translate-x-1/2 md:left-1/2 md:transform md:-translate-x-1/2 lg:left-1/2 lg:transform lg:-translate-x-1/2'
              }`}>
                {tooltip}
                {/* Arrow pointing up */}
                <div className={`absolute bottom-full w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-900 ${
                  isRightSide 
                    ? 'right-4 md:right-4 lg:right-4' 
                    : 'left-1/2 transform -translate-x-1/2 md:left-1/2 md:transform md:-translate-x-1/2 lg:left-1/2 lg:transform lg:-translate-x-1/2'
                }`}></div>
              </div>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="text-2xl font-bold">
            {loading ? (
              <div className="h-8 w-16 bg-gray-300 rounded animate-pulse" />
            ) : (
              value
            )}
          </div>
          
          {description && (
            <CardDescription className="text-xs">
              {description}
            </CardDescription>
          )}

          {trend && trendValue && (
            <div className="flex items-center gap-1 text-xs">
              {getTrendIcon()}
              <span className={trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-500'}>
                {trendValue}
              </span>
              <span className="text-gray-500">vs ayer</span>
            </div>
          )}

          {actionButton && actionButton.show && (
            <Button
              onClick={() => router.push(actionButton.href)}
              variant="outline"
              size="sm"
              className="mt-2 text-xs h-7"
            >
              {actionButton.label}
              <ExternalLink className="h-3 w-3 ml-1" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export function StatsCards() {
  const { dashboardData, loading, error, lastUpdated } = useSupervisorDashboard();
  const { escalations } = useEscalations();
  const { totalIssues: resourceIssues, loading: resourceLoading } = useResourceManagement();

  // Auto-refresh is now handled by TanStack Query hooks - no need for component-level intervals

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            <span>Error cargando estadísticas: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Extract real data from backend response
  const summary = dashboardData?.summary;
  const urgentEscalations = escalations?.filter(esc => 
    isPriorityUrgent(esc.escalation?.priority || esc.priority)
  ).length ?? 0;
  const activeAgents = summary?.available ?? 0;
  const totalAgents = summary?.totalAgents ?? 0;
  const activeSupervisions = 0; // TODO: Get from interventions data
  const pendingAuthorizations = dashboardData?.pendingAuthorizations?.length ?? 0;
  const totalResourceIssues = resourceLoading ? 0 : resourceIssues;

  const statsData = [
    {
      title: 'Escalaciones Urgentes',
      value: urgentEscalations,
      description: 'Requieren atención inmediata',
      icon: <AlertTriangle className="h-5 w-5" />,
      color: urgentEscalations > 0 ? 'red' : 'green',
      tooltip: 'Conversaciones explícitamente escaladas por agentes que presionaron "Escalar a Supervisor". Requieren decisión directa del supervisor para resolver el caso específico. No incluye problemas sistémicos de asignación.',
      actionButton: {
        label: 'Gestionar',
        href: '/supervisor/escalations',
        show: true
      }
    },
    {
      title: 'Agentes Disponibles',
      value: `${activeAgents}/${totalAgents}`,
      description: `${summary?.busy ?? 0} ocupados, ${summary?.away ?? 0} ausentes`,
      icon: <Users className="h-5 w-5" />,
      color: activeAgents > 0 ? 'green' : 'red',
      tooltip: 'Muestra cuántos agentes están disponibles para recibir nuevas conversaciones del total de agentes activos. Los agentes ocupados están atendiendo chats, los ausentes están en descanso o lunch.',
      actionButton: undefined    },
    {
      title: 'Autorizaciones Pendientes',
      value: pendingAuthorizations,
      description: 'Solicitudes por aprobar',
      icon: <Eye className="h-5 w-5" />,
      color: pendingAuthorizations > 0 ? 'orange' : 'gray',
      tooltip: 'Solicitudes de los agentes que requieren aprobación del supervisor: cambios de estado (busy, away), descansos, o permisos especiales. Revisa y aprueba en la sección Autorizaciones.',
      actionButton: undefined    },
    {
      title: 'Gestión de Recursos',
      value: totalResourceIssues,
      description: 'Problemas de asignación y carga',
      icon: <Clock className="h-5 w-5" />,
      color: totalResourceIssues > 0 ? 'orange' : 'green',
      tooltip: 'Problemas sistémicos de operación: agentes no disponibles con chats activos, sobrecarga de agentes, chats prioritarios sin asignar, y cuellos de botella departamentales. Se enfoca en redistribuir carga de trabajo, no en casos específicos escalados.',
      actionButton: {
        label: 'Gestionar',
        href: '/supervisor/interventions',
        show: true
      }
    }
  ] as const;

  return (
    <div className="space-y-4">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 overflow-visible">
        {statsData.map((stat, index) => {
          // Card should use right-aligned tooltip if it's in the rightmost column
          // lg: 4 columns, rightmost = index 3 (4th card)
          // md: 2 columns, rightmost = index 1, 3 (2nd and 4th cards)
          const isRightSide = index === 1 || index === 3;
          
          return (
            <StatCard
              key={index}
              title={stat.title}
              value={stat.value}
              description={stat.description}
              icon={stat.icon}
              color={stat.color}
              tooltip={stat.tooltip}
              loading={loading || (stat.title === 'Gestión de Recursos' ? resourceLoading : false)}
              isRightSide={isRightSide}
              actionButton={stat.actionButton}
            />
          );
        })}
      </div>

      {/* Last Updated */}
      <div className="flex justify-end">
        <div className="text-xs text-gray-500 flex items-center gap-1">
          <Clock className="h-3 w-3" />
          Actualizado: {lastUpdated ? lastUpdated.toLocaleTimeString('es-ES') : '--:--'}
        </div>
      </div>
    </div>
  );
}