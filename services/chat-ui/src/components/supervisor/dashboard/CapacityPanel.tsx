'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSupervisorQueries as useSupervisorDashboard } from '@/hooks/supervisor/useSupervisorQueries';

export function CapacityPanel() {
  const { dashboardData, loading } = useSupervisorDashboard();

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-semibold flex items-center gap-2">
            <div className="h-2 w-2 bg-gray-300 rounded-full animate-pulse"></div>
            Carga vs Capacidad
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 animate-pulse">
            <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            <div className="h-2 bg-gray-300 rounded w-full"></div>
            <div className="h-3 bg-gray-300 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const summary = dashboardData?.summary;
  const totalAgents = summary?.totalAgents ?? 0;
  const availableAgents = summary?.available ?? 0;
  const busyAgents = summary?.busy ?? 0;
  const awayAgents = summary?.away ?? 0;

  // Calculate system capacity (assuming 5 chats max per agent)
  const maxChatsPerAgent = 5;
  const totalCapacity = totalAgents * maxChatsPerAgent;
  const activeChats = busyAgents * 3; // Estimate: busy agents have ~3 chats each
  const utilization = totalCapacity > 0 ? (activeChats / totalCapacity) * 100 : 0;

  // Queue simulation (TODO: get real queue data)
  const queueLength = Math.max(0, activeChats - (availableAgents * 2)); // Rough estimate
  const estimatedWaitTime = queueLength > 0 ? Math.ceil(queueLength / Math.max(availableAgents, 1)) : 0;

  const getUtilizationColor = (util: number) => {
    if (util >= 90) return 'bg-red-500';
    if (util >= 75) return 'bg-orange-500';
    if (util >= 50) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const getQueueVariant = (length: number) => {
    if (length > 10) return 'destructive';
    if (length > 5) return 'secondary';
    return 'outline';
  };


  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-semibold">
          Carga vs Capacidad
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Chats Activos</span>
            <span className="font-semibold">{activeChats}/{totalCapacity}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${getUtilizationColor(utilization)}`}
              style={{ width: `${Math.min(utilization, 100)}%` }}
            ></div>
          </div>
          <div className="text-xs text-gray-500">{utilization.toFixed(1)}% utilización</div>
        </div>
        
        <div className="border-t pt-3 space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">Cola</span>
            <Badge variant={getQueueVariant(queueLength)} className="text-xs">
              {queueLength} conversaciones
            </Badge>
          </div>
          <div className="text-xs text-gray-500">
            Tiempo estimado: ~{estimatedWaitTime} minutos
          </div>
          {queueLength > 10 && (
            <div className="text-xs text-red-600 font-medium">
              ⚠️ Cola &gt; 10 conversaciones por más de 15 min
            </div>
          )}
        </div>

        {/* Agent breakdown */}
        <div className="border-t pt-3 space-y-2">
          <div className="text-xs text-gray-500 space-y-1">
            <div className="flex justify-between">
              <span>Disponibles:</span>
              <span className="text-green-600">{availableAgents}</span>
            </div>
            <div className="flex justify-between">
              <span>Ocupados:</span>
              <span className="text-orange-600">{busyAgents}</span>
            </div>
            <div className="flex justify-between">
              <span>Ausentes:</span>
              <span className="text-gray-600">{awayAgents}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}