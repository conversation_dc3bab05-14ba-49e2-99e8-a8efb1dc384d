'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  AlertTriangle, 
  Clock, 
  User,
  MessageCircle,
  CheckCircle,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import { useEscalationsQuery as useEscalations } from '@/hooks/supervisor/useEscalationsQuery';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

interface EscalationCardProps {
  escalation: any;
  onResolve: (id: string) => void;
  onView: (conversationId: string) => void;
  resolving?: boolean;
}

const PriorityBadge = ({ priority }: { priority: string | number }) => {
  const getPriorityConfig = (priority: string | number) => {
    const p = String(priority).toLowerCase();
    switch (p) {
      case 'critical':
      case '5':
        return { label: '<PERSON>r<PERSON><PERSON><PERSON>', color: 'bg-red-600 text-white', pulse: true };
      case 'urgent':
      case '4':
        return { label: 'Urgente', color: 'bg-red-500 text-white', pulse: true };
      case 'high':
      case '3':
        return { label: 'Alto', color: 'bg-orange-500 text-white', pulse: false };
      case 'medium':
      case '2':
        return { label: 'Medio', color: 'bg-yellow-500 text-white', pulse: false };
      default:
        return { label: 'Bajo', color: 'bg-gray-500 text-white', pulse: false };
    }
  };

  const config = getPriorityConfig(priority);
  
  return (
    <Badge 
      className={`${config.color} ${config.pulse ? 'animate-pulse' : ''} text-xs font-semibold`}
    >
      {config.label}
    </Badge>
  );
};

const EscalationCard = ({ escalation, onResolve, onView, resolving }: EscalationCardProps) => {
  const escalatedAt = escalation.escalation?.escalatedAt || escalation.metadata?.escalationTimestamp || Date.now();
  const timeAgo = formatDistanceToNow(new Date(escalatedAt), { 
    addSuffix: true, 
    locale: es 
  });

  const handleResolve = () => {
    onResolve(escalation.id);
  };

  const handleView = () => {
    onView(escalation.id);
  };

  return (
    <Card className="border-l-4 border-l-red-500 hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-sm font-semibold flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              {escalation.customer?.name || 'Cliente Anónimo'}
            </CardTitle>
            <CardDescription className="text-xs mt-1">
              ID: {escalation.id?.slice(0, 8)}... • {escalation.channel?.toUpperCase()}
            </CardDescription>
          </div>
          <PriorityBadge priority={escalation.escalation?.priority || escalation.priority || 'medium'} />
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Escalation Info */}
        <div className="space-y-2">
          {escalation.escalation?.reason && (
            <div className="text-sm">
              <span className="font-medium text-gray-700">Razón:</span>
              <p className="text-gray-600 mt-1">{escalation.escalation.reason}</p>
            </div>
          )}
          
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {timeAgo}
              </span>
              {escalation.assignedAgentId && (
                <span className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  Asignado
                </span>
              )}
              <span className="flex items-center gap-1">
                <MessageCircle className="h-3 w-3" />
                {escalation.metadata?.messageCount || 0} mensajes
              </span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button 
            onClick={handleResolve}
            disabled={resolving}
            size="sm"
            className="flex-1 bg-green-600 hover:bg-green-700 text-white"
          >
            {resolving ? (
              <>
                <RefreshCw className="h-3 w-3 mr-2 animate-spin" />
                Resolviendo...
              </>
            ) : (
              <>
                <CheckCircle className="h-3 w-3 mr-2" />
                Resolver
              </>
            )}
          </Button>
          <Button 
            onClick={handleView}
            variant="outline" 
            size="sm"
            className="px-3"
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export function EscalationsPanel() {
  const { 
    escalations, 
    loading, 
    error, 
    resolveEscalation, 
    resolving, 
    refreshEscalations 
  } = useEscalations();

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refreshEscalations();
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshEscalations]);

  const handleResolve = async (escalationId: string) => {
    try {
      await resolveEscalation(escalationId, 'Resuelto por supervisor');
      // Success feedback will be handled by the hook
    } catch (error) {
      console.error('Error resolving escalation:', error);
    }
  };

  const handleView = (conversationId: string) => {
    // Navigate to agent view with this conversation
    window.open(`/agent?conversation=${conversationId}`, '_blank');
  };

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            <span>Error cargando escalaciones: {error}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const urgentEscalations = escalations.filter(esc => 
    ['critical', 'urgent', '4', '5'].includes(String(esc.escalation?.priority || esc.priority))
  );

  return (
    <div className="space-y-4">
      {/* Header with Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-base font-medium">
            {urgentEscalations.length > 0 
              ? `${urgentEscalations.length} escalación${urgentEscalations.length > 1 ? 'es' : ''} urgente${urgentEscalations.length > 1 ? 's' : ''}` 
              : 'Sin escalaciones urgentes'
            }
          </h3>
          <p className="text-sm text-gray-500">
            Actualizadas automáticamente cada 30 segundos
          </p>
        </div>
        <Button
          onClick={refreshEscalations}
          variant="outline"
          size="sm"
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Actualizar
        </Button>
      </div>

      {/* Escalations List */}
      {loading && escalations.length === 0 ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                  <div className="h-8 bg-gray-300 rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : urgentEscalations.length > 0 ? (
        <div className="space-y-4">
          {urgentEscalations.map((escalation) => (
            <EscalationCard
              key={escalation.id}
              escalation={escalation}
              onResolve={handleResolve}
              onView={handleView}
              resolving={resolving[escalation.id] || false}
            />
          ))}
        </div>
      ) : (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-green-900 mb-2">
                ¡Todo bajo control!
              </h3>
              <p className="text-green-700">
                No hay escalaciones urgentes en este momento.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Show all escalations count if there are more */}
      {escalations.length > urgentEscalations.length && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">
            {escalations.length - urgentEscalations.length} escalación{escalations.length - urgentEscalations.length > 1 ? 'es' : ''} adicional{escalations.length - urgentEscalations.length > 1 ? 'es' : ''} de menor prioridad
          </p>
        </div>
      )}
    </div>
  );
}