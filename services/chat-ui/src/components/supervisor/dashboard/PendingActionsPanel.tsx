'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useSupervisorQueries as useSupervisorDashboard } from '@/hooks/supervisor/useSupervisorQueries';
import { useEscalationsQuery as useEscalations } from '@/hooks/supervisor/useEscalationsQuery';
import { useRouter } from 'next/navigation';

export function PendingActionsPanel() {
  const { dashboardData, loading } = useSupervisorDashboard();
  const { escalations } = useEscalations();
  const router = useRouter();

  if (loading) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-semibold">Acciones Pendientes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 animate-pulse">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-300 rounded w-3/4"></div>
                <div className="h-3 bg-gray-300 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const pendingAuthorizations = dashboardData?.pendingAuthorizations?.length ?? 0;
  const urgentEscalations = escalations?.filter(esc => 
    ['critical', 'urgent', '4', '5'].includes(String(esc.escalation?.priority || esc.priority))
  ).length ?? 0;
  
  // TODO: Get real interventions data
  const pendingInterventions = dashboardData?.summary?.withActiveChatActions ?? 0;

  const totalActions = pendingAuthorizations + urgentEscalations + pendingInterventions;

  const actionItems = [
    {
      title: 'Autorizaciones',
      count: pendingAuthorizations,
      description: 'Solicitudes de cambio de estado',
      variant: pendingAuthorizations > 0 ? 'secondary' : 'outline',
      href: '/supervisor/authorizations',
      show: true
    },
    {
      title: 'Intervenciones',
      count: pendingInterventions,
      description: 'Chats requieren supervisión',
      variant: pendingInterventions > 0 ? 'destructive' : 'outline',
      href: '/supervisor/interventions',
      show: true
    },
    {
      title: 'Escalaciones',
      count: urgentEscalations,
      description: urgentEscalations > 0 ? 'Casos críticos/urgentes' : 'Sin escalaciones urgentes',
      variant: urgentEscalations > 0 ? 'destructive' : 'outline',
      href: '/supervisor/escalations',
      show: true
    }
  ];

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold">Acciones Pendientes</CardTitle>
          <Badge 
            variant={totalActions > 0 ? "default" : "outline"} 
            className="text-xs"
          >
            {totalActions} items
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 max-h-80 overflow-y-auto">
        {actionItems.map((item, index) => (
          item.show && (
            <div key={index} className="border-b pb-4 last:border-b-0 last:pb-0">
              <div className="flex items-start justify-between mb-2">
                <div className="font-medium text-sm">{item.title}</div>
                <Badge variant={item.variant as any} className="text-xs">
                  {item.count} {item.count === 1 ? 'pendiente' : 'pendientes'}
                </Badge>
              </div>
              <div className="text-xs text-gray-500 mb-3">{item.description}</div>
              <Button 
                variant="outline" 
                size="sm" 
                className="text-xs h-7"
                onClick={() => router.push(item.href)}
              >
                {item.count > 0 ? 'Revisar' : 'Ver Todas'} →
              </Button>
            </div>
          )
        ))}
        
        {totalActions === 0 && (
          <div className="text-center py-6">
            <div className="text-green-600 text-sm font-medium mb-2">
              ✅ Todo al día
            </div>
            <div className="text-xs text-gray-500">
              No hay acciones pendientes en este momento
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}