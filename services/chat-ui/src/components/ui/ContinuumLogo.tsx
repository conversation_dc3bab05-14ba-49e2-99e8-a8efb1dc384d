import React from 'react';
import Image from 'next/image';

interface ContinuumLogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export const ContinuumLogo: React.FC<ContinuumLogoProps> = ({ 
  className = "", 
  width = 200, 
  height = 80
}) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <Image
        src="/logo-cx.webp"
        alt="Continuum"
        width={width}
        height={height}
        className="object-contain"
        priority
      />
    </div>
  );
};