/**
 * Connection Status Indicator
 * 
 * Clean, minimal visual indicator showing real-time connection state:
 * - Green dot: Real-time connected
 * - Yellow dot: Polling/fallback mode  
 * - Red dot: Offline/disconnected
 * 
 * Features:
 * - Hover tooltip with detailed status
 * - Click to force reconnect
 * - Smooth color transitions
 * - MVP-focused design
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { useConnectionState } from '@/hooks/useFirebaseRealtime';
import type { ConnectionState } from '@/services/firebaseRealtime';

interface ConnectionIndicatorProps {
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function ConnectionIndicator({ 
  className, 
  showLabel = false,
  size = 'sm'
}: ConnectionIndicatorProps) {
  const { connectionState, forceReconnect } = useConnectionState();

  // Color mapping for connection states
  const getIndicatorColor = (state: ConnectionState): string => {
    switch (state) {
      case 'connected':
        return 'bg-green-500 shadow-green-500/50';
      case 'polling':
        return 'bg-yellow-500 shadow-yellow-500/50';
      case 'reconnecting':
        return 'bg-yellow-500 shadow-yellow-500/50 animate-pulse';
      case 'offline':
        return 'bg-red-500 shadow-red-500/50';
      default:
        return 'bg-gray-400 shadow-gray-400/50';
    }
  };

  // Label text for each state
  const getStatusLabel = (state: ConnectionState): string => {
    switch (state) {
      case 'connected':
        return 'Real-time';
      case 'polling':
        return 'Syncing';
      case 'reconnecting':
        return 'Reconnecting...';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  // Tooltip text with more details
  const getTooltipText = (state: ConnectionState): string => {
    switch (state) {
      case 'connected':
        return 'Connected to real-time updates';
      case 'polling':
        return 'Using polling mode - attempting to reconnect';
      case 'reconnecting':
        return 'Reconnecting to real-time...';
      case 'offline':
        return 'No connection - click to retry';
      default:
        return 'Connection status unknown';
    }
  };

  // Size classes
  const getSizeClasses = (size: 'sm' | 'md' | 'lg'): string => {
    switch (size) {
      case 'sm':
        return 'h-2 w-2';
      case 'md':
        return 'h-3 w-3';
      case 'lg':
        return 'h-4 w-4';
      default:
        return 'h-2 w-2';
    }
  };

  const handleClick = () => {
    if (connectionState !== 'connected') {
      forceReconnect();
    }
  };

  return (
    <div 
      className={cn(
        "flex items-center space-x-2 select-none",
        className
      )}
      title={getTooltipText(connectionState)}
    >
      {/* Connection Dot */}
      <button
        onClick={handleClick}
        disabled={connectionState === 'connected'}
        className={cn(
          "rounded-full shadow-sm transition-all duration-300 border border-white/20",
          getSizeClasses(size),
          getIndicatorColor(connectionState),
          connectionState !== 'connected' 
            ? "cursor-pointer hover:scale-110 hover:shadow-md" 
            : "cursor-default"
        )}
        aria-label={`Connection status: ${getStatusLabel(connectionState)}`}
      />

      {/* Optional Label */}
      {showLabel && (
        <span 
          className={cn(
            "text-xs font-medium transition-colors duration-300",
            connectionState === 'connected' 
              ? "text-green-600" 
              : connectionState === 'offline'
              ? "text-red-600"
              : "text-yellow-600"
          )}
        >
          {getStatusLabel(connectionState)}
        </span>
      )}
    </div>
  );
}

/**
 * Connection Status Bar - More detailed indicator for debugging
 */
interface ConnectionStatusBarProps {
  className?: string;
}

export function ConnectionStatusBar({ className }: ConnectionStatusBarProps) {
  const { connectionState, isConnected, isPolling, forceReconnect } = useConnectionState();

  return (
    <div className={cn(
      "flex items-center justify-between p-2 bg-gray-50 border-b text-xs",
      className
    )}>
      <div className="flex items-center space-x-2">
        <ConnectionIndicator size="sm" />
        <span className="text-gray-600">
          Status: {connectionState}
        </span>
      </div>

      {!isConnected && (
        <button
          onClick={forceReconnect}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          Retry Connection
        </button>
      )}
    </div>
  );
}

/**
 * Floating Connection Indicator - Top-right corner placement
 */
interface FloatingConnectionIndicatorProps {
  className?: string;
}

export function FloatingConnectionIndicator({ className }: FloatingConnectionIndicatorProps) {
  const { connectionState } = useConnectionState();

  return (
    <div className={cn(
      "fixed top-4 right-4 z-50",
      className
    )}>
      <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-2 shadow-lg border border-gray-200">
        <ConnectionIndicator showLabel size="sm" />
      </div>
    </div>
  );
}