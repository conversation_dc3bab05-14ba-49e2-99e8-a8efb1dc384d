'use client';

import { Loader2 } from 'lucide-react';
import { useAuthRedirect } from '@/hooks/useAuthRedirect';
import { Agent } from '@/types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: Agent['role'][];
  redirectTo?: string;
}

export function ProtectedRoute({ 
  children, 
  allowedRoles,
  redirectTo = '/login' 
}: ProtectedRouteProps) {
  const { user, loading, isAllowed } = useAuthRedirect({
    allowedRoles,
    redirectTo
  });

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAllowed) {
    return null; // useAuthRedirect handles the redirect
  }

  return <>{children}</>;
}