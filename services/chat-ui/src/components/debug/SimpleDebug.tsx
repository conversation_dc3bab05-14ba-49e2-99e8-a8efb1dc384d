'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Activity } from 'lucide-react';

interface SimpleDebugProps {
  className?: string;
}

export function SimpleDebug({ className }: SimpleDebugProps) {
  const [isVisible, setIsVisible] = useState(false);

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        variant="outline"
        size="sm"
        className={`fixed bottom-4 right-4 z-50 ${className}`}
      >
        <Activity className="h-4 w-4 mr-2" />
        Simple Debug
      </Button>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 w-80 p-4 bg-white border shadow-lg z-50 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium">Simple Debug Panel</h3>
        <Button
          onClick={() => setIsVisible(false)}
          variant="ghost"
          size="sm"
        >
          ×
        </Button>
      </div>
      <div className="text-xs text-gray-600">
        Debug panel is working! Original component had hook issues.
      </div>
    </div>
  );
}