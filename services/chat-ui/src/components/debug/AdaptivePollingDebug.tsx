/**
 * Adaptive Polling Debug Component
 * 
 * Displays real-time information about adaptive polling behavior:
 * - Current polling intervals
 * - System load detection
 * - Agent activity tracking
 * - Configuration changes history
 * 
 * Use this component during development to optimize polling behavior
 */

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Clock, 
  Users, 
  MessageSquare, 
  TrendingUp,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react';
import { useAdaptivePollingDebug } from '@/hooks/useAdaptivePolling';

interface AdaptivePollingDebugProps {
  conversationId?: string | null;
  className?: string;
}

export function AdaptivePollingDebug({ 
  conversationId, 
  className 
}: AdaptivePollingDebugProps) {
  const [isVisible, setIsVisible] = useState(false);
  const pollingDebug = useAdaptivePollingDebug();
  
  // Debug data - simplified without useMemo for now  
  const debugData = {
    conversationsInterval: pollingDebug.conversationsInterval,
    messagesInterval: pollingDebug.messagesInterval,
    systemLoad: pollingDebug.systemLoad,
    totalConversations: pollingDebug.totalConversations,
    isAgentActive: pollingDebug.isAgentActive,
    isTyping: pollingDebug.isTyping,
    debugReason: pollingDebug.debugInfo.reason,
    configChanges: pollingDebug.configChanges,
    averageConversationsInterval: pollingDebug.averageConversationsInterval,
    averageMessagesInterval: pollingDebug.averageMessagesInterval,
    recentHistory: pollingDebug.history.slice(-3).reverse()
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        variant="outline"
        size="sm"
        className={`fixed bottom-4 right-4 z-50 ${className}`}
      >
        <Activity className="h-4 w-4 mr-2" />
        Debug Polling
      </Button>
    );
  }

  // Utility functions - simplified without useCallback for now
  const formatInterval = (ms: number): string => {
    if (ms >= 60000) {
      return `${(ms / 60000).toFixed(1)}m`;
    }
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getLoadColor = (load: string) => {
    switch (load) {
      case 'low': return 'bg-green-500';
      case 'medium': return 'bg-yellow-500';
      case 'high': return 'bg-orange-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <Card className={`fixed bottom-4 right-4 w-80 max-h-96 overflow-y-auto z-50 shadow-lg ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center">
            <Activity className="h-4 w-4 mr-2" />
            Adaptive Polling Debug
          </CardTitle>
          <Button
            onClick={() => setIsVisible(false)}
            variant="ghost"
            size="sm"
          >
            <EyeOff className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3 text-xs">
        {/* Current Intervals */}
        <div>
          <h4 className="font-medium mb-1 flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            Current Intervals
          </h4>
          <div className="grid grid-cols-2 gap-2">
            <div className="bg-gray-50 p-2 rounded">
              <div className="text-gray-600">Conversations</div>
              <div className="font-mono font-bold">
                {formatInterval(debugData.conversationsInterval)}
              </div>
            </div>
            <div className="bg-gray-50 p-2 rounded">
              <div className="text-gray-600">Messages</div>
              <div className="font-mono font-bold">
                {formatInterval(debugData.messagesInterval)}
              </div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div>
          <h4 className="font-medium mb-1 flex items-center">
            <TrendingUp className="h-3 w-3 mr-1" />
            System Status
          </h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span>Load Level:</span>
              <Badge className={`text-white text-xs ${getLoadColor(debugData.systemLoad)}`}>
                {debugData.systemLoad.toUpperCase()}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Active Chats:</span>
              <span className="font-mono">{debugData.totalConversations}</span>
            </div>
          </div>
        </div>

        {/* Agent Activity */}
        <div>
          <h4 className="font-medium mb-1 flex items-center">
            <Users className="h-3 w-3 mr-1" />
            Agent Activity
          </h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span>Status:</span>
              <Badge variant={debugData.isAgentActive ? "default" : "secondary"}>
                {debugData.isAgentActive ? "ACTIVE" : "IDLE"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Typing:</span>
              <Badge variant={debugData.isTyping ? "default" : "outline"}>
                {debugData.isTyping ? "YES" : "NO"}
              </Badge>
            </div>
            {conversationId && (
              <div className="flex items-center justify-between">
                <span>Chat ID:</span>
                <span className="font-mono text-xs truncate max-w-20" title={conversationId}>
                  {conversationId.slice(-8)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Configuration Info */}
        <div>
          <h4 className="font-medium mb-1 flex items-center">
            <MessageSquare className="h-3 w-3 mr-1" />
            Configuration
          </h4>
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span>Reason:</span>
              <span className="text-xs bg-gray-100 px-1 rounded max-w-32 truncate" title={debugData.debugReason}>
                {debugData.debugReason.split('_').pop()}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span>Changes:</span>
              <span className="font-mono">{debugData.configChanges}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Avg Conv:</span>
              <span className="font-mono">{formatInterval(debugData.averageConversationsInterval)}</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Avg Msg:</span>
              <span className="font-mono">{formatInterval(debugData.averageMessagesInterval)}</span>
            </div>
          </div>
        </div>

        {/* Recent History */}
        {debugData.recentHistory.length > 0 && (
          <div>
            <h4 className="font-medium mb-1">Recent Changes</h4>
            <div className="space-y-1 max-h-20 overflow-y-auto">
              {debugData.recentHistory.map((entry, index) => (
                <div key={index} className="text-xs bg-gray-50 p-1 rounded">
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      {entry.timestamp.toLocaleTimeString()}
                    </span>
                    <span className="font-mono">
                      {formatInterval(entry.config.conversationsInterval)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 truncate">
                    {entry.config.reason.replace(/_/g, ' ')}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Manual Actions */}
        <div className="pt-2 border-t">
          <Button
            onClick={pollingDebug.forceSystemRefresh}
            variant="outline"
            size="sm"
            className="w-full text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Force System Check
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Simple polling indicator for production use
 */
export function AdaptivePollingIndicator({ className }: { className?: string }) {
  const pollingDebug = useAdaptivePollingDebug();
  
  // Indicator data - simplified without useMemo for now
  const indicatorData = {
    conversationsInterval: pollingDebug.conversationsInterval,
    systemLoad: pollingDebug.systemLoad,
    isAgentActive: pollingDebug.isAgentActive
  };

  const formatInterval = (ms: number): string => {
    if (ms >= 60000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getLoadColor = (load: string) => {
    switch (load) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-orange-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className={`flex items-center space-x-2 text-xs ${className}`}>
      <div className="flex items-center space-x-1">
        <Clock className="h-3 w-3" />
        <span>{formatInterval(indicatorData.conversationsInterval)}</span>
      </div>
      <div className="w-1 h-1 bg-gray-300 rounded-full" />
      <div className={`flex items-center space-x-1 ${getLoadColor(indicatorData.systemLoad)}`}>
        <Activity className="h-3 w-3" />
        <span className="capitalize">{indicatorData.systemLoad}</span>
      </div>
      {indicatorData.isAgentActive && (
        <>
          <div className="w-1 h-1 bg-gray-300 rounded-full" />
          <div className="text-green-600">
            <Eye className="h-3 w-3" />
          </div>
        </>
      )}
    </div>
  );
}