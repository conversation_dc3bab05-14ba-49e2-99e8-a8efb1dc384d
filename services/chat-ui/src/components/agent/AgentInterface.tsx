'use client';

import { useState, useEffect } from 'react';
import { mutate } from 'swr';
import { 
  ResizableHandle, 
  ResizablePanel, 
  ResizablePanelGroup 
} from '@/components/ui/resizable';
import { AgentHeader } from './AgentHeader';
import { ConversationList } from './ConversationList';
import { ChatArea } from './ChatArea';
import { CustomerPanel } from './CustomerPanel';

export function AgentInterface() {
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);

  // 🐛 SETUP DEBUG TOOLS for transfer cache issues
  useEffect(() => {
    // Expose debug tools to window object
    window.cxDebugTransfers = {
      // Clear all SWR cache aggressively
      clearAllCache: () => {
        console.log('🧹 Clearing ALL SWR cache');
        mutate(() => true, undefined, { revalidate: true });
        
        // Also clear localStorage and sessionStorage
        localStorage.clear();
        sessionStorage.clear();
        
        console.log('✅ All cache cleared - refresh page to reload');
      },
      
      // Force refresh conversations specifically
      forceRefreshConversations: () => {
        console.log('🔄 Force refreshing conversations cache');
        mutate(['conversations'], undefined, { revalidate: true });
        mutate((key) => key && Array.isArray(key) && key[0] === 'conversations', undefined, { revalidate: true });
      },
      
      // Show current cache state
      showCacheState: () => {
        console.log('📊 SWR Cache State:');
        console.log('Conversations:', (mutate as any).cache?.get(['conversations']));
        console.log('All keys:', Array.from((mutate as any).cache?.keys() || []));
      },
      
      // Nuclear reset - everything
      nuclearReset: () => {
        console.log('💥 NUCLEAR RESET - clearing everything');
        mutate(() => true, undefined, { revalidate: true });
        localStorage.clear();
        sessionStorage.clear();
        location.reload();
      }
    };
    
    console.log('🔧 Debug tools loaded - use window.cxDebugTransfers');
    
    return () => {
      // Cleanup
      delete window.cxDebugTransfers;
    };
  }, []);

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <AgentHeader />
      
      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal" className="h-full min-h-0">
          {/* Conversation List */}
          <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
            <ConversationList 
              selectedConversationId={selectedConversationId}
              onSelectConversation={setSelectedConversationId}
            />
          </ResizablePanel>
          
          <ResizableHandle withHandle />
          
          {/* Chat Area */}
          <ResizablePanel defaultSize={50} minSize={40}>
            <ChatArea 
              conversationId={selectedConversationId} 
              onConversationSelect={setSelectedConversationId}
            />
          </ResizablePanel>
          
          <ResizableHandle withHandle />
          
          {/* Customer Information Panel */}
          <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
            <CustomerPanel conversationId={selectedConversationId} />
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </div>
  );
}