'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ContinuumLogo } from '@/components/ui/ContinuumLogo';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger, 
  DropdownMenuSeparator 
} from '@/components/ui/dropdown-menu';
import { 
  Settings, 
  LogOut, 
  User, 
  ChevronDown,
  Circle,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { authService } from '@/services/supabase';
import chatRealtimeAPI from '@/services/api';
import { ConnectionIndicator } from '@/components/ui/connection-indicator';
import { AdaptivePollingIndicator } from '@/components/debug/AdaptivePollingDebug';
import { useHybridConversations } from '@/hooks/useHybridConversations';
import { AgentStatus } from '@/types/api';
import { useRouter } from 'next/navigation';
import { AuthorizationRequestModal } from './AuthorizationRequestModal';

export function AgentHeader() {
  const { user, signOut } = useAuth();
  const router = useRouter();
  const [agentStatus, setAgentStatus] = useState<AgentStatus>('available');
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<AgentStatus | null>(null);
  
  // Get real hybrid status (not just Firebase connection)
  useHybridConversations(
    { limit: 1 }, // Just for status check
    user?.id
  );

  // Initialize status from Firebase (real-time status), not Supabase
  useEffect(() => {
    const fetchCurrentStatus = async () => {
      if (!user?.id) return;
      
      try {
        console.log('🔍 Fetching agent status for user ID:', user.id);
        // Import API service dynamically to avoid circular dependencies  
        const { default: chatRealtimeAPI } = await import('@/services/api');
        const response = await chatRealtimeAPI.getAgents();
        
        if (response.success && response.data?.agents) {
          const currentAgent = response.data.agents.find(agent => agent.id === user.id);
          if (currentAgent?.status?.availability) {
            console.log('🔄 Loading current agent status from Firebase:', currentAgent.status.availability);
            setAgentStatus(currentAgent.status.availability);
          } else {
            console.log('⚠️ Agent not found in agents list, using default: available');
            setAgentStatus('available');
          }
        } else {
          // Fallback to default only if no Firebase status exists
          console.log('⚠️ No Firebase status found, using default: available');
          setAgentStatus('available');
        }
      } catch (error) {
        console.error('Error fetching agent status:', error);
        // Fallback to default if Firebase fails completely
        setAgentStatus('available');
      }
    };
    
    fetchCurrentStatus();
  }, [user?.id]);

  const handleSignOut = async () => {
    await signOut();
  };

  const handleViewAsSupervisor = () => {
    router.push('/supervisor');
  };

  // Check if user has supervisor privileges
  const canViewAsSupervisor = user?.role === 'supervisor' || user?.role === 'admin';

  const handleStatusChange = async (status: AgentStatus) => {
    if (!user?.id || isUpdatingStatus) return;
    
    // Status 'busy' requires supervisor authorization
    if (status === 'busy') {
      setPendingStatus(status);
      setAuthModalOpen(true);
      return;
    }
    
    // Other statuses can be changed directly
    await updateAgentStatus(status);
  };

  const updateAgentStatus = async (status: AgentStatus, supervisorAuthorizationId?: string) => {
    if (!user?.id) return;
    
    setIsUpdatingStatus(true);
    try {
      console.log('📤 Updating agent status:', { agentId: user.id, status, supervisorAuthorizationId });
      const { error } = await authService.updateAgentStatus(user.id, status, undefined, supervisorAuthorizationId);
      
      if (error) {
        console.error('Failed to update agent status:', error);
        // TODO: Show user-friendly error notification
        return;
      }
      
      setAgentStatus(status);
      console.log('Agent status updated successfully to:', status);
    } catch (error) {
      console.error('Error updating agent status:', error);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleAuthorizationRequest = async (reason: string, maxDuration?: number) => {
    if (!user?.id || !pendingStatus) return;

    try {
      console.log('📤 Requesting authorization:', { agentId: user.id, status: pendingStatus, reason, maxDuration });
      
      const response = await chatRealtimeAPI.createSupervisorAuthorization({
        agentId: user.id,
        authorizedStatus: pendingStatus as 'busy',
        reason,
        maxDuration: maxDuration ? maxDuration * 60 : undefined, // Convert minutes to seconds
        expiresAt: maxDuration ? new Date(Date.now() + maxDuration * 60 * 1000).toISOString() : undefined
      });

      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to create authorization request');
      }

      console.log('✅ Authorization request created:', response.data);
      
      // TODO: Show notification that request was sent
      // TODO: Implement real-time listening for authorization approval
      
    } catch (error) {
      console.error('Error requesting authorization:', error);
      throw error;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'text-green-500';
      case 'busy': return 'text-red-500';
      case 'away': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'available': return 'default';
      case 'busy': return 'destructive';
      case 'away': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-2 flex items-center justify-between h-[60px]">
      {/* Left Side - Logo and Title */}
      <div className="flex items-center space-x-4">
        <div>
          <ContinuumLogo width={120} height={40} />
        </div>
      </div>

      {/* Right Side - Agent Info and Controls */}
      <div className="flex items-center space-x-4">
        {/* Connection & Polling Status */}
        <div className="flex items-center space-x-4">
          <ConnectionIndicator showLabel size="sm" />
          <AdaptivePollingIndicator />
        </div>
        
        {/* Agent Status Indicator */}
        <div className="flex items-center space-x-2">
          <Circle className={cn(
            `h-3 w-3 fill-current ${getStatusColor(agentStatus)}`,
            isUpdatingStatus && 'animate-pulse opacity-50'
          )} />
          <Badge variant={getStatusBadgeVariant(agentStatus)} className={cn(
            "capitalize",
            isUpdatingStatus && 'opacity-50'
          )}>
            {agentStatus}
          </Badge>
        </div>

        {/* Agent Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center space-x-2 hover:bg-gray-100">
              <Avatar className="h-8 w-8">
                {/* Avatar URL not available in Agent type */}
                <AvatarFallback className="text-sm">
                  {user?.name?.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="text-left">
                <p className="text-sm font-medium text-gray-900">{user?.name}</p>
                <p className="text-xs text-gray-500">{user?.email}</p>
              </div>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </Button>
          </DropdownMenuTrigger>
          
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuItem>
              <User className="mr-2 h-4 w-4" />
              Profile
            </DropdownMenuItem>
            
            {canViewAsSupervisor && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleViewAsSupervisor}>
                  <Shield className="mr-2 h-4 w-4" />
                  Ver como Supervisor
                </DropdownMenuItem>
              </>
            )}
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem 
              onClick={() => handleStatusChange('available')}
              disabled={isUpdatingStatus}
              className={cn(
                agentStatus === 'available' && 'bg-green-50 text-green-700'
              )}
            >
              <Circle className="mr-2 h-3 w-3 fill-current text-green-500" />
              Available
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleStatusChange('busy')}
              disabled={isUpdatingStatus}
              className={cn(
                agentStatus === 'busy' && 'bg-red-50 text-red-700'
              )}
            >
              <Circle className="mr-2 h-3 w-3 fill-current text-red-500" />
              Busy
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleStatusChange('away')}
              disabled={isUpdatingStatus}
              className={cn(
                agentStatus === 'away' && 'bg-yellow-50 text-yellow-700'
              )}
            >
              <Circle className="mr-2 h-3 w-3 fill-current text-yellow-500" />
              Away
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </DropdownMenuItem>
            
            <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
              <LogOut className="mr-2 h-4 w-4" />
              Sign Out
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      
      {/* Authorization Request Modal */}
      <AuthorizationRequestModal
        isOpen={authModalOpen}
        onClose={() => {
          setAuthModalOpen(false);
          setPendingStatus(null);
        }}
        targetStatus={pendingStatus || 'busy'}
        onSubmit={handleAuthorizationRequest}
        isSubmitting={isUpdatingStatus}
      />
    </header>
  );
}
