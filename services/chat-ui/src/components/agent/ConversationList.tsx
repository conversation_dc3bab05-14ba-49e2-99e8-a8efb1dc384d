'use client';

import { useState, useMemo } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { 
  Search, 
  Filter, 
  MessageCircle, 
  Clock, 
  AlertCircle,
  Phone,
  Mail,
  RefreshCw,
  Grid3X3,
  Play,
  Square,
  ArrowRightLeft
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useHybridConversations } from '@/hooks/useHybridConversations';
import { useAuth } from '@/contexts/AuthContext';
import { useConversationMutations } from '@/hooks/useConversationMutations';
import { useOptimisticConversationAnimations } from '@/hooks/useOptimisticConversationAnimations';

interface ConversationListProps {
  selectedConversationId: string | null;
  onSelectConversation: (conversationId: string) => void;
}


export function ConversationList({ selectedConversationId, onSelectConversation }: ConversationListProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<'all' | 'active' | 'pending' | 'transferring'>('all');
  const { user } = useAuth();

  // TanStack Query mutations for optimistic conversation updates
  const { markAsRead, applyOptimisticFilter } = useConversationMutations();

  // Use hybrid API data (real-time when available, fallback to REST)
  // Fetch ALL conversations and filter client-side for better optimistic UX
  const { 
    conversations: allConversations, 
    loading, 
    error, 
    refresh, 
    isRealtime 
  } = useHybridConversations(
    {
      status: undefined, // Fetch all statuses for client-side filtering
      limit: 50,
    },
    user?.id // Pass agent ID for real-time filtering
  );

  // Apply optimistic filtering (instant UI updates)
  const conversations = useMemo(() => {
    if (!allConversations) return [];
    return applyOptimisticFilter(allConversations, filter);
  }, [allConversations, filter, applyOptimisticFilter]);

  // Conversation animations for optimistic feedback
  const { 
    getAnimationClass, 
    isNewConversation, 
    isRecentlyUpdated 
  } = useOptimisticConversationAnimations(conversations);

  // Enhanced conversation selection with optimistic unread count update
  const handleSelectConversation = (conversationId: string) => {
    // Call the original onSelectConversation first
    onSelectConversation(conversationId);
    
    // Optimistically mark as read (immediate UI update + background API call)
    markAsRead(conversationId);
  };

  // DEBUG: Removed console.log to prevent infinite loops
  // This was causing Maximum update depth exceeded errors


  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 1) return 'Now';
    if (diffMins < 60) return `${diffMins}m`;
    if (diffHours < 24) return `${diffHours}h`;
    return date.toLocaleDateString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'transferring': return 'bg-blue-100 text-blue-800';
      case 'pending_acceptance': return 'bg-orange-100 text-orange-800';
      case 'supervised': return 'bg-purple-100 text-purple-800';
      case 'escalated': return 'bg-red-100 text-red-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityIcon = (conversation: any) => {
    const { priority, status, lastMessage } = conversation;
    
    // Convert numeric priority to string for unified handling
    // Backend: 1=low, 2=normal, 3=high, 4=urgent, 5=critical
    const priorityString = typeof priority === 'number' 
      ? (priority >= 5 ? 'critical' : 
         priority >= 4 ? 'urgent' : 
         priority >= 3 ? 'high' : 
         priority >= 2 ? 'medium' : 'low')
      : priority;
    
    // Critical/Urgent priority - always show red alert (most urgent)
    if (priorityString === 'critical' || priorityString === 'urgent') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    
    // High priority always shows red alert (critical issues don't disappear)
    if (priorityString === 'high') {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    
    // Medium priority shows clock ONLY if needs immediate attention
    if (priorityString === 'medium') {
      // Hide clock if conversation is already being handled (active)
      if (status === 'active') {
        return null;
      }
      
      // Hide clock if agent has already responded
      if (lastMessage?.senderType === 'agent') {
        return null;
      }
      
      // Show clock if still pending and needs attention
      if (status === 'pending' && lastMessage?.senderType === 'customer') {
        return <Clock className="h-4 w-4 text-yellow-500" />;
      }
    }
    
    return null;
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp': return <MessageCircle className="h-4 w-4 text-green-500" />;
      case 'phone': return <Phone className="h-4 w-4 text-blue-500" />;
      case 'email': return <Mail className="h-4 w-4 text-gray-500" />;
      default: return <MessageCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getFilterIcon = (filterType: string) => {
    switch (filterType) {
      case 'all': return <Grid3X3 className="h-3.5 w-3.5" />;
      case 'active': return <Play className="h-3.5 w-3.5" />;
      case 'pending': return <Clock className="h-3.5 w-3.5" />;
      case 'transferring': return <ArrowRightLeft className="h-3.5 w-3.5" />;
      default: return <Grid3X3 className="h-3.5 w-3.5" />;
    }
  };

  const getFilterLabel = (filterType: string) => {
    switch (filterType) {
      case 'all': return 'Todos';
      case 'active': return 'Activas';
      case 'pending': return 'Pendientes'; 
      case 'transferring': return 'Transfiriendo';
      default: return 'Todos';
    }
  };

  // Client-side filtering (API already filters by status)
  const filteredConversations = useMemo(() => {
    if (!searchTerm) return conversations;
    
    return conversations.filter(conv => 
      conv.metadata?.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conv.metadata?.customerPhone?.includes(searchTerm) ||
      conv.metadata?.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [conversations, searchTerm]);

  return (
    <div className="h-full min-h-0 flex flex-col bg-white border-r border-gray-200 min-w-[280px] max-w-[480px] overflow-hidden">
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <h2 className="text-base font-semibold text-gray-900">Conversations</h2>
            {isRealtime && (
              <Badge variant="outline" className="text-xs px-1.5 py-0.5 text-green-600 border-green-200">
                Live
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Badge 
              variant="secondary" 
              className="text-xs px-2 py-1"
              title={`${filteredConversations.length} conversación${filteredConversations.length !== 1 ? 'es' : ''} mostrada${filteredConversations.length !== 1 ? 's' : ''}`}
            >
              {filteredConversations.length}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refresh()}
              disabled={loading}
              className="h-7 w-7 p-0"
              title="Actualizar conversaciones"
            >
              <RefreshCw className={cn("h-3 w-3", loading && "animate-spin")} />
            </Button>
          </div>
        </div>

        {/* Compact Search */}
        <div className="relative mb-3">
          <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-400" />
          <Input
            placeholder="Buscar..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8 h-8 text-sm"
          />
        </div>

        {/* Icon-based Filters with Tooltips */}
        <div className="grid grid-cols-4 gap-0">
          {(['all', 'active', 'pending', 'transferring'] as const).map((filterOption) => (
            <Button
              key={filterOption}
              variant={filter === filterOption ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(filterOption)}
              className={cn(
                "h-8 flex-1 p-0 flex items-center justify-center transition-all rounded-none first:rounded-l-md last:rounded-r-md",
                filter === filterOption 
                  ? "bg-blue-600 text-white hover:bg-blue-700" 
                  : "hover:bg-gray-100"
              )}
              title={getFilterLabel(filterOption)} // Tooltip nativo
            >
              {getFilterIcon(filterOption)}
            </Button>
          ))}
        </div>
      </div>

      {/* Conversation List */}
      <ScrollArea className="flex-1 min-h-0">
        <div className="p-2 pb-4">
          {error ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-red-300 mx-auto mb-4" />
              <p className="text-red-600 mb-2">Error cargando conversaciones</p>
              <Button variant="outline" size="sm" onClick={() => refresh()}>
                Reintentar
              </Button>
            </div>
          ) : loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-12 w-12 text-blue-300 mx-auto mb-4 animate-spin" />
              <p className="text-gray-500">Cargando conversaciones...</p>
            </div>
          ) : filteredConversations.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No se encontraron conversaciones</p>
              {searchTerm && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setSearchTerm('')}
                  className="mt-2"
                >
                  Limpiar búsqueda
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredConversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => handleSelectConversation(conversation.id)}
                  className={cn(
                    "p-2.5 rounded-lg cursor-pointer transition-all hover:bg-gray-50 relative",
                    selectedConversationId === conversation.id
                      ? "bg-blue-50 ring-2 ring-blue-200 ring-inset"
                      : "",
                    getAnimationClass(conversation.id) // Optimistic animations
                  )}
                >
                  <div className="flex items-start space-x-3">
                    {/* Smart Avatar with Status Indicator */}
                    <div className="relative flex-shrink-0">
                      <Avatar className="h-11 w-11">
                        <AvatarImage src={conversation.metadata?.customerAvatar || undefined} />
                        <AvatarFallback className={cn(
                          "text-sm font-medium",
                          // Priority-based avatar background (support both numeric and string priorities)
                          (() => {
                            const priorityString = typeof conversation.priority === 'number' 
                              ? (conversation.priority >= 5 ? 'critical' : 
                                 conversation.priority >= 4 ? 'urgent' : 
                                 conversation.priority >= 3 ? 'high' : 
                                 conversation.priority >= 2 ? 'medium' : 'low')
                              : conversation.priority;
                            
                            if (priorityString === 'critical' || priorityString === 'urgent') {
                              return 'bg-red-200 text-red-800';
                            }
                            if (priorityString === 'high') {
                              return 'bg-red-100 text-red-700';
                            }
                            if (priorityString === 'medium') {
                              return 'bg-yellow-100 text-yellow-700';
                            }
                            return 'bg-gray-100 text-gray-700';
                          })()
                        )}>
                          {(conversation.customer?.name || conversation.metadata?.customerName)?.split(' ').map((n: string) => n[0]).join('') || '??'}
                        </AvatarFallback>
                      </Avatar>
                      
                      {/* Status & Channel Indicators */}
                      <div className="absolute -bottom-1 -right-1 flex items-center space-x-1">
                        {/* Channel Icon */}
                        <div className="bg-white rounded-full p-1 shadow-sm border">
                          {getChannelIcon(conversation.channel)}
                        </div>
                        {/* Unread Count */}
                        {(conversation.unreadCount || 0) > 0 && (
                          <Badge variant="destructive" className="text-[10px] h-5 w-5 rounded-full p-0 flex items-center justify-center">
                            {conversation.unreadCount && conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Adaptive Content Area */}
                    <div className="flex-1 min-w-0">
                      {/* Top Row: Name + Time + Priority */}
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2 flex-1">
                          <h3 className="text-sm font-semibold text-gray-900 truncate">
                            {conversation.customer?.name || conversation.metadata?.customerName || 'Unknown'}
                          </h3>
                          {/* NEW conversation badge */}
                          {isNewConversation(conversation.id) && (
                            <Badge variant="destructive" className="text-[10px] px-1.5 py-0.5 animate-pulse">
                              NEW
                            </Badge>
                          )}
                          {/* Recently updated indicator */}
                          {isRecentlyUpdated(conversation.id) && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                          )}
                        </div>
                        <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                          {getPriorityIcon(conversation)}
                          <span className="text-xs text-gray-500 font-medium">
                            {conversation.lastMessage?.timestamp 
                              ? formatTimestamp(new Date(conversation.lastMessage.timestamp))
                              : formatTimestamp(new Date(conversation.updatedAt))}
                          </span>
                        </div>
                      </div>

                      {/* Debug IDs Row */}
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-[10px] font-mono text-gray-400 bg-gray-50 px-1.5 py-0.5 rounded">
                          Conv: {conversation.id.slice(-8)}
                        </span>
                        {conversation.agentId && (
                          <span className="text-[10px] font-mono text-blue-600 bg-blue-50 px-1.5 py-0.5 rounded">
                            Agent: {conversation.agentId.slice(-8)}
                          </span>
                        )}
                      </div>

                      {/* Message Preview - Responsive */}
                      <p className="text-sm text-gray-600 mb-2 leading-relaxed" 
                         style={{
                           display: '-webkit-box',
                           WebkitLineClamp: 2,
                           WebkitBoxOrient: 'vertical',
                           overflow: 'hidden',
                           wordBreak: 'break-word'
                         }}>
                        {conversation.lastMessage?.content || `${conversation.metadata?.totalMessages || 0} mensajes`}
                      </p>

                      {/* Bottom Row: Status Badge */}
                      <div className="flex items-center justify-between">
                        <Badge
                          variant="secondary"
                          className={cn(
                            "text-xs font-medium px-2 py-1",
                            getStatusColor(conversation.status)
                          )}
                        >
                          {conversation.status}
                        </Badge>
                        
                        {/* Department Tag (hidden on small widths) */}
                        <span className="hidden lg:block text-xs text-gray-500 truncate">
                          {conversation.department?.replace('_', ' ')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}