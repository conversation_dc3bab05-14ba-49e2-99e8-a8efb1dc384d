'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clock, AlertCircle, CheckCircle } from 'lucide-react';
import { AgentStatus } from '@/types/api';

interface AuthorizationRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  targetStatus: AgentStatus;
  onSubmit: (reason: string, maxDuration?: number) => Promise<void>;
  isSubmitting?: boolean;
}

const STATUS_LABELS = {
  busy: 'Ocupado',
  away: 'Ausente',
  available: 'Disponible'
};

const DURATION_OPTIONS = [
  { value: 15, label: '15 minutos' },
  { value: 30, label: '30 minutos' },
  { value: 60, label: '1 hora' },
  { value: 120, label: '2 horas' },
  { value: 240, label: '4 horas' },
  { value: null, label: 'Sin límite de tiempo' }
];

export function AuthorizationRequestModal({
  isOpen,
  onClose,
  targetStatus,
  onSubmit,
  isSubmitting = false
}: AuthorizationRequestModalProps) {
  const [reason, setReason] = useState('');
  const [maxDuration, setMaxDuration] = useState<number | null>(30); // Default 30 minutes
  const [step, setStep] = useState<'form' | 'submitting' | 'success'>('form');

  const handleSubmit = async () => {
    if (!reason.trim()) return;
    
    setStep('submitting');
    try {
      await onSubmit(reason, maxDuration || undefined);
      setStep('success');
      
      // Auto close after showing success
      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch {
      setStep('form');
      // Error will be handled by parent component
    }
  };

  const handleClose = () => {
    setStep('form');
    setReason('');
    setMaxDuration(30);
    onClose();
  };

  if (step === 'submitting') {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
            <h3 className="text-lg font-semibold">Enviando solicitud...</h3>
            <p className="text-sm text-gray-500">Esperando aprobación del supervisor</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (step === 'success') {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="flex flex-col items-center justify-center py-8">
            <CheckCircle className="h-12 w-12 text-green-600 mb-4" />
            <h3 className="text-lg font-semibold">¡Solicitud enviada!</h3>
            <p className="text-sm text-gray-500 text-center">
              Tu supervisor ha recibido la solicitud de cambio a {STATUS_LABELS[targetStatus].toLowerCase()}
            </p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            Solicitar Autorización
          </DialogTitle>
          <DialogDescription>
            Necesitas autorización del supervisor para cambiar tu estado a{' '}
            <span className="font-semibold">{STATUS_LABELS[targetStatus].toLowerCase()}</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="reason">Motivo de la solicitud *</Label>
            <Textarea
              id="reason"
              placeholder="Ej: Descanso programado, almuerzo, reunión de equipo..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              maxLength={200}
            />
            <div className="text-xs text-gray-500 text-right">
              {reason.length}/200 caracteres
            </div>
          </div>

          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Duración estimada
            </Label>
            <Select value={maxDuration?.toString() || 'null'} onValueChange={(value) => 
              setMaxDuration(value === 'null' ? null : parseInt(value))
            }>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {DURATION_OPTIONS.map((option) => (
                  <SelectItem 
                    key={option.value || 'null'} 
                    value={option.value?.toString() || 'null'}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex gap-2">
              <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-blue-800">
                <p className="font-medium mb-1">¿Por qué necesito autorización?</p>
                <p>Los cambios de estado requieren aprobación del supervisor para mantener la cobertura adecuada del equipo.</p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancelar
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!reason.trim() || isSubmitting}
          >
            {isSubmitting ? 'Enviando...' : 'Enviar Solicitud'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}