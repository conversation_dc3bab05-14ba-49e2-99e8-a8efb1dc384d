'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  User, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar, 
  MessageSquare, 
  ArrowUpRight, 
  FileText, 
  Tag, 
  Clock,
  Users,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { useConversation, useConversationActions } from '@/hooks/useConversations';
import { useAuth } from '@/contexts/AuthContext';

interface CustomerPanelProps {
  conversationId: string | null;
}

// Mock data - will be replaced with API calls
const mockCustomerData = {
  '1': {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+1234567890',
    avatar: null,
    status: 'active',
    location: 'Madrid, España',
    joinDate: '2023-06-15',
    totalConversations: 12,
    tags: ['VIP', 'Billing Support'],
    notes: [
      {
        id: '1',
        content: 'Cliente VIP con historial de facturación complejo. Siempre muy cortés.',
        author: 'Agent Smith',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      },
      {
        id: '2',
        content: 'Prefiere comunicación por WhatsApp. Disponible de 9-18h.',
        author: 'Agent Johnson',
        timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      },
    ],
    recentActivity: [
      {
        id: '1',
        type: 'conversation',
        description: 'Started conversation about billing',
        timestamp: new Date(Date.now() - 10 * 60 * 1000),
      },
      {
        id: '2',
        type: 'note',
        description: 'Note added by Agent Smith',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      },
    ],
  },
  '2': {
    id: '2',
    name: 'Carlos Rodríguez',
    email: '<EMAIL>',
    phone: '+1234567891',
    avatar: null,
    status: 'resolved',
    location: 'Barcelona, España',
    joinDate: '2023-08-22',
    totalConversations: 5,
    tags: ['Technical Support'],
    notes: [],
    recentActivity: [
      {
        id: '1',
        type: 'conversation',
        description: 'Technical issue resolved',
        timestamp: new Date(Date.now() - 15 * 60 * 1000),
      },
    ],
  },
  '3': {
    id: '3',
    name: 'Ana Silva',
    email: '<EMAIL>',
    phone: '+1234567892',
    avatar: null,
    status: 'waiting',
    location: 'Valencia, España',
    joinDate: '2024-01-10',
    totalConversations: 2,
    tags: ['New Customer'],
    notes: [],
    recentActivity: [
      {
        id: '1',
        type: 'conversation',
        description: 'First time contacting support',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
      },
    ],
  },
} as const;


export function CustomerPanel({ conversationId }: CustomerPanelProps) {
  const [newNote, setNewNote] = useState('');
  const { user } = useAuth();
  
  // Get conversation data for basic customer info (name, phone)
  const { conversation, loading: conversationLoading, error: conversationError } = useConversation(conversationId);
  const { addNote, loading: actionLoading } = useConversationActions();
  
  // Mock customer data for CRM-related info (notes, tags, history, etc.)
  // TODO: Replace with actual CRM service calls when available
  const mockCrmData = conversationId ? mockCustomerData[conversationId as keyof typeof mockCustomerData] : null;
  
  // Combine real conversation data with mock CRM data
  const customerData = conversation ? {
    ...mockCrmData,
    id: conversation.id,
    name: conversation.customer?.name || conversation.metadata?.customerName || 'Unknown Customer',
    email: conversation.customer?.email || conversation.metadata?.customerEmail || 'No email',
    phone: conversation.customer?.phone || conversation.metadata?.customerPhone || 'No phone',
    status: conversation.status,
    // ✅ Provide fallback values for missing mock data
    location: mockCrmData?.location || 'Unknown location',
    joinDate: mockCrmData?.joinDate || '2024-01-01',
    totalConversations: mockCrmData?.totalConversations || conversation.customer?.totalConversations || 1,
    tags: mockCrmData?.tags || conversation.metadata?.tags || [],
    notes: mockCrmData?.notes || [],
    recentActivity: mockCrmData?.recentActivity || []
  } : null;

  // Loading state
  if (conversationLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-white border-l border-gray-200">
        <div className="text-center">
          <Loader2 className="h-8 w-8 text-gray-400 animate-spin mx-auto mb-4" />
          <p className="text-gray-500">Loading customer info...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (conversationError) {
    return (
      <div className="h-full flex items-center justify-center bg-white border-l border-gray-200 p-6">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load customer info: {conversationError.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Empty state
  if (!conversationId || !customerData) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 border-l border-gray-200">
        <div className="text-center p-6">
          <User className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No customer selected</h3>
          <p className="text-gray-500">Select a conversation to view customer information</p>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'Now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const handleAddNote = async () => {
    if (newNote.trim() && conversationId && user) {
      try {
        const result = await addNote(conversationId, {
          content: newNote.trim(),
          authorId: user.id,
          authorName: user.name,
          type: 'agent_note'
        });

        if (result.success) {
          setNewNote('');
        } else {
          console.error('Failed to add note:', result.error);
        }
      } catch (error) {
        console.error('Error adding note:', error);
      }
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'conversation': return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'note': return <FileText className="h-4 w-4 text-green-500" />;
      case 'transfer': return <ArrowUpRight className="h-4 w-4 text-orange-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="h-full bg-white border-l border-gray-200">
      <ScrollArea className="h-full">
        <div className="p-4 space-y-6">
          {/* Customer Header */}
          <div className="text-center">
            <Avatar className="h-20 w-20 mx-auto mb-4">
              <AvatarFallback className="text-lg">
                {customerData.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <h2 className="text-xl font-semibold text-gray-900 mb-1">
              {customerData.name}
            </h2>
            <Badge 
              variant={customerData.status === 'active' ? 'default' : 'secondary'}
              className="capitalize"
            >
              {customerData.status}
            </Badge>
          </div>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center">
                <User className="h-4 w-4 mr-2" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">{customerData.email}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">{customerData.phone}</span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">{customerData.location}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  Customer since {formatDate(customerData.joinDate)}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Customer Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center">
                <Users className="h-4 w-4 mr-2" />
                Customer Stats
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">{customerData.totalConversations}</p>
                  <p className="text-xs text-gray-500">Total Conversations</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-900">{customerData.notes?.length || 0}</p>
                  <p className="text-xs text-gray-500">Notes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                Tags
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {customerData.tags?.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                )) || <p className="text-sm text-gray-500">No tags</p>}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Notes
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add Note */}
              <div className="space-y-2">
                <Textarea
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  placeholder="Add a note about this customer..."
                  className="min-h-[80px]"
                />
                <Button 
                  onClick={handleAddNote} 
                  disabled={!newNote.trim() || actionLoading.addNote}
                  size="sm"
                  className="w-full"
                >
                  {actionLoading.addNote ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Adding...
                    </>
                  ) : (
                    'Add Note'
                  )}
                </Button>
              </div>

              <Separator />

              {/* Existing Notes */}
              <div className="space-y-3">
                {(customerData.notes?.length || 0) === 0 ? (
                  <p className="text-sm text-gray-500 text-center py-4">
                    No notes yet
                  </p>
                ) : (
                  customerData.notes?.map((note) => (
                    <div key={note.id} className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-900 mb-2">{note.content}</p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{note.author}</span>
                        <span>{formatTimestamp(note.timestamp)}</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {customerData.recentActivity?.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    {getActivityIcon(activity.type)}
                    <div className="flex-1">
                      <p className="text-sm text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">{formatTimestamp(activity.timestamp)}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm font-medium">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <ArrowUpRight className="h-4 w-4 mr-2" />
                Transfer Conversation
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Users className="h-4 w-4 mr-2" />
                Request Supervisor
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <FileText className="h-4 w-4 mr-2" />
                View Full History
              </Button>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </div>
  );
}
