'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Send, 
  Paperclip, 
  Smile, 
  MoreVertical, 
  Clock, 
  CheckCheck,
  MessageSquare,
  AlertCircle,
  Loader2,
  ArrowRightLeft,
  Eye,
  AlertTriangle,
  CheckCircle,
  History,
  StickyNote,
  Timer,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useConversation, useConversationActions } from '@/hooks/useConversations';
import { useMessageMutations } from '@/hooks/useMessageMutations';
import { useHybridMessages } from '@/hooks/useHybridConversations';
import { useTypingIndicators } from '@/hooks/useFirebaseRealtime';
import { useAdaptivePolling } from '@/hooks/useAdaptivePolling';
import { useAuth } from '@/contexts/AuthContext';

interface ChatAreaProps {
  conversationId: string | null;
  onConversationSelect?: (conversationId: string | null) => void;
}

export function ChatArea({ conversationId, onConversationSelect }: ChatAreaProps) {
  const [message, setMessage] = useState('');
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [closeReason, setCloseReason] = useState('');
  const [isClosing, setIsClosing] = useState(false);

  // Status color function (same as ConversationList)
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'transferring': return 'bg-blue-100 text-blue-800';
      case 'pending_acceptance': return 'bg-orange-100 text-orange-800';
      case 'supervised': return 'bg-purple-100 text-purple-800';
      case 'escalated': return 'bg-red-100 text-red-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Transfer modal states
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [transferToAgentId, setTransferToAgentId] = useState('');
  const [transferNote, setTransferNote] = useState('');
  const [availableAgents, setAvailableAgents] = useState<any[]>([]);
  const [isTransferring, setIsTransferring] = useState(false);
  const [loadingAgents, setLoadingAgents] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  
  // Get adaptive polling hooks for activity tracking
  const { setTyping, trackActivity } = useAdaptivePolling(conversationId);
  
  // API hooks - using hybrid for messages (real-time when available)
  const { 
    messages, 
    loading: messagesLoading, 
    error: messagesError,
    isRealtime 
  } = useHybridMessages(conversationId);
  const { conversation, loading: conversationLoading, error: conversationError } = useConversation(conversationId);
  const { transferConversation, acceptTransfer, rejectTransfer, cancelTransfer, closeConversation } = useConversationActions();
  const { sendMessage, isLoading: isSendingMessage } = useMessageMutations();
  
  // Typing indicators (MVP - web chat only)
  const { typingUsers, startTyping, stopTyping } = useTypingIndicators(conversationId);

  // Smart auto-scroll function
  const autoScrollIfNeeded = useCallback((reason: string) => {
    const container = scrollAreaRef.current;
    if (!container) return;
    
    const { scrollTop, scrollHeight, clientHeight } = container;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    
    // Only scroll if user is near bottom AND reason is valid
    const validReasons = ['new_optimistic_message', 'new_real_message', 'conversation_loaded'];
    
    if (isNearBottom && validReasons.includes(reason)) {
      // Remove console.log to prevent re-render loops
      setTimeout(() => {
        container.scrollTo({
          top: container.scrollHeight,
          behavior: 'smooth'
        });
      }, 50);
    }
  }, []);

  // Auto-scroll is handled in handleSendMessage for optimistic messages
  // No need for auto-scroll on messages.length change to prevent loops

  // Initial scroll when conversation loads
  useEffect(() => {
    if (messages.length > 0) {
      autoScrollIfNeeded('conversation_loaded');
    }
  }, [conversationId, messages.length, autoScrollIfNeeded]);

  // Cleanup typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Check conversation states
  const isConversationClosed = conversation?.status === 'closed';
  const isPendingAcceptance = conversation?.status === 'pending_acceptance';
  
  // Check if current user is the one who initiated a pending transfer
  const isTransferInitiator = conversation?.status === 'pending_acceptance' && 
    (conversation as any)?.assignedAgentId === user?.id &&
    (conversation as any)?.transferInfo?.currentTransfer;

  // Send message with optimistic UI
  const handleSendMessage = () => {
    if (!message.trim() || !conversationId || !user || isConversationClosed || isPendingAcceptance) return;
    
    // Track activity for adaptive polling
    trackActivity();
    setTyping(false);
    
    const messageContent = message.trim();
    setMessage(''); // Clear input immediately for better UX
    
    // Send message with optimistic updates
    sendMessage({
      conversationId,
      messageData: {
        content: messageContent,
        type: 'text',
        senderId: user.id,
        senderType: 'agent',
        senderName: user.name,
        metadata: {}
      }
    });
  };

  // Load available agents when transfer modal opens
  const loadAvailableAgents = async () => {
    if (!conversationId) return;
    
    setLoadingAgents(true);
    try {
      // Get real agents from API
      const response = await fetch('/api/agents');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error?.message || 'Failed to fetch agents');
      }
      
      // Transform API response to match UI format
      const agents = data.data.agents
        .filter((agent: any) => agent.agentId !== user?.id) // Filter out current user
        .filter((agent: any) => agent.status.isAvailable) // Only available agents
        .map((agent: any) => ({
          id: agent.agentId,
          name: agent.name || `Agent ${agent.agentId.slice(-4)}`, // Fallback name if not found
          status: agent.status.availability,
          department: agent.departments[0] || 'general'
        }));
      
      setAvailableAgents(agents);
    } catch (error) {
      console.error('Error loading agents:', error);
      setAvailableAgents([]);
    } finally {
      setLoadingAgents(false);
    }
  };

  // Conversation actions handlers
  const handleTransferConversation = () => {
    setShowTransferModal(true);
    loadAvailableAgents();
  };

  // Execute the transfer
  const executeTransfer = async () => {
    if (!conversationId || !transferToAgentId) return;
    
    setIsTransferring(true);
    try {
      const result = await transferConversation(conversationId, {
        targetAgentId: transferToAgentId,
        reason: transferNote.trim() || 'Manual transfer'
      });
      
      if (result.success) {
        // Close modal and reset states
        setShowTransferModal(false);
        setTransferToAgentId('');
        setTransferNote('');
        
        // Redirect to conversation list after successful transfer
        onConversationSelect?.(null);
      } else {
        console.error('❌ Transfer failed:', result.error);
        alert(`Error al transferir: ${result.error}`);
      }
    } catch (error) {
      console.error('❌ Transfer error:', error);
      alert('Error al transferir la conversación');
    } finally {
      setIsTransferring(false);
    }
  };

  const handleRequestSupervision = () => {
    // TODO: Implement supervision logic
  };

  const handleEscalateConversation = () => {
    // TODO: Implement escalation logic
  };

  const handleCloseConversation = () => {
    setShowCloseModal(true);
  };

  const handleViewHistory = () => {
    // TODO: Implement history view logic
  };

  const handleInternalNotes = () => {
    // TODO: Implement internal notes logic
  };

  const handleSetReminder = () => {
    // TODO: Implement reminder logic
  };

  const handleConfirmClose = async () => {
    if (!conversationId || !closeReason || !user) return;

    setIsClosing(true);
    try {

      // Use existing hook method instead of manual fetch
      const result = await closeConversation(conversationId, closeReason);
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to close conversation');
      }
      
      // Reset modal state
      setShowCloseModal(false);
      setCloseReason('');
      
    } catch (error) {
      console.error('Failed to close conversation:', error);
      // TODO: Show error toast to user
      alert('Error al finalizar la conversación. Por favor, intenta de nuevo.');
    } finally {
      setIsClosing(false);
    }
  };

  const handleCancelClose = () => {
    setShowCloseModal(false);
    setCloseReason('');
  };

  // Typing indicator logic
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setMessage(newValue);
    
    // Track activity for adaptive polling
    trackActivity();
    setTyping(true);
    
    // Handle typing indicators (only if real-time available and user exists)
    if (isRealtime && user?.id) {
      // Start typing
      startTyping(user.id);
      
      // Clear previous timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      // Stop typing after 3 seconds of inactivity
      typingTimeoutRef.current = setTimeout(() => {
        stopTyping(user.id);
        setTyping(false); // Also stop adaptive polling typing indicator
      }, 3000);
    } else {
      // For non-realtime mode, still manage adaptive polling typing indicator
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      
      typingTimeoutRef.current = setTimeout(() => {
        setTyping(false);
      }, 3000);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      
      // Stop typing when sending message
      if (isRealtime && user?.id) {
        stopTyping(user.id);
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
        }
      }
      
      handleSendMessage();
    }
  };

  // Transfer handlers using existing hook methods
  const handleAcceptTransfer = async () => {
    if (!conversationId || !user?.id) return;
    
    try {
      const result = await acceptTransfer(conversationId, user.id);
      if (!result.success) {
        alert('Error accepting transfer: ' + result.error);
      }
    } catch (error) {
      console.error('Error accepting transfer:', error);
      alert('Error accepting transfer. Please try again.');
    }
  };

  const handleRejectTransfer = async () => {
    if (!conversationId || !user?.id) return;
    
    try {
      const result = await rejectTransfer(conversationId, user.id, 'Transfer declined by agent');
      if (!result.success) {
        alert('Error rejecting transfer: ' + result.error);
      }
    } catch (error) {
      console.error('Error rejecting transfer:', error);
      alert('Error rejecting transfer. Please try again.');
    }
  };

  const handleCancelTransfer = async () => {
    if (!conversationId || !user?.id) return;
    
    try {
      const result = await cancelTransfer(conversationId, user.id, 'Transfer cancelled by originating agent');
      
      if (result.success) {
        console.log('✅ Transfer cancelled successfully');
      } else {
        console.error('❌ Cancel transfer failed:', result.error);
        alert(`Error cancelling transfer: ${result.error}`);
      }
    } catch (error) {
      console.error('Error cancelling transfer:', error);
      alert('Error cancelling transfer. Please try again.');
    }
  };

  const formatTimestamp = (date: Date | string | undefined) => {
    if (!date) return '--:--';
    
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    if (isNaN(dateObj.getTime())) {
      return '--:--';
    }
    
    return dateObj.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sending': return <Loader2 className="h-3 w-3 text-gray-400 animate-spin" />;
      case 'sent': return <Clock className="h-3 w-3 text-gray-400" />;
      case 'delivered': return <CheckCheck className="h-3 w-3 text-gray-400" />;
      case 'read': return <CheckCheck className="h-3 w-3 text-blue-500" />;
      case 'failed': return <AlertCircle className="h-3 w-3 text-red-500" />;
      default: return <Clock className="h-3 w-3 text-gray-400" />;
    }
  };

  // Debug info commented out to prevent render loops
  // console.log('🔍 ChatArea State:', {
  //   conversationId,
  //   messages: messages.length,
  //   loading: messagesLoading
  // });

  if (!conversationId) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <MessageSquare className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No conversation selected</h3>
          <p className="text-gray-500">Choose a conversation from the list to start chatting</p>
        </div>
      </div>
    );
  }

  if (conversationLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 text-gray-400 animate-spin mx-auto mb-4" />
          <p className="text-gray-500">Loading conversation...</p>
        </div>
      </div>
    );
  }

  if (conversationError) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50 p-6">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load conversation: {conversationError.message}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Chat Header */}
      {conversation && (
        <div className="px-6 py-4 border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="text-sm">
                  {conversation.customer?.name ? 
                    conversation.customer.name.split(' ').map(n => n[0]).join('') :
                    conversation.metadata?.customerName?.split(' ').map(n => n[0]).join('') || '??'
                  }
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h2 className="text-lg font-semibold text-gray-900">
                  {conversation.customer?.name || conversation.metadata?.customerName || 'Unknown Customer'}
                </h2>
                <p className="text-sm text-gray-500">
                  {conversation.customer?.phone || conversation.metadata?.customerPhone || 'No phone'}
                </p>
                
                {/* Debug IDs */}
                <div className="flex flex-col gap-1 mt-2">
                  <span className="text-[10px] font-mono text-gray-400 bg-gray-100 px-2 py-1 rounded">
                    Conv: {conversation.id}
                  </span>
                  {conversation.agentId && (
                    <span className="text-[10px] font-mono text-blue-600 bg-blue-50 px-2 py-1 rounded">
                      Agent: {conversation.agentId}
                    </span>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="secondary"
                className={`capitalize text-xs font-medium px-2 py-1 ${getStatusColor(conversation.status)}`}
              >
                {conversation.status}
              </Badge>
              {isRealtime && (
                <Badge variant="secondary" className="text-xs">
                  Live
                </Badge>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" disabled={isConversationClosed}>
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleTransferConversation}>
                    <ArrowRightLeft className="h-4 w-4 mr-2" />
                    Transferir conversación
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleRequestSupervision}>
                    <Eye className="h-4 w-4 mr-2" />
                    Solicitar supervisión
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEscalateConversation}>
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Escalar conversación
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={handleCloseConversation}
                    className="text-green-600 focus:text-green-600"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Finalizar conversación
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleViewHistory}>
                    <History className="h-4 w-4 mr-2" />
                    Ver historial completo
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleInternalNotes}>
                    <StickyNote className="h-4 w-4 mr-2" />
                    Notas internas
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSetReminder}>
                    <Timer className="h-4 w-4 mr-2" />
                    Configurar recordatorios
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-6" ref={scrollAreaRef}>
        <div className="py-4 space-y-4">
          
          {/* Transfer Waiting UI - For agent who initiated the transfer */}
          {isTransferInitiator && (
            <div className="mb-6 p-4 border border-blue-200 bg-blue-50 rounded-lg">
              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-blue-900 mb-1">
                    Transfer Pending
                  </h3>
                  <p className="text-sm text-blue-800 mb-3">
                    You have initiated a transfer of this conversation. Waiting for the receiving agent to accept.
                    {(conversation as any).transferInfo.currentTransfer.reason && (
                      <>
                        <br />
                        <span className="font-medium">Reason:</span> {(conversation as any).transferInfo.currentTransfer.reason}
                      </>
                    )}
                  </p>
                  <div className="flex space-x-3">
                    <Button
                      onClick={handleCancelTransfer}
                      size="sm"
                      variant="outline"
                      className="border-red-300 text-red-700 hover:bg-red-50"
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel Transfer
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Transfer Acceptance UI - For receiving agent */}
          {isPendingAcceptance && !isTransferInitiator && (conversation as any)?.transferInfo?.currentTransfer && (
            <div className="mb-6 p-4 border border-orange-200 bg-orange-50 rounded-lg">
              <div className="flex items-start space-x-3">
                <ArrowRightLeft className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-orange-900 mb-1">
                    Transfer Pending Acceptance
                  </h3>
                  <p className="text-sm text-orange-800 mb-3">
                    This conversation has been transferred to you from another agent.
                    {(conversation as any).transferInfo.currentTransfer.reason && (
                      <>
                        <br />
                        <span className="font-medium">Reason:</span> {(conversation as any).transferInfo.currentTransfer.reason}
                      </>
                    )}
                  </p>
                  <div className="flex space-x-3">
                    <Button
                      onClick={handleAcceptTransfer}
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Accept Transfer
                    </Button>
                    <Button
                      onClick={handleRejectTransfer}
                      variant="outline"
                      size="sm"
                      className="border-orange-200 hover:bg-orange-100"
                    >
                      Decline
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
          {messagesLoading && messages.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 text-gray-400 animate-spin mr-2" />
              <span className="text-gray-500">Loading messages...</span>
            </div>
          ) : messagesError && messages.length === 0 ? (
            <div className="py-8">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Failed to load messages: {messagesError.message}
                </AlertDescription>
              </Alert>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <p className="text-gray-500">No messages yet</p>
            </div>
          ) : (
            messages.map((msg) => {
              const isOptimistic = (msg as any).isOptimistic;
              
              // System messages
              if (msg.senderType === 'system') {
                return (
                  <div key={msg.id} className="flex justify-center my-2">
                    <div className="max-w-[80%] text-center">
                      <div className="inline-block bg-gray-50 text-gray-600 text-xs px-3 py-1.5 rounded-full border border-gray-200">
                        {msg.content}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {formatTimestamp(msg.timestamp)}
                      </div>
                    </div>
                  </div>
                );
              }

              // Regular messages
              return (
                <div
                  key={msg.id}
                  className={cn(
                    "flex transition-opacity duration-200",
                    msg.senderType === 'customer' ? "justify-start" : "justify-end",
                    isOptimistic && (msg as any).status === 'failed' ? "opacity-50" : "opacity-100"
                  )}
                >
                  <div
                    className={cn(
                      "max-w-[70%] rounded-lg px-4 py-2",
                      msg.senderType === 'customer'
                        ? "bg-gray-100 text-gray-900"
                        : isOptimistic && (msg as any).status === 'failed'
                        ? "bg-red-100 text-red-900 border border-red-200"
                        : "bg-blue-600 text-white"
                    )}
                  >
                    <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                    <div className={cn(
                      "flex items-center justify-end space-x-1 mt-1",
                      msg.senderType === 'customer' 
                        ? "text-gray-500" 
                        : isOptimistic && (msg as any).status === 'failed'
                        ? "text-red-600"
                        : "text-blue-100"
                    )}>
                      <span className="text-xs">{formatTimestamp(msg.timestamp)}</span>
                      {msg.senderType !== 'customer' && (
                        <>
                          {isOptimistic && (msg as any).status ? 
                            getStatusIcon((msg as any).status) :
                            getStatusIcon('delivered')
                          }
                          {isOptimistic && (msg as any).status === 'failed' && (
                            <span className="text-xs ml-1">Failed</span>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>

      {/* Conversation Closed Notice */}
      {isConversationClosed && (
        <div className="px-6 py-3 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span className="font-medium">Conversación finalizada</span>
            <span>•</span>
            <span>Esta conversación ha sido cerrada y ya no se pueden enviar mensajes</span>
          </div>
        </div>
      )}

      {/* Typing Indicators */}
      {isRealtime && typingUsers.length > 0 && !isConversationClosed && (
        <div className="px-6 py-2 border-t border-gray-100 bg-gray-50">
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0ms'}}></div>
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '150ms'}}></div>
              <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '300ms'}}></div>
            </div>
            <span>
              {typingUsers.length === 1 
                ? "Customer is typing..." 
                : `${typingUsers.length} people are typing...`}
            </span>
          </div>
        </div>
      )}

      {/* Message Input */}
      <div className={cn(
        "p-4 border-t border-gray-200",
        isConversationClosed ? "bg-gray-50" : "bg-white"
      )}>
        {isConversationClosed ? (
          /* Closed Conversation Input - Read Only */
          <div className="flex items-center justify-center py-4">
            <div className="flex items-center space-x-2 text-gray-500">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium">Esta conversación ha sido finalizada</span>
            </div>
          </div>
        ) : (
          /* Active Conversation Input */
          <div className="flex items-end space-x-2">
            <Button variant="ghost" size="sm">
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm">
              <Smile className="h-4 w-4" />
            </Button>
            
            <div className="flex-1">
              <Textarea
                value={message}
                onChange={handleInputChange}
                onKeyDown={handleKeyPress}
                placeholder={
                  isPendingAcceptance && !isTransferInitiator 
                    ? "Accept the transfer to start chatting..." 
                    : isTransferInitiator
                    ? "Transfer pending - cancel to resume chatting..."
                    : "Type a message..."
                }
                className="min-h-[40px] max-h-[120px] resize-none"
                rows={1}
                disabled={isConversationClosed || isPendingAcceptance}
              />
            </div>
            
            <Button 
              onClick={handleSendMessage} 
              disabled={!message.trim() || isSendingMessage || isConversationClosed || isPendingAcceptance}
              className="h-10 w-10 p-0"
            >
              {isSendingMessage ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Close Conversation Modal */}
      <Dialog open={showCloseModal} onOpenChange={setShowCloseModal}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Finalizar Conversación</DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            <p className="text-sm text-gray-600 mb-4">
              ¿Estás seguro de finalizar esta conversación con{' '}
              <strong>
                {conversation?.customer?.name || conversation?.metadata?.customerName || 'el cliente'}
              </strong>?
            </p>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Razón de cierre *
                </label>
                <Select value={closeReason} onValueChange={setCloseReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona una razón" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="resolved">✅ Problema resuelto</SelectItem>
                    <SelectItem value="no_response">📞 Cliente no responde</SelectItem>
                    <SelectItem value="cancelled">🚫 Cliente canceló</SelectItem>
                    <SelectItem value="info_provided">📋 Información proporcionada</SelectItem>
                    <SelectItem value="other">⚠️ Otro motivo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={handleCancelClose}
              disabled={isClosing}
            >
              Cancelar
            </Button>
            <Button 
              onClick={handleConfirmClose}
              disabled={!closeReason || isClosing}
              className="bg-green-600 hover:bg-green-700"
            >
              {isClosing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Finalizando...
                </>
              ) : (
                'Finalizar'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Transfer Conversation Modal */}
      <Dialog open={showTransferModal} onOpenChange={setShowTransferModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Transferir Conversación</DialogTitle>
          </DialogHeader>
          
          <div className="py-4 space-y-4">
            <p className="text-sm text-gray-600">
              Transferir conversación con{' '}
              <strong>
                {conversation?.customer?.name || conversation?.metadata?.customerName || 'el cliente'}
              </strong>{' '}
              a otro agente.
            </p>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Agente destino *
                </label>
                {loadingAgents ? (
                  <div className="flex items-center space-x-2 p-3 border rounded-md">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-gray-500">Cargando agentes...</span>
                  </div>
                ) : (
                  <Select value={transferToAgentId} onValueChange={setTransferToAgentId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona un agente" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableAgents.length === 0 ? (
                        <div className="p-2 text-sm text-gray-500">
                          No hay agentes disponibles
                        </div>
                      ) : (
                        availableAgents.map((agent) => (
                          <SelectItem key={agent.id} value={agent.id}>
                            <div className="flex items-center space-x-2">
                              <div className={cn(
                                "w-2 h-2 rounded-full",
                                agent.status === 'available' ? 'bg-green-500' :
                                agent.status === 'busy' ? 'bg-yellow-500' : 'bg-gray-400'
                              )} />
                              <span>{agent.name}</span>
                              <Badge variant="secondary" className="text-xs">
                                {agent.department?.replace('_', ' ') || 'General'}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                )}
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Nota de transferencia (opcional)
                </label>
                <Textarea
                  placeholder="Incluye información relevante para el agente receptor..."
                  value={transferNote}
                  onChange={(e) => setTransferNote(e.target.value)}
                  className="min-h-[80px] resize-none"
                  maxLength={500}
                />
                <div className="text-xs text-gray-500 text-right mt-1">
                  {transferNote.length}/500
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowTransferModal(false)}
              disabled={isTransferring}
            >
              Cancelar
            </Button>
            <Button 
              onClick={executeTransfer}
              disabled={!transferToAgentId || isTransferring || availableAgents.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isTransferring ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Transfiriendo...
                </>
              ) : (
                'Transferir'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}