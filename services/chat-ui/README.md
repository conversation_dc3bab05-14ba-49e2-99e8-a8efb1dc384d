# Chat UI - CX System Frontend

Next.js-based user interface for the Customer Experience (CX) System, providing real-time chat capabilities for agents, supervisors, and administrators.

## 🚀 Features

### ✅ Production-Ready Components
- **Agent Interface**: Complete conversation management with dropdown actions, real-time messaging, customer panels
- **Conversation Management**: Full lifecycle management with close functionality, read-only states, system messages
- **Supervisor Interface**: Queue management, agent oversight, conversation monitoring  
- **Admin Interface**: System configuration, user management, analytics dashboard

### 🔥 Firebase Real-time Hybrid System
- **Smart Real-time Updates**: Firebase WebSocket for instant updates when available
- **Automatic Fallback**: Seamless switch to REST API polling when WebSocket fails
- **Connection Indicators**: Visual feedback showing actual data flow status
- **Zero Breaking Changes**: All existing APIs maintained for backward compatibility

### 💬 Advanced Chat Features
- **Optimistic UI**: Instant message display with smart conflict resolution
- **Conversation Actions**: Complete dropdown menu (⋮) with 7 actions: Transfer, Supervision, Escalation, Close, History, Notes, Reminders
- **Close Functionality**: Modal-based conversation closing with localized reasons, automatic system messages
- **Read-Only States**: Automatic UI disabling for closed conversations with visual feedback
- **Typing Indicators**: Real-time "user is typing" feedback (web chat)
- **Message States**: Sent, delivered, read, failed status tracking with visual icons
- **Auto-scroll**: Intelligent conversation scrolling without interruption
- **Dynamic State Management**: Seamless transitions between active and closed conversation states

### 🔧 Technical Architecture
- **Hybrid Data Flow**: Real-time → Polling fallback → Real-time recovery
- **Connection Management**: Exponential backoff reconnection (1s-30s)
- **State Management**: Clean separation of real-time vs polling states  
- **Error Resilience**: Graceful degradation without data loss

## 🏗️ Architecture

```
┌─────────────────┐    WebSocket     ┌──────────────────┐
│   Chat UI       │ ←──────────────→ │ Firebase Realtime│
│                 │                  │ Database         │
└─────────────────┘                  └──────────────────┘
         │                                    │
         │ Fallback                          │
         ▼                                    ▼  
┌─────────────────┐    REST API      ┌──────────────────┐
│ SWR Hooks       │ ←──────────────→ │ Chat Realtime    │
│ (Polling)       │                  │ Service (3003)   │
└─────────────────┘                  └──────────────────┘
```

## 🚦 Connection States

| State | Indicator | Description |
|-------|-----------|-------------|
| **Real-time** | 🟢 Green | WebSocket active, instant updates |
| **Syncing** | 🟡 Yellow | Polling mode, 5-30s updates |
| **Offline** | 🔴 Red | No connection, cached data only |

## 🛠️ Development

### Prerequisites
- Node.js 18+
- Firebase Emulator Suite
- Chat Realtime Service (port 3003)
- Supabase Auth configured

### Quick Start
```bash
# Install dependencies
npm install

# Start development server
npm run dev -- --port 3007

# Open browser
http://localhost:3007
```

### Environment Variables
```bash
# Supabase Authentication
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Google Cloud Project (for Firebase namespace)
GOOGLE_CLOUD_PROJECT_ID=cx-system-469120
```

### Firebase Real-time Setup
The system automatically connects to Firebase Realtime Database emulator:
- **Database URL**: Constructed from `GOOGLE_CLOUD_PROJECT_ID`
- **Namespace**: `{projectId}-default-rtdb` format
- **Emulator**: `127.0.0.1:9000` (development)
- **Fallback**: REST API when WebSocket unavailable

## 🧪 Testing Real-time Features

### Test Connection States
```bash
# Start Firebase emulator
./scripts/start-all-emulators.sh

# Start services
cd services/chat-realtime && npm run dev &
cd services/chat-ui && npm run dev -- --port 3007

# Test fallback: Kill Firebase emulator
# UI should automatically switch to "Syncing" mode
```

### Debug Tools
Access debug tools in browser console:
```javascript
window.cxDebug.clearCache()        // Clear cached data
window.cxDebug.showCacheState()    // Inspect cache
window.cxDebug.forceSync()         // Force data sync
window.cxDebug.simulateOffline()   // Test offline mode
```

## 📁 Project Structure

```
src/
├── components/           # UI Components
│   ├── agent/           # Agent interface components
│   ├── supervisor/      # Supervisor interface components  
│   ├── auth/           # Authentication components
│   └── ui/             # Shared UI components
├── hooks/               # React Hooks
│   ├── useConversations.ts      # SWR-based hooks
│   ├── useFirebaseRealtime.ts   # Real-time hooks
│   └── useHybridConversations.ts # Hybrid switching logic
├── services/            # API Services
│   ├── api.ts          # REST API client
│   ├── firebaseRealtime.ts # WebSocket manager
│   └── supabase.ts     # Authentication
├── types/              # TypeScript definitions
└── utils/              # Utilities and helpers
```

## 🔥 Firebase Real-time Implementation

### Connection Manager
- **Auto-initialization**: Connects on app start
- **Health monitoring**: Continuous connection status tracking  
- **Smart reconnection**: Exponential backoff with max 30s intervals
- **Cleanup**: Proper resource management on unmount

### Real-time Hooks
- `useRealtimeMessages()` - Live message updates
- `useRealtimeConversations()` - Live conversation list
- `useRealtimeAgentStatus()` - Agent availability status
- `useConnectionState()` - Connection monitoring
- `useTypingIndicators()` - Typing feedback (web chat)

### Hybrid Hooks (Production)
- `useHybridMessages()` - Smart message loading
- `useHybridConversations()` - Smart conversation management  
- Automatic switching between real-time ↔ polling
- Transparent for existing components

## 🎯 Performance

### Optimizations
- **Smart Caching**: SWR-based with intelligent invalidation
- **Optimistic Updates**: Instant UI feedback with conflict resolution
- **Connection Pooling**: Reuse Firebase connections
- **Data Filtering**: Client-side filtering for better UX

### Metrics
- **Real-time Latency**: < 100ms (WebSocket)
- **Fallback Latency**: 5-30s (configurable polling)
- **Reconnection Time**: 1-30s (exponential backoff)
- **Memory Usage**: Optimized cleanup and subscription management

## 🔒 Security

- **Authentication**: Supabase Auth with JWT tokens
- **Authorization**: Role-based access (agent/supervisor/admin)
- **Connection Security**: WSS/HTTPS only in production
- **Data Validation**: Client-side and server-side validation

## 📊 Monitoring

### Connection Quality Indicators
- Real-time connection uptime
- Fallback frequency metrics
- Reconnection attempt tracking
- Data sync latency monitoring

### User Experience Metrics
- Message delivery confirmation
- UI responsiveness tracking
- Error frequency monitoring
- Typing indicator effectiveness

## 🚀 Deployment

### Production Configuration
```bash
# Firebase Production
GOOGLE_CLOUD_PROJECT_ID=your-production-project
# Firebase will use production URLs automatically

# Supabase Production
NEXT_PUBLIC_SUPABASE_URL=https://your-prod.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-key
```

### Build & Deploy
```bash
npm run build    # TypeScript compilation + Next.js build
npm run start    # Production server

# Vercel deployment
vercel deploy --prod
```

## 📝 Development Notes

### Key Decisions
- **Hybrid Architecture**: Best of both worlds (real-time + reliability)
- **Zero Breaking Changes**: Backward compatibility maintained
- **Progressive Enhancement**: Works without WebSocket, better with it
- **User Transparency**: Automatic switching without user intervention

### Future Optimizations
- WhatsApp typing indicators via Meta Graph API
- WebSocket connection pooling
- Advanced caching strategies  
- Performance monitoring dashboard

## 🤝 Contributing

1. Follow MVP principles - simple, testable code
2. Maintain TypeScript strict mode
3. Test both real-time and fallback modes
4. Update documentation for new features
5. Ensure backward compatibility

---

**Status**: ✅ **Production Ready**  
**Real-time**: ✅ **Fully Implemented**  
**Fallback**: ✅ **Automatic & Reliable**  
**UI/UX**: ✅ **Optimistic & Responsive**