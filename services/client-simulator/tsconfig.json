{"compilerOptions": {"target": "es2020", "lib": ["es2020"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": false, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "outDir": "./dist", "rootDir": "./src", "declaration": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}