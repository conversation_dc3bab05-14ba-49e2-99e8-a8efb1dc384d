export interface IncomingMessage {
  id: string;
  from: string;
  to: string;
  body: string;
  type?: 'text' | 'media' | 'audio' | 'image' | 'video' | 'document';
  channel: 'whatsapp' | 'web' | 'sms' | 'simulation';
  timestamp: Date;
  mediaUrl?: string;
  mediaType?: string;
}

export interface OutgoingMessage {
  id: string;
  to: string;
  body: string;
  channel: 'whatsapp' | 'web' | 'sms' | 'simulation';
  conversationId: string;
  timestamp: Date;
  mediaUrl?: string;
  mediaType?: string;
}

export interface SimulatedConversation {
  id: string;
  customerId: string;
  customerName: string;
  messages: ConversationMessage[];
  isActive: boolean;
  createdAt: Date;
}

export interface ConversationMessage {
  id: string;
  from: 'customer' | 'agent';
  body: string;
  timestamp: Date;
}

export interface CreateConversationRequest {
  customerId: string;
  customerName: string;
  customerPhone: string;
  agentPhone: string;
}