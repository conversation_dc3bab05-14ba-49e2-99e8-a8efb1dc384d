import { Router } from 'express';
import { logger } from './logger';
import { config } from './config';
import { SimulatedConversation, IncomingMessage, OutgoingMessage, CreateConversationRequest, ConversationMessage } from './types';

const router = Router();

// In-memory storage for conversations (simple for testing purposes)
const conversations: Map<string, SimulatedConversation> = new Map();
const messageHistory: Map<string, ConversationMessage[]> = new Map();

// Generate unique IDs
const generateId = () => `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Get all conversations
router.get('/conversations', (req, res) => {
  const conversationsList = Array.from(conversations.values());
  res.json(conversationsList);
});

// Create new conversation
router.post('/conversations', (req, res) => {
  try {
    const { customerId, customerName, customerPhone, agentPhone }: CreateConversationRequest = req.body;

    if (!customerId || !customerName || !customerPhone || !agentPhone) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['customerId', 'customerName', 'customerPhone', 'agentPhone']
      });
    }

    const conversationId = generateId();
    const conversation: SimulatedConversation = {
      id: conversationId,
      customerId,
      customerName,
      messages: [],
      isActive: true,
      createdAt: new Date()
    };

    conversations.set(conversationId, conversation);
    messageHistory.set(conversationId, []);

    logger.info(`Created new simulated conversation: ${conversationId} for customer: ${customerName}`);
    res.json(conversation);
  } catch (error) {
    logger.error('Error creating conversation:', error);
    res.status(500).json({ error: 'Failed to create conversation' });
  }
});

// Get conversation messages
router.get('/conversations/:id/messages', (req, res) => {
  const conversationId = req.params.id;
  const messages = messageHistory.get(conversationId) || [];
  res.json(messages);
});

// Send message to Channel Router (customer → system)
router.post('/conversations/:id/send', async (req, res) => {
  try {
    const conversationId = req.params.id;
    const { message, payloadData } = req.body;

    if (!message || !payloadData) {
      return res.status(400).json({
        error: 'Missing required fields',
        required: ['message', 'payloadData']
      });
    }

    const conversation = conversations.get(conversationId);
    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }

    // Create the message payload for Channel Router
    const incomingMessage: IncomingMessage = {
      id: generateId(),
      from: payloadData.customerPhone || conversation.customerId,
      to: payloadData.agentPhone || 'agent',
      body: message,
      type: 'text',
      channel: 'simulation',
      timestamp: new Date()
    };

    // Add customer message to local history
    const customerMessage: ConversationMessage = {
      id: incomingMessage.id,
      from: 'customer',
      body: message,
      timestamp: new Date()
    };

    const messages = messageHistory.get(conversationId) || [];
    messages.push(customerMessage);
    messageHistory.set(conversationId, messages);

    // Send to Channel Router
    const channelRouterResponse = await fetch(`${config.channelRouterUrl}/route`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ message: incomingMessage })
    });

    const channelRouterResult = await channelRouterResponse.json();

    logger.info(`Sent message to Channel Router for conversation ${conversationId}:`, channelRouterResult);

    res.json({
      success: true,
      messageId: incomingMessage.id,
      channelRouterResponse: channelRouterResult
    });

  } catch (error) {
    logger.error('Error sending message:', error);
    res.status(500).json({ error: 'Failed to send message' });
  }
});

// Webhook endpoint to receive responses from Channel Router
router.post('/webhook/response', (req, res) => {
  try {
    const message: OutgoingMessage = req.body;

    logger.info('Received response from Channel Router:', message);

    // Find conversation by customer phone (message.to should be the customer)
    let targetConversation: SimulatedConversation | null = null;
    for (const conversation of conversations.values()) {
      if (conversation.customerId === message.to) {
        targetConversation = conversation;
        break;
      }
    }

    if (targetConversation) {
      // Add agent response to message history
      const agentMessage: ConversationMessage = {
        id: message.id,
        from: 'agent',
        body: message.body,
        timestamp: new Date()
      };

      const messages = messageHistory.get(targetConversation.id) || [];
      messages.push(agentMessage);
      messageHistory.set(targetConversation.id, messages);

      logger.info(`Added agent response to conversation ${targetConversation.id}`);
    } else {
      logger.warn(`Could not find conversation for customer ${message.to}`);
    }

    // Acknowledge receipt
    res.json({ success: true, processed: true });

  } catch (error) {
    logger.error('Error processing webhook response:', error);
    res.status(500).json({ error: 'Failed to process response' });
  }
});

// Refresh messages for a conversation (get latest)
router.post('/conversations/:id/refresh', (req, res) => {
  const conversationId = req.params.id;
  const messages = messageHistory.get(conversationId) || [];
  
  logger.info(`Refreshed messages for conversation ${conversationId}: ${messages.length} messages`);
  res.json(messages);
});

export { router };