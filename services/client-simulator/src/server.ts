import express from 'express';
import path from 'path';
import { config } from './config';
import { logger } from './logger';
import { router as simulatorRoutes } from './routes';

const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files (HTML, CSS, JS)
app.use(express.static(path.join(__dirname, '../public')));

// API routes
app.use('/api', simulatorRoutes);

// Serve the main interface
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'client-simulator', 
    role: 'CX System client simulation for testing',
    timestamp: new Date().toISOString() 
  });
});

// Error handler
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Unhandled error:', err);
  res.status(500).json({ 
    error: 'Internal server error',
    message: err.message 
  });
});

// Start server
app.listen(config.port, () => {
  logger.info(`Client Simulator started on port ${config.port}`);
  logger.info(`Interface available at: http://localhost:${config.port}`);
  logger.info(`Channel Router URL: ${config.channelRouterUrl}`);
});

export { app };