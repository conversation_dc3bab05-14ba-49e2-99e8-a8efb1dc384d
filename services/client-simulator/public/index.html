<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CX System - Client Simulator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            height: 80vh;
        }

        .sidebar {
            width: 300px;
            border-right: 1px solid #eee;
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            background: #f8f9fa;
        }

        .chat-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }

        .message-input {
            padding: 20px;
            border-top: 1px solid #eee;
            background: white;
        }

        .conversation-list {
            flex: 1;
            margin-bottom: 20px;
        }

        .conversation-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .conversation-item:hover {
            background: #f0f0f0;
        }

        .conversation-item.active {
            background: #007bff;
            color: white;
        }

        .btn {
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .payload-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .message {
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 70%;
            word-wrap: break-word;
        }

        .message.customer {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.agent {
            background: #e9ecef;
            color: #333;
            margin-right: auto;
        }

        .timestamp {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .no-conversation {
            text-align: center;
            color: #666;
            padding: 40px;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 12px;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar: Conversations -->
        <div class="sidebar">
            <h3>Conversaciones</h3>
            <button class="btn" onclick="showNewConversationModal()">Nueva Conversación</button>
            
            <div class="conversation-list" id="conversationList">
                <div class="no-conversation">No hay conversaciones</div>
            </div>

            <button class="btn btn-secondary" onclick="refreshConversations()">Actualizar Lista</button>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h2 id="conversationTitle">Selecciona una conversación</h2>
                <div id="statusArea"></div>
            </div>

            <!-- Chat Messages -->
            <div class="chat-area" id="chatArea">
                <div class="no-conversation">
                    <p>Selecciona una conversación para comenzar</p>
                </div>
            </div>

            <!-- Message Input -->
            <div class="message-input" id="messageInputArea" style="display: none;">
                <!-- Payload Form -->
                <div class="payload-form">
                    <div class="form-group">
                        <label class="form-label">Teléfono Cliente:</label>
                        <input type="text" class="form-control" id="customerPhone" placeholder="+1234567890" value="+1234567890">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Teléfono Agente:</label>
                        <input type="text" class="form-control" id="agentPhone" placeholder="+0987654321" value="+0987654321">
                    </div>
                </div>

                <!-- Message Input -->
                <div style="display: flex; gap: 10px;">
                    <textarea class="form-control" id="messageText" placeholder="Escribe tu mensaje..." rows="3" style="flex: 1;"></textarea>
                    <div style="display: flex; flex-direction: column; gap: 5px;">
                        <button class="btn" onclick="sendMessage()">Enviar</button>
                        <button class="btn btn-secondary" onclick="refreshMessages()">Actualizar</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- New Conversation Modal (Simple) -->
    <div id="newConversationModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; width: 400px;">
            <h3>Nueva Conversación</h3>
            
            <div class="form-group">
                <label class="form-label">ID Cliente:</label>
                <input type="text" class="form-control" id="newCustomerId" placeholder="customer_123">
            </div>
            
            <div class="form-group">
                <label class="form-label">Nombre Cliente:</label>
                <input type="text" class="form-control" id="newCustomerName" placeholder="Juan Pérez">
            </div>
            
            <div class="form-group">
                <label class="form-label">Teléfono Cliente:</label>
                <input type="text" class="form-control" id="newCustomerPhone" placeholder="+1234567890">
            </div>
            
            <div class="form-group">
                <label class="form-label">Teléfono Agente:</label>
                <input type="text" class="form-control" id="newAgentPhone" placeholder="+0987654321">
            </div>

            <div style="display: flex; gap: 10px; margin-top: 20px;">
                <button class="btn" onclick="createConversation()">Crear</button>
                <button class="btn btn-secondary" onclick="closeNewConversationModal()">Cancelar</button>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>