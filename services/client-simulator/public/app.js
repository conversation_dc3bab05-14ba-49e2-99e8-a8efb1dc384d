// Global state
let conversations = [];
let currentConversationId = null;
let currentMessages = [];

// DOM elements
const conversationList = document.getElementById('conversationList');
const chatArea = document.getElementById('chatArea');
const conversationTitle = document.getElementById('conversationTitle');
const messageInputArea = document.getElementById('messageInputArea');
const statusArea = document.getElementById('statusArea');

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    refreshConversations();
});

// Show status message
function showStatus(message, type = 'success') {
    const statusDiv = document.createElement('div');
    statusDiv.className = `status ${type}`;
    statusDiv.textContent = message;
    statusArea.innerHTML = '';
    statusArea.appendChild(statusDiv);
    
    setTimeout(() => {
        statusArea.innerHTML = '';
    }, 5000);
}

// Modal functions
function showNewConversationModal() {
    document.getElementById('newConversationModal').style.display = 'block';
}

function closeNewConversationModal() {
    document.getElementById('newConversationModal').style.display = 'none';
    // Clear form
    document.getElementById('newCustomerId').value = '';
    document.getElementById('newCustomerName').value = '';
    document.getElementById('newCustomerPhone').value = '';
    document.getElementById('newAgentPhone').value = '';
}

// Create new conversation
async function createConversation() {
    const customerId = document.getElementById('newCustomerId').value.trim();
    const customerName = document.getElementById('newCustomerName').value.trim();
    const customerPhone = document.getElementById('newCustomerPhone').value.trim();
    const agentPhone = document.getElementById('newAgentPhone').value.trim();

    if (!customerId || !customerName || !customerPhone || !agentPhone) {
        showStatus('Todos los campos son requeridos', 'error');
        return;
    }

    try {
        const response = await fetch('/api/conversations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                customerId,
                customerName,
                customerPhone,
                agentPhone
            })
        });

        const result = await response.json();

        if (response.ok) {
            showStatus('Conversación creada exitosamente');
            closeNewConversationModal();
            await refreshConversations();
            selectConversation(result.id, result.customerName);
        } else {
            showStatus(result.error || 'Error creando conversación', 'error');
        }
    } catch (error) {
        console.error('Error creating conversation:', error);
        showStatus('Error de conexión', 'error');
    }
}

// Refresh conversations list
async function refreshConversations() {
    try {
        const response = await fetch('/api/conversations');
        conversations = await response.json();
        renderConversationList();
    } catch (error) {
        console.error('Error fetching conversations:', error);
        showStatus('Error cargando conversaciones', 'error');
    }
}

// Render conversations in sidebar
function renderConversationList() {
    if (conversations.length === 0) {
        conversationList.innerHTML = '<div class="no-conversation">No hay conversaciones</div>';
        return;
    }

    conversationList.innerHTML = conversations.map(conv => `
        <div class="conversation-item ${conv.id === currentConversationId ? 'active' : ''}" 
             onclick="selectConversation('${conv.id}', '${conv.customerName}')">
            <div style="font-weight: 500;">${conv.customerName}</div>
            <div style="font-size: 12px; color: #666;">${conv.customerId}</div>
            <div style="font-size: 11px; color: #999;">
                ${new Date(conv.createdAt).toLocaleString()}
            </div>
        </div>
    `).join('');
}

// Select a conversation
async function selectConversation(conversationId, customerName) {
    currentConversationId = conversationId;
    conversationTitle.textContent = `Conversación con ${customerName}`;
    
    // Update active conversation in list
    renderConversationList();
    
    // Show message input area
    messageInputArea.style.display = 'block';
    
    // Load messages
    await refreshMessages();
}

// Refresh messages for current conversation
async function refreshMessages() {
    if (!currentConversationId) return;

    try {
        const response = await fetch(`/api/conversations/${currentConversationId}/messages`);
        currentMessages = await response.json();
        renderMessages();
        showStatus('Mensajes actualizados');
    } catch (error) {
        console.error('Error fetching messages:', error);
        showStatus('Error cargando mensajes', 'error');
    }
}

// Render messages in chat area
function renderMessages() {
    if (currentMessages.length === 0) {
        chatArea.innerHTML = '<div class="no-conversation">No hay mensajes</div>';
        return;
    }

    chatArea.innerHTML = currentMessages.map(msg => `
        <div class="message ${msg.from}">
            <div>${msg.body}</div>
            <div class="timestamp">
                ${msg.from === 'customer' ? 'Cliente' : 'Agente'} - 
                ${new Date(msg.timestamp).toLocaleTimeString()}
            </div>
        </div>
    `).join('');

    // Scroll to bottom
    chatArea.scrollTop = chatArea.scrollHeight;
}

// Send message
async function sendMessage() {
    if (!currentConversationId) {
        showStatus('Selecciona una conversación primero', 'error');
        return;
    }

    const messageText = document.getElementById('messageText').value.trim();
    const customerPhone = document.getElementById('customerPhone').value.trim();
    const agentPhone = document.getElementById('agentPhone').value.trim();

    if (!messageText) {
        showStatus('Escribe un mensaje', 'error');
        return;
    }

    if (!customerPhone || !agentPhone) {
        showStatus('Completa los datos del payload', 'error');
        return;
    }

    try {
        const response = await fetch(`/api/conversations/${currentConversationId}/send`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: messageText,
                payloadData: {
                    customerPhone,
                    agentPhone
                }
            })
        });

        const result = await response.json();

        if (response.ok) {
            document.getElementById('messageText').value = '';
            showStatus('Mensaje enviado al sistema');
            
            // Refresh messages after short delay
            setTimeout(refreshMessages, 500);
        } else {
            showStatus(result.error || 'Error enviando mensaje', 'error');
        }
    } catch (error) {
        console.error('Error sending message:', error);
        showStatus('Error de conexión', 'error');
    }
}

// Handle Enter key in message textarea
document.addEventListener('keydown', (event) => {
    if (event.target.id === 'messageText' && event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
});

// Auto-refresh messages every 10 seconds for active conversation
setInterval(() => {
    if (currentConversationId) {
        refreshMessages();
    }
}, 10000);