{"name": "@cx-system/client-simulator", "version": "1.0.0", "private": true, "description": "Client Simulator for testing CX System flows", "main": "dist/server.js", "scripts": {"build": "npm install && tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js"}, "dependencies": {"dotenv": "^16.3.1", "express": "^4.18.2", "winston": "^3.17.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.11.0", "tsx": "^4.7.0", "typescript": "^5.3.3"}}