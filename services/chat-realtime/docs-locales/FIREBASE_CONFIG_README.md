# Firebase Centralized Configuration System

## Overview

This system provides a centralized, consistent Firebase configuration for all helper scripts and services. It eliminates the need for manual configuration updates and ensures all scripts use the same Firebase setup.

## Benefits

✅ **Automatic Environment Variable Loading**: No more manual `.env` path configuration  
✅ **Consistent Firebase Setup**: All scripts use identical Firebase initialization  
✅ **Emulator/Production Switching**: Automatic environment detection  
✅ **Standard Constants**: Predefined IDs, departments, and conversation types  
✅ **Future-Proof**: New scripts automatically inherit consistent configuration  

## Usage

### For New Helper Scripts

```javascript
#!/usr/bin/env node

// 1. Import centralized configuration
const { 
  initializeFirebase,
  getFirebaseDatabase,
  getFirebaseRef,
  CONSTANTS 
} = require('./firebase-config');

// 2. Initialize Firebase (automatic emulator/production detection)
initializeFirebase();
const db = getFirebaseDatabase();

// 3. Use standard constants instead of hardcoded values
const AGENT_ID = CONSTANTS.JUAN_PEREZ_ID;
const DEPARTMENT = CONSTANTS.DEPARTMENTS.TECHNICAL_SUPPORT;

// 4. Your script logic here
async function yourHelperFunction() {
  try {
    // Get Firebase reference
    const conversationsRef = getFirebaseRef('conversations');
    
    // Use database operations
    const snapshot = await conversationsRef.once('value');
    // ... your logic
    
  } catch (error) {
    console.error('Script failed:', error.message);
  }
}
```

### Available Exports

#### Main Functions
- `initializeFirebase()` - Initialize Firebase with automatic emulator/production setup
- `getFirebaseDatabase()` - Get initialized Firebase Database instance
- `getFirebaseRef(path)` - Get Firebase Database reference for specific path

#### Configuration Constants
- `FIREBASE_CONFIG` - Current Firebase configuration object
- `CONSTANTS.JUAN_PEREZ_ID` - Standard test agent ID
- `CONSTANTS.SUPERVISOR_ID` - Standard supervisor ID
- `CONSTANTS.DEPARTMENTS` - Department IDs (TECHNICAL_SUPPORT, SALES, BILLING, GENERAL)
- `CONSTANTS.CONVERSATION_TYPES` - Conversation type identifiers

#### Utility Functions
- `generateConversationId(type, identifier)` - Generate consistent conversation IDs
- `admin` - Direct access to Firebase Admin SDK

## Configuration

All configuration comes from environment variables in the project root `.env` file:

```bash
# Required
GOOGLE_CLOUD_PROJECT_ID=cx-system-469120

# Optional (defaults provided)
NODE_ENV=development
FIREBASE_USE_EMULATOR=true
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/
FIREBASE_SERVICE_ACCOUNT_PATH=/path/to/service-account.json
```

## Environment Detection

- **Development**: Automatically uses Firebase Emulator (`localhost:9000`)
- **Production**: Uses Firebase Production with service account authentication

The system automatically detects the environment based on:
1. `NODE_ENV` environment variable
2. `FIREBASE_USE_EMULATOR` setting

## Migration Guide

### Old Pattern (DON'T DO THIS)
```javascript
// ❌ Manual configuration in each script
const admin = require('firebase-admin');
require('dotenv').config({ path: '../../../.env' });

admin.initializeApp({
  projectId: 'cx-system-469120', // Hardcoded!
  databaseURL: 'http://127.0.0.1:9000/?ns=cx-system-469120' // Hardcoded!
});

const JUAN_ID = '5f4fb378-908d-4b49-83ce-be4ce3b50c5d'; // Hardcoded!
```

### New Pattern (USE THIS)
```javascript
// ✅ Centralized configuration
const { initializeFirebase, getFirebaseDatabase, CONSTANTS } = require('./firebase-config');

initializeFirebase(); // Automatic configuration
const db = getFirebaseDatabase();

const JUAN_ID = CONSTANTS.JUAN_PEREZ_ID; // From constants
```

## Updated Scripts

The following scripts have been migrated to use centralized configuration:

- ✅ `verify_firebase_data.js`
- ✅ `direct_assign_juan.js` 
- ✅ `debug_juan_status.js`
- ✅ `create_and_assign_conversations.js`

## Service Integration

The main Chat Realtime service (`src/firebase.ts`) has been updated to use centralized configuration as a fallback, ensuring consistency between scripts and services.

## Testing

Run the configuration test:
```bash
cd services/chat-realtime
node firebase-config.js
```

Expected output:
```
🧪 Testing Firebase Configuration...
Configuration: { PROJECT_ID: 'cx-system-469120', ... }
🔧 Initializing Firebase with Emulator Configuration
✅ Firebase Emulator initialized for project: cx-system-469120
✅ Firebase configuration test successful
```

## Best Practices

1. **Always use centralized config**: Import from `firebase-config.js`
2. **Use constants**: Never hardcode IDs, use `CONSTANTS.*`
3. **Let it auto-detect**: Don't manually set emulator vs production
4. **Follow the template**: Use `example-helper-script.js` as template
5. **Test first**: Run `node firebase-config.js` to verify setup

## Troubleshooting

### Multiple Firebase Apps Error
- The system automatically handles app reuse
- Each script safely reuses existing Firebase initialization

### Environment Variables Not Loaded  
- Configuration automatically loads from project root `.env`
- No manual `dotenv.config()` needed in new scripts

### Wrong Database Namespace
- All scripts now use `FIREBASE_CONFIG.PROJECT_ID` consistently
- No more multiple database creation issues

## Future Scripts

For any new helper scripts:
1. Copy `example-helper-script.js` as template
2. Modify the specific logic while keeping the configuration pattern
3. Test with `node your-new-script.js`

This ensures automatic consistency and prevents configuration drift.