import { config } from 'dotenv';

// Load environment variables for testing
config();

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.FIREBASE_USE_EMULATOR = 'true';
process.env.FIREBASE_EMULATOR_HOST = 'localhost:9000';
process.env.FIREBASE_PROJECT_ID = 'cx-system-test';
process.env.PORT = '0'; // Use random port for tests

// Mock console methods to reduce test output noise
global.console = {
  ...console,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test timeout
jest.setTimeout(30000);

// Setup global test hooks
beforeAll(() => {
  console.log('🧪 Starting Chat Realtime Service Tests');
});

afterAll(() => {
  console.log('✅ Chat Realtime Service Tests Completed');
});

export {};