import request from 'supertest';
import express from 'express';
import routes from '../../src/routes';
import { conversationService } from '../../src/conversationService';
import { CreateConversationRequest, SendMessageRequest } from '../../src/types';

// Mock the conversation service
jest.mock('../../src/conversationService', () => ({
  conversationService: {
    healthCheck: jest.fn(),
    createConversation: jest.fn(),
    getConversation: jest.fn(),
    updateConversation: jest.fn(),
    deleteConversation: jest.fn(),
    listConversations: jest.fn(),
    sendMessage: jest.fn(),
    getMessages: jest.fn(),
    markMessageAsRead: jest.fn(),
    transferConversation: jest.fn(),
    acceptTransfer: jest.fn(),
    closeConversation: jest.fn(),
    setTypingIndicator: jest.fn(),
    getTypingIndicators: jest.fn(),
    updateAgentStatus: jest.fn(),
    getAgentStatus: jest.fn(),
    addNote: jest.fn(),
  },
}));

describe('Chat Realtime API Integration Tests', () => {
  let app: express.Application;
  const mockService = conversationService as jest.Mocked<typeof conversationService>;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    app.use('/api', routes);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Health Check', () => {
    it('should return healthy status when Firebase is working', async () => {
      // Arrange
      mockService.healthCheck.mockResolvedValue(true);

      // Act
      const response = await request(app).get('/api/health');

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          service: 'chat-realtime',
          status: 'healthy',
          firebase: true,
        },
      });
    });

    it('should return unhealthy status when Firebase is down', async () => {
      // Arrange
      mockService.healthCheck.mockResolvedValue(false);

      // Act
      const response = await request(app).get('/api/health');

      // Assert
      expect(response.status).toBe(200);
      expect(response.body).toMatchObject({
        success: true,
        data: {
          service: 'chat-realtime',
          status: 'unhealthy',
          firebase: false,
        },
      });
    });

    it('should return 500 when health check throws error', async () => {
      // Arrange
      mockService.healthCheck.mockRejectedValue(new Error('Health check failed'));

      // Act
      const response = await request(app).get('/api/health');

      // Assert
      expect(response.status).toBe(500);
      expect(response.body).toMatchObject({
        success: false,
        error: {
          code: 'HEALTH_CHECK_FAILED',
          message: 'Health check failed',
        },
      });
    });
  });

  describe('Conversation CRUD Operations', () => {
    describe('POST /api/conversations', () => {
      it('should create conversation successfully', async () => {
        // Arrange
        const request_data: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          priority: 2,
        };

        const mockConversation = {
          id: 'conv_123',
          ...request_data,
          status: 'new',
          createdAt: Date.now(),
        };

        mockService.createConversation.mockResolvedValue(mockConversation as any);

        // Act
        const response = await request(app)
          .post('/api/conversations')
          .send(request_data);

        // Assert
        expect(response.status).toBe(201);
        expect(response.body).toMatchObject({
          success: true,
          data: mockConversation,
        });
        expect(mockService.createConversation).toHaveBeenCalledWith(request_data);
      });

      it('should return 400 when service throws error', async () => {
        // Arrange
        const request_data: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
        };

        const serviceError = {
          code: 'VALIDATION_FAILED',
          message: 'Invalid customer data',
          details: { field: 'customerId' },
        };

        mockService.createConversation.mockRejectedValue(serviceError);

        // Act
        const response = await request(app)
          .post('/api/conversations')
          .send(request_data);

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toMatchObject({
          success: false,
          error: {
            code: 'VALIDATION_FAILED',
            message: 'Invalid customer data',
            details: { field: 'customerId' },
          },
        });
      });
    });

    describe('GET /api/conversations/:id', () => {
      it('should return conversation when found', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockConversation = {
          id: conversationId,
          customerId: 'customer123',
          status: 'active',
        };

        mockService.getConversation.mockResolvedValue(mockConversation as any);

        // Act
        const response = await request(app).get(`/api/conversations/${conversationId}`);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: mockConversation,
        });
        expect(mockService.getConversation).toHaveBeenCalledWith(conversationId);
      });

      it('should return 404 when conversation not found', async () => {
        // Arrange
        const conversationId = 'nonexistent';
        mockService.getConversation.mockResolvedValue(null);

        // Act
        const response = await request(app).get(`/api/conversations/${conversationId}`);

        // Assert
        expect(response.status).toBe(404);
        expect(response.body).toMatchObject({
          success: false,
          error: {
            code: 'CONVERSATION_NOT_FOUND',
            message: `Conversation ${conversationId} not found`,
          },
        });
      });

      it('should return 500 when service throws error', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const serviceError = {
          code: 'DATABASE_ERROR',
          message: 'Firebase connection failed',
        };

        mockService.getConversation.mockRejectedValue(serviceError);

        // Act
        const response = await request(app).get(`/api/conversations/${conversationId}`);

        // Assert
        expect(response.status).toBe(500);
        expect(response.body).toMatchObject({
          success: false,
          error: {
            code: 'DATABASE_ERROR',
            message: 'Firebase connection failed',
          },
        });
      });
    });

    describe('PUT /api/conversations/:id', () => {
      it('should update conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const updateData = {
          status: 'active',
          assignedTo: 'human', assignedAgentId: 'agent456',
          priority: 3,
        };

        mockService.updateConversation.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .put(`/api/conversations/${conversationId}`)
          .send(updateData);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: { id: conversationId, updated: true },
        });
        expect(mockService.updateConversation).toHaveBeenCalledWith(conversationId, updateData);
      });
    });

    describe('DELETE /api/conversations/:id', () => {
      it('should delete conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        mockService.deleteConversation.mockResolvedValue(undefined);

        // Act
        const response = await request(app).delete(`/api/conversations/${conversationId}`);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: { id: conversationId, deleted: true },
        });
        expect(mockService.deleteConversation).toHaveBeenCalledWith(conversationId);
      });
    });

    describe('GET /api/conversations', () => {
      it('should list conversations with filters', async () => {
        // Arrange
        const mockConversations = [
          { id: 'conv_1', status: 'active' },
          { id: 'conv_2', status: 'active' },
        ];

        mockService.listConversations.mockResolvedValue(mockConversations as any);

        // Act
        const response = await request(app)
          .get('/api/conversations')
          .query({ status: 'active', limit: '10' });

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            conversations: mockConversations,
            count: 2,
            filters: { status: 'active', limit: 10 },
          },
        });

        expect(mockService.listConversations).toHaveBeenCalledWith({
          status: 'active',
          limit: 10,
        });
      });

      it('should handle empty query parameters', async () => {
        // Arrange
        const mockConversations: any[] = [];
        mockService.listConversations.mockResolvedValue(mockConversations);

        // Act
        const response = await request(app).get('/api/conversations');

        // Assert
        expect(response.status).toBe(200);
        expect(mockService.listConversations).toHaveBeenCalledWith({});
      });
    });
  });

  describe('Message Operations', () => {
    describe('POST /api/conversations/:id/messages', () => {
      it('should send message successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: 'Hello, how can I help?',
          senderId: 'agent456',
          senderType: 'agent',
          messageType: 'text',
        };

        const mockMessage = {
          id: 'msg_123',
          conversationId,
          ...messageRequest,
          timestamp: Date.now(),
          status: 'sent',
        };

        mockService.sendMessage.mockResolvedValue(mockMessage as any);

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/messages`)
          .send(messageRequest);

        // Assert
        expect(response.status).toBe(201);
        expect(response.body).toMatchObject({
          success: true,
          data: mockMessage,
        });
        expect(mockService.sendMessage).toHaveBeenCalledWith(conversationId, messageRequest);
      });
    });

    describe('GET /api/conversations/:id/messages', () => {
      it('should get messages successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockMessages = [
          { id: 'msg_1', content: 'Hello' },
          { id: 'msg_2', content: 'How are you?' },
        ];

        mockService.getMessages.mockResolvedValue(mockMessages as any);

        // Act
        const response = await request(app)
          .get(`/api/conversations/${conversationId}/messages`)
          .query({ limit: '50' });

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            messages: mockMessages,
            count: 2,
            conversationId,
          },
        });
        expect(mockService.getMessages).toHaveBeenCalledWith(conversationId, 50);
      });
    });

    describe('PUT /api/conversations/:conversationId/messages/:messageId/read', () => {
      it('should mark message as read successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageId = 'msg_456';
        const userId = 'agent789';

        mockService.markMessageAsRead.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .put(`/api/conversations/${conversationId}/messages/${messageId}/read`)
          .send({ userId });

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            conversationId,
            messageId,
            readBy: userId,
          },
        });
        expect(mockService.markMessageAsRead).toHaveBeenCalledWith(conversationId, messageId, userId);
      });

      it('should return 400 when userId is missing', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageId = 'msg_456';

        // Act
        const response = await request(app)
          .put(`/api/conversations/${conversationId}/messages/${messageId}/read`)
          .send({});

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toMatchObject({
          success: false,
          error: {
            code: 'MISSING_USER_ID',
            message: 'userId is required',
          },
        });
      });
    });
  });

  describe('Conversation Actions', () => {
    describe('POST /api/conversations/:id/transfer', () => {
      it('should transfer conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const transferData = {
          targetAgentId: 'agent789',
          reason: 'Skill-based transfer',
        };

        mockService.transferConversation.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/transfer`)
          .send(transferData);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            conversationId,
            transferred: true,
            targetAgentId: 'agent789',
            reason: 'Skill-based transfer',
          },
        });
        expect(mockService.transferConversation).toHaveBeenCalledWith(
          conversationId,
          'agent789',
          'Skill-based transfer'
        );
      });

      it('should return 400 when required fields are missing', async () => {
        // Arrange
        const conversationId = 'conv_123';

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/transfer`)
          .send({ targetAgentId: 'agent789' }); // Missing reason

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toMatchObject({
          success: false,
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: 'targetAgentId and reason are required',
          },
        });
      });
    });

    describe('POST /api/conversations/:id/accept-transfer', () => {
      it('should accept transfer successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const agentId = 'agent789';

        mockService.acceptTransfer.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/accept-transfer`)
          .send({ agentId });

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            conversationId,
            acceptedBy: agentId,
          },
        });
        expect(mockService.acceptTransfer).toHaveBeenCalledWith(conversationId, agentId);
      });

      it('should return 400 when agentId is missing', async () => {
        // Arrange
        const conversationId = 'conv_123';

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/accept-transfer`)
          .send({});

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toMatchObject({
          success: false,
          error: {
            code: 'MISSING_AGENT_ID',
            message: 'agentId is required',
          },
        });
      });
    });

    describe('POST /api/conversations/:id/close', () => {
      it('should close conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const closeData = {
          reason: 'Issue resolved',
          notes: 'Customer satisfied',
        };

        mockService.closeConversation.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/close`)
          .send(closeData);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            conversationId,
            closed: true,
            reason: 'Issue resolved',
          },
        });
        expect(mockService.closeConversation).toHaveBeenCalledWith(
          conversationId,
          'Issue resolved',
          'Customer satisfied'
        );
      });

      it('should close conversation without optional fields', async () => {
        // Arrange
        const conversationId = 'conv_123';
        mockService.closeConversation.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/close`)
          .send({});

        // Assert
        expect(response.status).toBe(200);
        expect(mockService.closeConversation).toHaveBeenCalledWith(
          conversationId,
          undefined,
          undefined
        );
      });
    });
  });

  describe('Typing Indicators', () => {
    describe('POST /api/conversations/:id/typing', () => {
      it('should set typing indicator successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const typingData = {
          userId: 'agent456',
          userType: 'agent',
          isTyping: true,
        };

        mockService.setTypingIndicator.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/typing`)
          .send(typingData);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            conversationId,
            userId: 'agent456',
            isTyping: true,
          },
        });
        expect(mockService.setTypingIndicator).toHaveBeenCalledWith(
          conversationId,
          'agent456',
          'agent',
          true
        );
      });

      it('should return 400 when required fields are missing', async () => {
        // Arrange
        const conversationId = 'conv_123';

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/typing`)
          .send({ userId: 'agent456' }); // Missing userType and isTyping

        // Assert
        expect(response.status).toBe(400);
        expect(response.body).toMatchObject({
          success: false,
          error: {
            code: 'INVALID_TYPING_DATA',
            message: 'userId, userType, and isTyping are required',
          },
        });
      });
    });

    describe('GET /api/conversations/:id/typing', () => {
      it('should get typing indicators successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockIndicators = [
          { userId: 'agent456', isTyping: true },
          { userId: 'customer123', isTyping: true },
        ];

        mockService.getTypingIndicators.mockResolvedValue(mockIndicators as any);

        // Act
        const response = await request(app).get(`/api/conversations/${conversationId}/typing`);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            conversationId,
            indicators: mockIndicators,
          },
        });
        expect(mockService.getTypingIndicators).toHaveBeenCalledWith(conversationId);
      });
    });
  });

  describe('Agent Status', () => {
    describe('PUT /api/agents/:id/status', () => {
      it('should update agent status successfully', async () => {
        // Arrange
        const agentId = 'agent456';
        const statusUpdate = {
          status: 'online',
          isAvailable: true,
          maxConcurrentChats: 5,
        };

        mockService.updateAgentStatus.mockResolvedValue(undefined);

        // Act
        const response = await request(app)
          .put(`/api/agents/${agentId}/status`)
          .send(statusUpdate);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: {
            agentId,
            updated: true,
          },
        });
        expect(mockService.updateAgentStatus).toHaveBeenCalledWith(agentId, statusUpdate);
      });
    });

    describe('GET /api/agents/:id/status', () => {
      it('should get agent status successfully', async () => {
        // Arrange
        const agentId = 'agent456';
        const mockStatus = {
          id: agentId,
          status: 'online',
          isAvailable: true,
          lastSeen: Date.now(),
        };

        mockService.getAgentStatus.mockResolvedValue(mockStatus as any);

        // Act
        const response = await request(app).get(`/api/agents/${agentId}/status`);

        // Assert
        expect(response.status).toBe(200);
        expect(response.body).toMatchObject({
          success: true,
          data: mockStatus,
        });
        expect(mockService.getAgentStatus).toHaveBeenCalledWith(agentId);
      });
    });
  });

  describe('Internal Notes', () => {
    describe('POST /api/conversations/:id/notes', () => {
      it('should add note successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const noteData = {
          agentId: 'agent456',
          content: 'Customer seems frustrated',
          category: 'general',
          priority: 'medium',
          visibility: 'team',
        };

        const mockNote = {
          id: 'note_123',
          ...noteData,
          createdAt: Date.now(),
        };

        mockService.addNote.mockResolvedValue(mockNote as any);

        // Act
        const response = await request(app)
          .post(`/api/conversations/${conversationId}/notes`)
          .send(noteData);

        // Assert
        expect(response.status).toBe(201);
        expect(response.body).toMatchObject({
          success: true,
          data: mockNote,
        });
        expect(mockService.addNote).toHaveBeenCalledWith(conversationId, noteData);
      });
    });
  });
});