import { ConversationService } from '../../src/conversationService';
import { firebaseService } from '../../src/firebase';
import {
  CreateConversationRequest,
  SendMessageRequest,
  UpdateConversationRequest,
  Conversation,
  Message,
  ConversationStatus,
} from '../../src/types';

// Mock Firebase service completely
jest.mock('../../src/firebase', () => ({
  firebaseService: {
    set: jest.fn(),
    get: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getRef: jest.fn(),
    transaction: jest.fn(),
    healthCheck: jest.fn(),
  },
}));

describe('ConversationService', () => {
  let service: ConversationService;
  const mockFirebase = firebaseService as jest.Mocked<typeof firebaseService>;

  beforeEach(() => {
    service = new ConversationService();
    jest.clearAllMocks();
  });

  describe('Conversation CRUD Operations', () => {
    describe('createConversation', () => {
      it('should create conversation successfully with required fields', async () => {
        // Arrange
        const request: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
        };

        mockFirebase.set.mockResolvedValue(undefined);

        // Act
        const result = await service.createConversation(request);

        // Assert
        expect(result).toMatchObject({
          customerId: 'customer123',
          customer: request.customer,
          channel: 'whatsapp',
          status: 'new',
          priority: 2,
          source: 'direct',
          assignedTo: 'human',
          assignedAgentId: 'agent456',
          metadata: {
            tags: [],
            notes: [],
            transferCount: 0,
            escalationLevel: 0,
            messageCount: 0,
          },
        });

        expect(result.id).toMatch(/^conv_\d+_[a-z0-9]+$/);
        expect(result.createdAt).toBeGreaterThan(0);
        expect(result.updatedAt).toBeGreaterThan(0);
        expect(mockFirebase.set).toHaveBeenCalledTimes(1);
      });

      it('should create conversation with department and routing info', async () => {
        // Arrange
        const request: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          departmentId: 'technical_support',
          priority: 3,
        };

        mockFirebase.set.mockResolvedValue(undefined);

        // Act
        const result = await service.createConversation(request);

        // Assert
        expect(result.departmentId).toBe('technical_support');
        expect(result.priority).toBe(3);
        expect(result.routingInfo).toMatchObject({
          assignedDepartment: 'technical_support',
          aiAnalysisAttempts: 0,
          aiAnalysisHistory: [],
        });
        expect(result.routingInfo?.departmentAssignedAt).toBeGreaterThan(0);
      });

      it('should create conversation with initial message', async () => {
        // Arrange
        const request: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          initialMessage: 'Hello, I need help',
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.createConversation(request);

        // Assert
        expect(mockFirebase.set).toHaveBeenCalledTimes(2); // Conversation + Message
        expect(mockFirebase.transaction).toHaveBeenCalledTimes(1); // Message count increment
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const request: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
        };

        const firebaseError = new Error('Firebase connection failed');
        mockFirebase.set.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.createConversation(request)).rejects.toMatchObject({
          code: 'CONVERSATION_CREATE_FAILED',
          message: 'Failed to create conversation',
        });
      });
    });

    describe('getConversation', () => {
      it('should return conversation when found', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockConversation: Conversation = {
          id: conversationId,
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          status: 'active',
          priority: 2,
          source: 'direct',
          createdAt: Date.now(),
          updatedAt: Date.now(),
          assignedTo: 'human',
          assignedAgentId: 'agent456',
          metadata: {
            tags: [],
            notes: [],
            transferCount: 0,
            escalationLevel: 0,
            messageCount: 5,
          },
        };

        mockFirebase.get.mockResolvedValue(mockConversation);

        // Act
        const result = await service.getConversation(conversationId);

        // Assert
        expect(result).toEqual(mockConversation);
        expect(mockFirebase.get).toHaveBeenCalledWith(`conversations/${conversationId}`);
      });

      it('should return null when conversation not found', async () => {
        // Arrange
        const conversationId = 'nonexistent';
        mockFirebase.get.mockResolvedValue(null);

        // Act
        const result = await service.getConversation(conversationId);

        // Assert
        expect(result).toBeNull();
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const firebaseError = new Error('Firebase get failed');
        mockFirebase.get.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.getConversation(conversationId)).rejects.toMatchObject({
          code: 'CONVERSATION_NOT_FOUND',
          message: `Conversation ${conversationId} not found`,
        });
      });
    });

    describe('updateConversation', () => {
      it('should update conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const updates: UpdateConversationRequest = {
          status: 'active',
          assignedTo: 'human',
          assignedAgentId: 'agent456',
          priority: 3,
        };

        mockFirebase.update.mockResolvedValue(undefined);

        // Act
        await service.updateConversation(conversationId, updates);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'active',
            assignedTo: 'human',
          assignedAgentId: 'agent456',
            priority: 3,
            updatedAt: expect.any(Number),
          })
        );
      });

      it('should set assignedAt timestamp when status changes to assigned', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const updates: UpdateConversationRequest = {
          status: 'assigned',
          assignedTo: 'human',
          assignedAgentId: 'agent456',
        };

        mockFirebase.update.mockResolvedValue(undefined);

        // Act
        await service.updateConversation(conversationId, updates);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'assigned',
            assignedTo: 'human',
          assignedAgentId: 'agent456',
            assignedAt: expect.any(Number),
            updatedAt: expect.any(Number),
          })
        );
      });

      it('should set closedAt timestamp when status changes to closed', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const updates: UpdateConversationRequest = {
          status: 'closed',
        };

        mockFirebase.update.mockResolvedValue(undefined);

        // Act
        await service.updateConversation(conversationId, updates);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'closed',
            closedAt: expect.any(Number),
            updatedAt: expect.any(Number),
          })
        );
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const updates: UpdateConversationRequest = { status: 'active' };
        const firebaseError = new Error('Firebase update failed');
        mockFirebase.update.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.updateConversation(conversationId, updates)).rejects.toMatchObject({
          code: 'CONVERSATION_UPDATE_FAILED',
          message: `Failed to update conversation ${conversationId}`,
        });
      });
    });

    describe('deleteConversation', () => {
      it('should delete conversation and related data', async () => {
        // Arrange
        const conversationId = 'conv_123';
        mockFirebase.remove.mockResolvedValue(undefined);

        // Act
        await service.deleteConversation(conversationId);

        // Assert
        expect(mockFirebase.remove).toHaveBeenCalledTimes(3);
        expect(mockFirebase.remove).toHaveBeenCalledWith(`conversations/${conversationId}`);
        expect(mockFirebase.remove).toHaveBeenCalledWith(`messages/${conversationId}`);
        expect(mockFirebase.remove).toHaveBeenCalledWith(`typing/${conversationId}`);
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const firebaseError = new Error('Firebase remove failed');
        mockFirebase.remove.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.deleteConversation(conversationId)).rejects.toMatchObject({
          code: 'CONVERSATION_DELETE_FAILED',
          message: `Failed to delete conversation ${conversationId}`,
        });
      });
    });

    describe('listConversations', () => {
      it('should return conversations list', async () => {
        // Arrange
        const mockQuery = {
          orderByChild: jest.fn().mockReturnThis(),
          equalTo: jest.fn().mockReturnThis(),
          limitToLast: jest.fn().mockReturnThis(),
          once: jest.fn().mockResolvedValue({
            val: () => ({
              'conv_1': { id: 'conv_1', status: 'active' },
              'conv_2': { id: 'conv_2', status: 'active' },
            }),
          }),
        };

        mockFirebase.getRef.mockReturnValue(mockQuery as any);

        // Act
        const result = await service.listConversations({ status: 'active', limit: 10 });

        // Assert
        expect(result).toHaveLength(2);
        expect(mockQuery.orderByChild).toHaveBeenCalledWith('status');
        expect(mockQuery.equalTo).toHaveBeenCalledWith('active');
        expect(mockQuery.limitToLast).toHaveBeenCalledWith(10);
      });

      it('should handle empty results', async () => {
        // Arrange
        const mockQuery = {
          orderByChild: jest.fn().mockReturnThis(),
          once: jest.fn().mockResolvedValue({ val: () => null }),
        };

        mockFirebase.getRef.mockReturnValue(mockQuery as any);

        // Act
        const result = await service.listConversations();

        // Assert
        expect(result).toEqual([]);
      });
    });
  });

  describe('Message Operations', () => {
    describe('sendMessage', () => {
      it('should send message successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: 'Hello, how can I help?',
          senderId: 'agent456',
          senderType: 'agent',
          messageType: 'text',
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.sendMessage(conversationId, messageRequest);

        // Assert
        expect(result).toMatchObject({
          conversationId,
          senderId: 'agent456',
          senderType: 'agent',
          content: 'Hello, how can I help?',
          type: 'text',
          status: 'sent',
        });

        expect(result.id).toMatch(/^msg_\d+_[a-z0-9]+$/);
        expect(result.timestamp).toBeGreaterThan(0);
        expect(mockFirebase.set).toHaveBeenCalledTimes(1);
        expect(mockFirebase.transaction).toHaveBeenCalledTimes(1); // Message count increment
      });

      it('should send message with system data', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: 'Conversation transferred to technical support',
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'transfer',
            details: { targetDepartment: 'technical_support', reason: 'User needs technical help' },
          },
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.sendMessage(conversationId, messageRequest);

        // Assert
        expect(result.type).toBe('system');
        expect(result.systemData).toEqual(messageRequest.systemData);
        expect(result.content).toBe('Conversation transferred to technical support');
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: 'Hello',
          senderId: 'agent456',
          senderType: 'agent',
        };

        const firebaseError = new Error('Firebase set failed');
        mockFirebase.set.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.sendMessage(conversationId, messageRequest)).rejects.toMatchObject({
          code: 'MESSAGE_SEND_FAILED',
          message: 'Failed to send message',
        });
      });
    });

    describe('getMessages', () => {
      it('should return messages for conversation', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockMessages = {
          'msg_1': { id: 'msg_1', content: 'Hello' },
          'msg_2': { id: 'msg_2', content: 'How are you?' },
        };

        const mockQuery = {
          orderByChild: jest.fn().mockReturnThis(),
          limitToLast: jest.fn().mockReturnThis(),
          once: jest.fn().mockResolvedValue({ val: () => mockMessages }),
        };

        mockFirebase.getRef.mockReturnValue(mockQuery as any);

        // Act
        const result = await service.getMessages(conversationId, 50);

        // Assert
        expect(result).toHaveLength(2);
        expect(mockQuery.orderByChild).toHaveBeenCalledWith('timestamp');
        expect(mockQuery.limitToLast).toHaveBeenCalledWith(50);
      });

      it('should return empty array when no messages', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockQuery = {
          orderByChild: jest.fn().mockReturnThis(),
          once: jest.fn().mockResolvedValue({ val: () => null }),
        };

        mockFirebase.getRef.mockReturnValue(mockQuery as any);

        // Act
        const result = await service.getMessages(conversationId);

        // Assert
        expect(result).toEqual([]);
      });
    });

    describe('markMessageAsRead', () => {
      it('should mark message as read', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageId = 'msg_456';
        const userId = 'agent789';

        mockFirebase.update.mockResolvedValue(undefined);

        // Act
        await service.markMessageAsRead(conversationId, messageId, userId);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `messages/${conversationId}/${messageId}/readBy/${userId}`,
          expect.any(Number)
        );
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageId = 'msg_456';
        const userId = 'agent789';

        const firebaseError = new Error('Firebase update failed');
        mockFirebase.update.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.markMessageAsRead(conversationId, messageId, userId)).rejects.toMatchObject({
          code: 'MESSAGE_READ_FAILED',
          message: 'Failed to mark message as read',
        });
      });
    });
  });

  describe('Conversation Actions', () => {
    describe('transferConversation', () => {
      it('should transfer conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const targetAgentId = 'agent789';
        const reason = 'Skill-based transfer';

        const mockConversation: Conversation = {
          id: conversationId,
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          status: 'active',
          assignedTo: 'human',
          assignedAgentId: 'agent456',
          priority: 2,
          source: 'direct',
          createdAt: Date.now(),
          updatedAt: Date.now(),
          metadata: {
            tags: [],
            notes: [],
            transferCount: 0,
            escalationLevel: 0,
            messageCount: 5,
          },
        };

        mockFirebase.get.mockResolvedValue(mockConversation);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        await service.transferConversation(conversationId, targetAgentId, reason);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'transferring',
            'metadata.transferCount': 1,
          })
        );

        // Should also send system message
        expect(mockFirebase.set).toHaveBeenCalledTimes(1);
      });

      it('should throw error when conversation not found', async () => {
        // Arrange
        const conversationId = 'nonexistent';
        const targetAgentId = 'agent789';
        const reason = 'Transfer';

        mockFirebase.get.mockResolvedValue(null);

        // Act & Assert
        await expect(service.transferConversation(conversationId, targetAgentId, reason)).rejects.toMatchObject({
          code: 'CONVERSATION_TRANSFER_FAILED',
          message: 'Failed to transfer conversation',
        });
      });
    });

    describe('acceptTransfer', () => {
      it('should accept transfer successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const agentId = 'agent789';

        mockFirebase.update.mockResolvedValue(undefined);

        // Act
        await service.acceptTransfer(conversationId, agentId);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'active',
            assignedTo: agentId,
            assignedAt: expect.any(Number),
            'transferInfo.currentTransfer': null,
          })
        );
      });
    });

    describe('closeConversation', () => {
      it('should close conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const reason = 'Issue resolved';
        const notes = 'Customer satisfied';

        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        await service.closeConversation(conversationId, reason, notes);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'closed',
            closedAt: expect.any(Number),
          })
        );

        // Should also send system message
        expect(mockFirebase.set).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Typing Indicators', () => {
    describe('setTypingIndicator', () => {
      it('should set typing indicator when isTyping is true', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const userId = 'agent456';
        const userType = 'agent' as const;

        mockFirebase.set.mockResolvedValue(undefined);

        // Act
        await service.setTypingIndicator(conversationId, userId, userType, true);

        // Assert
        expect(mockFirebase.set).toHaveBeenCalledWith(
          `typing/${conversationId}/${userId}`,
          expect.objectContaining({
            conversationId,
            userId,
            userType: 'agent',
            isTyping: true,
            timestamp: expect.any(Number),
          })
        );
      });

      it('should remove typing indicator when isTyping is false', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const userId = 'agent456';
        const userType = 'agent' as const;

        mockFirebase.remove.mockResolvedValue(undefined);

        // Act
        await service.setTypingIndicator(conversationId, userId, userType, false);

        // Assert
        expect(mockFirebase.remove).toHaveBeenCalledWith(`typing/${conversationId}/${userId}`);
      });

      it('should not throw error when Firebase fails (typing not critical)', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const userId = 'agent456';
        const userType = 'agent' as const;

        const firebaseError = new Error('Firebase set failed');
        mockFirebase.set.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.setTypingIndicator(conversationId, userId, userType, true)).resolves.toBeUndefined();
      });
    });

    describe('getTypingIndicators', () => {
      it('should return typing indicators', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockIndicators = {
          'agent456': { userId: 'agent456', isTyping: true },
          'customer123': { userId: 'customer123', isTyping: true },
        };

        mockFirebase.get.mockResolvedValue(mockIndicators);

        // Act
        const result = await service.getTypingIndicators(conversationId);

        // Assert
        expect(result).toHaveLength(2);
        expect(mockFirebase.get).toHaveBeenCalledWith(`typing/${conversationId}`);
      });

      it('should return empty array when error occurs', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const firebaseError = new Error('Firebase get failed');
        mockFirebase.get.mockRejectedValue(firebaseError);

        // Act
        const result = await service.getTypingIndicators(conversationId);

        // Assert
        expect(result).toEqual([]);
      });
    });
  });

  describe('Agent Status', () => {
    describe('updateAgentStatus', () => {
      it('should update agent status successfully', async () => {
        // Arrange
        const agentId = 'agent456';
        const statusUpdate = {
          status: 'online' as const,
          isAvailable: true,
          maxConcurrentChats: 5,
        };

        mockFirebase.update.mockResolvedValue(undefined);

        // Act
        await service.updateAgentStatus(agentId, statusUpdate);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `agents/${agentId}`,
          expect.objectContaining({
            ...statusUpdate,
            lastSeen: expect.any(Number),
          })
        );
      });
    });

    describe('getAgentStatus', () => {
      it('should return agent status', async () => {
        // Arrange
        const agentId = 'agent456';
        const mockStatus = {
          id: agentId,
          status: 'online',
          isAvailable: true,
          lastSeen: Date.now(),
        };

        mockFirebase.get.mockResolvedValue(mockStatus);

        // Act
        const result = await service.getAgentStatus(agentId);

        // Assert
        expect(result).toEqual(mockStatus);
        expect(mockFirebase.get).toHaveBeenCalledWith(`agents/${agentId}`);
      });

      it('should return null when error occurs', async () => {
        // Arrange
        const agentId = 'agent456';
        const firebaseError = new Error('Firebase get failed');
        mockFirebase.get.mockRejectedValue(firebaseError);

        // Act
        const result = await service.getAgentStatus(agentId);

        // Assert
        expect(result).toBeNull();
      });
    });
  });

  describe('Internal Notes', () => {
    describe('addNote', () => {
      it('should add note successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const noteData = {
          agentId: 'agent456',
          content: 'Customer seems frustrated',
          category: 'general' as const,
          priority: 'medium' as const,
          visibility: 'team' as const,
        };

        mockFirebase.update.mockResolvedValue(undefined);

        // Act
        const result = await service.addNote(conversationId, noteData);

        // Assert
        expect(result).toMatchObject({
          ...noteData,
          id: expect.stringMatching(/^note_\d+_[a-z0-9]+$/),
          createdAt: expect.any(Number),
        });

        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}/metadata/notes/${result.id}`,
          result
        );
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const noteData = {
          agentId: 'agent456',
          content: 'Test note',
          category: 'general' as const,
          priority: 'low' as const,
          visibility: 'private' as const,
        };

        const firebaseError = new Error('Firebase update failed');
        mockFirebase.update.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.addNote(conversationId, noteData)).rejects.toMatchObject({
          code: 'NOTE_ADD_FAILED',
          message: 'Failed to add note',
        });
      });
    });
  });

  describe('Health Check', () => {
    it('should return true when Firebase is healthy', async () => {
      // Arrange
      mockFirebase.healthCheck.mockResolvedValue(true);

      // Act
      const result = await service.healthCheck();

      // Assert
      expect(result).toBe(true);
      expect(mockFirebase.healthCheck).toHaveBeenCalledTimes(1);
    });

    it('should return false when Firebase health check fails', async () => {
      // Arrange
      const firebaseError = new Error('Firebase connection failed');
      mockFirebase.healthCheck.mockRejectedValue(firebaseError);

      // Act
      const result = await service.healthCheck();

      // Assert
      expect(result).toBe(false);
    });
  });
});