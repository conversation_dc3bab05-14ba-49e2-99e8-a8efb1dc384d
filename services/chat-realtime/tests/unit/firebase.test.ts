import * as admin from 'firebase-admin';

// Mock Firebase Admin SDK completely
const mockApp = {
  delete: jest.fn(),
};

const mockDatabase = {
  ref: jest.fn(),
};

const mockRef = {
  once: jest.fn(),
  set: jest.fn(),
  update: jest.fn(),
  push: jest.fn(),
  remove: jest.fn(),
  transaction: jest.fn(),
};

jest.mock('firebase-admin', () => ({
  apps: [],
  initializeApp: jest.fn(() => mockApp),
  database: jest.fn(() => mockDatabase),
  credential: {
    cert: jest.fn(),
  },
}));

jest.mock('../../src/config', () => ({
  CONFIG: {
    FIREBASE: {
      USE_EMULATOR: true,
      PROJECT_ID: 'test-project',
      EMULATOR_HOST: 'localhost:9000',
      SERVICE_ACCOUNT_PATH: null,
      DATABASE_URL: 'https://test-project.firebaseio.com',
    },
  },
}));

describe('FirebaseService', () => {
  let firebaseService: any;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
    
    // Set up clean mock state
    mockDatabase.ref.mockReturnValue(mockRef);
    
    // Require the service fresh for each test
    firebaseService = require('../../src/firebase').firebaseService;
  });

  describe('Database Operations', () => {
    describe('get', () => {
      it('should get data from Firebase successfully', async () => {
        // Arrange
        const testData = { id: 'test', value: 'data' };
        mockRef.once.mockResolvedValue({
          val: () => testData,
        });

        // Act
        const result = await firebaseService.get('test/path');

        // Assert
        expect(result).toEqual(testData);
        expect(mockDatabase.ref).toHaveBeenCalledWith('test/path');
        expect(mockRef.once).toHaveBeenCalledWith('value');
      });

      it('should handle Firebase errors', async () => {
        // Arrange
        const firebaseError = new Error('Firebase get failed');
        mockRef.once.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(firebaseService.get('test/path')).rejects.toThrow('Firebase get failed');
      });
    });

    describe('set', () => {
      it('should set data in Firebase successfully', async () => {
        // Arrange
        const testData = { id: 'test', value: 'data' };
        mockRef.set.mockResolvedValue(undefined);

        // Act
        await firebaseService.set('test/path', testData);

        // Assert
        expect(mockDatabase.ref).toHaveBeenCalledWith('test/path');
        expect(mockRef.set).toHaveBeenCalledWith(testData);
      });

      it('should handle Firebase errors', async () => {
        // Arrange
        const testData = { id: 'test' };
        const firebaseError = new Error('Firebase set failed');
        mockRef.set.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(firebaseService.set('test/path', testData)).rejects.toThrow('Firebase set failed');
      });
    });

    describe('update', () => {
      it('should update data in Firebase successfully', async () => {
        // Arrange
        const updateData = { status: 'updated' };
        mockRef.update.mockResolvedValue(undefined);

        // Act
        await firebaseService.update('test/path', updateData);

        // Assert
        expect(mockDatabase.ref).toHaveBeenCalledWith('test/path');
        expect(mockRef.update).toHaveBeenCalledWith(updateData);
      });

      it('should handle Firebase errors', async () => {
        // Arrange
        const updateData = { status: 'updated' };
        const firebaseError = new Error('Firebase update failed');
        mockRef.update.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(firebaseService.update('test/path', updateData)).rejects.toThrow('Firebase update failed');
      });
    });

    describe('push', () => {
      it('should push data to Firebase successfully', async () => {
        // Arrange
        const testData = { value: 'new item' };
        const mockPushRef = { key: 'generated_key_123' };
        mockRef.push.mockResolvedValue(mockPushRef);

        // Act
        const result = await firebaseService.push('test/path', testData);

        // Assert
        expect(result).toBe('generated_key_123');
        expect(mockDatabase.ref).toHaveBeenCalledWith('test/path');
        expect(mockRef.push).toHaveBeenCalledWith(testData);
      });

      it('should handle Firebase errors', async () => {
        // Arrange
        const testData = { value: 'new item' };
        const firebaseError = new Error('Firebase push failed');
        mockRef.push.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(firebaseService.push('test/path', testData)).rejects.toThrow('Firebase push failed');
      });
    });

    describe('remove', () => {
      it('should remove data from Firebase successfully', async () => {
        // Arrange
        mockRef.remove.mockResolvedValue(undefined);

        // Act
        await firebaseService.remove('test/path');

        // Assert
        expect(mockDatabase.ref).toHaveBeenCalledWith('test/path');
        expect(mockRef.remove).toHaveBeenCalledTimes(1);
      });

      it('should handle Firebase errors', async () => {
        // Arrange
        const firebaseError = new Error('Firebase remove failed');
        mockRef.remove.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(firebaseService.remove('test/path')).rejects.toThrow('Firebase remove failed');
      });
    });

    describe('transaction', () => {
      it('should perform transaction successfully', async () => {
        // Arrange
        const updateFunction = jest.fn((currentValue) => currentValue + 1);
        const transactionResult = { committed: true, snapshot: { val: () => 5 } };
        mockRef.transaction.mockResolvedValue(transactionResult);

        // Act
        const result = await firebaseService.transaction('test/path', updateFunction);

        // Assert
        expect(result).toEqual(transactionResult);
        expect(mockDatabase.ref).toHaveBeenCalledWith('test/path');
        expect(mockRef.transaction).toHaveBeenCalledWith(updateFunction);
      });

      it('should handle Firebase transaction errors', async () => {
        // Arrange
        const updateFunction = jest.fn();
        const firebaseError = new Error('Firebase transaction failed');
        mockRef.transaction.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(firebaseService.transaction('test/path', updateFunction)).rejects.toThrow('Firebase transaction failed');
      });
    });

    describe('healthCheck', () => {
      it('should return true when Firebase is connected', async () => {
        // Arrange
        const infoRef = {
          once: jest.fn().mockResolvedValue({
            val: () => true,
          }),
        };
        mockDatabase.ref.mockImplementation((path) => {
          if (path === '.info/connected') {
            return infoRef;
          }
          return mockRef;
        });

        // Act
        const result = await firebaseService.healthCheck();

        // Assert
        expect(result).toBe(true);
        expect(mockDatabase.ref).toHaveBeenCalledWith('.info/connected');
      });

      it('should return false when Firebase is not connected', async () => {
        // Arrange
        const infoRef = {
          once: jest.fn().mockResolvedValue({
            val: () => false,
          }),
        };
        mockDatabase.ref.mockImplementation((path) => {
          if (path === '.info/connected') {
            return infoRef;
          }
          return mockRef;
        });

        // Act
        const result = await firebaseService.healthCheck();

        // Assert
        expect(result).toBe(false);
      });

      it('should return false when Firebase health check throws error', async () => {
        // Arrange
        const infoRef = {
          once: jest.fn().mockRejectedValue(new Error('Connection failed')),
        };
        mockDatabase.ref.mockImplementation((path) => {
          if (path === '.info/connected') {
            return infoRef;
          }
          return mockRef;
        });

        // Act
        const result = await firebaseService.healthCheck();

        // Assert
        expect(result).toBe(false);
      });
    });

    describe('getRef', () => {
      it('should return Firebase reference', () => {
        // Act
        const ref = firebaseService.getRef('test/path');

        // Assert
        expect(ref).toBe(mockRef);
        expect(mockDatabase.ref).toHaveBeenCalledWith('test/path');
      });
    });

    describe('getDatabase', () => {
      it('should return Firebase database instance', () => {
        // Act
        const database = firebaseService.getDatabase();

        // Assert
        expect(database).toBe(mockDatabase);
      });
    });

    describe('close', () => {
      it('should close Firebase app successfully', async () => {
        // Arrange
        mockApp.delete.mockResolvedValue(undefined);

        // Act
        await firebaseService.close();

        // Assert
        expect(mockApp.delete).toHaveBeenCalledTimes(1);
      });

      it('should handle close errors', async () => {
        // Arrange
        const closeError = new Error('Close failed');
        mockApp.delete.mockRejectedValue(closeError);

        // Act & Assert
        await expect(firebaseService.close()).rejects.toThrow('Close failed');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle various Firebase error types', async () => {
      // Arrange
      const mockRefWithErrors = {
        once: jest.fn().mockRejectedValue(new Error('PERMISSION_DENIED')),
        set: jest.fn().mockRejectedValue(new Error('NETWORK_ERROR')),
        update: jest.fn().mockRejectedValue(new Error('DATABASE_ERROR')),
      };

      mockDatabase.ref.mockReturnValue(mockRefWithErrors);

      // Act & Assert
      await expect(firebaseService.get('test')).rejects.toThrow('PERMISSION_DENIED');
      await expect(firebaseService.set('test', {})).rejects.toThrow('NETWORK_ERROR');
      await expect(firebaseService.update('test', {})).rejects.toThrow('DATABASE_ERROR');
    });
  });
});