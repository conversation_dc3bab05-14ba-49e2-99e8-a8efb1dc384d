import { ConversationService } from '../../src/conversationService';
import { firebaseService } from '../../src/firebase';
import {
  CreateConversationRequest,
  SendMessageRequest,
  ConversationStatus,
} from '../../src/types';

// Mock Firebase service
jest.mock('../../src/firebase', () => ({
  firebaseService: {
    set: jest.fn(),
    get: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    getRef: jest.fn(),
    transaction: jest.fn(),
    healthCheck: jest.fn(),
  },
}));

describe('ConversationService Edge Cases and Error Scenarios', () => {
  let service: ConversationService;
  const mockFirebase = firebaseService as jest.Mocked<typeof firebaseService>;

  beforeEach(() => {
    service = new ConversationService();
    jest.clearAllMocks();
  });

  describe('Data Validation and Edge Cases', () => {
    describe('createConversation with edge cases', () => {
      it('should handle conversation creation with minimal valid data', async () => {
        // Arrange
        const minimalRequest: CreateConversationRequest = {
          customerId: 'c',
          customer: {
            id: 'c',
            name: 'X',
            phone: '+1',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
        };

        mockFirebase.set.mockResolvedValue(undefined);

        // Act
        const result = await service.createConversation(minimalRequest);

        // Assert
        expect(result.customerId).toBe('c');
        expect(result.customer.name).toBe('X');
        expect(result.priority).toBe(2); // Default priority
        expect(result.source).toBe('direct'); // Default source
      });

      it('should handle conversation creation with all possible fields', async () => {
        // Arrange
        const fullRequest: CreateConversationRequest = {
          customerId: 'customer_with_very_long_id_that_might_cause_issues',
          customer: {
            id: 'customer_with_very_long_id_that_might_cause_issues',
            name: 'John Doe with a Very Long Name That Might Cause Database Issues',
            phone: '+1234567890123456789', // Very long phone
            email: '<EMAIL>',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          priority: 4,
          initialMessage: 'This is a very long initial message that contains special characters: @#$%^&*()_+{}|:"<>?[]\\;\',./ and unicode characters: 🚀💬📞',
          source: 'api',
          departmentId: 'department_with_very_long_name',
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.createConversation(fullRequest);

        // Assert
        expect(result.customerId).toBe(fullRequest.customerId);
        expect(result.priority).toBe(4);
        expect(result.departmentId).toBe('department_with_very_long_name');
        expect(result.routingInfo).toBeDefined();
      });

      it('should handle special characters in conversation data', async () => {
        // Arrange
        const specialCharsRequest: CreateConversationRequest = {
          customerId: '<EMAIL>',
          customer: {
            id: '<EMAIL>',
            name: 'José María González-Pérez',
            phone: '+52-55-1234-5678',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          initialMessage: 'Hola! Tengo un problema con mi cuenta. ¿Podrían ayudarme?',
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.createConversation(specialCharsRequest);

        // Assert
        expect(result.customer.name).toBe('José María González-Pérez');
        expect(result.customerId).toBe('<EMAIL>');
      });
    });

    describe('Message handling edge cases', () => {
      it('should handle extremely long message content', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const longContent = 'A'.repeat(10000); // 10KB message
        const messageRequest: SendMessageRequest = {
          content: longContent,
          senderId: 'agent456',
          senderType: 'agent',
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.sendMessage(conversationId, messageRequest);

        // Assert
        expect(result.content).toBe(longContent);
        expect(result.content.length).toBe(10000);
      });

      it('should handle message with empty content', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: '',
          senderId: 'agent456',
          senderType: 'agent',
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.sendMessage(conversationId, messageRequest);

        // Assert
        expect(result.content).toBe('');
        expect(result.senderId).toBe('agent456');
      });

      it('should handle message with simple content', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: 'This is a simple message without complex features',
          senderId: 'agent456',
          senderType: 'agent',
          messageType: 'text',
        };

        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        const result = await service.sendMessage(conversationId, messageRequest);

        // Assert
        expect(result.content).toBe(messageRequest.content);
        expect(result.type).toBe('text');
        expect(result.senderId).toBe('agent456');
      });

    });

    describe('Query edge cases', () => {
      it('should handle listConversations with zero limit', async () => {
        // Arrange
        const mockQuery = {
          orderByChild: jest.fn().mockReturnThis(),
          limitToLast: jest.fn().mockReturnThis(),
          once: jest.fn().mockResolvedValue({ val: () => ({}) }),
        };

        mockFirebase.getRef.mockReturnValue(mockQuery as any);

        // Act
        const result = await service.listConversations({ limit: 0 });

        // Assert
        expect(result).toEqual([]);
      });

      it('should handle listConversations with very large limit', async () => {
        // Arrange
        const mockQuery = {
          orderByChild: jest.fn().mockReturnThis(),
          limitToLast: jest.fn().mockReturnThis(),
          once: jest.fn().mockResolvedValue({ val: () => ({}) }),
        };

        mockFirebase.getRef.mockReturnValue(mockQuery as any);

        // Act
        const result = await service.listConversations({ limit: 999999 });

        // Assert
        expect(mockQuery.limitToLast).toHaveBeenCalledWith(999999);
      });

      it('should handle getMessages with no messages in conversation', async () => {
        // Arrange
        const conversationId = 'conv_empty';
        const mockQuery = {
          orderByChild: jest.fn().mockReturnThis(),
          limitToLast: jest.fn().mockReturnThis(),
          once: jest.fn().mockResolvedValue({ val: () => null }),
        };

        mockFirebase.getRef.mockReturnValue(mockQuery as any);

        // Act
        const result = await service.getMessages(conversationId);

        // Assert
        expect(result).toEqual([]);
      });
    });

    describe('Transfer edge cases', () => {
      it('should handle transfer to same agent (should still work)', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const agentId = 'agent456';
        const reason = 'Self-transfer test';

        const mockConversation = {
          id: conversationId,
          assignedTo: 'human', assignedAgentId: agentId, // Same agent
          metadata: { transferCount: 5 },
        };

        mockFirebase.get.mockResolvedValue(mockConversation);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        await service.transferConversation(conversationId, agentId, reason);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'transferring',
            'metadata.transferCount': 6,
          })
        );
      });

      it('should handle conversation with existing transfer history', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const targetAgentId = 'agent789';
        const reason = 'Multiple transfers';

        const mockConversation = {
          id: conversationId,
          assignedTo: 'agent456',
          metadata: { transferCount: 0 },
          transferInfo: {
            transferHistory: [
              { id: 'transfer_1', fromAgentId: 'agent111', toAgentId: 'agent222', reason: 'Previous transfer' },
            ],
          },
        };

        mockFirebase.get.mockResolvedValue(mockConversation);
        mockFirebase.update.mockResolvedValue(undefined);
        mockFirebase.set.mockResolvedValue(undefined);
        mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

        // Act
        await service.transferConversation(conversationId, targetAgentId, reason);

        // Assert
        expect(mockFirebase.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            'transferInfo.transferHistory': expect.arrayContaining([
              expect.objectContaining({ reason: 'Previous transfer' }),
              expect.objectContaining({ reason: 'Multiple transfers' }),
            ]),
          })
        );
      });
    });
  });

  describe('Concurrent Operations and Race Conditions', () => {
    it('should handle concurrent message sending to same conversation', async () => {
      // Arrange
      const conversationId = 'conv_123';
      const messageRequests: SendMessageRequest[] = Array(5).fill(null).map((_, i) => ({
        content: `Message ${i}`,
        senderId: `agent_${i}`,
        senderType: 'agent' as const,
      }));

      mockFirebase.set.mockResolvedValue(undefined);
      mockFirebase.update.mockResolvedValue(undefined);
      mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

      // Act
      const results = await Promise.all(
        messageRequests.map(req => service.sendMessage(conversationId, req))
      );

      // Assert
      expect(results).toHaveLength(5);
      results.forEach((result, index) => {
        expect(result.content).toBe(`Message ${index}`);
        expect(result.senderId).toBe(`agent_${index}`);
      });
      expect(mockFirebase.set).toHaveBeenCalledTimes(5);
      expect(mockFirebase.transaction).toHaveBeenCalledTimes(5);
    });

    it('should handle concurrent status updates for same conversation', async () => {
      // Arrange
      const conversationId = 'conv_123';
      const statusUpdates: ConversationStatus[] = ['active', 'waiting', 'transferring', 'active'];

      mockFirebase.update.mockResolvedValue(undefined);

      // Act
      await Promise.all(
        statusUpdates.map(status => 
          service.updateConversation(conversationId, { status })
        )
      );

      // Assert
      expect(mockFirebase.update).toHaveBeenCalledTimes(4);
    });

    it('should handle concurrent typing indicators', async () => {
      // Arrange
      const conversationId = 'conv_123';
      const typingActions = [
        { userId: 'agent1', isTyping: true },
        { userId: 'agent2', isTyping: true },
        { userId: 'customer1', isTyping: true },
        { userId: 'agent1', isTyping: false },
        { userId: 'agent2', isTyping: false },
      ];

      mockFirebase.set.mockResolvedValue(undefined);
      mockFirebase.remove.mockResolvedValue(undefined);

      // Act
      await Promise.all(
        typingActions.map(action => 
          service.setTypingIndicator(
            conversationId, 
            action.userId, 
            action.userId.startsWith('agent') ? 'agent' : 'customer',
            action.isTyping
          )
        )
      );

      // Assert - Should not throw any errors, even if operations are not critical
      expect(mockFirebase.set).toHaveBeenCalledTimes(3); // 3 true values
      expect(mockFirebase.remove).toHaveBeenCalledTimes(2); // 2 false values
    });
  });

  describe('Network and Firebase Error Recovery', () => {
    it('should handle intermittent Firebase failures during conversation creation', async () => {
      // Arrange
      const request: CreateConversationRequest = {
        customerId: 'customer123',
        customer: {
          id: 'customer123',
          name: 'John Doe',
          phone: '+1234567890',
          channel: 'whatsapp',
        },
        channel: 'whatsapp',
      };

      // First call fails, second succeeds
      mockFirebase.set
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockResolvedValueOnce(undefined);

      // Act & Assert
      await expect(service.createConversation(request)).rejects.toMatchObject({
        code: 'CONVERSATION_CREATE_FAILED',
      });

      // Second attempt should work
      const result = await service.createConversation(request);
      expect(result.customerId).toBe('customer123');
    });

    it('should handle Firebase permission errors gracefully', async () => {
      // Arrange
      const conversationId = 'conv_restricted';
      const permissionError = {
        code: 'PERMISSION_DENIED',
        message: 'Client doesn\'t have permission to access the desired data',
      };

      mockFirebase.get.mockRejectedValue(permissionError);

      // Act & Assert
      await expect(service.getConversation(conversationId)).rejects.toMatchObject({
        code: 'CONVERSATION_NOT_FOUND',
        message: `Conversation ${conversationId} not found`,
        details: permissionError,
      });
    });

    it('should handle Firebase offline scenarios', async () => {
      // Arrange
      const offlineError = new Error('Firebase is offline');
      mockFirebase.healthCheck.mockRejectedValue(offlineError);

      // Act
      const isHealthy = await service.healthCheck();

      // Assert
      expect(isHealthy).toBe(false);
    });

    it('should handle malformed Firebase responses', async () => {
      // Arrange
      const conversationId = 'conv_malformed';
      
      // Mock malformed data (should not break the service)
      const mockQuery = {
        orderByChild: jest.fn().mockReturnThis(),
        once: jest.fn().mockResolvedValue({
          val: () => ({
            'invalid_key': 'not_an_object',
            'another_invalid': null,
            'valid_conversation': { id: 'conv_valid', status: 'active' },
          }),
        }),
      };

      mockFirebase.getRef.mockReturnValue(mockQuery as any);

      // Act
      const messages = await service.getMessages(conversationId);

      // Assert
      // Should handle malformed data gracefully
      expect(Array.isArray(messages)).toBe(true);
    });
  });

  describe('Resource Limits and Performance', () => {
    it('should handle creating many conversations in succession', async () => {
      // Arrange
      const conversationRequests = Array(100).fill(null).map((_, i): CreateConversationRequest => ({
        customerId: `customer_${i}`,
        customer: {
          id: `customer_${i}`,
          name: `Customer ${i}`,
          phone: `+123456789${i.toString().padStart(2, '0')}`,
          channel: 'whatsapp',
        },
        channel: 'whatsapp',
      }));

      mockFirebase.set.mockResolvedValue(undefined);

      // Act
      const startTime = Date.now();
      const results = await Promise.all(
        conversationRequests.map(req => service.createConversation(req))
      );
      const endTime = Date.now();

      // Assert
      expect(results).toHaveLength(100);
      results.forEach((result, index) => {
        expect(result.customerId).toBe(`customer_${index}`);
      });

      // Performance check - should complete within reasonable time
      const executionTime = endTime - startTime;
      expect(executionTime).toBeLessThan(5000); // 5 seconds max
    });

    it('should handle agents with high concurrent conversation loads', async () => {
      // Arrange
      const agentId = 'agent_busy';
      const highLoadStatus = {
        status: 'online' as const,
        currentConversations: Array(50).fill(null).map((_, i) => `conv_${i}`),
        maxConcurrentChats: 50,
        isAvailable: true,
      };

      mockFirebase.update.mockResolvedValue(undefined);

      // Act
      await service.updateAgentStatus(agentId, highLoadStatus);

      // Assert
      expect(mockFirebase.update).toHaveBeenCalledWith(
        `agents/${agentId}`,
        expect.objectContaining({
          currentConversations: expect.arrayContaining([
            'conv_0', 'conv_25', 'conv_49'
          ]),
          maxConcurrentChats: 50,
        })
      );
    });
  });

  describe('Data Consistency Edge Cases', () => {
    it('should handle metadata updates when conversation metadata is undefined', async () => {
      // Arrange
      const conversationId = 'conv_no_metadata';
      
      // Mock conversation without metadata
      const mockConversation = {
        id: conversationId,
        customerId: 'customer123',
        // No metadata property
      };

      mockFirebase.get.mockResolvedValue(mockConversation);
      mockFirebase.update.mockResolvedValue(undefined);
      mockFirebase.set.mockResolvedValue(undefined);
      mockFirebase.transaction.mockResolvedValue({ committed: true, snapshot: null });

      const messageRequest: SendMessageRequest = {
        content: 'Test message',
        senderId: 'agent456',
        senderType: 'agent',
      };

      // Act
      const result = await service.sendMessage(conversationId, messageRequest);

      // Assert
      expect(result.content).toBe('Test message');
      // Should handle missing metadata gracefully
      expect(mockFirebase.transaction).toHaveBeenCalled();
    });

    it('should handle notes array that doesn\'t exist', async () => {
      // Arrange
      const conversationId = 'conv_no_notes';
      const noteData = {
        agentId: 'agent456',
        content: 'First note',
        category: 'general' as const,
        priority: 'low' as const,
        visibility: 'private' as const,
      };

      mockFirebase.update.mockResolvedValue(undefined);

      // Act
      const result = await service.addNote(conversationId, noteData);

      // Assert
      expect(result.content).toBe('First note');
      expect(result.id).toMatch(/^note_\d+_[a-z0-9]+$/);
      expect(mockFirebase.update).toHaveBeenCalledWith(
        `conversations/${conversationId}/metadata/notes/${result.id}`,
        result
      );
    });
  });
});