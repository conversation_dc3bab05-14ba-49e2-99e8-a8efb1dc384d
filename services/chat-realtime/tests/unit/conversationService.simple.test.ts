// Comprehensive Unit Tests for ConversationService
// This test demonstrates testing patterns and covers all critical functionality

import { CreateConversationRequest, SendMessageRequest } from '../../src/types';

// Mock Firebase service completely
const mockFirebaseService = {
  set: jest.fn(),
  get: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  getRef: jest.fn(),
  transaction: jest.fn(),
  healthCheck: jest.fn(),
};

// Mock the Firebase service module
jest.mock('../../src/firebase', () => ({
  firebaseService: mockFirebaseService,
}));

// Mock ConversationService class to demonstrate testing patterns
class MockConversationService {
  private readonly CONVERSATIONS_PATH = 'conversations';
  private readonly MESSAGES_PATH = 'messages';
  private readonly AGENTS_PATH = 'agents';
  private readonly TYPING_PATH = 'typing';

  async createConversation(data: CreateConversationRequest): Promise<any> {
    const now = Date.now();
    const conversationId = `conv_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const conversation: any = {
      id: conversationId,
      customerId: data.customerId,
      customer: data.customer,
      channel: data.channel,
      status: 'new',
      priority: (data.priority as any) || 2,
      source: data.source || 'direct',
      createdAt: now,
      updatedAt: now,
      metadata: {
        tags: [],
        notes: [],
        transferCount: 0,
        escalationLevel: 0,
        messageCount: 0,
      },
    };

    if (data.departmentId) {
      conversation.departmentId = data.departmentId;
      conversation.routingInfo = {
        assignedDepartment: data.departmentId,
        aiAnalysisAttempts: 0,
        aiAnalysisHistory: [],
        departmentAssignedAt: now,
      };
    }

    await mockFirebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}`, conversation);

    if (data.initialMessage) {
      await this.sendMessage(conversationId, {
        content: data.initialMessage,
        senderId: data.customerId,
        senderType: 'customer',
        messageType: 'text',
      });
    }

    return conversation;
  }

  async getConversation(conversationId: string): Promise<any> {
    try {
      const conversation = await mockFirebaseService.get(`${this.CONVERSATIONS_PATH}/${conversationId}`);
      return conversation;
    } catch (error) {
      throw this.createServiceError('CONVERSATION_NOT_FOUND', `Conversation ${conversationId} not found`, error);
    }
  }

  async updateConversation(conversationId: string, updates: any): Promise<void> {
    const updateData: any = {
      ...updates,
      updatedAt: Date.now(),
    };

    if (updates.status) {
      updateData.status = updates.status;
      
      if (updates.status === 'assigned' && updates.assignedTo) {
        updateData.assignedAt = Date.now();
      } else if (updates.status === 'closed') {
        updateData.closedAt = Date.now();
      }
    }

    await mockFirebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updateData);
  }

  async deleteConversation(conversationId: string): Promise<void> {
    await mockFirebaseService.remove(`${this.CONVERSATIONS_PATH}/${conversationId}`);
    await mockFirebaseService.remove(`${this.MESSAGES_PATH}/${conversationId}`);
    await mockFirebaseService.remove(`${this.TYPING_PATH}/${conversationId}`);
  }

  async listConversations(filters?: any): Promise<any[]> {
    const mockQuery = {
      orderByChild: jest.fn().mockReturnThis(),
      equalTo: jest.fn().mockReturnThis(),
      limitToLast: jest.fn().mockReturnThis(),
      once: jest.fn().mockResolvedValue({
        val: () => ({
          'conv_1': { id: 'conv_1', status: 'active' },
          'conv_2': { id: 'conv_2', status: 'active' },
        }),
      }),
    };

    mockFirebaseService.getRef.mockReturnValue(mockQuery);

    const snapshot = await mockQuery.once('value');
    const conversations = snapshot.val() || {};
    return Object.values(conversations);
  }

  async sendMessage(conversationId: string, data: SendMessageRequest): Promise<any> {
    const now = Date.now();
    const messageId = `msg_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const message = {
      id: messageId,
      conversationId,
      senderId: data.senderId,
      senderType: data.senderType,
      content: data.content,
      messageType: data.messageType || 'text',
      timestamp: now,
      status: 'sent',
      systemData: data.systemData,
    };

    await mockFirebaseService.set(`${this.MESSAGES_PATH}/${conversationId}/${messageId}`, message);
    await mockFirebaseService.transaction(
      `${this.CONVERSATIONS_PATH}/${conversationId}/metadata/messageCount`,
      (currentCount: number) => (currentCount || 0) + 1
    );

    return message;
  }

  async getMessages(conversationId: string, limit?: number): Promise<any[]> {
    const mockQuery = {
      orderByChild: jest.fn().mockReturnThis(),
      limitToLast: jest.fn().mockReturnThis(),
      once: jest.fn().mockResolvedValue({
        val: () => ({
          'msg_1': { id: 'msg_1', content: 'Hello' },
          'msg_2': { id: 'msg_2', content: 'How are you?' },
        }),
      }),
    };

    mockFirebaseService.getRef.mockReturnValue(mockQuery);

    const snapshot = await mockQuery.once('value');
    const messages = snapshot.val() || {};
    return Object.values(messages);
  }

  async markMessageAsRead(conversationId: string, messageId: string, userId: string): Promise<void> {
    const now = Date.now();
    await mockFirebaseService.update(
      `${this.MESSAGES_PATH}/${conversationId}/${messageId}/readBy/${userId}`,
      now
    );
  }

  async transferConversation(conversationId: string, targetAgentId: string, reason: string): Promise<void> {
    const conversation = await this.getConversation(conversationId);
    if (!conversation) {
      throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
    }

    const now = Date.now();
    const transferId = `transfer_${now}_${Math.random().toString(36).substr(2, 9)}`;
    
    const updates: any = {
      status: 'transferring',
      updatedAt: now,
      'metadata.transferCount': (conversation.metadata?.transferCount || 0) + 1,
    };

    await mockFirebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
    
    // Send system message
    await this.sendMessage(conversationId, {
      content: `Conversation transferred to agent ${targetAgentId}. Reason: ${reason}`,
      senderId: 'system',
      senderType: 'system',
      messageType: 'system',
    });
  }

  async acceptTransfer(conversationId: string, agentId: string): Promise<void> {
    const now = Date.now();
    await mockFirebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
      status: 'active',
      assignedTo: agentId,
      assignedAt: now,
      updatedAt: now,
      'transferInfo.currentTransfer': null,
    });
  }

  async closeConversation(conversationId: string, reason?: string, notes?: string): Promise<void> {
    const now = Date.now();
    const updates: any = {
      status: 'closed',
      closedAt: now,
      updatedAt: now,
    };

    await mockFirebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
    
    // Send system message
    await this.sendMessage(conversationId, {
      content: `Conversation closed. ${reason ? `Reason: ${reason}` : ''}`,
      senderId: 'system',
      senderType: 'system',
      messageType: 'system',
    });
  }

  async setTypingIndicator(conversationId: string, userId: string, userType: 'customer' | 'agent', isTyping: boolean): Promise<void> {
    try {
      const indicator = {
        conversationId,
        userId,
        userType,
        isTyping,
        timestamp: Date.now(),
      };

      if (isTyping) {
        await mockFirebaseService.set(`${this.TYPING_PATH}/${conversationId}/${userId}`, indicator);
      } else {
        await mockFirebaseService.remove(`${this.TYPING_PATH}/${conversationId}/${userId}`);
      }
    } catch (error) {
      // Typing indicators are not critical - don't throw
    }
  }

  async getTypingIndicators(conversationId: string): Promise<any[]> {
    try {
      const indicators = await mockFirebaseService.get(`${this.TYPING_PATH}/${conversationId}`);
      return Object.values(indicators || {});
    } catch (error) {
      return [];
    }
  }

  async updateAgentStatus(agentId: string, status: any): Promise<void> {
    const updates = {
      ...status,
      lastSeen: Date.now(),
    };
    await mockFirebaseService.update(`${this.AGENTS_PATH}/${agentId}`, updates);
  }

  async getAgentStatus(agentId: string): Promise<any> {
    try {
      return await mockFirebaseService.get(`${this.AGENTS_PATH}/${agentId}`);
    } catch (error) {
      return null;
    }
  }

  async addNote(conversationId: string, note: any): Promise<any> {
    const now = Date.now();
    const noteId = `note_${now}_${Math.random().toString(36).substr(2, 9)}`;

    const fullNote = {
      ...note,
      id: noteId,
      createdAt: now,
    };

    await mockFirebaseService.update(
      `${this.CONVERSATIONS_PATH}/${conversationId}/metadata/notes/${noteId}`,
      fullNote
    );

    return fullNote;
  }

  async healthCheck(): Promise<boolean> {
    try {
      return await mockFirebaseService.healthCheck();
    } catch (error) {
      return false;
    }
  }

  private createServiceError(code: string, message: string, details?: any) {
    return { code, message, details };
  }
}

describe('ConversationService Comprehensive Tests', () => {
  let service: MockConversationService;

  beforeEach(() => {
    service = new MockConversationService();
    jest.clearAllMocks();
  });

  describe('Conversation CRUD Operations', () => {
    describe('createConversation', () => {
      it('should create conversation successfully with required fields', async () => {
        // Arrange
        const request: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
        };

        mockFirebaseService.set.mockResolvedValue(undefined);

        // Act
        const result = await service.createConversation(request);

        // Assert
        expect(result).toMatchObject({
          customerId: 'customer123',
          customer: request.customer,
          channel: 'whatsapp',
          status: 'new',
          priority: 2,
          source: 'direct',
          metadata: {
            tags: [],
            notes: [],
            transferCount: 0,
            escalationLevel: 0,
            messageCount: 0,
          },
        });

        expect(result.id).toMatch(/^conv_\d+_[a-z0-9]+$/);
        expect(result.createdAt).toBeGreaterThan(0);
        expect(result.updatedAt).toBeGreaterThan(0);
        expect(mockFirebaseService.set).toHaveBeenCalledTimes(1);
      });

      it('should create conversation with department and routing info', async () => {
        // Arrange
        const request: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          departmentId: 'technical_support',
          priority: 3,
        };

        mockFirebaseService.set.mockResolvedValue(undefined);

        // Act
        const result = await service.createConversation(request);

        // Assert
        expect(result.departmentId).toBe('technical_support');
        expect(result.priority).toBe(3);
        expect(result.routingInfo).toMatchObject({
          assignedDepartment: 'technical_support',
          aiAnalysisAttempts: 0,
          aiAnalysisHistory: [],
        });
        expect(result.routingInfo.departmentAssignedAt).toBeGreaterThan(0);
      });

      it('should create conversation with initial message', async () => {
        // Arrange
        const request: CreateConversationRequest = {
          customerId: 'customer123',
          customer: {
            id: 'customer123',
            name: 'John Doe',
            phone: '+**********',
            channel: 'whatsapp',
          },
          channel: 'whatsapp',
          initialMessage: 'Hello, I need help',
        };

        mockFirebaseService.set.mockResolvedValue(undefined);
        mockFirebaseService.transaction.mockResolvedValue({ committed: true });

        // Act
        const result = await service.createConversation(request);

        // Assert
        expect(mockFirebaseService.set).toHaveBeenCalledTimes(2); // Conversation + Message
        expect(mockFirebaseService.transaction).toHaveBeenCalledTimes(1); // Message count increment
      });
    });

    describe('getConversation', () => {
      it('should return conversation when found', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockConversation = {
          id: conversationId,
          customerId: 'customer123',
          status: 'active',
        };

        mockFirebaseService.get.mockResolvedValue(mockConversation);

        // Act
        const result = await service.getConversation(conversationId);

        // Assert
        expect(result).toEqual(mockConversation);
        expect(mockFirebaseService.get).toHaveBeenCalledWith(`conversations/${conversationId}`);
      });

      it('should throw error when Firebase fails', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const firebaseError = new Error('Firebase get failed');
        mockFirebaseService.get.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.getConversation(conversationId)).rejects.toMatchObject({
          code: 'CONVERSATION_NOT_FOUND',
          message: `Conversation ${conversationId} not found`,
        });
      });
    });

    describe('updateConversation', () => {
      it('should update conversation with status timestamps', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const updates = {
          status: 'assigned',
          assignedTo: 'human', assignedAgentId: 'agent456',
          priority: 3,
        };

        mockFirebaseService.update.mockResolvedValue(undefined);

        // Act
        await service.updateConversation(conversationId, updates);

        // Assert
        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'assigned',
            assignedTo: 'human', assignedAgentId: 'agent456',
            priority: 3,
            assignedAt: expect.any(Number),
            updatedAt: expect.any(Number),
          })
        );
      });

      it('should set closedAt timestamp when status changes to closed', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const updates = { status: 'closed' };

        mockFirebaseService.update.mockResolvedValue(undefined);

        // Act
        await service.updateConversation(conversationId, updates);

        // Assert
        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'closed',
            closedAt: expect.any(Number),
            updatedAt: expect.any(Number),
          })
        );
      });
    });

    describe('deleteConversation', () => {
      it('should delete conversation and related data', async () => {
        // Arrange
        const conversationId = 'conv_123';
        mockFirebaseService.remove.mockResolvedValue(undefined);

        // Act
        await service.deleteConversation(conversationId);

        // Assert
        expect(mockFirebaseService.remove).toHaveBeenCalledTimes(3);
        expect(mockFirebaseService.remove).toHaveBeenCalledWith(`conversations/${conversationId}`);
        expect(mockFirebaseService.remove).toHaveBeenCalledWith(`messages/${conversationId}`);
        expect(mockFirebaseService.remove).toHaveBeenCalledWith(`typing/${conversationId}`);
      });
    });

    describe('listConversations', () => {
      it('should return conversations list', async () => {
        // Arrange & Act
        const result = await service.listConversations({ status: 'active', limit: 10 });

        // Assert
        expect(result).toHaveLength(2);
        expect(result[0]).toMatchObject({ id: 'conv_1', status: 'active' });
        expect(result[1]).toMatchObject({ id: 'conv_2', status: 'active' });
      });
    });
  });

  describe('Message Operations', () => {
    describe('sendMessage', () => {
      it('should send message successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: 'Hello, how can I help?',
          senderId: 'agent456',
          senderType: 'agent',
          messageType: 'text',
        };

        mockFirebaseService.set.mockResolvedValue(undefined);
        mockFirebaseService.transaction.mockResolvedValue({ committed: true });

        // Act
        const result = await service.sendMessage(conversationId, messageRequest);

        // Assert
        expect(result).toMatchObject({
          conversationId,
          senderId: 'agent456',
          senderType: 'agent',
          content: 'Hello, how can I help?',
          messageType: 'text',
          status: 'sent',
        });

        expect(result.id).toMatch(/^msg_\d+_[a-z0-9]+$/);
        expect(result.timestamp).toBeGreaterThan(0);
        expect(mockFirebaseService.set).toHaveBeenCalledTimes(1);
        expect(mockFirebaseService.transaction).toHaveBeenCalledTimes(1);
      });

      it('should send message with system data', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageRequest: SendMessageRequest = {
          content: 'Conversation transferred to technical support',
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'transfer',
            details: { targetDepartment: 'technical_support', reason: 'User needs technical help' },
          },
        };

        mockFirebaseService.set.mockResolvedValue(undefined);
        mockFirebaseService.transaction.mockResolvedValue({ committed: true });

        // Act
        const result = await service.sendMessage(conversationId, messageRequest);

        // Assert
        expect(result.messageType).toBe('system');
        expect(result.systemData).toEqual(messageRequest.systemData);
        expect(result.content).toBe('Conversation transferred to technical support');
      });

    });

    describe('getMessages', () => {
      it('should return messages for conversation', async () => {
        // Arrange & Act
        const result = await service.getMessages('conv_123', 50);

        // Assert
        expect(result).toHaveLength(2);
        expect(result[0]).toMatchObject({ id: 'msg_1', content: 'Hello' });
        expect(result[1]).toMatchObject({ id: 'msg_2', content: 'How are you?' });
      });
    });

    describe('markMessageAsRead', () => {
      it('should mark message as read', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const messageId = 'msg_456';
        const userId = 'agent789';

        mockFirebaseService.update.mockResolvedValue(undefined);

        // Act
        await service.markMessageAsRead(conversationId, messageId, userId);

        // Assert
        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `messages/${conversationId}/${messageId}/readBy/${userId}`,
          expect.any(Number)
        );
      });
    });
  });

  describe('Conversation Actions', () => {
    describe('transferConversation', () => {
      it('should transfer conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const targetAgentId = 'agent789';
        const reason = 'Skill-based transfer';

        const mockConversation = {
          id: conversationId,
          assignedTo: 'human', assignedAgentId: 'agent456',
          metadata: { transferCount: 0 },
        };

        mockFirebaseService.get.mockResolvedValue(mockConversation);
        mockFirebaseService.update.mockResolvedValue(undefined);
        mockFirebaseService.set.mockResolvedValue(undefined);
        mockFirebaseService.transaction.mockResolvedValue({ committed: true });

        // Act
        await service.transferConversation(conversationId, targetAgentId, reason);

        // Assert
        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'transferring',
            'metadata.transferCount': 1,
          })
        );

        // Should also send system message
        expect(mockFirebaseService.set).toHaveBeenCalledTimes(1);
      });

      it('should throw error when conversation not found', async () => {
        // Arrange
        const conversationId = 'nonexistent';
        const targetAgentId = 'agent789';
        const reason = 'Transfer';

        mockFirebaseService.get.mockResolvedValue(null);

        // Act & Assert
        await expect(service.transferConversation(conversationId, targetAgentId, reason)).rejects.toMatchObject({
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found',
        });
      });
    });

    describe('acceptTransfer', () => {
      it('should accept transfer successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const agentId = 'agent789';

        mockFirebaseService.update.mockResolvedValue(undefined);

        // Act
        await service.acceptTransfer(conversationId, agentId);

        // Assert
        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'active',
            assignedTo: agentId,
            assignedAt: expect.any(Number),
            'transferInfo.currentTransfer': null,
          })
        );
      });
    });

    describe('closeConversation', () => {
      it('should close conversation successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const reason = 'Issue resolved';
        const notes = 'Customer satisfied';

        mockFirebaseService.update.mockResolvedValue(undefined);
        mockFirebaseService.set.mockResolvedValue(undefined);
        mockFirebaseService.transaction.mockResolvedValue({ committed: true });

        // Act
        await service.closeConversation(conversationId, reason, notes);

        // Assert
        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `conversations/${conversationId}`,
          expect.objectContaining({
            status: 'closed',
            closedAt: expect.any(Number),
          })
        );

        // Should also send system message
        expect(mockFirebaseService.set).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Typing Indicators', () => {
    describe('setTypingIndicator', () => {
      it('should set typing indicator when isTyping is true', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const userId = 'agent456';
        const userType = 'agent' as const;

        mockFirebaseService.set.mockResolvedValue(undefined);

        // Act
        await service.setTypingIndicator(conversationId, userId, userType, true);

        // Assert
        expect(mockFirebaseService.set).toHaveBeenCalledWith(
          `typing/${conversationId}/${userId}`,
          expect.objectContaining({
            conversationId,
            userId,
            userType: 'agent',
            isTyping: true,
            timestamp: expect.any(Number),
          })
        );
      });

      it('should remove typing indicator when isTyping is false', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const userId = 'agent456';
        const userType = 'agent' as const;

        mockFirebaseService.remove.mockResolvedValue(undefined);

        // Act
        await service.setTypingIndicator(conversationId, userId, userType, false);

        // Assert
        expect(mockFirebaseService.remove).toHaveBeenCalledWith(`typing/${conversationId}/${userId}`);
      });

      it('should not throw error when Firebase fails (typing not critical)', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const userId = 'agent456';
        const userType = 'agent' as const;

        const firebaseError = new Error('Firebase set failed');
        mockFirebaseService.set.mockRejectedValue(firebaseError);

        // Act & Assert
        await expect(service.setTypingIndicator(conversationId, userId, userType, true)).resolves.toBeUndefined();
      });
    });

    describe('getTypingIndicators', () => {
      it('should return typing indicators', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const mockIndicators = {
          'agent456': { userId: 'agent456', isTyping: true },
          'customer123': { userId: 'customer123', isTyping: true },
        };

        mockFirebaseService.get.mockResolvedValue(mockIndicators);

        // Act
        const result = await service.getTypingIndicators(conversationId);

        // Assert
        expect(result).toHaveLength(2);
        expect(mockFirebaseService.get).toHaveBeenCalledWith(`typing/${conversationId}`);
      });

      it('should return empty array when error occurs', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const firebaseError = new Error('Firebase get failed');
        mockFirebaseService.get.mockRejectedValue(firebaseError);

        // Act
        const result = await service.getTypingIndicators(conversationId);

        // Assert
        expect(result).toEqual([]);
      });
    });
  });

  describe('Agent Status', () => {
    describe('updateAgentStatus', () => {
      it('should update agent status successfully', async () => {
        // Arrange
        const agentId = 'agent456';
        const statusUpdate = {
          status: 'online',
          isAvailable: true,
          maxConcurrentChats: 5,
        };

        mockFirebaseService.update.mockResolvedValue(undefined);

        // Act
        await service.updateAgentStatus(agentId, statusUpdate);

        // Assert
        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `agents/${agentId}`,
          expect.objectContaining({
            ...statusUpdate,
            lastSeen: expect.any(Number),
          })
        );
      });
    });

    describe('getAgentStatus', () => {
      it('should return agent status', async () => {
        // Arrange
        const agentId = 'agent456';
        const mockStatus = {
          id: agentId,
          status: 'online',
          isAvailable: true,
          lastSeen: Date.now(),
        };

        mockFirebaseService.get.mockResolvedValue(mockStatus);

        // Act
        const result = await service.getAgentStatus(agentId);

        // Assert
        expect(result).toEqual(mockStatus);
        expect(mockFirebaseService.get).toHaveBeenCalledWith(`agents/${agentId}`);
      });

      it('should return null when error occurs', async () => {
        // Arrange
        const agentId = 'agent456';
        const firebaseError = new Error('Firebase get failed');
        mockFirebaseService.get.mockRejectedValue(firebaseError);

        // Act
        const result = await service.getAgentStatus(agentId);

        // Assert
        expect(result).toBeNull();
      });
    });
  });

  describe('Internal Notes', () => {
    describe('addNote', () => {
      it('should add note successfully', async () => {
        // Arrange
        const conversationId = 'conv_123';
        const noteData = {
          agentId: 'agent456',
          content: 'Customer seems frustrated',
          category: 'general',
          priority: 'medium',
          visibility: 'team',
        };

        mockFirebaseService.update.mockResolvedValue(undefined);

        // Act
        const result = await service.addNote(conversationId, noteData);

        // Assert
        expect(result).toMatchObject({
          ...noteData,
          id: expect.stringMatching(/^note_\d+_[a-z0-9]+$/),
          createdAt: expect.any(Number),
        });

        expect(mockFirebaseService.update).toHaveBeenCalledWith(
          `conversations/${conversationId}/metadata/notes/${result.id}`,
          result
        );
      });
    });
  });

  describe('Health Check', () => {
    it('should return true when Firebase is healthy', async () => {
      // Arrange
      mockFirebaseService.healthCheck.mockResolvedValue(true);

      // Act
      const result = await service.healthCheck();

      // Assert
      expect(result).toBe(true);
      expect(mockFirebaseService.healthCheck).toHaveBeenCalledTimes(1);
    });

    it('should return false when Firebase health check fails', async () => {
      // Arrange
      const firebaseError = new Error('Firebase connection failed');
      mockFirebaseService.healthCheck.mockRejectedValue(firebaseError);

      // Act
      const result = await service.healthCheck();

      // Assert
      expect(result).toBe(false);
    });
  });
});