/**
 * Test if getAgentById is working correctly
 */

require('dotenv').config();
const axios = require('axios');

async function testAgentCheck() {
  console.log('🧪 Testing agent check...');
  
  // Test both agents exist individually
  const juanId = '5f4fb378-908d-4b49-83ce-be4ce3b50c5d';
  const mariaId = '6e3ba129-1008-4d6a-a25d-bdce5d55082c';
  
  try {
    console.log('\n👤 Testing Juan Pérez...');
    const baseUrl = process.env.CHAT_REALTIME_URL || 'http://localhost:3003';
    const juanResponse = await axios.get(`${baseUrl}/api/agents/${juanId}`);
    console.log('✅ Juan found:', {
      name: juanResponse.data.data.profile.name,
      availability: juanResponse.data.data.status.availability
    });
  } catch (error) {
    console.error('❌ Juan not found:', error.response?.data || error.message);
  }
  
  try {
    console.log('\n👤 Testing María González...');
    const mariaResponse = await axios.get(`${baseUrl}/api/agents/${mariaId}`);
    console.log('✅ María found:', {
      name: mariaResponse.data.data.profile.name,
      availability: mariaResponse.data.data.status.availability
    });
  } catch (error) {
    console.error('❌ María not found:', error.response?.data || error.message);
  }
  
  // Test conversation exists
  try {
    console.log('\n💬 Testing conversation...');
    const convResponse = await axios.get(`${baseUrl}/api/conversations/CHaaaaaaaaaaaaaaaaaaaaaaatest01`);
    console.log('✅ Conversation found:', {
      id: convResponse.data.data.id,
      assignedTo: convResponse.data.data.assignedTo,
      assignedAgentId: convResponse.data.data.assignedAgentId,
      status: convResponse.data.data.status
    });
  } catch (error) {
    console.error('❌ Conversation not found:', error.response?.data || error.message);
  }
  
  // Now test the actual transfer
  try {
    console.log('\n🔄 Testing transfer call...');
    const transferResponse = await axios.post(`${baseUrl}/api/conversations/CHaaaaaaaaaaaaaaaaaaaaaaatest01/transfer`, {
      targetAgentId: mariaId,
      reason: 'Direct API test'
    });
    console.log('✅ Transfer successful:', transferResponse.data);
  } catch (error) {
    console.error('❌ Transfer failed:');
    console.error('  Status:', error.response?.status);
    console.error('  Error:', error.response?.data?.error);
    
    // Let's check what's in the server logs by making a simple request
    console.log('\n📝 Checking if the error logs show details...');
  }
}

testAgentCheck().catch(console.error);