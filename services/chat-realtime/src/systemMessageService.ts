import { SYSTEM_MESSAGE_TEMPLATES, SystemMessageTemplate, SystemMessageData, Message } from './types';

export class SystemMessageService {
  constructor() {
    // Use singleton instance
  }

  /**
   * Generate and send a system message
   */
  async generateSystemMessage(
    type: 'welcome' | 'agent_assigned' | 'agent_transferred' | 'conversation_ended' | 'session_ended',
    conversationId: string,
    data: { agentName?: string; agentId?: string; reason?: string } = {}
  ): Promise<Message> {
    const template = SYSTEM_MESSAGE_TEMPLATES[type];
    if (!template) {
      throw new Error(`Unknown system message type: ${type}`);
    }

    // Replace template variables
    let message = template.message;
    if (data.agentName) {
      message = message.replace('{agentName}', data.agentName);
    }

    // Create system message
    const systemMessage: Message = {
      id: this.generateMessageId(),
      conversationId,
      senderId: 'system',
      senderType: 'system',
      content: message,
      type: 'system',
      timestamp: new Date().toISOString(),
      status: 'sent',
      systemData: {
        action: type,
        details: {
          ...data,
          template: type,
          sendToWhatsApp: template.sendToWhatsApp,
          showToAgent: template.showToAgent
        }
      }
    };

    return systemMessage;
  }

  /**
   * Check if system message should be sent to WhatsApp
   */
  shouldSendToWhatsApp(message: Message): boolean {
    return message.systemData?.details?.sendToWhatsApp === true;
  }

  /**
   * Generate a welcome message for new conversations
   */
  async sendWelcomeMessage(conversationId: string): Promise<Message> {
    return await this.generateSystemMessage('welcome', conversationId);
  }

  /**
   * Generate agent assigned message
   */
  async sendAgentAssignedMessage(
    conversationId: string, 
    agentName: string, 
    agentId: string
  ): Promise<Message> {
    return await this.generateSystemMessage('agent_assigned', conversationId, {
      agentName,
      agentId
    });
  }

  /**
   * Generate agent transferred message
   */
  async sendAgentTransferredMessage(
    conversationId: string, 
    agentName: string, 
    agentId: string,
    reason?: string
  ): Promise<Message> {
    return await this.generateSystemMessage('agent_transferred', conversationId, {
      agentName,
      agentId,
      reason
    });
  }

  /**
   * Generate conversation ended message
   */
  async sendConversationEndedMessage(conversationId: string, reason?: string): Promise<Message> {
    return await this.generateSystemMessage('conversation_ended', conversationId, { reason });
  }

  /**
   * Generate session ended message
   */
  async sendSessionEndedMessage(conversationId: string, reason?: string): Promise<Message> {
    return await this.generateSystemMessage('session_ended', conversationId, { reason });
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `sys_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all available system message templates
   */
  getAvailableTemplates(): Record<string, SystemMessageTemplate> {
    return SYSTEM_MESSAGE_TEMPLATES;
  }

  /**
   * Update system message template (for future customization)
   */
  updateTemplate(type: string, template: Partial<SystemMessageTemplate>): void {
    if (SYSTEM_MESSAGE_TEMPLATES[type]) {
      SYSTEM_MESSAGE_TEMPLATES[type] = {
        ...SYSTEM_MESSAGE_TEMPLATES[type],
        ...template
      };
    }
  }
}

export const systemMessageService = new SystemMessageService();