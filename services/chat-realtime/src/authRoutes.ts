// Authentication Routes for Testing
import { Router, Request, Response } from 'express';
import { authService } from './auth';
import { ApiResponse } from './types';

const authRouter = Router();

// Login endpoint for testing
authRouter.post('/login', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_CREDENTIALS',
          message: 'Email and password are required'
        },
        timestamp: Date.now()
      });
    }

    const loginResult = await authService.login(email, password);
    
    res.json({
      success: true,
      data: {
        user: {
          id: loginResult.user.id,
          email: loginResult.user.email,
          emailVerified: loginResult.user.email_confirmed_at != null
        },
        accessToken: loginResult.accessToken,
        expiresAt: loginResult.session?.expires_at
      },
      timestamp: Date.now()
    });
  } catch (error: any) {
    console.error('Login error:', error);
    
    let statusCode = 500;
    let errorCode = 'LOGIN_FAILED';
    
    if (error.message?.includes('Invalid login credentials')) {
      statusCode = 401;
      errorCode = 'INVALID_CREDENTIALS';
    }
    
    res.status(statusCode).json({
      success: false,
      error: {
        code: errorCode,
        message: error.message || 'Login failed'
      },
      timestamp: Date.now()
    });
  }
});

// Logout endpoint
authRouter.post('/logout', async (req: Request, res: Response<ApiResponse>) => {
  try {
    await authService.logout();
    
    res.json({
      success: true,
      data: { message: 'Logged out successfully' },
      timestamp: Date.now()
    });
  } catch (error: any) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'LOGOUT_FAILED',
        message: error.message || 'Logout failed'
      },
      timestamp: Date.now()
    });
  }
});

// Get current user profile
authRouter.get('/profile', authService.authenticate, async (req: any, res: Response<ApiResponse>) => {
  try {
    res.json({
      success: true,
      data: {
        user: {
          id: req.user.id,
          email: req.user.email,
          emailVerified: req.user.email_confirmed_at != null
        },
        profile: req.userProfile
      },
      timestamp: Date.now()
    });
  } catch (error: any) {
    console.error('Profile error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PROFILE_FETCH_FAILED',
        message: error.message || 'Failed to fetch profile'
      },
      timestamp: Date.now()
    });
  }
});

// Verify token endpoint (useful for frontend apps)
authRouter.get('/verify', authService.authenticate, async (req: any, res: Response<ApiResponse>) => {
  try {
    res.json({
      success: true,
      data: {
        valid: true,
        userId: req.user.id,
        role: req.userProfile.role,
        departments: req.userProfile.departments
      },
      timestamp: Date.now()
    });
  } catch (error: any) {
    console.error('Token verification error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'TOKEN_VERIFICATION_FAILED',
        message: error.message || 'Token verification failed'
      },
      timestamp: Date.now()
    });
  }
});

export default authRouter;