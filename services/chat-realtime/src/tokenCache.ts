// Token cache for authentication performance
import { User } from '@supabase/supabase-js';

export interface AgentDepartment {
  departmentId: string;
  departmentName: string;
  isPrimary: boolean;
  skillLevel: number;
}

export interface AgentProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  departments: string[];
  maxConcurrentChats: number;
  departmentDetails: AgentDepartment[];
  // Note: status field removed - now handled in Firebase real-time data
  organizationId: string;
}

interface CachedToken {
  user: User;
  profile: AgentProfile;
  cachedAt: number;
  expiresAt: number;
}

class TokenCache {
  private cache = new Map<string, CachedToken>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  get(token: string): { user: User; profile: AgentProfile } | null {
    const cached = this.cache.get(token);
    
    if (!cached) {
      return null;
    }
    
    // Check if expired
    if (Date.now() > cached.expiresAt) {
      this.cache.delete(token);
      return null;
    }
    
    return {
      user: cached.user,
      profile: cached.profile
    };
  }

  set(token: string, user: User, profile: AgentProfile): void {
    const now = Date.now();
    this.cache.set(token, {
      user,
      profile,
      cachedAt: now,
      expiresAt: now + this.TTL
    });
    
    // Cleanup expired entries periodically
    if (this.cache.size > 100) {
      this.cleanup();
    }
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [token, cached] of this.cache.entries()) {
      if (now > cached.expiresAt) {
        this.cache.delete(token);
      }
    }
  }

  getStats() {
    const now = Date.now();
    let validCount = 0;
    let expiredCount = 0;
    
    for (const cached of this.cache.values()) {
      if (now > cached.expiresAt) {
        expiredCount++;
      } else {
        validCount++;
      }
    }
    
    return {
      total: this.cache.size,
      valid: validCount,
      expired: expiredCount
    };
  }

  clear(): void {
    this.cache.clear();
  }
}

export const tokenCache = new TokenCache();