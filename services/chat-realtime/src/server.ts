import express, { Application, Request, Response, NextFunction } from 'express';
import { CONFIG } from './config';
import routes from './routes';
import logger from './logger';
import { conversationService } from './conversationService';

class ChatRealtimeServer {
  private app: Application;

  constructor() {
    this.app = express();
    this.configureMiddleware();
    this.configureRoutes();
    this.configureErrorHandling();
  }

  private configureMiddleware(): void {
    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // CORS middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      res.header('Access-Control-Allow-Origin', CONFIG.CORS_ORIGIN);
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // Request logging middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const sessionId = req.headers['x-session-id'] as string;
      const startTime = Date.now();
      
      logger.info(`Incoming request: ${req.method} ${req.path}`, {
        method: req.method,
        path: req.path,
        sessionId,
        userAgent: req.headers['user-agent'],
        ip: req.ip
      });
      
      // Log response when finished
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        logger.info(`Request completed: ${req.method} ${req.path}`, {
          method: req.method,
          path: req.path,
          statusCode: res.statusCode,
          duration,
          sessionId
        });
      });
      
      next();
    });

    // Request timeout middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      res.setTimeout(CONFIG.REQUEST_TIMEOUT, () => {
        res.status(408).json({
          success: false,
          error: {
            code: 'REQUEST_TIMEOUT',
            message: 'Request timeout',
          },
          timestamp: Date.now(),
        });
      });
      next();
    });
  }

  private configureRoutes(): void {
    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        service: 'chat-realtime',
        version: CONFIG.VERSION,
        description: 'Chat Realtime service - Exclusive Firebase Realtime Database interface',
        environment: CONFIG.NODE_ENV,
        firebase: {
          useEmulator: CONFIG.FIREBASE.USE_EMULATOR,
          projectId: CONFIG.FIREBASE.PROJECT_ID,
        },
        timestamp: Date.now(),
      });
    });

    // No authentication routes in production service
    // Testing authentication handled by separate E2E scripts

    // API routes
    this.app.use('/api', routes);

    // Catch all route
    this.app.use('*', (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'ROUTE_NOT_FOUND',
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: Date.now(),
      });
    });
  }

  private configureErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      console.error('Global error handler:', error);

      // Don't send error details in production
      const errorDetails = CONFIG.NODE_ENV === 'development' ? error.stack : undefined;

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Internal server error',
          details: errorDetails,
        },
        timestamp: Date.now(),
      });
    });

    // Graceful shutdown handling
    process.on('SIGTERM', () => {
      console.log('SIGTERM received. Shutting down gracefully...');
      this.shutdown();
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received. Shutting down gracefully...');
      this.shutdown();
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error: Error) => {
      console.error('Uncaught Exception:', error);
      this.shutdown(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      this.shutdown(1);
    });
  }

  private shutdown(exitCode: number = 0): void {
    console.log('Performing cleanup before shutdown...');
    
    // Here we could add cleanup logic if needed:
    // - Close Firebase connections
    // - Complete pending operations
    // - Save state if necessary

    setTimeout(() => {
      console.log('Cleanup completed. Exiting...');
      process.exit(exitCode);
    }, 1000);
  }

  private async initializeDepartmentQueues(): Promise<void> {
    try {
      console.log('🔄 Initializing dynamic department queues...');
      await conversationService.syncDepartmentQueues();
      const activeQueues = await conversationService.getActiveQueues();
      console.log(`✅ Department queues initialized: ${activeQueues.length} active queues`);
    } catch (error) {
      console.error('⚠️ Warning: Failed to initialize department queues:', error);
      console.log('🔄 Service will continue with default queue configuration');
    }
  }

  public async start(): Promise<void> {
    try {
      // Initialize Redis connection first
      console.log('🔄 Initializing Chat Realtime Service...');
      await conversationService.initialize();
      
      this.app.listen(CONFIG.PORT, async () => {
        console.log('='.repeat(50));
        console.log('🚀 CHAT REALTIME SERVICE STARTED');
        console.log('='.repeat(50));
        console.log(`🌐 Server: http://localhost:${CONFIG.PORT}`);
        console.log(`📱 Environment: ${CONFIG.NODE_ENV}`);
        console.log(`🔥 Firebase Project: ${CONFIG.FIREBASE.PROJECT_ID}`);
        console.log(`🧪 Using Emulator: ${CONFIG.FIREBASE.USE_EMULATOR ? 'YES' : 'NO'}`);
        if (CONFIG.FIREBASE.USE_EMULATOR) {
          console.log(`🔧 Emulator Host: ${CONFIG.FIREBASE.EMULATOR_HOST}`);
        }
        console.log(`🔗 Redis connection: established`);
        console.log('='.repeat(50));
        
        // Initialize dynamic department queues
        await this.initializeDepartmentQueues();
        
        console.log('📡 Available Endpoints:');
        console.log('   GET  / - Service info');
        console.log('   GET  /api/health - Health check');
        console.log('   POST /api/conversations - Create conversation');
        console.log('   GET  /api/conversations - List conversations');
        console.log('   GET  /api/conversations/:id - Get conversation');
        console.log('   PUT  /api/conversations/:id - Update conversation');
        console.log('   POST /api/conversations/:id/messages - Send message');
        console.log('   GET  /api/conversations/:id/messages - Get messages');
        console.log('   POST /api/conversations/:id/transfer - Transfer conversation');
        console.log('   POST /api/conversations/:id/close - Close conversation');
        console.log('   POST /api/conversations/:id/typing - Set typing indicator');
        console.log('   PUT  /api/agents/:id/status - Update agent status');
        console.log('   GET  /api/agents/:id/status - Get agent status');
        console.log('   POST /api/admin/sync-queues - Sync department queues');
        console.log('='.repeat(50));
      });
    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new ChatRealtimeServer();
  server.start().catch((error) => {
    console.error('Failed to start Chat Realtime Server:', error);
    process.exit(1);
  });
}

export default ChatRealtimeServer;