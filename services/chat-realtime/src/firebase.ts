import * as admin from 'firebase-admin';
import { CONFIG } from './config';

// Import centralized Firebase configuration for consistency
const firebaseConfigJs = require('../firebase-config.js');

class FirebaseService {
  private app: admin.app.App;
  private database: admin.database.Database;

  constructor() {
    this.initializeApp();
    this.database = admin.database(this.app);
  }

  private initializeApp() {
    // Use centralized Firebase configuration if available
    if (firebaseConfigJs && firebaseConfigJs.initializeFirebase) {
      console.log('🔗 Using centralized Firebase configuration');
      this.app = firebaseConfigJs.initializeFirebase();
      return;
    }

    // Fallback to local configuration (for backwards compatibility)
    // Check if Firebase app is already initialized
    if (admin.apps.length > 0) {
      this.app = admin.apps[0] as admin.app.App;
      return;
    }

    if (CONFIG.FIREBASE.USE_EMULATOR) {
      console.log('🔧 Initializing Firebase with Emulator (fallback)');
      
      // Initialize Firebase Admin SDK for emulator
      this.app = admin.initializeApp({
        projectId: CONFIG.FIREBASE.PROJECT_ID,
        databaseURL: `http://${CONFIG.FIREBASE.EMULATOR_HOST}/?ns=${CONFIG.FIREBASE.PROJECT_ID}`,
      });

      // Set emulator host for database
      process.env.FIREBASE_DATABASE_EMULATOR_HOST = CONFIG.FIREBASE.EMULATOR_HOST;
    } else {
      console.log('🚀 Initializing Firebase for Production (fallback)');
      
      const serviceAccountConfig = CONFIG.FIREBASE.SERVICE_ACCOUNT_PATH
        ? require(CONFIG.FIREBASE.SERVICE_ACCOUNT_PATH)
        : {
            // Fallback to environment variables
            projectId: CONFIG.FIREBASE.PROJECT_ID,
            // Add other service account details from env vars if needed
          };

      this.app = admin.initializeApp({
        credential: admin.credential.cert(serviceAccountConfig),
        databaseURL: CONFIG.FIREBASE.DATABASE_URL,
      });
    }

    console.log(`✅ Firebase initialized for project: ${CONFIG.FIREBASE.PROJECT_ID}`);
  }

  public getDatabase(): admin.database.Database {
    return this.database;
  }

  public getRef(path: string): admin.database.Reference {
    return this.database.ref(path);
  }

  // Utility methods for common operations
  public async get(path: string): Promise<any> {
    const snapshot = await this.database.ref(path).once('value');
    return snapshot.val();
  }

  public async set(path: string, data: any): Promise<void> {
    await this.database.ref(path).set(data);
  }

  public async update(path: string, data: any): Promise<void> {
    await this.database.ref(path).update(data);
  }

  public async batchUpdate(updates: Record<string, any>): Promise<void> {
    await this.database.ref().update(updates);
  }

  public async push(path: string, data: any): Promise<string> {
    const ref = await this.database.ref(path).push(data);
    return ref.key!;
  }

  public async remove(path: string): Promise<void> {
    await this.database.ref(path).remove();
  }

  // Query method for filtered searches (mock implementation for Firebase Realtime Database)
  public async query(path: string, filters: Array<{ field: string; operator: string; value: any }>): Promise<any[]> {
    try {
      // Note: Firebase Realtime Database doesn't support complex queries like Firestore
      // This is a simplified implementation that gets all data and filters in memory
      // For production, consider using Firebase Firestore or implement server-side indexing
      
      const snapshot = await this.database.ref(path).once('value');
      const data = snapshot.val();
      
      if (!data) return [];
      
      // Convert to array format
      const items = Object.keys(data).map(key => ({ id: key, ...data[key] }));
      
      // Apply filters in memory (simplified)
      return items.filter(item => {
        return filters.every(filter => {
          const fieldValue = this.getNestedValue(item, filter.field);
          
          switch (filter.operator) {
            case '==':
              return fieldValue === filter.value;
            case '!=':
              return fieldValue !== filter.value;
            case 'in':
              return Array.isArray(filter.value) && filter.value.includes(fieldValue);
            case '>':
              return fieldValue > filter.value;
            case '>=':
              return fieldValue >= filter.value;
            case '<':
              return fieldValue < filter.value;
            case '<=':
              return fieldValue <= filter.value;
            default:
              return true;
          }
        });
      });
    } catch (error) {
      console.error('Query failed:', error);
      return [];
    }
  }

  // Helper method to get nested object values
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }


  // Increment a numeric value at a path
  public async increment(path: string, delta: number = 1): Promise<void> {
    await this.database.ref(path).transaction((current) => {
      return (current || 0) + delta;
    });
  }  // Alias for remove method (delete is a reserved word)
  public async delete(path: string): Promise<void> {
    return this.remove(path);
  }

  public async transaction(path: string, updateFunction: (currentData: any) => any): Promise<any> {
    const result = await this.database.ref(path).transaction(updateFunction);
    return result;
  }

  // Health check method
  public async healthCheck(): Promise<boolean> {
    try {
      const testRef = this.database.ref('.info/connected');
      const snapshot = await testRef.once('value');
      return snapshot.val() === true;
    } catch (error) {
      console.error('Firebase health check failed:', error);
      return false;
    }
  }

  // Close connection
  public async close(): Promise<void> {
    await this.app.delete();
  }
}

// Export singleton instance
export const firebaseService = new FirebaseService();
export default firebaseService;
