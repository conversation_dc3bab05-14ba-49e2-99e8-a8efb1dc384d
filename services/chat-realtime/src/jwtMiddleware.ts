// Minimal JWT Middleware for Chat Realtime Service
// USAGE: For manual testing with curl only - not for production UI
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

export interface AuthenticatedRequest extends Request {
  userId?: string;
  userRole?: string;
  userEmail?: string;
}

/**
 * Minimal JWT validation middleware - FOR TESTING ONLY
 * 
 * To enable for testing specific endpoints:
 * router.put('/endpoint', validateJWT, handler)
 * 
 * To disable (current state):
 * router.put('/endpoint', handler)
 */
export const validateJWT = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_AUTH_TOKEN',
          message: 'Authorization header with Bearer token is required'
        },
        timestamp: Date.now()
      });
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    // For testing purposes, we'll decode without verification
    // In production UI, authentication is handled by Supabase client directly
    const decoded = jwt.decode(token) as any;
    
    if (!decoded || !decoded.sub) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_AUTH_TOKEN',
          message: 'Invalid authentication token'
        },
        timestamp: Date.now()
      });
    }

    // Attach minimal user info to request
    req.userId = decoded.sub;
    req.userEmail = decoded.email;
    req.userRole = decoded.user_metadata?.role || 'agent';
    
    next();
  } catch (error) {
    console.error('JWT validation error:', error);
    return res.status(401).json({
      success: false,
      error: {
        code: 'JWT_VALIDATION_ERROR',
        message: 'Token validation failed'
      },
      timestamp: Date.now()
    });
  }
};

// Helper to conditionally apply JWT middleware based on environment
export const conditionalJWT = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (process.env.ENABLE_JWT_TESTING === 'true') {
    return validateJWT(req, res, next);
  }
  next();
};