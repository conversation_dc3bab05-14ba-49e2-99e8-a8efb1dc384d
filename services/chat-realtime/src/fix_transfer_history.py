import re

# Read the file
with open('conversationService.ts', 'r') as f:
    content = f.read()

# Pattern to match the transferHistory.push object
old_push = '''      transferHistory.push({
        id: transferInfo.id,
        fromAgentId: transferInfo.fromAgentId,
        toAgentId: transferInfo.targetAgentId,
        toDepartmentId: transferInfo.targetDepartmentId,
        reason,
        timestamp: now,
        status: 'rejected',
        rejectedAt: now,
        rejectedBy
      });'''

# New push with required properties
new_push = '''      transferHistory.push({
        id: transferInfo.id,
        // From (source) assignment
        fromAssignedTo: 'human', // Current assignment is human (agent)
        fromAgentId: transferInfo.fromAgentId,
        // To (target) assignment  
        toAssignedTo: transferInfo.targetAssignedTo || 'human', // Required field
        toAgentId: transferInfo.targetAgentId,
        toDepartmentId: transferInfo.targetDepartmentId,
        reason,
        timestamp: now,
        status: 'rejected',
        rejectedAt: now,
        rejectedBy
      });'''

# Replace the content
content = content.replace(old_push, new_push)

# Write back to file
with open('conversationService.ts', 'w') as f:
    f.write(content)

print("Fixed transferHistory.push object")
