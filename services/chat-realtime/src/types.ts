// Firebase Realtime Database Types for CX System

export interface Conversation {
  id: string;
  customerId: string;
  customer: CustomerInfo;
  channel: 'whatsapp' | 'web' | 'email' | 'sms';
  status: ConversationStatus;
  
  // NEW: Bot vs Human assignment architecture
  assignedTo: 'bot' | 'human'; // Type of assignment
  assignedBotId?: string; // Bot ID when assignedTo === 'bot'
  assignedAgentId?: string; // Agent ID when assignedTo === 'human'
  
  departmentId?: string;
  priority: 1 | 2 | 3 | 4; // 1=low, 2=medium, 3=high, 4=urgent
  source: 'bot_transfer' | 'manual' | 'api' | 'direct';
  
  // Timestamps
  createdAt: number;
  updatedAt: number;
  assignedAt?: number;
  closedAt?: number;
  
  // Conversation metadata
  metadata: ConversationMetadata;
  
  // Routing information
  routingInfo?: RoutingInfo;
  
  // Transfer information
  transferInfo?: TransferInfo;
  
  // Supervision functionality
  supervision?: {
    supervisorId?: string;
    mode: 'observe' | 'participate';
    startedAt: number;
    endedAt?: number;
    isActive: boolean;
    isSupervised?: boolean;
    supervisionId?: string;
    status?: string;
    reason?: string;  };

  // Escalation functionality
  escalation?: {
    isEscalated: boolean;
    escalationId?: string;
    escalatedAt?: number;
    escalatedBy?: string;
    reason?: string;
    level?: number;
    originalPriority?: number;
    resolvedAt?: number;
    resolvedBy?: string;
    resolutionReason?: string;
  };
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: 'customer' | 'agent' | 'system' | 'bot' | 'supervisor';
  content: string;
  type: 'text' | 'image' | 'file' | 'system' | 'note'; // Match frontend contract
  timestamp: string; // ISO string format to match frontend
  
  // Message status
  status: MessageStatus;
  readBy?: Record<string, string>; // userId -> ISO timestamp string
  
  // System message data (only for system messages)
  systemData?: SystemMessageData;
}

export interface CustomerInfo {
  id: string;
  name: string;
  email?: string;
  phone: string;
  channel: string;
}

export interface ConversationMetadata {
  subject?: string;
  tags: string[];
  notes: InternalNote[];
  transferCount: number;
  escalationLevel: number;
  department?: string;
  
  // Analytics
  firstResponseTime?: number;
  avgResponseTime?: number;
  messageCount: number;
  
  // Customer info (for compatibility)
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  
  // Escalation fields
  isEscalated?: boolean;
  escalatedConversation?: boolean;
  supervisorQueueEntry?: boolean;
  requiresSupervisorReview?: boolean;
  escalationPriority?: string;
  escalationTimestamp?: number;
  originalPriority?: string | number;
  escalationHistory?: Array<{
    escalationId: string;
    timestamp: number;
    reason: string;
    escalatedBy: string;
    level: number;
  }>;
  
  // Bot and assignment tracking
  botAttempts?: number;
  assignmentHistory?: Array<{
    agentId: string;
    assignedAt: number;
    unassignedAt?: number;
    unassignedBy?: string;
    reason?: string;
  }>;
}

export interface RoutingInfo {
  assignedDepartment?: string;
  aiAnalysisAttempts: number;
  aiAnalysisHistory: Array<{
    attempt: number;
    input: string;
    result: string;
    timestamp: number;
  }>;
  departmentAssignedAt?: number;
}

export interface TransferInfo {
  transferHistory: Array<{
    id: string;
    // From (source) assignment
    fromAssignedTo?: 'bot' | 'human';
    fromBotId?: string;
    fromAgentId?: string;
    // To (target) assignment  
    toAssignedTo: 'bot' | 'human';
    toBotId?: string;
    toAgentId?: string;
    toDepartmentId?: string;
    reason: string;
    timestamp: number;
    status: 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
    rejectedAt?: number;
    rejectedBy?: string;
    completedAt?: number;
    acceptedBy?: string;
    cancelledAt?: number;
    cancelledBy?: string;
  }>;
  currentTransfer?: {
    id: string;
    // Target assignment details
    targetAssignedTo: 'bot' | 'human';
    targetBotId?: string;
    targetAgentId?: string;
    targetDepartmentId?: string;
    reason: string;
    initiatedAt: number;
    status: 'pending' | 'in_progress';
    // Additional properties
    requestedAt?: number;
    // Source assignment details
    fromAssignedTo?: 'bot' | 'human';
    fromBotId?: string;
    fromAgentId?: string;
    transferId?: string;
  };
}

export interface InternalNote {
  id: string;
  agentId: string;
  content: string;
  category: 'general' | 'follow_up' | 'escalation' | 'resolution' | 'customer_info' | 'technical';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  visibility: 'private' | 'team' | 'public';
  createdAt: number;
  updatedAt?: number;
}

export interface AgentStatus {
  id: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  currentConversations: string[]; // conversationIds
  maxConcurrentChats: number;
  isAvailable: boolean;
  lastSeen: number;
  currentActivity?: 'break' | 'lunch' | 'training' | 'admin' | 'coaching';
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  userType: 'customer' | 'agent';
  isTyping: boolean;
  timestamp: number;
}

export type ConversationStatus = 
  | 'new' 
  | 'waiting' 
  | 'pending' 
  | 'pending_acceptance' 
  | 'assigned' 
  | 'active' 
  | 'transferring'
  | 'supervised'
  | 'escalated' 
  | 'closed'
  | 'archived';

export type MessageStatus = 'sent' | 'delivered' | 'read' | 'failed';

export interface SystemMessageData {
  action: 'transfer' | 'assignment' | 'escalation' | 'close' | 'reopen' | 'note_added' 
    | 'unassign' | 'assign' | 'status_change' | 'transfer_accepted' 
    | 'transfer_rejected_reassigned' | 'transfer_rejected' | 'transfer_cancelled' | 'message_correction' 
    | 'conversation_transfer_rejected' | 'conversation_transfer_timeout' 
    | 'conversation_escalated_to_supervisor' | 'agent_performance_note' | 'escalate' 
    | 'supervision_started' | 'supervision_ended' | 'supervision_mode_changed' 
    | 'transfer_rejected_queued' | 'resolve_escalation'
    | 'welcome' | 'agent_assigned' | 'agent_transferred' | 'conversation_ended' | 'session_ended';
  details: Record<string, any>;
}

// System Messages Templates
export interface SystemMessageTemplate {
  type: 'welcome' | 'agent_assigned' | 'agent_transferred' | 'conversation_ended' | 'session_ended';
  message: string;
  sendToWhatsApp: boolean;
  showToAgent: boolean;
}

export const SYSTEM_MESSAGE_TEMPLATES: Record<string, SystemMessageTemplate> = {
  welcome: {
    type: 'welcome',
    message: 'Hola! Un agente estará contigo pronto para ayudarte.',
    sendToWhatsApp: true,
    showToAgent: true
  },
  agent_assigned: {
    type: 'agent_assigned',
    message: 'El chat fue asignado al agente {agentName}.',
    sendToWhatsApp: true,
    showToAgent: true
  },
  agent_transferred: {
    type: 'agent_transferred',
    message: 'El chat fue transferido al agente {agentName}.',
    sendToWhatsApp: true,
    showToAgent: true
  },
  conversation_ended: {
    type: 'conversation_ended',
    message: 'El chat ha sido finalizado. Gracias por contactarnos.',
    sendToWhatsApp: true,
    showToAgent: true
  },
  session_ended: {
    type: 'session_ended',
    message: 'La sesión se ha finalizado.',
    sendToWhatsApp: true,
    showToAgent: true
  }
};

// API Request/Response Types
export interface CreateConversationRequest {
  customerId: string;
  customer: CustomerInfo;
  channel: string;
  priority?: number;
  initialMessage?: string;
  source?: string;
  departmentId?: string;
  webhookData?: WebhookData; // Universal webhook support (Twilio + Direct WhatsApp)
  
  // Legacy support
  twilioData?: TwilioWebhookData; // Backward compatibility
}

// Flexible webhook data interface (Twilio + Direct WhatsApp)
export interface WebhookData {
  // === Twilio Fields ===
  // Conversations API
  ConversationSid?: string;
  ParticipantSid?: string;
  
  // Direct Twilio WhatsApp webhook  
  MessageSid?: string;
  WaId?: string;
  From?: string;
  To?: string;
  ProfileName?: string;
  Body?: string;
  
  // === Direct WhatsApp Business API Fields ===
  // Meta WhatsApp Cloud API / Business API
  object?: 'whatsapp_business_account';
  entry?: WhatsAppEntry[];
  
  // === Common Fields ===
  [key: string]: any; // Allow additional fields from any platform
}

// WhatsApp Business API structure
export interface WhatsAppEntry {
  id: string;
  changes: WhatsAppChange[];
}

export interface WhatsAppChange {
  value: {
    messaging_product: 'whatsapp';
    metadata: {
      display_phone_number: string;
      phone_number_id: string;
    };
    messages?: WhatsAppMessage[];
    statuses?: WhatsAppStatus[];
  };
  field: string;
}

export interface WhatsAppMessage {
  from: string;
  id: string;
  timestamp: string;
  type: 'text' | 'image' | 'audio' | 'video' | 'document' | 'location' | 'contacts';
  text?: {
    body: string;
  };
  image?: {
    caption?: string;
    mime_type: string;
    sha256: string;
    id: string;
  };
  // Add other message types as needed
  context?: {
    from: string;
    id: string;
  };
}

export interface WhatsAppStatus {
  id: string;
  status: 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: string;
  recipient_id: string;
}

// Legacy alias for backward compatibility
export interface TwilioWebhookData extends WebhookData {}

export interface SendMessageRequest {
  content: string;
  senderId: string;
  senderType: 'customer' | 'agent' | 'system' | 'bot' | 'supervisor';
  messageType?: 'text' | 'image' | 'file' | 'system' | 'note'; // Match frontend contract
  systemData?: SystemMessageData; // Only for system messages
  // Additional metadata property that's being used
  metadata?: Record<string, any>;
}

export interface UpdateConversationRequest {
  status?: ConversationStatus;
  // NEW: Bot vs Human assignment
  assignedTo?: 'bot' | 'human';
  assignedBotId?: string;
  assignedAgentId?: string;
  departmentId?: string;
  priority?: number;
  tags?: string[];
}

// Error Types
export interface ServiceError {
  code: string;
  message: string;
  details?: any;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ServiceError;
  timestamp: number;
}
