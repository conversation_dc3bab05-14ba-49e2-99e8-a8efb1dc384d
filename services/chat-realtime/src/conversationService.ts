import { firebaseService } from './firebase';
import * as admin from 'firebase-admin';
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { pubsubService, OutboundMessage } from './pubsubService';
import { systemMessageService } from './systemMessageService';
import { RedisService } from './redisService';
import {
  Conversation,
  Message,
  CreateConversationRequest,
  SendMessageRequest,
  UpdateConversationRequest,
  ConversationStatus,
  AgentStatus,
  TypingIndicator,
  InternalNote,
  ServiceError,
} from './types';

export class ConversationService {
  private readonly CONVERSATIONS_PATH = 'conversations';
  private readonly MESSAGES_PATH = 'messages';
  private readonly AGENTS_PATH = 'agents';
  private readonly AGENT_STATUS_PATH = 'agent_status';
  private readonly TYPING_PATH = 'typing';
  private readonly QUEUES_PATH = 'queues';
  private readonly SUPABASE_TIMEOUT = parseInt(process.env.SUPABASE_TIMEOUT || '5000');
  private supabase: SupabaseClient;
  private redisService: RedisService;

  constructor() {
    // Initialize Supabase client for department queries
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
    
    if (!supabaseUrl || !supabaseAnonKey) {
      console.warn('Supabase credentials not found - department sync will be disabled');
    }
    
    this.supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: {
        fetch: (url, options = {}) => {
          return fetch(url, {
            ...options,
            signal: AbortSignal.timeout(this.SUPABASE_TIMEOUT)
          });
        }
      }
    });

    // Initialize Redis for conversation state management
    this.redisService = RedisService.getInstance();
  }

  /**
   * Initialize Chat Realtime with Redis connection
   */
  async initialize(): Promise<void> {
    try {
      await this.redisService.connect();
      console.log('✅ Chat Realtime Service initialized with Redis');
    } catch (error) {
      console.error('❌ Failed to initialize Chat Realtime Service:', error);
      throw error;
    }
  }

  // Helper method to add timeout to Supabase queries
  private withTimeout<T>(postgrestBuilder: any, timeoutMs: number = this.SUPABASE_TIMEOUT): Promise<T> {
    const promise = postgrestBuilder as Promise<T>;
    return Promise.race([
      promise,
      new Promise<T>((_, reject) => 
        setTimeout(() => reject(new Error('Supabase query timeout')), timeoutMs)
      )
    ]);
  }

  // ========================================
  // INTELLIGENT MESSAGE PROCESSING (CORE)
  // ========================================

  async processMessage(messageData: any): Promise<any> {
    try {
      console.log('🧠 Processing message intelligently:', messageData);
      
      // Extract message details
      const { id, from, body, channel, sessionId, metadata, decisionInfo } = messageData;
      
      // Check if Bot Human Router has already made a decision
      if (decisionInfo) {
        console.log('🤖 Decision Engine info received:', decisionInfo);
      }
      
      // Step 1: Find or create conversation
      let conversationId = await this.findOrCreateConversation(from, channel, sessionId, decisionInfo);
      
      // Step 2: Store the incoming message
      const customerMessage = await this.storeCustomerMessage(conversationId, {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
        body,
        from,
        channel,
        metadata
      });
      
      // Step 3: Analyze message and determine action
      const analysis = await this.analyzeMessageAndDecideAction(conversationId, body, from, decisionInfo);
      
      // Step 4: Execute the decided action
      const actionResult = await this.executeAction(conversationId, analysis, messageData);
      
      // Step 5: Prepare response according to API contract
      const response = {
        processed: true,
        conversationId,
        action: analysis.action,
        response: actionResult.response || null,
        conversation: {
          id: conversationId,
          status: actionResult.conversationStatus,
          departmentId: analysis.departmentId,
          agentId: actionResult.agentId || null,
          queuePosition: actionResult.queuePosition || null
        },
        processing: {
          aiAnalysis: analysis.aiAnalysis,
          botAttempts: analysis.botAttempts || 0,
          escalationReason: analysis.escalationReason || null
        }
      };
      
      console.log('✅ Message processed successfully:', response.action);
      return response;
      
    } catch (error) {
      console.error('❌ Error processing message:', error);
      throw this.createServiceError('MESSAGE_PROCESSING_FAILED', 'Failed to process message intelligently', error);
    }
  }
  
  private async findOrCreateConversation(customerId: string, channel: string, sessionId: string, decisionInfo?: any): Promise<string> {
    try {
      // First, check if there's an active conversation for this customer
      const existingConversation = await this.findActiveConversationByCustomer(customerId);
      
      if (existingConversation) {
        console.log('📞 Found existing active conversation:', existingConversation.id);
        return existingConversation.id;
      }
      
      // Create new conversation
      console.log('🆕 Creating new conversation for customer:', customerId);
      
      const conversationData = {
        customerId,
        customer: {
          id: customerId,
          name: `Customer ${customerId.split(':').pop() || customerId}`,
          phone: customerId, // Using customerId as phone for now
          channel
        },
        channel,
        // Apply Decision Engine results if available
        ...(decisionInfo && {
          assignedTo: decisionInfo.assignedTo,
          assignedBotId: decisionInfo.assignedBotId,
          departmentId: decisionInfo.departmentId
        })
      };      
      const newConversation = await this.createConversation(conversationData);
      return newConversation.id;
      
    } catch (error) {
      console.error('Error finding/creating conversation:', error);
      throw error;
    }
  }
  
  private async findActiveConversationByCustomer(customerId: string): Promise<Conversation | null> {
    try {
      const conversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'customerId', operator: '==', value: customerId },
        { field: 'status', operator: 'in', value: ['new', 'queued', 'active'] }
      ]);
      
      // Return the most recent active conversation
      if (conversations && conversations.length > 0) {
        const sorted = conversations.sort((a, b) => b.updatedAt - a.updatedAt);
        return sorted[0] as Conversation;
      }
      
      return null;
    } catch (error) {
      console.error('Error finding active conversation:', error);
      return null;
    }
  }
  
  private async storeCustomerMessage(conversationId: string, messageData: any): Promise<any> {
    try {
      const message = await this.sendMessage(conversationId, {
        content: messageData.body,
        senderId: messageData.from,
        senderType: 'customer',
        messageType: 'text',
        metadata: messageData.metadata
      });
      
      // Update conversation last activity
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        updatedAt: Date.now()
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/indicators`, {
        customerLastSeen: Date.now()
      });      
      return message;
    } catch (error) {
      console.error('Error storing customer message:', error);
      throw error;
    }
  }
  
  private async analyzeMessageAndDecideAction(conversationId: string, messageBody: string, customerId: string, decisionInfo?: any): Promise<any> {
    try {
      // Get conversation context
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }
      
      console.log('🎯 Executing decision from Bot Human Router...');
      
      // Simple fallback analysis - BHR should provide real decision
      const analysis = { department: 'general', needsHuman: true };
      
      // Determine action based on Decision Engine or fallback to analysis
      let action: 'bot_response' | 'agent_assigned' | 'queued' | 'transferred' | 'escalated';
      let departmentId = analysis.department || 'general';
      
      // ✅ EXECUTE Bot Human Router decision
      if (decisionInfo) {
        departmentId = decisionInfo.departmentId || departmentId;
        
        if (decisionInfo.assignedTo === 'bot') {
          action = 'bot_response'; // N8N will handle via endpoints
          console.log('🤖 Bot Human Router → Bot assigned (N8N handles response)');
        } else if (decisionInfo.assignedTo === 'human') {
          action = 'queued'; // Assign to human agent
          console.log('👤 Bot Human Router → Human assigned');
        } else {
          action = 'queued'; // Default to human
        }
      } else {
        // 📊 FALLBACK: When BHR doesn't provide decision
        if (conversation.status === 'active' && conversation.assignedTo) {
          action = 'agent_assigned'; // Forward to assigned agent
        } else {
          action = 'queued'; // Default: assign to human
        }
      }
      
      return {
        action,
        departmentId,
        aiAnalysis: {
          department: analysis.department,
          needsHuman: analysis.needsHuman
        },
        botAttempts: conversation.metadata?.botAttempts || 0
      };
      
    } catch (error) {
      console.error('Error analyzing message:', error);
      throw error;
    }
  }

  private calculateResponseTimes(messages: any[], conversationCreatedAt: number): { firstResponse: number, averageResponse: number } {
    const customerMessages = messages
      .filter(m => m.senderType === 'customer')
      .map(m => ({
        ...m,
        timestamp: typeof m.timestamp === 'string' ? new Date(m.timestamp).getTime() : m.timestamp
      }))
      .sort((a, b) => a.timestamp - b.timestamp);

    const agentMessages = messages
      .filter(m => m.senderType === 'agent' || m.senderType === 'bot')
      .map(m => ({
        ...m,
        timestamp: typeof m.timestamp === 'string' ? new Date(m.timestamp).getTime() : m.timestamp
      }))
      .sort((a, b) => a.timestamp - b.timestamp);

    if (customerMessages.length === 0 || agentMessages.length === 0) {
      return { firstResponse: 0, averageResponse: 0 };
    }

    // First response time: time from conversation start (or first customer message) to first agent response
    const conversationStart = conversationCreatedAt || customerMessages[0].timestamp;
    const firstAgentMessage = agentMessages[0];
    const firstResponse = Math.floor((firstAgentMessage.timestamp - conversationStart) / 1000); // seconds

    // Average response time: average time between customer messages and subsequent agent responses
    const responseTimes: number[] = [];
    
    customerMessages.forEach(customerMsg => {
      // Find next agent message after this customer message
      const nextAgentMsg = agentMessages.find(agentMsg => agentMsg.timestamp > customerMsg.timestamp);
      if (nextAgentMsg) {
        const responseTime = Math.floor((nextAgentMsg.timestamp - customerMsg.timestamp) / 1000);
        responseTimes.push(responseTime);
      }
    });

    const averageResponse = responseTimes.length > 0 
      ? Math.floor(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length)
      : firstResponse;

    console.log(`📊 Response Times: first=${firstResponse}s, average=${averageResponse}s (${responseTimes.length} interactions)`);
    
    return { 
      firstResponse: Math.max(0, firstResponse), 
      averageResponse: Math.max(0, averageResponse) 
    };
  }
  
  
  private async executeAction(conversationId: string, analysis: any, originalMessage: any): Promise<any> {
    try {
      const action = analysis.action;
      let result: any = {
        conversationStatus: 'new',
        response: null,
        agentId: null,
        queuePosition: null
      };
      
      switch (action) {
        case 'bot_response':
          // ❌ OBSOLETO: N8N maneja respuestas de bot directamente vía endpoints
          console.log('⚠️  Bot response should be handled by N8N, not Chat Realtime');
          result.conversationStatus = 'active';
          break;
          
        case 'queued':
          result = await this.handleQueueAssignment(conversationId, analysis.departmentId);
          break;
          
        case 'agent_assigned':
          result = await this.handleAgentMessage(conversationId, originalMessage);
          break;
          
        case 'escalated':
          result = await this.handleEscalation(conversationId, analysis);
          break;
          
        default:
          console.warn('Unknown action:', action);
          result.conversationStatus = 'new';
      }
      
      return result;
    } catch (error) {
      console.error('Error executing action:', error);
      throw error;
    }
  }
  
  
  
  private async handleQueueAssignment(conversationId: string, departmentId: string): Promise<any> {
    try {
      console.log(`📋 Adding conversation ${conversationId} to ${departmentId} queue`);
      
      // Try to find available agent first
      const availableAgent = await this.findAvailableAgent(departmentId);
      
      if (availableAgent) {
        // Assign directly to available agent
        await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
          status: 'active',
          assignedTo: 'human',
          assignedAgentId: availableAgent.id,
          assignedAt: Date.now(),
          departmentId,
          updatedAt: Date.now()
        });
        
        console.log(`✅ Assigned directly to agent ${availableAgent.id}`);
        return {
          conversationStatus: 'active',
          assignedAgentId: availableAgent.id,
          agentId: availableAgent.id // Keep for backward compatibility
        };
      } else {
        // Add to queue
        const queuePosition = await this.addToQueue(conversationId, departmentId);
        
        await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
          status: 'queued',
          departmentId,
          updatedAt: Date.now()
        });
        
        console.log(`📋 Added to queue at position ${queuePosition}`);
        return {
          conversationStatus: 'queued',
          queuePosition
        };
      }
    } catch (error) {
      console.error('Error handling queue assignment:', error);
      throw error;
    }
  }
  
  
  private async addToQueue(conversationId: string, departmentId: string): Promise<number> {
    try {
      const queuePath = `queues/${departmentId}/conversations`;
      
      // Add to queue
      await firebaseService.set(`${queuePath}/${conversationId}`, {
        addedAt: Date.now(),
        priority: 'normal'
      });
      
      // Update queue statistics
      await firebaseService.update(`queues/${departmentId}/statistics/current`, {
        waitingCount: admin.database.ServerValue.increment(1),
        lastUpdated: Date.now()
      });
      
      // Return position in queue (simplified)
      const queueConversations = await firebaseService.get(queuePath);
      return queueConversations ? Object.keys(queueConversations).length : 1;
      
    } catch (error) {
      console.error('Error adding to queue:', error);
      throw error;
    }
  }
  
  private async handleAgentMessage(conversationId: string, originalMessage: any): Promise<any> {
    try {
      // For active conversations, just acknowledge message received
      console.log(`💬 Message forwarded to active conversation ${conversationId}`);
      
      return {
        conversationStatus: 'active'
      };
    } catch (error) {
      console.error('Error handling agent message:', error);
      throw error;
    }
  }
  
  private async handleEscalation(conversationId: string, analysis: any): Promise<any> {
    try {
      console.log(`⚠️ Auto-escalating conversation ${conversationId} based on analysis`);
      
      // Auto-escalate based on AI analysis
      await this.escalateConversation(conversationId, {
        reason: analysis.escalationReason || 'Conversation requires supervisor attention',
        priority: 'urgent',
        escalatedBy: 'system',
        notes: `Auto-escalated based on AI analysis. Intent: ${analysis.intent}`
      });
      
      return {
        conversationStatus: 'escalated'
      };
    } catch (error) {
      console.error('Error handling escalation:', error);
      throw error;
    }
  }

  /**
   * Get last message from agent for supervisor context
   */
  private async getLastAgentMessage(conversationId: string): Promise<string | null> {
    try {
      const messagesSnapshot = await firebaseService.get(`${this.CONVERSATIONS_PATH}/${conversationId}/messages`);
      const messages = messagesSnapshot ? Object.values(messagesSnapshot) : [];
      
      // Find last agent message
      const sortedMessages = (messages as any[]).sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );
      
      const lastAgentMessage = sortedMessages
        .reverse()
        .find((msg: any) => msg.senderType === 'agent');
      
      return lastAgentMessage?.content || null;
    } catch (error) {
      console.error('Error getting last agent message:', error);
      return null;
    }
  }

  /**
   * Add conversation to supervisor queue for monitoring
   */
  private async addToSupervisorQueue(conversationId: string, queueData: any): Promise<void> {
    try {
      const queueEntry = {
        conversationId,
        addedAt: Date.now(),
        addedAtTimestamp: new Date().toISOString(),
        status: 'pending_review',
        ...queueData
      };

      // Add to supervisor queue in Firebase
      await firebaseService.set(`supervisorQueue/${conversationId}`, queueEntry);
      
      console.log(`📋 Added conversation ${conversationId} to supervisor queue:`, {
        escalationId: queueData.escalationId,
        priority: queueData.priority,
        level: queueData.level
      });
      
      // TODO: Send push notification to supervisors
      // await this.notifySupervisors(queueEntry);
      
    } catch (error) {
      console.error('Error adding to supervisor queue:', error);
      throw error;
    }
  }

  /**
   * Get supervisor queue - all escalated conversations waiting for review
   */
  async getSupervisorQueue(filters: any = {}): Promise<any> {
    try {
      console.log('📋 Getting supervisor queue with filters:', filters);
      
      const { 
        priority, 
        limit = 50, 
        status = 'pending_review',
        sortBy = 'urgency' 
      } = filters;

      // Get supervisor queue entries
      const queueSnapshot = await firebaseService.get('supervisorQueue');
      const queueEntries = queueSnapshot ? Object.values(queueSnapshot) : [];
      
      // Filter and sort
      let filteredEntries = (queueEntries as any[]).filter((entry: any) => {
        if (status && entry.status !== status) return false;
        if (priority && entry.priority !== priority) return false;
        return true;
      });

      // Sort by urgency (urgent first, then by escalation level, then by timestamp)
      if (sortBy === 'urgency') {
        filteredEntries.sort((a, b) => {
          // Urgent conversations first
          if (a.urgent !== b.urgent) {
            return b.urgent ? 1 : -1;
          }
          // Then by escalation level (higher level = more urgent)
          if (a.level !== b.level) {
            return b.level - a.level;
          }
          // Finally by timestamp (newer first)
          return b.timestamp - a.timestamp;
        });
      }

      // Apply limit
      const limitedEntries = filteredEntries.slice(0, limit);

      return {
        success: true,
        data: {
          queue: limitedEntries,
          total: filteredEntries.length,
          summary: {
            totalPending: filteredEntries.length,
            urgent: filteredEntries.filter((e: any) => e.urgent).length,
            levels: {
              level1: filteredEntries.filter((e: any) => e.level === 1).length,
              level2: filteredEntries.filter((e: any) => e.level === 2).length,
              level3: filteredEntries.filter((e: any) => e.level >= 3).length,
            },
            priorities: {
              urgent: filteredEntries.filter((e: any) => e.priority === 'urgent').length,
              critical: filteredEntries.filter((e: any) => e.priority === 'critical').length,
            }
          }
        },
        timestamp: Date.now()
      };

    } catch (error) {
      console.error('Error getting supervisor queue:', error);
      throw this.createServiceError('SUPERVISOR_QUEUE_ERROR', error.message);
    }
  }

  /**
   * Get escalated conversations with full conversation data for supervisor dashboard
   */
  async getEscalatedConversations(filters: any = {}): Promise<any> {
    try {
      console.log('📋 Getting escalated conversations with filters:', filters);
      
      const { 
        priority, 
        level,
        limit = 20,
        includeMessages = false 
      } = filters;

      // Get all conversations
      const conversationsSnapshot = await firebaseService.get(this.CONVERSATIONS_PATH);
      const conversations = conversationsSnapshot ? Object.values(conversationsSnapshot) : [];
      
      // Filter for escalated conversations
      let escalatedConversations = (conversations as any[]).filter((conv: any) => {
        // Must be escalated status
        if (conv.status !== 'escalated') return false;
        
        // Must have escalation metadata
        if (!conv.metadata?.isEscalated) return false;
        
        // Apply filters
        if (priority && conv.escalation?.priority !== priority) return false;
        if (level && conv.escalation?.level !== level) return false;
        
        return true;
      });

      // Sort by escalation priority and timestamp
      escalatedConversations.sort((a, b) => {
        // Urgent priority first
        const priorityOrder = { critical: 5, urgent: 4, high: 3, medium: 2, low: 1 };
        const aPriority = priorityOrder[a.escalation?.priority] || 1;
        const bPriority = priorityOrder[b.escalation?.priority] || 1;
        
        if (aPriority !== bPriority) {
          return bPriority - aPriority;
        }
        
        // Then by escalation level
        if (a.escalation?.level !== b.escalation?.level) {
          return (b.escalation?.level || 0) - (a.escalation?.level || 0);
        }
        
        // Finally by escalation timestamp (newer first)
        return (b.escalation?.escalatedAt || 0) - (a.escalation?.escalatedAt || 0);
      });

      // Apply limit and format response
      const limitedConversations = escalatedConversations.slice(0, limit);
      
      const formattedConversations = limitedConversations.map((conv: any) => {
        const formatted: any = {
          id: conv.id,
          status: conv.status,
          priority: conv.priority,
          customer: conv.customer,
          assignedAgentId: conv.assignedAgentId,
          assignedTo: conv.assignedTo,
          createdAt: conv.createdAt,
          updatedAt: conv.updatedAt,
          escalation: conv.escalation,
          metadata: {
            escalationLevel: conv.metadata?.escalationLevel,
            isEscalated: conv.metadata?.isEscalated,
            escalationPriority: conv.metadata?.escalationPriority,
            escalationTimestamp: conv.metadata?.escalationTimestamp,
            requiresSupervisorReview: conv.metadata?.requiresSupervisorReview
          }
        };

        // Include messages if requested
        if (includeMessages && conv.messages) {
          const messages = Object.values(conv.messages);
          // Sort messages by timestamp and get last 10
          const sortedMessages = (messages as any[]).sort((a, b) => 
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
          formatted.recentMessages = sortedMessages.slice(-10);
        }

        return formatted;
      });

      return {
        success: true,
        data: {
          conversations: formattedConversations,
          total: escalatedConversations.length,
          summary: {
            totalEscalated: escalatedConversations.length,
            byPriority: {
              critical: escalatedConversations.filter((c: any) => c.escalation?.priority === 'critical').length,
              urgent: escalatedConversations.filter((c: any) => c.escalation?.priority === 'urgent').length,
              high: escalatedConversations.filter((c: any) => c.escalation?.priority === 'high').length,
            },
            byLevel: {
              level1: escalatedConversations.filter((c: any) => c.escalation?.level === 1).length,
              level2: escalatedConversations.filter((c: any) => c.escalation?.level === 2).length,
              level3Plus: escalatedConversations.filter((c: any) => (c.escalation?.level || 0) >= 3).length,
            },
            averageEscalationTime: this.calculateAverageEscalationTime(escalatedConversations)
          }
        },
        timestamp: Date.now()
      };

    } catch (error) {
      console.error('Error getting escalated conversations:', error);
      throw this.createServiceError('ESCALATIONS_ERROR', error.message);
    }
  }

  /**
   * Calculate average time conversations stay in escalated state
   */
  private calculateAverageEscalationTime(escalatedConversations: any[]): number {
    if (escalatedConversations.length === 0) return 0;
    
    const now = Date.now();
    const totalTime = escalatedConversations.reduce((sum, conv) => {
      const escalatedAt = conv.escalation?.escalatedAt || conv.updatedAt || now;
      return sum + (now - escalatedAt);
    }, 0);
    
    return Math.round(totalTime / escalatedConversations.length / 1000 / 60); // Return in minutes
  }

  /**
   * Resolve escalation - return conversation to normal priority and remove from supervisor queue
   */
  async resolveEscalation(conversationId: string, resolutionData: any): Promise<any> {
    try {
      console.log(`🔧 Resolving escalation for conversation ${conversationId}`);
      
      const { reason, resolvedBy, resolvedAt } = resolutionData;
      const now = resolvedAt || Date.now();
      
      // Get current conversation
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      // Check if conversation is actually escalated
      if (conversation.status !== 'escalated') {
        throw this.createServiceError('CONVERSATION_NOT_ESCALATED', 'Conversation is not currently escalated');
      }
      
      if (!conversation.escalation || !conversation.metadata?.isEscalated) {
        throw this.createServiceError('NO_ESCALATION_DATA', 'No escalation data found for this conversation');
      }
      
      // Calculate escalation duration
      const escalationDuration = now - (conversation.escalation.escalatedAt || now);
      const escalationDurationMinutes = Math.round(escalationDuration / 1000 / 60);
      
      console.log(`📊 Escalation resolution details:`, {
        conversationId,
        originalPriority: conversation.escalation.originalPriority,
        escalationLevel: conversation.escalation.level,
        durationMinutes: escalationDurationMinutes,
        resolvedBy
      });
      
      // Update conversation - return to normal state
      const updates: any = {
        status: 'active' as ConversationStatus,
        priority: 2, // Return to normal priority (2 = medium/normal)
        updatedAt: now,
      };

      // Update escalation data - mark as resolved but keep history
      updates.escalation = {
        ...conversation.escalation,
        status: 'resolved',
        resolvedAt: now,
        resolvedBy,
        resolutionReason: reason,
        escalationDuration: escalationDuration,
        escalationDurationMinutes
      };

      // Update metadata - remove escalation flags
      updates.metadata = {
        ...(conversation.metadata || {}),
        
        // Remove escalation flags
        isEscalated: false,
        escalatedConversation: false,
        supervisorQueueEntry: false,
        requiresSupervisorReview: false,
        
        // Mark as resolved
        escalationResolved: true,
        escalationResolvedAt: now,
        escalationResolvedBy: resolvedBy,
        
        // Update escalation history
        escalationHistory: [
          ...(conversation.metadata?.escalationHistory || []),
          {
            escalationId: conversation.escalation.escalationId,
            timestamp: now,
            action: 'resolved',
            reason,
            resolvedBy,
            durationMinutes: escalationDurationMinutes,
            level: conversation.escalation.level
          }
        ]
      };
      
      // Apply updates to conversation
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      
      // Remove from supervisor queue
      try {
        await firebaseService.remove(`supervisorQueue/${conversationId}`);
        console.log(`🗑️ Removed conversation ${conversationId} from supervisor queue`);
      } catch (error) {
        console.error('Error removing from supervisor queue:', error);
        // Don't fail resolution if queue removal fails
      }
      
      // Send resolution system message
      await this.sendMessage(conversationId, {
        content: `✅ Escalación resuelta. Motivo: ${reason}\n\nLa conversación ha vuelto a prioridad normal y se ha removido de la cola de supervisores. Duración de escalación: ${escalationDurationMinutes} minutos.`,
        senderId: 'system',
        senderType: 'system',
        messageType: 'system',
        systemData: {
          action: 'resolve_escalation',
          details: { 
            escalationId: conversation.escalation.escalationId,
            reason,
            resolvedBy,
            durationMinutes: escalationDurationMinutes,
            newPriority: 'normal',
            newStatus: 'active'
          }
        }
      });
      
      console.log(`✅ Escalation resolved for conversation ${conversationId} by ${resolvedBy}`);
      
      return {
        success: true,
        data: {
          conversationId,
          escalationId: conversation.escalation.escalationId,
          previousStatus: 'escalated',
          newStatus: 'active',
          previousPriority: conversation.priority,
          newPriority: 2,
          resolvedBy,
          resolvedAt: new Date(now).toISOString(),
          escalationDuration: escalationDurationMinutes,
          resolutionReason: reason
        },
        timestamp: now
      };
      
    } catch (error) {
      console.error('Error resolving escalation:', error);
      throw error;
    }
  }

  // ========================================
  // AGENT ASSIGNMENT OPERATIONS
  // ========================================

  async assignAgent(conversationId: string, assignmentData: any): Promise<any> {
    try {
      console.log(`👨‍💼 Assigning agent to conversation ${conversationId}:`, assignmentData);
      
      const { agentId, departmentId, priority = 'normal', assignedBy, reason } = assignmentData;
      const now = Date.now();
      
      // If specific agent provided, assign directly
      if (agentId) {
        // Check if agent is available
        const agent = await this.getAgentById(agentId);
        if (!agent) {
          throw this.createServiceError('AGENT_NOT_FOUND', `Agent ${agentId} not found`);
        }
        
        if (agent.status.availability !== 'available') {
          throw this.createServiceError('AGENT_NOT_AVAILABLE', `Agent ${agentId} is not available`);
        }
        
        if (agent.status.currentConversations.length >= agent.status.maxConversations) {
          throw this.createServiceError('AGENT_AT_CAPACITY', `Agent ${agentId} is at maximum capacity`);
        }
        
        // Assign directly
        await this.executeAgentAssignment(conversationId, agent, assignedBy, reason);
        
        return {
          conversationId,
          agentId: agent.id,
          assignedAt: new Date(now).toISOString(),
          assignedBy,
          previousStatus: 'queued',
          newStatus: 'active',
          estimatedResponseTime: 60 // seconds
        };
      } else {
        // Auto-assign best available agent
        const availableAgent = await this.findAvailableAgent(departmentId || 'general');
        
        if (availableAgent) {
          await this.executeAgentAssignment(conversationId, availableAgent, assignedBy || 'system', reason);
          
          return {
            conversationId,
            agentId: availableAgent.id,
            assignedAt: new Date(now).toISOString(),
            assignedBy: assignedBy || 'system',
            previousStatus: 'queued',
            newStatus: 'active',
            estimatedResponseTime: 60
          };
        } else {
          throw this.createServiceError('NO_AGENTS_AVAILABLE', 'No agents available for assignment');
        }
      }
    } catch (error) {
      console.error('Error assigning agent:', error);
      throw error;
    }
  }

  async unassignAgent(conversationId: string, unassignmentData: any): Promise<any> {
    try {
      console.log(`👨‍💼❌ Unassigning agent from conversation ${conversationId}`);
      
      const { reason, transferToDepartment, unassignedBy } = unassignmentData;
      const now = Date.now();
      
      // Get current conversation
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      if (!conversation.assignedTo) {
        throw this.createServiceError('NO_AGENT_ASSIGNED', 'No agent currently assigned to this conversation');
      }
      
      const previousAgentId = conversation.assignedTo;
      
      // Update conversation status
      const updates: any = {
        status: 'queued',
        assignedTo: null,
        assignedAt: null,
        updatedAt: now,
        departmentId: transferToDepartment || conversation.departmentId
      };
      
      // Add to assignment history
      const assignmentHistory = conversation.metadata?.assignmentHistory || [];
      assignmentHistory.push({
        agentId: previousAgentId,
        assignedAt: conversation.assignedAt,
        unassignedAt: now,
        reason,
        unassignedBy
      });
      updates['metadata.assignmentHistory'] = assignmentHistory;
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      
      // Update agent's current conversations
      await this.removeConversationFromAgent(previousAgentId, conversationId);
      
      // Add back to queue
      const targetDepartment = transferToDepartment || conversation.departmentId || 'general';
      const queuePosition = await this.addToQueue(conversationId, targetDepartment);
      
      // Send system message
      await this.sendMessage(conversationId, {
        content: `Agent unassigned. Reason: ${reason}. Returned to ${targetDepartment} queue.`,
        senderId: 'system',
        senderType: 'system',
        messageType: 'system',
        systemData: {
          action: 'unassign',
          details: { reason, unassignedBy, previousAgentId, targetDepartment }
        }
      });
      
      return {
        conversationId,
        previousAgentId,
        unassignedAt: new Date(now).toISOString(),
        unassignedBy,
        newStatus: 'queued',
        queuePosition
      };
      
    } catch (error) {
      console.error('Error unassigning agent:', error);
      throw error;
    }
  }

  private async executeAgentAssignment(conversationId: string, agent: any, assignedBy: string, reason?: string): Promise<void> {
    const now = Date.now();
    
    // Update conversation with NEW schema
    await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
      status: 'active',
      assignedTo: 'human', // NEW: Type of assignment
      assignedAgentId: agent.id, // NEW: Specific agent ID
      assignedAt: now,
      updatedAt: now
    });
    
    // Update agent's current conversations
    await this.addConversationToAgent(agent.id, conversationId);
    
    // Remove from queue if it was there
    const conversation = await this.getConversation(conversationId);
    if (conversation?.departmentId) {
      await this.removeFromQueue(conversationId, conversation.departmentId);
    }
    
    // Send system message for agent assignment
    try {
      const assignmentMessage = await systemMessageService.sendAgentAssignedMessage(
        conversationId, 
        agent.profile.name, 
        agent.id
      );
      
      await this.sendMessage(conversationId, {
        content: assignmentMessage.content,
        senderId: 'system',
        senderType: 'system',
        messageType: 'system',
        systemData: assignmentMessage.systemData,
      });

      // System message will be automatically sent to WhatsApp via publishOutboundMessage
    } catch (error) {
      console.error('Error sending agent assignment message:', error);
    }
    
    console.log(`✅ Agent ${agent.id} assigned to conversation ${conversationId}`);
  }

  private async addConversationToAgent(agentId: string, conversationId: string): Promise<void> {
    try {
      const currentConversations = await firebaseService.get(`${this.AGENTS_PATH}/${agentId}/status/currentConversations`) || [];
      if (!currentConversations.includes(conversationId)) {
        currentConversations.push(conversationId);
        
        await firebaseService.update(`${this.AGENTS_PATH}/${agentId}/status`, {
          currentConversations,
          lastActivity: Date.now()
        });
      }
    } catch (error) {
      console.error('Error adding conversation to agent:', error);
    }
  }

  private async removeConversationFromAgent(agentId: string, conversationId: string): Promise<void> {
    try {
      const currentConversations = await firebaseService.get(`${this.AGENTS_PATH}/${agentId}/status/currentConversations`) || [];
      const updatedConversations = currentConversations.filter((id: string) => id !== conversationId);
      
      await firebaseService.update(`${this.AGENTS_PATH}/${agentId}/status`, {
        currentConversations: updatedConversations,
        lastActivity: Date.now()
      });
    } catch (error) {
      console.error('Error removing conversation from agent:', error);
    }
  }

  private async removeFromQueue(conversationId: string, departmentId: string): Promise<void> {
    try {
      const queuePath = `queues/${departmentId}/conversations/${conversationId}`;
      await firebaseService.delete(queuePath);
      
      // Update queue statistics
      await firebaseService.update(`queues/${departmentId}/statistics/current`, {
        waitingCount: admin.database.ServerValue.increment(-1),
        lastUpdated: Date.now()
      });
    } catch (error) {
      console.error('Error removing from queue:', error);
    }
  }

  private async getAgentById(agentId: string): Promise<any> {
    try {
      // Get agent profile from Supabase (source of truth for agent data)
      const { data: agentData, error } = await this.supabase
        .from('agents')
        .select(`
          id,
          name,
          email,
          role,
          agent_type,
          organization_id,
          max_concurrent_sessions,
          is_active
        `)
        .eq('id', agentId)
        .eq('is_active', true)
        .single();

      if (error || !agentData) {
        console.error('Agent not found in Supabase:', agentId);
        console.error('Supabase error details:', error);
        console.error('Agent data received:', agentData);
        return null;
      }
      
      console.log('✅ Agent found in Supabase:', agentData.name);

      // Get current status from Firebase (real-time data)
      const firebaseAgent = await firebaseService.get(`${this.AGENTS_PATH}/${agentId}`) || {};
      
      // Combine Supabase profile data with Firebase real-time status
      return {
        id: agentData.id,
        profile: {
          id: agentData.id,
          name: agentData.name,
          email: agentData.email,
          role: agentData.role,
          agent_type: agentData.agent_type, // NEW: Include agent_type for filtering
          organizationId: agentData.organization_id,
          maxConcurrentSessions: agentData.max_concurrent_sessions || 5
        },
        status: {
          availability: firebaseAgent.status?.availability || 'offline',
          currentActivity: firebaseAgent.status?.currentActivity || 'idle',
          isAvailable: firebaseAgent.status?.availability === 'available',
          lastSeen: firebaseAgent.status?.lastSeen || Date.now()
        },
        capacity: {
          maxConcurrentChats: agentData.max_concurrent_sessions || 5,
          currentChatCount: firebaseAgent.capacity?.currentChatCount || 0
        }
      };
    } catch (error) {
      console.error('Error getting agent by ID:', error);
      return null;
    }
  }

  // ========================================
  // CONVERSATION ID GENERATION (HYBRID TWILIO SUPPORT)
  // ========================================

  /**
   * Generate conversation ID from webhook data for universal integration support
   * Supports: Twilio Conversations API, Twilio WhatsApp Direct, WhatsApp Business API Direct
   * @param webhookData Universal webhook data from any platform
   * @returns Unique conversation ID
   */
  private generateConversationId(webhookData: any = {}): string {
    console.log('🆔 Generating conversation ID from:', JSON.stringify(webhookData, null, 2));
    
    // === Option A: Twilio Conversations API ===
    if (webhookData.ConversationSid) {
      console.log('📱 Using Twilio Conversations API SID:', webhookData.ConversationSid);
      return webhookData.ConversationSid;
    }
    
    // === Option B: Twilio Direct WhatsApp Webhook ===
    if (webhookData.WaId || webhookData.From) {
      const customerNumber = webhookData.WaId || (webhookData.From || '').replace('whatsapp:', '');
      const businessNumber = (webhookData.To || '').replace('whatsapp:', '') || 'default';
      const conversationId = `twilio_wa_${customerNumber}_${businessNumber}`;
      console.log('📞 Using Twilio WhatsApp direct webhook ID:', conversationId);
      return conversationId;
    }
    
    // === Option C: Direct WhatsApp Business API (Meta) ===
    if (webhookData.object === 'whatsapp_business_account' && webhookData.entry?.[0]?.changes?.[0]) {
      const change = webhookData.entry[0].changes[0];
      const message = change.value.messages?.[0];
      const phoneNumberId = change.value.metadata?.phone_number_id;
      
      if (message && phoneNumberId) {
        const customerNumber = message.from;
        const businessPhone = change.value.metadata.display_phone_number;
        const conversationId = `meta_wa_${customerNumber}_${phoneNumberId}`;
        console.log('🟢 Using WhatsApp Business API direct ID:', conversationId);
        return conversationId;
      }
    }
    
    // === Option D: WhatsApp Cloud API Simple Format ===
    if (webhookData.from && webhookData.id?.startsWith('wamid.')) {
      const customerNumber = webhookData.from;
      const phoneNumberId = webhookData.metadata?.phone_number_id || 'default';
      const conversationId = `cloud_wa_${customerNumber}_${phoneNumberId}`;
      console.log('☁️ Using WhatsApp Cloud API direct ID:', conversationId);
      return conversationId;
    }
    
    // === Option E: Fallback for testing/other sources ===
    const fallbackId = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    console.log('🔄 Using fallback ID:', fallbackId);
    return fallbackId;
  }

  // ========================================
  // LIFECYCLE MANAGEMENT
  // ========================================


  async closeConversationEnhanced(conversationId: string, closeData: any): Promise<any> {
    try {
      console.log(`🔚 Closing conversation ${conversationId}`);
      
      const { reason, closedBy, closedByName, sendSurvey = false, resolution, tags, addSystemMessage = true } = closeData;
      
      // Map reason to resolution if resolution is not provided
      const finalResolution = resolution || (() => {
        const resolutionMap: Record<string, string> = {
          'resolved': 'Successfully resolved customer issue',
          'no_response': 'Customer did not respond - session ended',
          'cancelled': 'Request cancelled by customer',
          'info_provided': 'Information provided to customer',
          'other': 'Conversation ended for other reasons'
        };
        return resolutionMap[reason] || 'Conversation completed';
      })();
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const previousStatus = conversation.status;
      const createdAtTimestamp = conversation.createdAt ? new Date(conversation.createdAt).getTime() : now;
      const sessionDuration = Math.floor((now - createdAtTimestamp) / 1000);
      
      // Calculate final metrics
      const messages = await this.getMessages(conversationId);
      const messageCount = {
        total: messages.length,
        customer: messages.filter(m => m.senderType === 'customer').length,
        agent: messages.filter(m => m.senderType === 'agent').length,
        bot: messages.filter(m => m.senderType === 'bot').length
      };
      
      // Calculate real response times from message timestamps
      const { firstResponse, averageResponse } = this.calculateResponseTimes(messages, conversation.createdAt);
      
      // Update conversation
      const updates: any = {
        status: 'closed',
        closedAt: now,
        closedBy,
        updatedAt: now,
        readyForExport: true, // Mark conversation as ready for export
        closureInfo: {
          reason: reason,
          resolution: finalResolution,
          sessionDuration: sessionDuration
        },
        analytics: {
          finalMetrics: {
            messageCount,
            responseTime: { firstResponse, averageResponse },
            sessionDuration
          }
        }
      };
      
      if (tags && tags.length > 0) {
        updates.metadata = {
          ...(conversation.metadata || {}),
          tags: [...(conversation.metadata?.tags || []), ...tags]
        };
      }
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      
      // Remove from agent's current conversations
      if (conversation.assignedTo) {
        await this.removeConversationFromAgent(conversation.assignedTo, conversationId);
      }
      
      // Send closure system message if requested
      if (addSystemMessage) {
        const reasonMessages: Record<string, string> = {
          resolved: 'Problema resuelto satisfactoriamente',
          no_response: 'Cliente no responde - sesión finalizada',
          cancelled: 'Solicitud cancelada por el cliente',
          info_provided: 'Información proporcionada',
          other: 'Conversación finalizada'
        };
        
        const reasonText = reasonMessages[reason] || 'Conversación finalizada';
        const agentName = closedByName || 'Agente';
        
        await this.sendMessage(conversationId, {
          content: `${reasonText}. ¡Gracias por contactarnos!`,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'conversation_ended',
            details: { 
              reason, 
              reasonText, 
              closedBy, 
              closedByName: agentName,
              finalResolution, 
              sessionDuration 
            }
          }
        });
      }
      
      // TODO: Schedule satisfaction survey if requested
      let surveyScheduled = false;
      if (sendSurvey) {
        // await scheduleCustomerSurvey(conversationId, conversation.customerId);
        surveyScheduled = true;
        console.log(`📋 Survey scheduled for conversation ${conversationId}`);
      }
      
      return {
        conversationId,
        previousStatus,
        newStatus: 'closed',
        closedAt: new Date(now).toISOString(),
        closedBy,
        sessionDuration,
        finalMetrics: {
          messageCount,
          responseTime: { firstResponse, averageResponse }
        },
        surveyScheduled
      };
      
    } catch (error) {
      console.error('Error closing conversation:', error);
      throw error;
    }
  }

  async updateConversationStatus(conversationId: string, statusData: any): Promise<any> {
    try {
      console.log(`📋 Updating conversation ${conversationId} status`);
      
      const { status, reason, agentId, metadata } = statusData;
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const previousStatus = conversation.status;
      
      // Validate status transition
      const validTransitions = this.getValidStatusTransitions(previousStatus);
      if (!validTransitions.includes(status)) {
        throw this.createServiceError('INVALID_STATUS_TRANSITION', 
          `Cannot transition from ${previousStatus} to ${status}`);
      }
      
      // Update conversation
      const updates: any = {
        status,
        updatedAt: now,
        updatedBy: agentId
      };
      
      if (metadata) {
        Object.keys(metadata).forEach(key => {
          updates[`metadata.${key}`] = metadata[key];
        });
      }
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      
      // Send system message
      if (reason) {
        await this.sendMessage(conversationId, {
          content: `Status changed to ${status}. Reason: ${reason}`,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'status_change',
            details: { previousStatus, newStatus: status, reason, agentId }
          }
        });
      }
      
      return {
        conversationId,
        previousStatus,
        newStatus: status,
        updatedAt: new Date(now).toISOString(),
        updatedBy: agentId
      };
      
    } catch (error) {
      console.error('Error updating conversation status:', error);
      throw error;
    }
  }

  private getValidStatusTransitions(currentStatus: string): string[] {
    const transitions: { [key: string]: string[] } = {
      'new': ['queued', 'active', 'closed'],
      'queued': ['active', 'closed'],
      'active': ['transferred', 'closed'],
      'transferred': ['active', 'closed'],
      'closed': ['active'] // Only if reopened
    };
    
    return transitions[currentStatus] || [];
  }

  // ========================================
  // TRANSFER OPERATIONS
  // ========================================

  async acceptTransferEnhanced(conversationId: string, acceptData: any): Promise<any> {
    try {
      console.log(`✅ Accepting transfer for conversation ${conversationId}`);
      
      const { acceptedBy, message } = acceptData;
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const transferInfo = conversation.transferInfo?.currentTransfer;
      if (!transferInfo || transferInfo.status !== 'pending') {
        throw this.createServiceError('NO_PENDING_TRANSFER', 'No pending transfer found for this conversation');
      }
      
      const transferDuration = Math.floor((now - transferInfo.initiatedAt) / 1000);
      
      // Update basic conversation fields first
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'active',
        assignedTo: 'human',
        assignedAgentId: acceptedBy,
        assignedAt: now,
        updatedAt: now
      });
      
      // Then update transferInfo separately
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, {
        ...conversation.transferInfo,
        currentTransfer: {
          ...conversation.transferInfo?.currentTransfer,
          status: 'completed',
          acceptedAt: now,
          acceptedBy: acceptedBy,
          transferDuration: transferDuration
        }
      });
      
      // Update agent's conversations
      await this.addConversationToAgent(acceptedBy, conversationId);
      
      // Remove from previous agent if different
      if (transferInfo.fromAgentId && transferInfo.fromAgentId !== acceptedBy) {
        await this.removeConversationFromAgent(transferInfo.fromAgentId, conversationId);
      }
      
      // Send system message
      await this.sendMessage(conversationId, {
        content: `Transfer accepted by agent ${acceptedBy}`,
        senderId: 'system',
        senderType: 'system',
        messageType: 'system',
        systemData: {
          action: 'transfer_accepted',
          details: { acceptedBy, transferDuration, transferId: transferInfo.transferId }
        }
      });
      
      // Send optional message to customer
      if (message) {
        await this.sendMessage(conversationId, {
          content: message,
          senderId: acceptedBy,
          senderType: 'agent',
          messageType: 'text',
          metadata: { transferMessage: true }
        });
      }
      
      return {
        conversationId,
        transferId: transferInfo.transferId,
        acceptedBy,
        acceptedAt: new Date(now).toISOString(),
        previousStatus: 'pending_transfer',
        newStatus: 'active',
        transferDuration
      };
      
    } catch (error) {
      console.error('Error accepting transfer:', error);
      throw error;
    }
  }

  async rejectTransfer(conversationId: string, rejectData: any): Promise<any> {
    try {
      console.log(`❌ Rejecting transfer for conversation ${conversationId}`);
      
      // Fix frontend parameter mismatch: agentId -> rejectedBy
      const { agentId, rejectedBy, reason, message } = rejectData;
      const actualRejectedBy = rejectedBy || agentId; // Support both parameter formats
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const transferInfo = conversation.transferInfo?.currentTransfer;
      if (!transferInfo || transferInfo.status !== 'pending') {
        throw this.createServiceError('NO_PENDING_TRANSFER', 'No pending transfer found for this conversation');
      }
      
      // Add rejection to history
      const transferHistory = conversation.transferInfo?.transferHistory || [];
      transferHistory.push({
        id: transferInfo.id,
        // From (source) assignment
        fromAssignedTo: 'human', // Current assignment is human (agent)
        fromAgentId: transferInfo.fromAgentId,
        // To (target) assignment  
        toAssignedTo: 'human', // Target was human (agent)
        toAgentId: transferInfo.targetAgentId,
        toDepartmentId: transferInfo.targetDepartmentId || null,
        reason,
        timestamp: now,
        status: 'rejected',
        rejectedAt: now,
        rejectedBy: actualRejectedBy
      });      
      // First, try to return to the ORIGINAL agent (fromAgentId)
      const fromAgentProfile = await this.getAgentProfile(transferInfo.fromAgentId);
      const departmentId = fromAgentProfile?.departments?.[0] || conversation.departmentId || 'general';
      
      // Check if original agent is available
      let targetAgent = null;
      let nextAction: 'returning_to_original' | 'reassigning' | 'returning_to_queue' = 'returning_to_queue';
      let newAgentId: string | undefined;
      let queuePosition: number | undefined;
      
      // Step 1: Try original agent first
      if (transferInfo.fromAgentId && transferInfo.fromAgentId !== actualRejectedBy) {
        const originalAgentAvailable = await this.isAgentAvailable(transferInfo.fromAgentId);
        if (originalAgentAvailable) {
          targetAgent = { id: transferInfo.fromAgentId, name: fromAgentProfile?.name || 'Unknown' };
          nextAction = 'returning_to_original';
        }
      }
      
      // Step 2: If original not available, find alternative in same department
      if (!targetAgent) {
        const alternativeAgent = await this.findAvailableAgent(departmentId);
        if (alternativeAgent && alternativeAgent.id !== actualRejectedBy) {
          targetAgent = alternativeAgent;
          nextAction = 'reassigning';
        }
      }
      
      if (targetAgent) {
        // Assign to target agent (original or alternative)
        await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
          status: 'active',
          assignedTo: 'human',
          assignedAgentId: targetAgent.id,
          assignedAt: now,
          updatedAt: now
        });
        
        // Update transferInfo separately to avoid dot notation
        await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, {
          ...conversation.transferInfo,
          currentTransfer: null,
          transferHistory
        });
        
        await this.addConversationToAgent(targetAgent.id, conversationId);
        newAgentId = targetAgent.id;
        
        // Send reassignment message
        const messageContent = nextAction === 'returning_to_original' 
          ? `Transfer rejected. Returned to original agent.`
          : `Transfer rejected. Reassigned to agent ${targetAgent.id}`;
          
        await this.sendMessage(conversationId, {
          content: messageContent,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'transfer_rejected_reassigned',
            details: { rejectedBy: actualRejectedBy, reason, newAgentId: targetAgent.id, action: nextAction }
          }
        });
      } else {
        // Return to queue
        queuePosition = await this.addToQueue(conversationId, departmentId);
        
        await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
          status: 'queued',
          assignedTo: null,
          assignedAt: null,
          updatedAt: now,
          departmentId
        });
        
        // Update transferInfo separately to avoid dot notation
        await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, {
          ...conversation.transferInfo,
          currentTransfer: null,
          transferHistory
        });
        
        // Send queue message
        await this.sendMessage(conversationId, {
          content: `Transfer rejected. Returned to ${departmentId} queue.`,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'transfer_rejected_queued',
            details: { rejectedBy: actualRejectedBy, reason, departmentId, queuePosition }
          }
        });
      }
      
      return {
        conversationId,
        transferId: transferInfo.transferId,
        rejectedBy,
        rejectedAt: new Date(now).toISOString(),
        reason,
        nextAction,
        newAssignedAgent: newAgentId,
        queuePosition
      };
      
    } catch (error) {
      console.error('Error rejecting transfer:', error);
      throw error;
    }
  }

  async cancelTransfer(conversationId: string, agentId: string, reason?: string): Promise<any> {
    try {
      console.log(`🚫 Canceling transfer for conversation ${conversationId} by agent ${agentId}`);
      
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const transferInfo = conversation.transferInfo?.currentTransfer;
      if (!transferInfo || transferInfo.status !== 'pending') {
        throw this.createServiceError('NO_PENDING_TRANSFER', 'No pending transfer found for this conversation');
      }
      
      // Verify the agent canceling is the original agent who initiated the transfer
      if (conversation.assignedAgentId !== agentId) {
        throw this.createServiceError('UNAUTHORIZED_CANCEL', 'Only the agent who initiated the transfer can cancel it');
      }
      
      // Return conversation to active state
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'active',
        updatedAt: now
      });
      
      // Clear transfer data using set (to avoid dot notation issues)
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo/currentTransfer`, null);
      
      // Send cancellation system message
      await this.sendMessage(conversationId, {
        content: `Transfer cancelled. Conversation returned to active state.`,
        senderId: 'system',
        senderType: 'system',
        messageType: 'system',
        systemData: {
          action: 'transfer_cancelled',
          details: {
            cancelledBy: agentId,
            reason: reason || 'Transfer cancelled by agent'
          }
        }
      });

      console.log(`✅ Transfer cancelled successfully for conversation ${conversationId}`);
      
      return {
        conversationId,
        status: 'cancelled',
        cancelledBy: agentId,
        reason: reason || 'Transfer cancelled by originating agent'
      };
      
    } catch (error) {
      console.error('Error canceling transfer:', error);
      throw error;
    }
  }

  async escalateConversation(conversationId: string, escalationData: any): Promise<any> {
    try {
      console.log(`⚠️ Escalating conversation ${conversationId}`);
      
      const { reason, priority = 'urgent', escalatedBy, notes, requestSupervisor } = escalationData;
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const escalationId = `esc_${now}_${Math.random().toString(36).substr(2, 6)}`;
      const currentEscalationLevel = (conversation.metadata?.escalationLevel || 0) + 1;
      const previousPriority = conversation.priority || 2;
      
      // Map priority to numeric value
      const priorityMap: { [key: string]: number } = {
        'low': 1, 'normal': 2, 'high': 3, 'urgent': 4, 'critical': 5
      };
      const newPriorityValue = priorityMap[priority] || 4; // Default to urgent (4)
      
      // 🎯 NEW: Escalated conversations go to supervisor queue but keep original agent
      // This allows agents to continue working while supervisors can monitor/intervene
      
      const updates: any = {
        status: 'escalated' as ConversationStatus,
        priority: newPriorityValue,
        updatedAt: now,
        // Keep original agent assignment - don't transfer
        // assignedAgentId: conversation.assignedAgentId (unchanged)
        // assignedTo: conversation.assignedTo (unchanged)
      };

      // Create comprehensive escalation metadata
      updates.escalation = {
        escalationId,
        reason,
        escalatedBy,
        escalatedAt: now,
        priority,
        notes,
        level: currentEscalationLevel,
        status: 'pending_supervisor_review',
        
        // Original conversation context
        originalAssignedAgentId: conversation.assignedAgentId,
        originalPriority: Object.keys(priorityMap).find(key => priorityMap[key] === previousPriority) || 'normal',
        
        // Supervisor assignment (if available)
        assignedSupervisor: null, // Will be assigned when supervisor takes it
        supervisorAssignedAt: null,
        
        // Timing
        estimatedResponseTime: 300, // 5 minutes for supervisor review
        escalatedAtTimestamp: new Date(now).toISOString(),
        
        // Queue information for supervisor interface
        needsSupervisorAttention: true,
        supervisorQueuePriority: currentEscalationLevel, // Higher level = higher priority
        lastAgentMessage: await this.getLastAgentMessage(conversationId),
        conversationSummary: reason // For quick supervisor overview
      };

      // Update metadata for tracking and filtering
      updates.metadata = {
        ...(conversation.metadata || {}),
        escalationLevel: currentEscalationLevel,
        isEscalated: true,
        escalatedConversation: true,
        supervisorQueueEntry: true,
        
        // For supervisor dashboard filtering
        requiresSupervisorReview: true,
        escalationPriority: priority,
        escalationTimestamp: now,
        
        // Keep original metadata
        originalPriority: conversation.priority,
        totalTransfers: conversation.metadata?.transferCount || 0,
        escalationHistory: [
          ...(conversation.metadata?.escalationHistory || []),
          {
            escalationId,
            timestamp: now,
            reason,
            escalatedBy,
            level: currentEscalationLevel
          }
        ]
      };
      
      console.log(`📋 Escalation details:`, {
        conversationId,
        escalationId,
        level: currentEscalationLevel,
        priority,
        keepingOriginalAgent: conversation.assignedAgentId
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      
      // Send escalation message
      await this.sendMessage(conversationId, {
        content: `⚠️ Conversación escalada a prioridad ${priority} (Nivel ${currentEscalationLevel}). Motivo: ${reason}\n\nEsta conversación ahora está en la cola de revisión de supervisores mientras puedes continuar asistiendo al cliente.`,
        senderId: 'system',
        senderType: 'system',
        messageType: 'system',
        systemData: {
          action: 'escalate',
          details: { 
            escalationId, 
            reason, 
            escalatedBy, 
            priority, 
            level: currentEscalationLevel,
            keepOriginalAgent: true,
            supervisorQueueEntry: true
          }
        }
      });
      
      // 🚨 NEW: Add to supervisor notification queue
      try {
        await this.addToSupervisorQueue(conversationId, {
          escalationId,
          priority,
          level: currentEscalationLevel,
          reason,
          escalatedBy,
          timestamp: now,
          originalAgent: conversation.assignedAgentId,
          customerName: conversation.customer?.name || conversation.metadata?.customerName,
          urgent: priority === 'urgent' || priority === 'critical'
        });
      } catch (error) {
        console.error('Error adding to supervisor queue:', error);
        // Don't fail the escalation if queue addition fails
      }
      
      return {
        conversationId,
        escalationId,
        previousPriority: Object.keys(priorityMap).find(key => priorityMap[key] === previousPriority) || 'normal',
        newPriority: priority,
        escalatedAt: new Date(now).toISOString(),
        escalatedBy,
        assignedSupervisor: null, // No auto-assignment, goes to queue
        estimatedResponseTime: 300 // 5 minutes for supervisor review
      };
      
    } catch (error) {
      console.error('Error escalating conversation:', error);
      throw error;
    }
  }

  private async findAvailableSupervisor(departmentId: string): Promise<any> {
    try {
      // TODO: Implement sophisticated supervisor matching
      const supervisors = await firebaseService.query(this.AGENTS_PATH, [
        { field: 'profile.role', operator: '==', value: 'supervisor' },
        { field: 'profile.departments', operator: 'array-contains', value: departmentId },
        { field: 'status.availability', operator: '==', value: 'available' }
      ]);
      
      if (supervisors && supervisors.length > 0) {
        // Return supervisor with least current conversations
        const sortedSupervisors = supervisors.sort((a: any, b: any) => 
          a.status.currentConversations.length - b.status.currentConversations.length
        );
        
        const supervisor = sortedSupervisors[0];
        if (supervisor.status.currentConversations.length < supervisor.status.maxConversations) {
          return supervisor;
        }
      }
      
      return null;
    } catch (error) {
      console.error('Error finding available supervisor:', error);
      return null;
    }
  }

  // ========================================
  // CONVERSATION CRUD OPERATIONS
  // ========================================

  async createConversation(data: CreateConversationRequest): Promise<Conversation> {
    try {
      const now = Date.now();
      // Support both new webhookData and legacy twilioData
      const webhookData = data.webhookData || data.twilioData || {};
      const conversationId = this.generateConversationId(webhookData);

      const conversation: Conversation = {
        id: conversationId,
        customerId: data.customerId,
        customer: data.customer,
        channel: data.channel as any,
        status: 'new',
        priority: (data.priority as 1 | 2 | 3 | 4) || 2,
        source: (data.source as any) || 'direct',
        createdAt: now,
        updatedAt: now,
        
        // NEW: Bot vs Human assignment architecture
        assignedTo: (data as any).assignedTo || 'human', // Default to human if not specified
        assignedBotId: (data as any).assignedBotId,
        assignedAgentId: (data as any).assignedAgentId,
        
        metadata: {
          tags: [],
          notes: [],
          transferCount: 0,
          escalationLevel: 0,
          messageCount: 0,
        },
      };

      if (data.departmentId) {
        conversation.departmentId = data.departmentId;
        conversation.routingInfo = {
          assignedDepartment: data.departmentId,
          aiAnalysisAttempts: 0,
          aiAnalysisHistory: [],
          departmentAssignedAt: now,
        };
      }

      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}`, conversation);

      // If initial message provided, create it
      if (data.initialMessage) {
        await this.sendMessage(conversationId, {
          content: data.initialMessage,
          senderId: data.customerId,
          senderType: 'customer',
          messageType: 'text',
        });
      }

      // Send welcome system message
      try {
        const welcomeMessage = await systemMessageService.sendWelcomeMessage(conversationId);
        await this.sendMessage(conversationId, {
          content: welcomeMessage.content,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: welcomeMessage.systemData,
        });

        // System message will be automatically sent to WhatsApp via publishOutboundMessage
      } catch (error) {
        console.error('Error sending welcome message:', error);
      }

      console.log(`✅ Conversation created: ${conversationId}`);
      return conversation;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw this.createServiceError('CONVERSATION_CREATE_FAILED', 'Failed to create conversation', error);
    }
  }

  async getConversation(conversationId: string): Promise<Conversation | null> {
    try {
      const conversation = await firebaseService.get(`${this.CONVERSATIONS_PATH}/${conversationId}`);
      return conversation;
    } catch (error) {
      console.error(`Error getting conversation ${conversationId}:`, error);
      throw this.createServiceError('CONVERSATION_NOT_FOUND', `Conversation ${conversationId} not found`, error);
    }
  }

  async updateConversation(conversationId: string, updates: UpdateConversationRequest): Promise<void> {
    try {
      const updateData: any = {
        ...updates,
        updatedAt: Date.now(),
      };

      // Handle status changes
      if (updates.status) {
        updateData.status = updates.status;
        
        // Set timestamps for specific status changes
        if (updates.status === 'assigned' && updates.assignedTo) {
          updateData.assignedAt = Date.now();
        } else if (updates.status === 'closed') {
          updateData.closedAt = Date.now();
        }
      }

      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updateData);
      console.log(`✅ Conversation updated: ${conversationId}`);
    } catch (error) {
      console.error(`Error updating conversation ${conversationId}:`, error);
      throw this.createServiceError('CONVERSATION_UPDATE_FAILED', `Failed to update conversation ${conversationId}`, error);
    }
  }

  async deleteConversation(conversationId: string): Promise<void> {
    try {
      // Delete conversation
      await firebaseService.remove(`${this.CONVERSATIONS_PATH}/${conversationId}`);
      
      // Delete all messages for this conversation (they're nested in conversations)
      // Messages are stored at conversations/{conversationId}/messages
      
      // Delete typing indicators
      await firebaseService.remove(`${this.TYPING_PATH}/${conversationId}`);

      console.log(`✅ Conversation deleted: ${conversationId}`);
    } catch (error) {
      console.error(`Error deleting conversation ${conversationId}:`, error);
      throw this.createServiceError('CONVERSATION_DELETE_FAILED', `Failed to delete conversation ${conversationId}`, error);
    }
  }

  async listConversations(filters?: {
    status?: ConversationStatus;
    assignedTo?: string;
    departmentId?: string;
    limit?: number;
    offset?: number;
  }): Promise<Conversation[]> {
    try {
      let query: admin.database.Query = firebaseService.getRef(this.CONVERSATIONS_PATH);

      // Apply filters (basic implementation - Firebase has limited querying)
      if (filters?.assignedTo) {
        query = query.orderByChild('assignedTo').equalTo(filters.assignedTo);
      } else if (filters?.status) {
        query = query.orderByChild('status').equalTo(filters.status);
      } else {
        query = query.orderByChild('updatedAt');
      }

      if (filters?.limit) {
        query = query.limitToLast(filters.limit);
      }

      const snapshot = await query.once('value');
      const conversations = snapshot.val() || {};

      return Object.values(conversations) as Conversation[];
    } catch (error) {
      console.error('Error listing conversations:', error);
      throw this.createServiceError('CONVERSATION_LIST_FAILED', 'Failed to list conversations', error);
    }
  }

  // ========================================
  // MESSAGE CRUD OPERATIONS
  // ========================================

  async sendMessage(conversationId: string, data: SendMessageRequest): Promise<Message> {
    try {
      console.log(`🚀 sendMessage called for conversation ${conversationId}:`, {
        senderId: data.senderId,
        senderType: data.senderType,
        content: data.content.substring(0, 50) + '...',
        messageType: data.messageType
      });

      const now = Date.now();
      const messageId = `msg_${now}_${Math.random().toString(36).substr(2, 9)}`;

      const message: Message = {
        id: messageId,
        conversationId,
        senderId: data.senderId,
        senderType: data.senderType,
        content: data.content,
        type: data.messageType || 'text',
        timestamp: new Date(now).toISOString(),
        status: 'sent',
        ...(data.systemData && { systemData: data.systemData }),
      };

      console.log(`💾 Saving message ${messageId} to Firebase...`);
      // Save message to Firebase
      await firebaseService.set(`conversations/${conversationId}/messages/${messageId}`, message);

      console.log(`📊 Updating conversation metadata...`);
      // Update conversation metadata including lastMessage
      await this.updateConversationMetadata(conversationId, {
        lastMessageAt: now,
        messageCount: 1, // This will be incremented via transaction
        lastActivity: now,
      });

      console.log(`🎯 CRITICAL: Updating lastMessage in conversation root...`, {
        messageId,
        content: message.content.substring(0, 30) + '...',
        senderId: message.senderId,
        senderType: message.senderType,
        timestamp: message.timestamp,
        type: message.type
      });
      // CRITICAL: Update lastMessage in conversation root for UI
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        lastMessage: {
          id: messageId,
          content: message.content,
          senderId: message.senderId,
          senderType: message.senderType,
          timestamp: message.timestamp,
          type: message.type
        },
        updatedAt: message.timestamp,
        lastActivityAt: message.timestamp
      });

      console.log(`✅ lastMessage update completed for conversation ${conversationId}`);

      // If this is an agent message, publish to PubSub for outbound delivery
      if (data.senderType === 'agent') {
        console.log(`📤 Publishing agent message to PubSub...`);
        await this.publishOutboundMessage(conversationId, message);
      }

      console.log(`✅ Message sent: ${messageId} in conversation ${conversationId}`);
      return message;
    } catch (error) {
      console.error(`Error sending message to conversation ${conversationId}:`, error);
      throw this.createServiceError('MESSAGE_SEND_FAILED', 'Failed to send message', error);
    }
  }

  async getMessages(conversationId: string, limit?: number): Promise<Message[]> {
    try {
      let query: admin.database.Query = firebaseService.getRef(`conversations/${conversationId}/messages`);
      query = query.orderByChild('timestamp'); // Always order by timestamp
      
      const snapshot = await query.once('value');
      const messages = snapshot.val() || {};
      
      // ✅ SOLUTION: Explicit sort by timestamp ALWAYS (ascending: old → new)
      const sortedMessages = Object.values(messages)
        .sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()) as Message[];
      
      // If limit requested, take the most recent N messages
      return limit ? sortedMessages.slice(-limit) as Message[] : sortedMessages;
    } catch (error) {
      console.error(`Error getting messages for conversation ${conversationId}:`, error);
      throw this.createServiceError('MESSAGE_GET_FAILED', 'Failed to get messages', error);
    }
  }

  async markMessageAsRead(conversationId: string, messageId: string, userId: string): Promise<void> {
    try {
      const now = Date.now();
      await firebaseService.update(
        `conversations/${conversationId}/messages/${messageId}/readBy/${userId}`,
        now
      );
    } catch (error) {
      console.error(`Error marking message as read:`, error);
      throw this.createServiceError('MESSAGE_READ_FAILED', 'Failed to mark message as read', error);
    }
  }

  async markConversationAsRead(conversationId: string, userId: string): Promise<void> {
    try {
      const now = Date.now();
      
      // Get all messages in the conversation
      const messagesRef = `conversations/${conversationId}/messages`;
      const messagesSnapshot = await firebaseService.get(messagesRef);
      const messages = messagesSnapshot || {};
      
      // Mark all messages as read by this user
      const readUpdates: Record<string, any> = {};
      Object.keys(messages).forEach(messageId => {
        readUpdates[`conversations/${conversationId}/messages/${messageId}/readBy/${userId}`] = now;
      });
      
      // Update unreadCount to 0 for the conversation
      readUpdates[`conversations/${conversationId}/unreadCount`] = 0;
      
      // Apply all updates atomically
      await firebaseService.batchUpdate(readUpdates);
      
      console.log(`✅ Marked conversation ${conversationId} as read by user ${userId}`);
    } catch (error) {
      console.error(`Error marking conversation as read:`, error);
      throw this.createServiceError('CONVERSATION_READ_FAILED', 'Failed to mark conversation as read', error);
    }
  }

  /**
   * Publish outbound message to PubSub for delivery to customer via Channel Router
   */
  private async publishOutboundMessage(conversationId: string, message: Message): Promise<void> {
    try {
      // Get conversation to get customer details
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        console.warn(`⚠️ Cannot publish outbound message: Conversation ${conversationId} not found`);
        return;
      }

      // Only publish if we have customer contact info
      const customerPhone = conversation.customer?.phone || conversation.customerId;
      if (!customerPhone) {
        console.warn(`⚠️ Cannot publish outbound message: No customer phone for conversation ${conversationId}`);
        return;
      }

      // Create outbound message for PubSub
      const outboundMessage: OutboundMessage = {
        to: customerPhone,
        body: message.content,
        channel: conversation.channel || 'whatsapp',
        conversationId: conversationId, // Use conversation ID
        messageId: message.id,
        senderId: message.senderId,
        senderType: 'system',
        timestamp: message.timestamp,
        metadata: {
          customerName: conversation.customer?.name,
          agentName: 'system', // For system messages
          department: conversation.departmentId,
          originalConversationId: conversationId // Keep original for tracking
        }
      };

      // Publish to PubSub outbound topic
      const result = await pubsubService.publishOutboundMessage(outboundMessage);
      
      if (result.success) {
        console.log(`📤 Outbound message published to PubSub: ${message.id} -> ${customerPhone}`);
      } else {
        console.error(`❌ Failed to publish outbound message: ${result.error}`);
      }
      
    } catch (error) {
      console.error(`Error publishing outbound message for ${conversationId}:`, error);
      // Don't throw error - outbound publishing is optional functionality
    }
  }

  // ========================================
  // REDIS STATE MANAGEMENT
  // ========================================

  /**
   * Update conversation state in Redis (shared with Bot Human Router)
   */
  private async updateConversationStateInRedis(
    conversationId: string, 
    assignedTo: 'n8n' | 'human',
    department?: string
  ): Promise<void> {
    try {
      const state = {
        conversationId,
        assignedTo,
        assignedAt: Date.now(),
        department: department || 'general',
        lastUpdated: Date.now(),
        updatedBy: 'chat-realtime'
      };

      const key = `conversation:${conversationId}`;
      const TTL = 7200; // 2 hours
      
      await this.redisService.setObject(key, state, TTL);
      console.log(`💾 Redis: Updated conversation state ${conversationId} -> ${assignedTo}`);
    } catch (error) {
      console.error(`❌ Redis: Failed to update conversation state for ${conversationId}:`, error);
      // Don't throw error - Redis update failure shouldn't break the transfer
    }
  }

  // ========================================
  // CONVERSATION ACTIONS
  // ========================================

  async transferConversation(conversationId: string, targetAgentId: string, reason: string): Promise<void> {
    try {
      console.log(`🔄 DEBUG: Starting transferConversation for ${conversationId} to ${targetAgentId}`);
      const now = Date.now();
      
      // Get current conversation
      console.log('🔍 DEBUG: Getting conversation...');
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        console.error('❌ DEBUG: Conversation not found');
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      console.log('✅ DEBUG: Conversation found:', { 
        id: conversation.id, 
        assignedTo: conversation.assignedTo,
        assignedAgentId: conversation.assignedAgentId 
      });

      // Create transfer record
      console.log('📝 DEBUG: Creating transfer record...');
      const transferId = `transfer_${now}_${Math.random().toString(36).substr(2, 9)}`;
      // Get target agent's department
      const targetAgentProfile = await this.getAgentProfile(targetAgentId);
      const targetDepartmentId = targetAgentProfile?.departments?.[0] || 'general';
      
      const transferRecord = {
        id: transferId,
        fromAgentId: conversation.assignedAgentId, // Use assignedAgentId per backend types.ts
        targetAssignedTo: 'human' as const,
        targetAgentId: targetAgentId,
        targetDepartmentId: targetDepartmentId,
        reason,
        initiatedAt: now,
        timestamp: now,
        status: 'pending' as const,
      };
      console.log('✅ DEBUG: Transfer record created:', transferRecord);

      // Update conversation with transfer info using separate operations to avoid nested object issues
      console.log('🔧 DEBUG: Updating Firebase with separate operations...');
      
      // 1. Update basic fields (flat properties only)
      console.log('📝 DEBUG: Updating basic conversation fields...');
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'transferring' as ConversationStatus,
        updatedAt: now,
        customerId: conversation.customerId, // Preserve customer ID
      });
      
      // 2. Explicitly preserve customer object using set()
      console.log('👤 DEBUG: Preserving customer data...');
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/customer`, conversation.customer);
      
      // 3. Update transferInfo using set() to avoid nested object issues
      console.log('🔄 DEBUG: Setting transfer info...');
      const updatedTransferInfo = {
        ...conversation.transferInfo,
        transferHistory: [
          ...(conversation.transferInfo?.transferHistory || []),
          transferRecord,
        ],
        currentTransfer: {
          id: transferId,
          targetAgentId,
          reason,
          initiatedAt: now,
          status: 'pending' as const,
        },
      };
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, updatedTransferInfo);
      
      // 4. Update metadata using set() to preserve existing data
      console.log('📊 DEBUG: Updating metadata...');
      const updatedMetadata = {
        ...conversation.metadata,
        transferCount: (conversation.metadata?.transferCount || 0) + 1
      };
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/metadata`, updatedMetadata);
      
      console.log('✅ DEBUG: All Firebase updates completed successfully');

      // Get target agent info for system message
      try {
        console.log('🎯 DEBUG: Getting target agent info...');
        const targetAgent = await this.getAgentById(targetAgentId);
        console.log('📋 DEBUG: Target agent result:', targetAgent);
        
        // Get agent name or use generic fallback
        const targetAgentName = targetAgent?.profile?.name || 'Agente';
        console.log('👤 DEBUG: Target agent name:', targetAgentName);

        const transferMessage = await systemMessageService.sendAgentTransferredMessage(
          conversationId, 
          targetAgentName, 
          targetAgentId,
          reason
        );
        
        await this.sendMessage(conversationId, {
          content: transferMessage.content,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: transferMessage.systemData,
        });

        // System message will be automatically sent to WhatsApp via publishOutboundMessage
      } catch (error) {
        console.error('Error sending agent transfer message:', error);
      }

      // Set transfer as pending acceptance (don't complete automatically)
      console.log('⏳ DEBUG: Setting transfer as pending acceptance...');
      
      // Update basic status first
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'pending_acceptance',
        updatedAt: now,
      });
      
      // Then update transferInfo with set operation to avoid dot notation issues
      const pendingTransferInfo = {
        ...conversation.transferInfo,
        currentTransfer: {
          ...transferRecord,
          pendingSince: now
        }
      };
      
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, pendingTransferInfo);
      console.log('✅ DEBUG: Transfer set to pending acceptance for agent:', targetAgentId);

      // 🆕 UPDATE REDIS STATE - CRITICAL for Bot Human Router sync
      await this.updateConversationStateInRedis(conversationId, 'human', conversation.metadata?.department);
      console.log(`🔄 Redis state updated: ${conversationId} -> human (via transfer)`);

      console.log(`✅ DEBUG: Conversation ${conversationId} transferred to ${targetAgentId}`);
    } catch (error) {
      console.error(`❌ DEBUG: Error in transferConversation:`, error);
      console.error(`❌ DEBUG: Error type:`, typeof error);
      console.error(`❌ DEBUG: Error name:`, error?.name);
      console.error(`❌ DEBUG: Error message:`, error?.message);
      console.error(`❌ DEBUG: Error stack:`, error?.stack);
      throw this.createServiceError('CONVERSATION_TRANSFER_FAILED', `Transfer failed: ${error?.message || 'Unknown error'}`, { 
        originalError: error?.message,
        conversationId,
        targetAgentId,
        errorType: error?.name,
        timestamp: new Date().toISOString()
      });
    }
  }

  async acceptTransfer(conversationId: string, agentId: string): Promise<void> {
    try {
      const now = Date.now();

      // Get conversation to update transfer history
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }

      // Mark current transfer as completed in history
      const updatedHistory = [...(conversation.transferInfo?.transferHistory || [])];
      if (updatedHistory.length > 0) {
        updatedHistory[updatedHistory.length - 1].status = 'completed';
        updatedHistory[updatedHistory.length - 1].completedAt = now;
        updatedHistory[updatedHistory.length - 1].acceptedBy = agentId;
      }

      // Update basic conversation fields first
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'active',
        assignedTo: 'human',
        assignedAgentId: agentId,
        assignedAt: now,
        updatedAt: now
      });
      
      // Then update transferInfo separately
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, {
        ...conversation.transferInfo,
        currentTransfer: null,
        transferHistory: updatedHistory
      });

      console.log(`✅ Transfer accepted for conversation ${conversationId} by agent ${agentId}`);
    } catch (error) {
      console.error(`Error accepting transfer:`, error);
      throw this.createServiceError('TRANSFER_ACCEPT_FAILED', 'Failed to accept transfer', error);
    }
  }

  async closeConversation(conversationId: string, reason?: string, notes?: string): Promise<void> {
    try {
      const now = Date.now();

      const updates: any = {
        status: 'closed' as ConversationStatus,
        closedAt: now,
        updatedAt: now,
      };

      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);

      // Send system message for conversation ended
      try {
        const closedMessage = await systemMessageService.sendConversationEndedMessage(conversationId, reason);
        
        await this.sendMessage(conversationId, {
          content: closedMessage.content,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: closedMessage.systemData,
        });

        // System message will be automatically sent to WhatsApp via publishOutboundMessage
      } catch (error) {
        console.error('Error sending conversation closed message:', error);
      }

      console.log(`✅ Conversation ${conversationId} closed`);
    } catch (error) {
      console.error(`Error closing conversation:`, error);
      throw this.createServiceError('CONVERSATION_CLOSE_FAILED', 'Failed to close conversation', error);
    }
  }

  // ========================================
  // TYPING INDICATORS
  // ========================================

  async setTypingIndicator(conversationId: string, userId: string, userType: 'customer' | 'agent', isTyping: boolean): Promise<void> {
    try {
      const indicator: TypingIndicator = {
        conversationId,
        userId,
        userType,
        isTyping,
        timestamp: Date.now(),
      };

      if (isTyping) {
        await firebaseService.set(`${this.TYPING_PATH}/${conversationId}/${userId}`, indicator);
      } else {
        await firebaseService.remove(`${this.TYPING_PATH}/${conversationId}/${userId}`);
      }
    } catch (error) {
      console.error(`Error setting typing indicator:`, error);
      // Don't throw error for typing indicators - they're not critical
    }
  }

  async getTypingIndicators(conversationId: string): Promise<TypingIndicator[]> {
    try {
      const indicators = await firebaseService.get(`${this.TYPING_PATH}/${conversationId}`);
      return Object.values(indicators || {}) as TypingIndicator[];
    } catch (error) {
      console.error(`Error getting typing indicators:`, error);
      return [];
    }
  }

  // ========================================
  // AGENT STATUS
  // ========================================


  async getAgentStatus(agentId: string): Promise<AgentStatus | null> {
    try {
      return await firebaseService.get(`${this.AGENTS_PATH}/${agentId}`);
    } catch (error) {
      console.error(`Error getting agent status:`, error);
      return null;
    }
  }

  // ========================================
  // INTERNAL NOTES
  // ========================================

  async addNote(conversationId: string, note: Omit<InternalNote, 'id' | 'createdAt'>): Promise<InternalNote> {
    try {
      const now = Date.now();
      const noteId = `note_${now}_${Math.random().toString(36).substr(2, 9)}`;

      const fullNote: InternalNote = {
        ...note,
        id: noteId,
        createdAt: now,
      };

      // Add note to conversation
      await firebaseService.update(
        `${this.CONVERSATIONS_PATH}/${conversationId}/metadata/notes/${noteId}`,
        fullNote
      );

      console.log(`✅ Note added to conversation ${conversationId}: ${noteId}`);
      return fullNote;
    } catch (error) {
      console.error(`Error adding note:`, error);
      throw this.createServiceError('NOTE_ADD_FAILED', 'Failed to add note', error);
    }
  }

  // ========================================
  // UTILITY METHODS
  // ========================================

  private async updateConversationMetadata(conversationId: string, metadata: any): Promise<void> {
    try {
      // Use transaction to safely increment message count
      if (metadata.messageCount) {
        await firebaseService.transaction(
          `${this.CONVERSATIONS_PATH}/${conversationId}/metadata/messageCount`,
          (currentCount) => (currentCount || 0) + metadata.messageCount
        );
        delete metadata.messageCount;
      }

      // Update other metadata - MERGE with existing metadata to preserve fields
      if (Object.keys(metadata).length > 0) {
        // Get existing conversation data (firebaseService.get already returns .val())
        const existingConversation = await firebaseService.get(`${this.CONVERSATIONS_PATH}/${conversationId}`);
        const existingMetadata = existingConversation?.metadata || {};
        
        // Merge new metadata with existing (preserve existing fields)
        const mergedMetadata = {
          ...existingMetadata,
          ...metadata
        };
        
        const updates: any = {
          metadata: mergedMetadata
        };

        await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      }
    } catch (error) {
      console.error(`Error updating conversation metadata:`, error);
      // Don't throw - metadata updates are not critical
    }
  }

  // ========================================
  // ADVANCED SUPERVISION OPERATIONS
  // ========================================

  async superviseConversation(conversationId: string, supervisionData: any): Promise<any> {
    try {
      console.log(`👁️ Starting supervision for conversation ${conversationId}`);
      
      const { supervisorId, mode = 'observe', reason } = supervisionData;
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      // Validate supervisor
      const supervisor = await this.getAgentById(supervisorId);
      if (!supervisor || supervisor.profile.role !== 'supervisor') {
        throw this.createServiceError('INVALID_SUPERVISOR', 'Invalid supervisor or insufficient permissions');
      }
      
      const supervisionId = `sup_${now}_${Math.random().toString(36).substr(2, 6)}`;
      
      // Update conversation with supervision info
      const updates: any = {
        updatedAt: now,
        'supervision.isSupervised': true,
        'supervision.supervisorId': supervisorId,
        'supervision.supervisionId': supervisionId,
        'supervision.mode': mode, // 'observe' or 'participate'
        'supervision.startedAt': now,
        'supervision.reason': reason || 'Quality assurance',
        'supervision.status': 'active'
      };
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      
      // Send system message (only if participating)
      if (mode === 'participate') {
        await this.sendMessage(conversationId, {
          content: `A supervisor has joined the conversation for assistance`,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'supervision_started',
            details: { supervisorId, mode, supervisionId }
          }
        });
      }
      
      // TODO: Notify agent about supervision
      // await notificationService.notifyAgent(conversation.assignedTo, 'SUPERVISION_STARTED', { supervisionId });
      
      return {
        conversationId,
        supervisionId,
        supervisorId,
        mode,
        startedAt: new Date(now).toISOString(),
        status: 'active'
      };
      
    } catch (error) {
      console.error('Error starting supervision:', error);
      throw error;
    }
  }

  async endSupervision(conversationId: string, endData: any): Promise<any> {
    try {
      console.log(`🔚 Ending supervision for conversation ${conversationId}`);
      
      const { supervisorId, notes, rating } = endData;
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      if (!conversation.supervision?.isSupervised || conversation.supervision?.supervisorId !== supervisorId) {
        throw this.createServiceError('NO_ACTIVE_SUPERVISION', 'No active supervision found for this supervisor');
      }
      
      const supervisionDuration = Math.floor((now - conversation.supervision.startedAt) / 1000);
      
      // Update conversation 
      const updates: any = {
        updatedAt: now,
        'supervision.isSupervised': false,
        'supervision.endedAt': now,
        'supervision.duration': supervisionDuration,
        'supervision.status': 'completed',
        'supervision.endNotes': notes,
        'supervision.rating': rating
      };
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      
      // Send system message if was participating
      if (conversation.supervision.mode === 'participate') {
        await this.sendMessage(conversationId, {
          content: `Supervisor has left the conversation`,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'supervision_ended',
            details: { supervisorId, duration: supervisionDuration, rating }
          }
        });
      }
      
      return {
        conversationId,
        supervisionId: conversation.supervision.supervisionId,
        endedAt: new Date(now).toISOString(),
        duration: supervisionDuration,
        rating,
        notes
      };
      
    } catch (error) {
      console.error('Error ending supervision:', error);
      throw error;
    }
  }

  async sendCoachingMessage(conversationId: string, coachingData: any): Promise<any> {
    try {
      console.log(`💬 Sending coaching message for conversation ${conversationId}`);
      
      const { supervisorId, message, isPrivate = true } = coachingData;
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      if (!conversation.supervision?.isSupervised || conversation.supervision?.supervisorId !== supervisorId) {
        throw this.createServiceError('NO_ACTIVE_SUPERVISION', 'No active supervision found for this supervisor');
      }
      
      const messageId = `coach_${now}_${Math.random().toString(36).substr(2, 6)}`;
      
      // Create coaching message (private to agent)
      const coachingMessage = {
        id: messageId,
        conversationId,
        senderId: supervisorId,
        senderType: 'supervisor' as const,
        content: message,
        messageType: 'coaching' as const,
        timestamp: now,
        status: 'sent' as const,
        isPrivate,
        visibleTo: isPrivate ? [conversation.assignedTo] : ['all'], // Private coaching or visible to customer too
        coachingData: {
          type: 'coaching',
          supervisorId,
          targetAgentId: conversation.assignedTo
        }
      };
      
      await firebaseService.set(`${this.CONVERSATIONS_PATH}/${conversationId}/messages/${messageId}`, coachingMessage);
      
      // Update conversation message count
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/metadata`, {
        messageCount: admin.database.ServerValue.increment(1),
        updatedAt: now
      });
      
      // TODO: Send real-time notification to agent
      // await notificationService.sendCoachingMessage(conversation.assignedTo, messageId, message);
      
      return {
        messageId,
        conversationId,
        supervisorId,
        targetAgentId: conversation.assignedTo,
        sentAt: new Date(now).toISOString(),
        isPrivate
      };
      
    } catch (error) {
      console.error('Error sending coaching message:', error);
      throw error;
    }
  }

  async changeSupervisionMode(conversationId: string, modeData: any): Promise<any> {
    try {
      console.log(`🔄 Changing supervision mode for conversation ${conversationId}`);
      
      const { supervisorId, newMode } = modeData;
      const now = Date.now();
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      if (!conversation.supervision?.isSupervised || conversation.supervision?.supervisorId !== supervisorId) {
        throw this.createServiceError('NO_ACTIVE_SUPERVISION', 'No active supervision found for this supervisor');
      }
      
      if (!['observe', 'participate'].includes(newMode)) {
        throw this.createServiceError('INVALID_SUPERVISION_MODE', 'Mode must be either "observe" or "participate"');
      }
      
      const previousMode = conversation.supervision.mode;
      
      // Update supervision mode
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        'supervision.mode': newMode,
        'supervision.modeChangedAt': now,
        updatedAt: now
      });
      
      // Send system message about mode change
      let systemMessage = '';
      if (previousMode === 'observe' && newMode === 'participate') {
        systemMessage = 'A supervisor has joined the conversation to assist';
      } else if (previousMode === 'participate' && newMode === 'observe') {
        systemMessage = 'Supervisor switched to observation mode';
      }
      
      if (systemMessage && newMode === 'participate') {
        await this.sendMessage(conversationId, {
          content: systemMessage,
          senderId: 'system',
          senderType: 'system',
          messageType: 'system',
          systemData: {
            action: 'supervision_mode_changed',
            details: { supervisorId, previousMode, newMode }
          }
        });
      }
      
      return {
        conversationId,
        supervisionId: conversation.supervision.supervisionId,
        previousMode,
        newMode,
        changedAt: new Date(now).toISOString(),
        supervisorId
      };
      
    } catch (error) {
      console.error('Error changing supervision mode:', error);
      throw error;
    }
  }

  async getSupervisionStatus(conversationId: string): Promise<any> {
    try {
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const supervision = conversation.supervision;
      if (!supervision?.isSupervised) {
        return {
          conversationId,
          isSupervised: false,
          status: 'none'
        };
      }
      
      return {
        conversationId,
        isSupervised: true,
        supervisionId: supervision.supervisionId,
        supervisorId: supervision.supervisorId,
        mode: supervision.mode,
        status: supervision.status,
        startedAt: new Date(supervision.startedAt).toISOString(),
        duration: supervision.endedAt 
          ? Math.floor((supervision.endedAt - supervision.startedAt) / 1000)
          : Math.floor((Date.now() - supervision.startedAt) / 1000),
        reason: supervision.reason
      };
      
    } catch (error) {
      console.error('Error getting supervision status:', error);
      throw error;
    }
  }

  async getSupervisedConversations(supervisorId: string, filters: any = {}): Promise<any> {
    try {
      console.log(`📋 Getting supervised conversations for supervisor ${supervisorId}`);
      
      const { status = 'active', limit = 50 } = filters;
      
      // Query conversations being supervised by this supervisor
      const conversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'supervision.supervisorId', operator: '==', value: supervisorId },
        { field: 'supervision.status', operator: '==', value: status }
      ]);
      
      const supervisedList = conversations.slice(0, limit).map((conv: any) => ({
        conversationId: conv.id,
        customerId: conv.customerId,
        customerName: conv.customer?.name,
        assignedAgentId: conv.assignedTo,
        departmentId: conv.departmentId,
        status: conv.status,
        priority: conv.priority,
        supervisionInfo: {
          supervisionId: conv.supervision?.supervisionId,
          mode: conv.supervision?.mode,
          startedAt: conv.supervision?.startedAt ? new Date(conv.supervision.startedAt).toISOString() : null,
          duration: conv.supervision?.startedAt ? Math.floor((Date.now() - conv.supervision.startedAt) / 1000) : 0,
          reason: conv.supervision?.reason
        },
        lastMessageAt: conv.updatedAt ? new Date(conv.updatedAt).toISOString() : null
      }));
      
      return {
        supervisorId,
        conversations: supervisedList,
        total: supervisedList.length,
        status,
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting supervised conversations:', error);
      throw error;
    }
  }

  // ========================================
  // AGENT MANAGEMENT OPERATIONS
  // ========================================

  async getAllAgents(filters: any = {}): Promise<any> {
    try {
      console.log('👥 Getting all agents with filters:', filters);
      
      const { departmentId, status, role, limit = 100 } = filters;
      
      const queryFilters: any[] = [];
      
      if (departmentId) {
        queryFilters.push({ field: 'profile.departments', operator: 'array-contains', value: departmentId });
      }
      
      if (status) {
        queryFilters.push({ field: 'status.availability', operator: '==', value: status });
      }
      
      if (role) {
        queryFilters.push({ field: 'profile.role', operator: '==', value: role });
      }
      
      const agents = await firebaseService.query(this.AGENTS_PATH, queryFilters);
      
      const agentsList = agents.slice(0, limit).map((agent: any) => ({
        agentId: agent.id,
        name: agent.profile?.name || 'Unknown Agent',
        email: agent.profile?.email,
        role: agent.profile?.role || 'agent',
        departments: agent.profile?.departments || [],
        status: {
          availability: agent.status?.availability || 'offline',
          currentActivity: agent.status?.currentActivity,
          lastSeen: agent.status?.lastSeen ? new Date(agent.status.lastSeen).toISOString() : null,
          isAvailable: agent.status?.isAvailable || false
        },
        capacity: {
          currentConversations: agent.status?.currentConversations?.length || 0,
          maxConcurrentChats: agent.status?.maxConcurrentChats || 5
        },
        performance: {
          totalConversationsToday: agent.performance?.totalConversationsToday || 0,
          avgResponseTime: agent.performance?.avgResponseTime || 0,
          customerSatisfaction: agent.performance?.customerSatisfaction || 0
        }
      }));
      
      return {
        agents: agentsList,
        total: agentsList.length,
        filters: { departmentId, status, role },
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting all agents:', error);
      throw error;
    }
  }

  async getAgentDetails(agentId: string): Promise<any> {
    try {
      console.log(`👤 Getting details for agent ${agentId}`);
      
      const agent = await this.getAgentById(agentId);
      if (!agent) {
        throw this.createServiceError('AGENT_NOT_FOUND', `Agent ${agentId} not found`);
      }
      
      // Get current conversations
      const currentConversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'agentId', operator: '==', value: agentId },
        { field: 'status', operator: 'in', value: ['active'] }
      ]);
      
      // Get recent performance metrics (mock data for now)
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);
      
      const conversationsToday = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'agentId', operator: '==', value: agentId },
        { field: 'assignedAt', operator: '>=', value: todayStart.getTime() }
      ]);
      
      return {
        agentId,
        profile: {
          name: agent.profile?.name || 'Unknown Agent',
          email: agent.profile?.email,
          role: agent.profile?.role || 'agent',
          departments: agent.profile?.departments || [],
          skills: agent.profile?.skills || [],
          languages: agent.profile?.languages || ['es'],
          joinedAt: agent.profile?.joinedAt ? new Date(agent.profile.joinedAt).toISOString() : null
        },
        status: {
          availability: agent.status?.availability || 'offline',
          currentActivity: agent.status?.currentActivity,
          lastSeen: agent.status?.lastSeen ? new Date(agent.status.lastSeen).toISOString() : null,
          isAvailable: agent.status?.isAvailable || false,
          statusUpdatedAt: agent.status?.updatedAt ? new Date(agent.status.updatedAt).toISOString() : null
        },
        capacity: {
          currentConversations: currentConversations.length,
          maxConcurrentChats: agent.status?.maxConcurrentChats || 5,
          availableSlots: (agent.status?.maxConcurrentChats || 5) - currentConversations.length,
          utilization: Math.round((currentConversations.length / (agent.status?.maxConcurrentChats || 5)) * 100)
        },
        performance: {
          conversationsToday: conversationsToday.length,
          totalConversations: agent.performance?.totalConversations || 0,
          avgResponseTime: agent.performance?.avgResponseTime || 0,
          avgResolutionTime: agent.performance?.avgResolutionTime || 0,
          customerSatisfaction: agent.performance?.customerSatisfaction || 0,
          firstResponseTimeAvg: agent.performance?.firstResponseTimeAvg || 0
        },
        currentConversations: currentConversations.map((conv: any) => ({
          conversationId: conv.id,
          customerId: conv.customerId,
          customerName: conv.customer?.name,
          status: conv.status,
          priority: conv.priority,
          startedAt: new Date(conv.assignedAt || conv.createdAt).toISOString(),
          lastMessageAt: conv.updatedAt ? new Date(conv.updatedAt).toISOString() : null
        })),
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting agent details:', error);
      throw error;
    }
  }


  async setAgentCapacity(agentId: string, capacityData: any): Promise<any> {
    try {
      console.log(`📊 Setting capacity for agent ${agentId}`);
      
      const { maxConcurrentChats, department, temporaryCapacity } = capacityData;
      const now = Date.now();
      
      const agent = await this.getAgentById(agentId);
      if (!agent) {
        throw this.createServiceError('AGENT_NOT_FOUND', `Agent ${agentId} not found`);
      }
      
      if (maxConcurrentChats < 1 || maxConcurrentChats > 20) {
        throw this.createServiceError('INVALID_CAPACITY', 'Max concurrent chats must be between 1 and 20');
      }
      
      const currentConversations = agent.status?.currentConversations?.length || 0;
      const previousCapacity = agent.status?.maxConcurrentChats || 5;
      
      // Update agent capacity
      const statusUpdates: any = {
        maxConcurrentChats: maxConcurrentChats,
        capacityUpdatedAt: now,
        capacityUpdatedBy: 'system' // Could be supervisorId in real system
      };
      
      if (temporaryCapacity) {
        statusUpdates.temporaryCapacity = {
          maxConcurrentChats,
          validUntil: temporaryCapacity.validUntil || (now + (24 * 60 * 60 * 1000)), // 24 hours default
          reason: temporaryCapacity.reason || 'Temporary capacity adjustment'
        };
      }
      
      await firebaseService.update(`${this.AGENTS_PATH}/${agentId}/status`, statusUpdates);      
      return {
        agentId,
        previousCapacity,
        newCapacity: maxConcurrentChats,
        currentConversations,
        availableSlots: maxConcurrentChats - currentConversations,
        utilization: Math.round((currentConversations / maxConcurrentChats) * 100),
        temporaryCapacity: temporaryCapacity || null,
        updatedAt: new Date(now).toISOString()
      };
      
    } catch (error) {
      console.error('Error setting agent capacity:', error);
      throw error;
    }
  }

  // ========================================
  // QUEUE MANAGEMENT OPERATIONS
  // ========================================

  async getAllQueues(): Promise<any> {
    try {
      console.log('📋 Getting all department queues');
      
      // Get all conversations in queue status
      const queuedConversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'status', operator: 'in', value: ['new', 'waiting', 'pending_acceptance'] }
      ]);
      
      // Group by department
      const queuesByDepartment: { [key: string]: any } = {};
      
      queuedConversations.forEach((conv: any) => {
        const departmentId = conv.departmentId || 'general';
        if (!queuesByDepartment[departmentId]) {
          queuesByDepartment[departmentId] = {
            departmentId,
            departmentName: this.getDepartmentName(departmentId),
            conversations: [],
            metrics: {
              total: 0,
              byStatus: { new: 0, waiting: 0, pending_acceptance: 0 },
              byPriority: { low: 0, normal: 0, high: 0, urgent: 0, critical: 0 },
              avgWaitTime: 0,
              longestWait: 0
            }
          };
        }
        
        const queue = queuesByDepartment[departmentId];
        queue.conversations.push({
          conversationId: conv.id,
          customerId: conv.customerId,
          customerName: conv.customer?.name,
          status: conv.status,
          priority: conv.priority || 2,
          channel: conv.channel,
          createdAt: new Date(conv.createdAt).toISOString(),
          waitTime: Math.floor((Date.now() - conv.createdAt) / 1000), // seconds
          lastMessageAt: conv.updatedAt ? new Date(conv.updatedAt).toISOString() : null
        });
        
        // Update metrics
        queue.metrics.total++;
        queue.metrics.byStatus[conv.status] = (queue.metrics.byStatus[conv.status] || 0) + 1;
        
        const priorityName = this.getPriorityName(conv.priority || 2);
        queue.metrics.byPriority[priorityName] = (queue.metrics.byPriority[priorityName] || 0) + 1;
      });
      
      // Calculate wait time metrics
      Object.values(queuesByDepartment).forEach((queue: any) => {
        if (queue.conversations.length > 0) {
          const waitTimes = queue.conversations.map((conv: any) => conv.waitTime);
          queue.metrics.avgWaitTime = Math.floor(waitTimes.reduce((sum: number, time: number) => sum + time, 0) / waitTimes.length);
          queue.metrics.longestWait = Math.max(...waitTimes);
        }
      });
      
      return {
        queues: Object.values(queuesByDepartment),
        totalQueued: queuedConversations.length,
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting all queues:', error);
      throw error;
    }
  }

  async getQueueByDepartment(departmentId: string): Promise<any> {
    try {
      console.log(`📋 Getting queue for department ${departmentId}`);
      
      const queuedConversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'departmentId', operator: '==', value: departmentId },
        { field: 'status', operator: 'in', value: ['new', 'waiting', 'pending_acceptance'] }
      ]);
      
      // Get available agents in this department
      const availableAgents = await firebaseService.query(this.AGENTS_PATH, [
        { field: 'profile.departments', operator: 'array-contains', value: departmentId },
        { field: 'status.isAvailable', operator: '==', value: true }
      ]);
      
      const conversations = queuedConversations.map((conv: any) => ({
        conversationId: conv.id,
        customerId: conv.customerId,
        customerName: conv.customer?.name,
        status: conv.status,
        priority: conv.priority || 2,
        priorityName: this.getPriorityName(conv.priority || 2),
        channel: conv.channel,
        createdAt: new Date(conv.createdAt).toISOString(),
        waitTime: Math.floor((Date.now() - conv.createdAt) / 1000),
        waitTimeFormatted: this.formatDuration(Math.floor((Date.now() - conv.createdAt) / 1000)),
        lastMessageAt: conv.updatedAt ? new Date(conv.updatedAt).toISOString() : null,
        routingInfo: {
          assignedDepartment: conv.routingInfo?.assignedDepartment,
          aiAnalysisAttempts: conv.routingInfo?.aiAnalysisAttempts || 0
        }
      }));
      
      // Sort by priority (highest first) then by wait time (longest first)
      conversations.sort((a: any, b: any) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority; // Higher priority first
        }
        return b.waitTime - a.waitTime; // Longer wait first
      });
      
      const metrics = {
        total: conversations.length,
        byStatus: this.groupBy(conversations, 'status'),
        byPriority: this.groupBy(conversations, 'priorityName'),
        avgWaitTime: conversations.length > 0 
          ? Math.floor(conversations.reduce((sum: number, conv: any) => sum + conv.waitTime, 0) / conversations.length)
          : 0,
        longestWait: conversations.length > 0 
          ? Math.max(...conversations.map((conv: any) => conv.waitTime))
          : 0,
        availableAgents: availableAgents.length,
        estimatedWaitTime: this.calculateEstimatedWaitTime(conversations.length, availableAgents.length)
      };
      
      return {
        departmentId,
        departmentName: this.getDepartmentName(departmentId),
        conversations,
        metrics,
        availableAgents: availableAgents.map((agent: any) => ({
          agentId: agent.id,
          name: agent.profile?.name,
          currentConversations: agent.status?.currentConversations?.length || 0,
          maxConcurrentChats: agent.status?.maxConcurrentChats || 5
        })),
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting department queue:', error);
      throw error;
    }
  }

  // ========================================
  // ANALYTICS OPERATIONS
  // ========================================

  async getConversationAnalytics(filters: any = {}): Promise<any> {
    try {
      console.log('📊 Getting conversation analytics with filters:', filters);
      
      const { 
        startDate, 
        endDate, 
        departmentId, 
        agentId, 
        status,
        period = 'today' // today, yesterday, last7days, last30days, custom
      } = filters;
      
      const timeRange = this.getTimeRange(period, startDate, endDate);
      
      const queryFilters: any[] = [
        { field: 'createdAt', operator: '>=', value: timeRange.start },
        { field: 'createdAt', operator: '<=', value: timeRange.end }
      ];
      
      if (departmentId) {
        queryFilters.push({ field: 'departmentId', operator: '==', value: departmentId });
      }
      
      if (agentId) {
        queryFilters.push({ field: 'agentId', operator: '==', value: agentId });
      }
      
      if (status) {
        queryFilters.push({ field: 'status', operator: '==', value: status });
      }
      
      const conversations = await firebaseService.query(this.CONVERSATIONS_PATH, queryFilters);
      
      // Calculate analytics
      const analytics = {
        totalConversations: conversations.length,
        conversationsByStatus: this.groupBy(conversations, 'status'),
        conversationsByChannel: this.groupBy(conversations, 'channel'),
        conversationsByDepartment: this.groupBy(conversations, 'departmentId'),
        averageResponseTime: this.calculateAverageResponseTime(conversations),
        averageResolutionTime: this.calculateAverageResolutionTime(conversations),
        customerSatisfaction: this.calculateCustomerSatisfaction(conversations),
        transferRate: this.calculateTransferRate(conversations),
        resolutionRate: this.calculateResolutionRate(conversations),
        hourlyDistribution: this.calculateHourlyDistribution(conversations),
        dailyTrend: this.calculateDailyTrend(conversations, timeRange),
        topAgents: this.calculateTopAgents(conversations),
        priorityDistribution: this.groupBy(conversations, 'priority')
      };
      
      return {
        period,
        timeRange: {
          start: new Date(timeRange.start).toISOString(),
          end: new Date(timeRange.end).toISOString()
        },
        filters: { departmentId, agentId, status },
        analytics,
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting conversation analytics:', error);
      throw error;
    }
  }

  // ========================================
  // HELPER METHODS
  // ========================================

  private getDepartmentName(departmentId: string): string {
    const departmentNames: { [key: string]: string } = {
      'technical_support': 'Technical Support',
      'sales': 'Sales',
      'billing': 'Billing',
      'general': 'General Support'
    };
    
    return departmentNames[departmentId] || departmentId;
  }

  private getPriorityName(priority: number): string {
    const priorityNames = ['', 'low', 'normal', 'high', 'urgent', 'critical'];
    return priorityNames[priority] || 'normal';
  }

  private formatDuration(seconds: number): string {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  }

  private groupBy(array: any[], key: string): { [key: string]: number } {
    return array.reduce((result: any, item: any) => {
      const group = item[key] || 'unknown';
      result[group] = (result[group] || 0) + 1;
      return result;
    }, {});
  }

  private calculateEstimatedWaitTime(queueLength: number, availableAgents: number): number {
    if (availableAgents === 0) return 600; // 10 minutes default
    const avgHandlingTime = 300; // 5 minutes average
    return Math.floor((queueLength / availableAgents) * avgHandlingTime);
  }

  private getTimeRange(period: string, startDate?: string, endDate?: string): { start: number, end: number } {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (period) {
      case 'today':
        return { start: today.getTime(), end: now.getTime() };
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return { start: yesterday.getTime(), end: today.getTime() };
      case 'last7days':
        const weekAgo = new Date(today);
        weekAgo.setDate(weekAgo.getDate() - 7);
        return { start: weekAgo.getTime(), end: now.getTime() };
      case 'last30days':
        const monthAgo = new Date(today);
        monthAgo.setDate(monthAgo.getDate() - 30);
        return { start: monthAgo.getTime(), end: now.getTime() };
      case 'custom':
        return {
          start: startDate ? new Date(startDate).getTime() : today.getTime(),
          end: endDate ? new Date(endDate).getTime() : now.getTime()
        };
      default:
        return { start: today.getTime(), end: now.getTime() };
    }
  }

  private calculateAverageResponseTime(conversations: any[]): number {
    // Mock calculation - would use actual message timestamps
    const responseTimes = conversations.map(() => Math.floor(Math.random() * 300) + 30); // 30-330 seconds
    return responseTimes.length > 0 ? Math.floor(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length) : 0;
  }

  private calculateAverageResolutionTime(conversations: any[]): number {
    const closedConversations = conversations.filter(conv => conv.closedAt);
    if (closedConversations.length === 0) return 0;
    
    const resolutionTimes = closedConversations.map((conv: any) => 
      Math.floor((conv.closedAt - conv.createdAt) / 1000)
    );
    
    return Math.floor(resolutionTimes.reduce((sum, time) => sum + time, 0) / resolutionTimes.length);
  }

  private calculateCustomerSatisfaction(conversations: any[]): number {
    // Mock - would use actual satisfaction ratings
    return Math.floor(Math.random() * 20) + 80; // 80-100%
  }

  private calculateTransferRate(conversations: any[]): number {
    const transferredConversations = conversations.filter(conv => 
      conv.metadata?.transferCount && conv.metadata.transferCount > 0
    );
    return conversations.length > 0 ? Math.floor((transferredConversations.length / conversations.length) * 100) : 0;
  }

  private calculateResolutionRate(conversations: any[]): number {
    const resolvedConversations = conversations.filter(conv => conv.status === 'closed');
    return conversations.length > 0 ? Math.floor((resolvedConversations.length / conversations.length) * 100) : 0;
  }

  private calculateHourlyDistribution(conversations: any[]): { [hour: string]: number } {
    const hourlyData: { [hour: string]: number } = {};
    for (let i = 0; i < 24; i++) {
      hourlyData[i.toString().padStart(2, '0')] = 0;
    }
    
    conversations.forEach((conv: any) => {
      const hour = new Date(conv.createdAt).getHours().toString().padStart(2, '0');
      hourlyData[hour]++;
    });
    
    return hourlyData;
  }

  private calculateDailyTrend(conversations: any[], timeRange: { start: number, end: number }): any[] {
    const dailyData: { [date: string]: number } = {};
    const startDate = new Date(timeRange.start);
    const endDate = new Date(timeRange.end);
    
    // Initialize all days in range
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      dailyData[dateStr] = 0;
    }
    
    // Count conversations by day
    conversations.forEach((conv: any) => {
      const dateStr = new Date(conv.createdAt).toISOString().split('T')[0];
      if (dailyData[dateStr] !== undefined) {
        dailyData[dateStr]++;
      }
    });
    
    return Object.entries(dailyData).map(([date, count]) => ({ date, count }));
  }

  private calculateTopAgents(conversations: any[]): any[] {
    const agentStats: { [agentId: string]: any } = {};
    
    conversations.forEach((conv: any) => {
      if (conv.assignedTo) {
        if (!agentStats[conv.assignedTo]) {
          agentStats[conv.assignedTo] = {
            agentId: conv.assignedTo,
            conversationsHandled: 0,
            avgResolutionTime: 0,
            customerSatisfaction: Math.floor(Math.random() * 20) + 80
          };
        }
        agentStats[conv.assignedTo].conversationsHandled++;
      }
    });
    
    return Object.values(agentStats)
      .sort((a: any, b: any) => b.conversationsHandled - a.conversationsHandled)
      .slice(0, 10); // Top 10 agents
  }

  // ========================================
  // REMAINING CRITICAL ENDPOINTS
  // ========================================

  async listConversationsWithFilters(filters: any = {}): Promise<any> {
    try {
      console.log('📋 Listing conversations with filters:', filters);
      
      const { 
        status, 
        assignedTo, 
        assignedAgentId,
        departmentId, 
        channel,
        priority,
        limit = 50,
        offset = 0 
      } = filters;
      
      const queryFilters: any[] = [];
      
      if (status) {
        queryFilters.push({ field: 'status', operator: '==', value: status });
      }
      
      if (assignedAgentId) {
        queryFilters.push({ field: 'assignedAgentId', operator: '==', value: assignedAgentId });
      }
      
      if (assignedTo) {
        queryFilters.push({ field: 'agentId', operator: '==', value: assignedTo });
      }
      
      if (departmentId) {
        queryFilters.push({ field: 'departmentId', operator: '==', value: departmentId });
      }
      
      if (channel) {
        queryFilters.push({ field: 'channel', operator: '==', value: channel });
      }
      
      if (priority) {
        queryFilters.push({ field: 'priority', operator: '==', value: priority });
      }
      
      const allConversations = await firebaseService.query(this.CONVERSATIONS_PATH, queryFilters);
      
      // Apply pagination
      const conversations = allConversations
        .slice(offset, offset + limit)
        .map((conv: any) => {
          // Validate dates before converting
          const createdAt = conv.createdAt && !isNaN(new Date(conv.createdAt).getTime()) 
            ? new Date(conv.createdAt).toISOString() 
            : new Date().toISOString(); // fallback to current date
          
          const updatedAt = conv.updatedAt && !isNaN(new Date(conv.updatedAt).getTime()) 
            ? new Date(conv.updatedAt).toISOString() 
            : createdAt; // fallback to createdAt
          
          const lastMessageAt = conv.lastMessageAt && !isNaN(new Date(conv.lastMessageAt).getTime()) 
            ? new Date(conv.lastMessageAt).toISOString() 
            : null;

          return {
            // ✅ Match TypeScript contracts exactly
            id: conv.id, // NOT conversationId
            customerId: conv.customerId,
            agentId: conv.agentId || null,
            supervisorId: conv.supervisorId || null,
            status: conv.status,
            priority: conv.priority,
            channel: conv.channel,
            department: conv.department, // NOT departmentId
            metadata: conv.metadata || {
              customerName: conv.customer?.name || 'Unknown',
              customerPhone: conv.customer?.phone,
              customerEmail: conv.customer?.email,
              tags: conv.metadata?.tags || []
            },
            createdAt,
            updatedAt,
            lastActivityAt: conv.lastActivityAt || updatedAt,
            assignedAt: conv.assignedAt,
            closedAt: conv.closedAt,
            // ✅ UI extension fields - full objects
            customer: conv.customer ? {
              id: conv.customerId,
              name: conv.customer.name,
              phone: conv.customer.phone,
              email: conv.customer.email,
              totalConversations: conv.customer.totalConversations || 1
            } : undefined,
            lastMessage: conv.lastMessage ? {
              id: conv.lastMessage.id,
              conversationId: conv.id,
              senderId: conv.lastMessage.senderId,
              senderType: conv.lastMessage.senderType,
              content: conv.lastMessage.content,
              type: conv.lastMessage.type,
              timestamp: conv.lastMessage.timestamp
            } : undefined,
            unreadCount: conv.unreadCount || 0
          };
        });
      
      return {
        conversations,
        pagination: {
          total: allConversations.length,
          limit,
          offset,
          hasMore: offset + limit < allConversations.length
        },
        filters,
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error listing conversations:', error);
      throw error;
    }
  }

  async getSystemStatus(): Promise<any> {
    try {
      console.log('🔍 Getting system status');
      
      // Get basic counts
      const [allConversations, allAgents] = await Promise.all([
        firebaseService.query(this.CONVERSATIONS_PATH, []),
        firebaseService.query(this.AGENTS_PATH, [])
      ]);
      
      const activeConversations = allConversations.filter((conv: any) => 
        ['active'].includes(conv.status)
      );
      
      const queuedConversations = allConversations.filter((conv: any) => 
        ['new', 'waiting', 'pending_acceptance'].includes(conv.status)
      );
      
      const onlineAgents = allAgents.filter((agent: any) => 
        agent.status?.availability === 'online' && agent.status?.isAvailable === true
      );
      
      const busyAgents = allAgents.filter((agent: any) => 
        agent.status?.availability === 'busy' || (agent.status?.currentConversations?.length || 0) > 0
      );
      
      return {
        service: 'chat-realtime',
        status: 'healthy',
        uptime: process.uptime(),
        timestamp: Date.now(),
        statistics: {
          conversations: {
            total: allConversations.length,
            active: activeConversations.length,
            queued: queuedConversations.length,
            completed: allConversations.filter((conv: any) => conv.status === 'closed').length
          },
          agents: {
            total: allAgents.length,
            online: onlineAgents.length,
            busy: busyAgents.length,
            available: onlineAgents.filter((agent: any) => 
              (agent.status?.currentConversations?.length || 0) < (agent.status?.maxConcurrentChats || 5)
            ).length
          },
          queues: {
            totalQueued: queuedConversations.length,
            avgWaitTime: queuedConversations.length > 0 
              ? Math.floor(queuedConversations.reduce((sum: number, conv: any) => 
                  sum + Math.floor((Date.now() - conv.createdAt) / 1000), 0
                ) / queuedConversations.length)
              : 0,
            longestWait: queuedConversations.length > 0
              ? Math.max(...queuedConversations.map((conv: any) => 
                  Math.floor((Date.now() - conv.createdAt) / 1000)
                ))
              : 0
          }
        },
        health: {
          firebase: await firebaseService.healthCheck(),
          memory: {
            used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
            total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
          }
        }
      };
      
    } catch (error) {
      console.error('Error getting system status:', error);
      return {
        service: 'chat-realtime',
        status: 'unhealthy',
        error: error.message,
        timestamp: Date.now()
      };
    }
  }

  async exportConversation(conversationId: string, exportData: any): Promise<any> {
    try {
      console.log(`📄 Exporting conversation ${conversationId}`);
      
      const { 
        format = 'json', 
        includeNotes = true, 
        includeSystemMessages = false,
        requestedBy 
      } = exportData;
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const messages = await this.getMessages(conversationId);
      
      // Filter messages based on options
      let filteredMessages = messages;
      if (!includeSystemMessages) {
        filteredMessages = messages.filter((msg: any) => msg.senderType !== 'system');
      }
      
      // Prepare export data
      const exportPayload = {
        conversation: {
          id: conversation.id,
          customer: conversation.customer,
          channel: conversation.channel,
          status: conversation.status,
          assignedTo: conversation.assignedTo,
          departmentId: conversation.departmentId,
          priority: conversation.priority,
          createdAt: new Date(conversation.createdAt).toISOString(),
          updatedAt: new Date(conversation.updatedAt).toISOString(),
          closedAt: conversation.closedAt ? new Date(conversation.closedAt).toISOString() : null
        },
        messages: filteredMessages.map((msg: any) => ({
          id: msg.id,
          senderId: msg.senderId,
          senderType: msg.senderType,
          content: msg.content,
          messageType: msg.messageType,
          timestamp: new Date(msg.timestamp).toISOString(),
          status: msg.status
        })),
        notes: includeNotes ? (conversation.metadata?.notes || []) : [],
        metadata: {
          exportedAt: new Date().toISOString(),
          exportedBy: requestedBy,
          format,
          totalMessages: filteredMessages.length,
          conversationDuration: conversation.closedAt 
            ? Math.floor((conversation.closedAt - conversation.createdAt) / 1000)
            : Math.floor((Date.now() - conversation.createdAt) / 1000)
        }
      };
      
      // TODO: Could add different format processing here (PDF, CSV, etc.)
      
      return {
        conversationId,
        exportId: `exp_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
        format,
        data: exportPayload,
        size: JSON.stringify(exportPayload).length,
        exportedAt: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('Error exporting conversation:', error);
      throw error;
    }
  }

  async getConversationHistory(conversationId: string): Promise<any> {
    try {
      console.log(`📚 Getting history for conversation ${conversationId}`);
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      // Get all system messages that track status changes
      const systemMessages = await firebaseService.query(
        `${this.CONVERSATIONS_PATH}/${conversationId}/messages`, 
        [{ field: 'senderType', operator: '==', value: 'system' }]
      );
      
      const historyEvents = systemMessages
        .map((msg: any) => ({
          eventId: msg.id,
          timestamp: new Date(msg.timestamp).toISOString(),
          action: msg.systemData?.action || 'unknown',
          description: msg.content,
          details: msg.systemData?.details || {},
          performedBy: msg.systemData?.details?.agentId || 'system'
        }))
        .sort((a: any, b: any) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      
      // Add creation event
      historyEvents.unshift({
        eventId: 'creation',
        timestamp: new Date(conversation.createdAt).toISOString(),
        action: 'create',
        description: 'Conversation created',
        details: {
          channel: conversation.channel,
          customerId: conversation.customerId,
          source: conversation.source
        },
        performedBy: 'system'
      });
      
      return {
        conversationId,
        customer: conversation.customer,
        currentStatus: conversation.status,
        createdAt: new Date(conversation.createdAt).toISOString(),
        history: historyEvents,
        totalEvents: historyEvents.length,
        timeline: {
          created: new Date(conversation.createdAt).toISOString(),
          assigned: conversation.assignedAt ? new Date(conversation.assignedAt).toISOString() : null,
          closed: conversation.closedAt ? new Date(conversation.closedAt).toISOString() : null,
          lastUpdated: new Date(conversation.updatedAt).toISOString()
        },
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting conversation history:', error);
      throw error;
    }
  }

  async getConversationMetrics(conversationId: string): Promise<any> {
    try {
      console.log(`📊 Getting metrics for conversation ${conversationId}`);
      
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw this.createServiceError('CONVERSATION_NOT_FOUND', 'Conversation not found');
      }
      
      const messages = await this.getMessages(conversationId);
      
      // Calculate metrics
      const customerMessages = messages.filter((msg: any) => msg.senderType === 'customer');
      const agentMessages = messages.filter((msg: any) => msg.senderType === 'agent');
      const systemMessages = messages.filter((msg: any) => msg.senderType === 'system');
      
      // Response time calculations (mock for now)
      const responseTimes = agentMessages.map(() => Math.floor(Math.random() * 300) + 30);
      const avgResponseTime = responseTimes.length > 0 
        ? Math.floor(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length)
        : 0;
      
      const firstResponseTime = conversation.assignedAt && messages.length > 0
        ? Math.floor((new Date(messages[0].timestamp).getTime() - conversation.assignedAt) / 1000)
        : null;
      
      const conversationDuration = conversation.closedAt
        ? Math.floor((conversation.closedAt - conversation.createdAt) / 1000)
        : Math.floor((Date.now() - conversation.createdAt) / 1000);
      
      return {
        conversationId,
        status: conversation.status,
        customer: conversation.customer,
        assignedAgent: conversation.assignedTo,
        departmentId: conversation.departmentId,
        metrics: {
          duration: {
            total: conversationDuration,
            formatted: this.formatDuration(conversationDuration),
            inQueue: conversation.assignedAt 
              ? Math.floor((conversation.assignedAt - conversation.createdAt) / 1000)
              : conversationDuration,
            active: conversation.assignedAt && conversation.closedAt
              ? Math.floor((conversation.closedAt - conversation.assignedAt) / 1000)
              : conversation.assignedAt
                ? Math.floor((Date.now() - conversation.assignedAt) / 1000)
                : 0
          },
          messages: {
            total: messages.length,
            customer: customerMessages.length,
            agent: agentMessages.length,
            system: systemMessages.length,
            ratio: agentMessages.length > 0 
              ? Math.round((customerMessages.length / agentMessages.length) * 100) / 100
              : 0
          },
          responseTimes: {
            average: avgResponseTime,
            first: firstResponseTime,
            averageFormatted: this.formatDuration(avgResponseTime),
            firstFormatted: firstResponseTime ? this.formatDuration(firstResponseTime) : null
          },
          transfers: {
            count: conversation.metadata?.transferCount || 0,
            history: conversation.transferInfo?.transferHistory || []
          },
          priority: {
            current: conversation.priority || 2,
            name: this.getPriorityName(conversation.priority || 2),
            escalationLevel: conversation.metadata?.escalationLevel || 0
          },
          satisfaction: {
            rating: null, // Would be set from actual surveys
            collected: false
          }
        },
        timeline: {
          created: new Date(conversation.createdAt).toISOString(),
          assigned: conversation.assignedAt ? new Date(conversation.assignedAt).toISOString() : null,
          closed: conversation.closedAt ? new Date(conversation.closedAt).toISOString() : null,
          lastActivity: new Date(conversation.updatedAt).toISOString()
        },
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('Error getting conversation metrics:', error);
      throw error;
    }
  }

  // ========================================
  // DYNAMIC QUEUE MANAGEMENT
  // ========================================

  /**
   * Synchronizes Firebase queues with active departments from Supabase
   * Creates/updates queues based on current department structure
   */
  async syncDepartmentQueues(): Promise<void> {
    try {
      console.log('🔄 Syncing department queues with Supabase...');
      
      // Get active departments from Supabase
      const { data: departments, error } = await this.supabase
        .from('departments')
        .select('id, name, description, is_active')
        .eq('is_active', true);

      if (error) {
        console.error('Failed to fetch departments from Supabase:', error);
        // Fallback to default queues
        await this.createDefaultQueues();
        return;
      }

      if (!departments || departments.length === 0) {
        console.warn('No active departments found - creating default queues');
        await this.createDefaultQueues();
        return;
      }

      // Ensure 'general' department exists as fallback
      const hasGeneral = departments.some(dept => dept.name === 'general');
      if (!hasGeneral) {
        departments.push({ id: 'general', name: 'general', description: 'General support queue', is_active: true });
      }

      // Create/update queues for each active department
      const queuePromises = departments.map(dept => this.createOrUpdateQueue(dept));
      await Promise.all(queuePromises);

      // Clean up inactive queues
      await this.cleanupInactiveQueues(departments.map(d => d.name));

      console.log(`✅ Successfully synced ${departments.length} department queues`);
    } catch (error) {
      console.error('Queue synchronization failed:', error);
      await this.createDefaultQueues(); // Fallback
    }
  }

  /**
   * Creates or updates a queue for a specific department
   */
  private async createOrUpdateQueue(department: any): Promise<void> {
    const queueId = `${department.name}_queue`;
    
    try {
      // Check if queue already exists
      const existingQueue = await firebaseService.get(`${this.QUEUES_PATH}/${queueId}`);
      
      const queueData = {
        id: queueId,
        departmentId: department.id,
        departmentName: department.name,
        description: department.description || `Queue for ${department.name} department`,
        isActive: true,
        conversations: existingQueue?.conversations || {},
        metrics: existingQueue?.metrics || {
          totalCount: 0,
          avgWaitTime: 0,
          longestWaitTime: 0,
          processedToday: 0
        },
        lastSyncAt: Date.now(),
        createdAt: existingQueue?.createdAt || Date.now(),
        updatedAt: Date.now()
      };

      await firebaseService.set(`${this.QUEUES_PATH}/${queueId}`, queueData);
      console.log(`📁 Queue synchronized: ${queueId}`);
    } catch (error) {
      console.error(`Failed to create/update queue ${queueId}:`, error);
    }
  }

  /**
   * Creates default queues when department sync fails
   */
  private async createDefaultQueues(): Promise<void> {
    const defaultQueues = ['general', 'technical_support', 'sales'];
    
    console.log('🔧 Creating default fallback queues...');
    
    for (const deptName of defaultQueues) {
      await this.createOrUpdateQueue({
        id: deptName,
        name: deptName,
        description: `Default ${deptName} queue`,
        is_active: true
      });
    }
  }

  /**
   * Removes queues for departments that are no longer active
   */
  private async cleanupInactiveQueues(activeDepartmentNames: string[]): Promise<void> {
    try {
      // Get all existing queues
      const allQueues = await firebaseService.get(this.QUEUES_PATH) || {};
      
      const cleanupPromises = Object.keys(allQueues).map(async (queueId) => {
        const queue = allQueues[queueId];
        const departmentName = queue.departmentName;
        
        // Skip if department is still active
        if (activeDepartmentNames.includes(departmentName)) {
          return;
        }
        
        // Archive inactive queue instead of deleting (preserve data)
        await firebaseService.update(`${this.QUEUES_PATH}/${queueId}`, {
          isActive: false,
          archivedAt: Date.now(),
          lastSyncAt: Date.now()
        });
        
        console.log(`📦 Archived inactive queue: ${queueId}`);
      });
      
      await Promise.all(cleanupPromises);
    } catch (error) {
      console.error('Queue cleanup failed:', error);
    }
  }

  /**
   * Gets the appropriate queue ID for a conversation based on department
   */
  getDepartmentQueueId(departmentName: string): string {
    return `${departmentName || 'general'}_queue`;
  }

  /**
   * Lists all active queues with their current state
   */
  async getActiveQueues(): Promise<any[]> {
    try {
      const allQueues = await firebaseService.get(this.QUEUES_PATH) || {};
      
      return Object.values(allQueues)
        .filter((queue: any) => queue.isActive)
        .map((queue: any) => ({
          id: queue.id,
          departmentName: queue.departmentName,
          conversationCount: Object.keys(queue.conversations || {}).length,
          metrics: queue.metrics,
          lastSyncAt: queue.lastSyncAt
        }));
    } catch (error) {
      console.error('Failed to get active queues:', error);
      return [];
    }
  }

  private createServiceError(code: string, message: string, details?: any): ServiceError {
    return {
      code,
      message,
      details,
    };
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      return await firebaseService.healthCheck();
    } catch (error) {
      console.error('ConversationService health check failed:', error);
      return false;
    }
  }

  // Agent status management
  async updateAgentStatus(agentId: string, statusData: any, userToken?: string): Promise<any> {
    try {
      console.log(`👨‍💼 Updating agent status for ${agentId}:`, statusData);

      const { 
        status, 
        reason, 
        supervisorAuthorizationId, 
        estimatedDuration,
        availability, 
        maxConcurrentSessions, 
        current_sessions 
      } = statusData;

      // Use class-level Supabase client with timeout protection
      const supabase = this.supabase;
      
      // Create user-authenticated Supabase client for RLS queries if token provided
      let userSupabase = supabase;
      if (userToken) {
        userSupabase = createClient(
          process.env.SUPABASE_URL!,
          process.env.SUPABASE_ANON_KEY!,
          {
            global: {
              headers: {
                Authorization: `Bearer ${userToken}`
              }
            }
          }
        );
      }

      // 1. Get current agent status for validation and history with timeout protection
      let currentAgent, fetchError;
      try {
        const result = await this.withTimeout<any>(
          supabase
            .from('agents')
            .select('name, role') // FIXED: removed status field
            .eq('id', agentId)
            .single()
        );
        currentAgent = result.data;
        fetchError = result.error;
      } catch (timeoutError: any) {
        throw this.createServiceError('SUPABASE_TIMEOUT', 'Database connection timeout - please try again');
      }

      if (fetchError) {
        throw this.createServiceError('AGENT_NOT_FOUND', `Agent ${agentId} not found`);
      }

      // Get current status from Firebase (hybrid architecture)
      console.log(`🔍 [HYBRID] Getting current status from Firebase for agent ${agentId}`);
      let firebaseStatus;
      try {
        firebaseStatus = await firebaseService.get(`agent_status/${agentId}`);
      } catch (error) {
        console.warn(`Failed to get Firebase status for agent ${agentId}, defaulting to 'offline':`, error);
        firebaseStatus = { status: 'offline' };
      }
      
      const previousStatus = firebaseStatus?.status || 'offline';
      console.log(`🔍 [HYBRID] Previous status from Firebase: ${previousStatus}`);

      // 2. AUTHORIZATION LOGIC: Check if status change requires authorization
      if (status === 'busy' && previousStatus !== 'busy') {
        console.log(`🔒 Status change to 'busy' requires supervisor authorization`);
        
        if (!supervisorAuthorizationId) {
          throw this.createServiceError(
            'AUTHORIZATION_REQUIRED', 
            'Changing status to "busy" requires supervisor authorization'
          );
        }

        // Validate the authorization exists and is usable with timeout protection
        let authorization, authError;
        try {
          const result = await this.withTimeout<any>(
            userSupabase
              .from('supervisor_authorizations')
              .select('*')
              .eq('id', supervisorAuthorizationId)
              .eq('agent_id', agentId)
              .eq('authorized_status', 'busy')
              .eq('is_used', false)
              .eq('is_expired', false)
              .limit(1)
              .maybeSingle()
          );
          authorization = result.data;
          authError = result.error;
        } catch (timeoutError: any) {
          console.error('🔐 Authorization validation timeout:', timeoutError.message);
          throw this.createServiceError('SUPABASE_TIMEOUT', 'Authorization validation timeout - please try again');
        }

        if (authError || !authorization) {
          throw this.createServiceError(
            'INVALID_AUTHORIZATION', 
            'Authorization not found, already used, or expired'
          );
        }

        // Check if authorization is expired (additional check)
        if (authorization.expires_at && new Date(authorization.expires_at) < new Date()) {
          try {
            await this.withTimeout<any>(
              supabase
                .from('supervisor_authorizations')
                .update({ is_expired: true })
                .eq('id', supervisorAuthorizationId)
            );
          } catch (timeoutError) {
            console.warn('⚠️ Could not mark authorization as expired due to timeout');
          }
          
          throw this.createServiceError(
            'AUTHORIZATION_EXPIRED', 
            'Authorization has expired'
          );
        }

        // Mark authorization as used
        try {
          await this.withTimeout<any>(
            supabase
              .from('supervisor_authorizations')
              .update({ 
                is_used: true, 
                used_at: new Date().toISOString() 
              })
              .eq('id', supervisorAuthorizationId)
          );
        } catch (timeoutError) {
          console.warn('⚠️ Could not mark authorization as used due to timeout, but continuing with status update');
        }

        console.log(`✅ Authorization ${supervisorAuthorizationId} validated and marked as used`);
      }

      // 3. Update agent metadata in Supabase (only capacity-related fields, status is in Firebase)
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      // Only update capacity fields in Supabase, NOT status (status is in Firebase now)
      if (maxConcurrentSessions) updateData.max_concurrent_sessions = maxConcurrentSessions;
      if (current_sessions !== undefined) updateData.current_sessions = current_sessions;

      // Update agent metadata with timeout protection (only if there's something to update)
      let updatedAgent, supabaseError;
      if (Object.keys(updateData).length > 1) { // More than just updated_at
        try {
          const result = await this.withTimeout<any>(
            supabase
              .from('agents')
              .update(updateData)
              .eq('id', agentId)
              .select()
              .single()
          );
          updatedAgent = result.data;
          supabaseError = result.error;
        } catch (timeoutError: any) {
          throw this.createServiceError('SUPABASE_TIMEOUT', 'Failed to update agent metadata - database timeout');
        }

        if (supabaseError) {
          throw supabaseError;
        }
      } else {
        // No Supabase update needed, just get current agent data
        try {
          const result = await this.withTimeout<any>(
            supabase
              .from('agents')
              .select()
              .eq('id', agentId)
              .single()
          );
          updatedAgent = result.data;
          supabaseError = result.error;
        } catch (timeoutError: any) {
          throw this.createServiceError('SUPABASE_TIMEOUT', 'Failed to get agent data - database timeout');
        }

        if (supabaseError) {
          throw supabaseError;
        }
      }

      // 4. Record status change in history
      const statusHistoryData = {
        agent_id: agentId,
        status: status || previousStatus,
        previous_status: previousStatus,
        reason,
        supervisor_authorization_id: supervisorAuthorizationId || null,
        start_time: new Date().toISOString()
      };

      // Record status change in history with timeout protection
      try {
        const result = await this.withTimeout<any>(
          supabase
            .from('agent_status_history')
            .insert(statusHistoryData)
        );
        if (result.error) {
          console.warn('Failed to record status history:', result.error);
        }
      } catch (timeoutError) {
        console.warn('⚠️ Could not record status history due to timeout');
        // Don't fail the entire operation for history logging
      }

      // 5. Update in Firebase (real-time status for UI) - HYBRID ARCHITECTURE
      const firebaseStatusData = {
        id: agentId,
        status: status || previousStatus, // Use 'status' field as expected by dashboard
        isAvailable: status === 'available',
        lastSeen: Date.now(),
        // Preserve existing data and only update what we need
        currentConversations: firebaseStatus?.currentConversations || [],
        capabilities: updatedAgent.departments || ['general'],
        maxConversations: updatedAgent.max_concurrent_sessions || 5,
        statusReason: reason || null,
        statusStartTime: Date.now(),
        // Keep connectedAt from original connection
        connectedAt: firebaseStatus?.connectedAt || Date.now()
      };

      console.log(`🔥 [HYBRID] Updating Firebase agent_status/${agentId}:`, firebaseStatusData);
      await firebaseService.update(`agent_status/${agentId}`, firebaseStatusData);

      console.log(`✅ Agent ${agentId} status updated: ${previousStatus} → ${status}`);

      // 6. If agent is no longer available, check for active conversations
      let activeConversationsWarning = null;
      if (status !== 'available' && previousStatus === 'available') {
        try {
          const activeChats = await firebaseService.query(this.CONVERSATIONS_PATH, [
            { field: 'agentId', operator: '==', value: agentId },
            { field: 'status', operator: 'in', value: ['active', 'pending_acceptance'] }
          ]);

          if (activeChats && activeChats.length > 0) {
            activeConversationsWarning = {
              count: activeChats.length,
              message: `Agent has ${activeChats.length} active conversation(s) that may need transfer or closure`
            };
          }
        } catch (error) {
          console.warn('Failed to check active conversations:', error);
        }
      }

      return {
        agentId,
        status: updatedAgent.status,
        previousStatus,
        statusChanged: status !== previousStatus,
        maxConcurrentSessions: updatedAgent.max_concurrent_sessions,
        updatedAt: updateData.updated_at,
        firebase: firebaseStatusData,
        activeConversationsWarning
      };

    } catch (error) {
      console.error('Error updating agent status:', error);
      throw error;
    }
  }

  // ========================================
  // SUPERVISOR AUTHORIZATION METHODS
  // ========================================

  async createSupervisorAuthorization(supervisorId: string, authData: any): Promise<any> {
    try {
      console.log(`👨‍💼 Creating supervisor authorization from ${supervisorId}:`, authData);

      const { agentId, authorizedStatus, reason, maxDuration, expiresAt } = authData;

      // Initialize Supabase client
      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(
        process.env.SUPABASE_URL, 
        process.env.SUPABASE_ANON_KEY
      );

      // Verify supervisor has permission
      const { data: supervisor, error: supervisorError } = await supabase
        .from('agents')
        .select('role, name')
        .eq('id', supervisorId)
        .single();

      if (supervisorError || !supervisor) {
        throw this.createServiceError('SUPERVISOR_NOT_FOUND', 'Supervisor not found');
      }

      if (!['supervisor', 'admin'].includes(supervisor.role)) {
        throw this.createServiceError('INSUFFICIENT_PERMISSIONS', 'Only supervisors can create authorizations');
      }

      // Verify agent exists
      const { data: agent, error: agentError } = await supabase
        .from('agents')
        .select('name')
        .eq('id', agentId)
        .single();

      if (agentError || !agent) {
        throw this.createServiceError('AGENT_NOT_FOUND', 'Agent not found');
      }

      // Create authorization
      const authorizationData = {
        supervisor_id: supervisorId,
        agent_id: agentId,
        authorized_status: authorizedStatus,
        reason,
        max_duration: maxDuration || null,
        expires_at: expiresAt || null
      };

      const { data: authorization, error: createError } = await supabase
        .from('supervisor_authorizations')
        .insert(authorizationData)
        .select(`
          id,
          supervisor_id,
          agent_id,
          authorized_status,
          reason,
          max_duration,
          created_at,
          expires_at,
          is_used,
          is_expired
        `)
        .single();

      if (createError) {
        throw createError;
      }

      console.log(`✅ Authorization created: ${authorization.id}`);

      return {
        ...authorization,
        supervisorName: supervisor.name,
        agentName: agent.name
      };

    } catch (error) {
      console.error('Error creating supervisor authorization:', error);
      throw error;
    }
  }

  async getAgentStatusHistory(agentId: string, queryParams: any): Promise<any> {
    try {
      console.log(`📊 Getting status history for agent ${agentId}`);

      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(
        process.env.SUPABASE_URL, 
        process.env.SUPABASE_ANON_KEY
      );

      const { limit = 50, offset = 0, days = 7 } = queryParams;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data: history, error } = await supabase
        .from('agent_status_history')
        .select(`
          id,
          status,
          previous_status,
          reason,
          start_time,
          end_time,
          duration,
          created_at,
          supervisor_authorizations(
            id,
            reason,
            supervisor_id,
            agents!supervisor_authorizations_supervisor_id_fkey(name)
          )
        `)
        .eq('agent_id', agentId)
        .gte('start_time', startDate.toISOString())
        .order('start_time', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      // Calculate current status duration if still active
      const currentHistory = history.find(h => !h.end_time);
      if (currentHistory) {
        currentHistory.currentDuration = Math.floor(
          (Date.now() - new Date(currentHistory.start_time).getTime()) / 1000
        );
      }

      return {
        agentId,
        history,
        total: history.length,
        queryParams: { limit, offset, days }
      };

    } catch (error) {
      console.error('Error getting agent status history:', error);
      throw error;
    }
  }

  async getSupervisorDashboard(supervisorId: string): Promise<any> {
    try {
      console.log(`📋 Getting supervisor dashboard for ${supervisorId}`);

      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(
        process.env.SUPABASE_URL, 
        process.env.SUPABASE_ANON_KEY
      );

      // Verify supervisor permissions
      const { data: supervisor, error: supervisorError } = await supabase
        .from('agents')
        .select('role')
        .eq('id', supervisorId)
        .single();

      if (supervisorError || !supervisor || !['supervisor', 'admin'].includes(supervisor.role)) {
        throw this.createServiceError('INSUFFICIENT_PERMISSIONS', 'Only supervisors can access dashboard');
      }

      // Get all agents in organization
      const { data: agents, error: agentsError } = await supabase
        .from('agents')
        .select('id, name, organization_id, role, agent_type') // NEW: Include agent_type for filtering
        .eq('is_active', true);

      if (agentsError) {
        throw agentsError;
      }

      // Get today's status history for all agents
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const { data: todayHistory, error: historyError } = await supabase
        .from('agent_status_history')
        .select(`
          agent_id,
          status,
          start_time,
          end_time,
          duration
        `)
        .gte('start_time', today.toISOString());

      if (historyError) {
        throw historyError;
      }

      // Get pending authorizations
      const { data: pendingAuthorizations, error: authError } = await supabase
        .from('supervisor_authorizations')
        .select(`
          id,
          agent_id,
          authorized_status,
          reason,
          created_at,
          expires_at,
          agents!supervisor_authorizations_agent_id_fkey(name)
        `)
        .eq('is_used', false)
        .eq('is_expired', false);

      if (authError) {
        throw authError;
      }

      // Build agent summaries
      const agentSummaries = await Promise.all(agents
        .filter(agent => agent.role === 'agent') // Only actual agents, not supervisors
        .map(async (agent) => {
          const agentHistory = todayHistory.filter(h => h.agent_id === agent.id);
          
          // Get current status from Firebase (hybrid architecture)
          let firebaseStatus;
          try {
            firebaseStatus = await firebaseService.get(`agent_status/${agent.id}`);
          } catch (error) {
            console.warn(`Failed to get Firebase status for agent ${agent.id}:`, error);
            firebaseStatus = { status: 'offline' };
          }
          const currentStatus = firebaseStatus?.status || 'offline';
          
          // Calculate current status duration
          const currentStatusHistory = agentHistory.find(h => 
            h.status === currentStatus && !h.end_time
          );
          const currentStatusDuration = currentStatusHistory
            ? Math.floor((Date.now() - new Date(currentStatusHistory.start_time).getTime()) / 1000)
            : 0;

          // Get active authorizations for this agent
          const activeAuths = pendingAuthorizations.filter(auth => 
            auth.agent_id === agent.id
          );

          // Get all working conversations for this agent (carga de trabajo)
          let hasActiveChatsThatNeedAction = false;
          let workloadConversations = 0;
          let conversationsByStatus = {
            active: 0,
            supervised: 0,
            escalated: 0,
            pending_acceptance: 0,
            transferring: 0
          };
          let totalConversationsToday = 0;

          try {
            // Get all working conversations - check both agentId and assignedAgentId fields
            // Include all statuses that represent active workload for the agent
            const workloadStatuses = ['active', 'supervised', 'escalated', 'pending_acceptance', 'transferring'];
            
            const workloadChatsQuery1 = await firebaseService.query(this.CONVERSATIONS_PATH, [
              { field: 'assignedAgentId', operator: '==', value: agent.id },
              { field: 'status', operator: 'in', value: workloadStatuses }
            ]);
            
            const workloadChatsQuery2 = await firebaseService.query(this.CONVERSATIONS_PATH, [
              { field: 'agentId', operator: '==', value: agent.id },
              { field: 'status', operator: 'in', value: workloadStatuses }
            ]);
            
            // Combine results and deduplicate
            const workloadChatsSet = new Set();
            const allWorkloadChats = [];
            
            if (workloadChatsQuery1) {
              workloadChatsQuery1.forEach(chat => {
                if (!workloadChatsSet.has(chat.id)) {
                  workloadChatsSet.add(chat.id);
                  allWorkloadChats.push(chat);
                }
              });
            }
            
            if (workloadChatsQuery2) {
              workloadChatsQuery2.forEach(chat => {
                if (!workloadChatsSet.has(chat.id)) {
                  workloadChatsSet.add(chat.id);
                  allWorkloadChats.push(chat);
                }
              });
            }
            
            // Count conversations by status
            allWorkloadChats.forEach(chat => {
              const status = chat.status;
              if (conversationsByStatus.hasOwnProperty(status)) {
                conversationsByStatus[status]++;
              }
            });
            
            workloadConversations = allWorkloadChats.length;
            
            // An agent needs supervisor attention if they have workload conversations but are not available
            // OR if they have escalated/supervised conversations
            hasActiveChatsThatNeedAction = (
              (workloadConversations > 0 && currentStatus !== 'available') ||
              conversationsByStatus.escalated > 0 ||
              conversationsByStatus.supervised > 0
            );

            // Get all conversations assigned to this agent today
            const todayStart = new Date();
            todayStart.setHours(0, 0, 0, 0);
            
            const todayChatsQuery1 = await firebaseService.query(this.CONVERSATIONS_PATH, [
              { field: 'assignedAgentId', operator: '==', value: agent.id },
              { field: 'assignedAt', operator: '>=', value: todayStart.getTime() }
            ]);
            
            const todayChatsQuery2 = await firebaseService.query(this.CONVERSATIONS_PATH, [
              { field: 'agentId', operator: '==', value: agent.id },
              { field: 'assignedAt', operator: '>=', value: todayStart.getTime() }
            ]);
            
            // Combine and deduplicate today's conversations
            const todayChatsSet = new Set();
            const allTodayChats = [];
            
            if (todayChatsQuery1) {
              todayChatsQuery1.forEach(chat => {
                if (!todayChatsSet.has(chat.id)) {
                  todayChatsSet.add(chat.id);
                  allTodayChats.push(chat);
                }
              });
            }
            
            if (todayChatsQuery2) {
              todayChatsQuery2.forEach(chat => {
                if (!todayChatsSet.has(chat.id)) {
                  todayChatsSet.add(chat.id);
                  allTodayChats.push(chat);
                }
              });
            }
            
            totalConversationsToday = allTodayChats.length;

          } catch (error) {
            console.warn(`Failed to check conversations for agent ${agent.id}:`, error);
          }

          return {
            agentId: agent.id,
            agentName: agent.name,
            agent_type: agent.agent_type, // NEW: Include agent_type for supervisor filtering
            currentStatus,
            currentStatusDuration,
            todaysStatusHistory: agentHistory,
            activeAuthorizations: activeAuths,
            hasActiveChatsThatNeedAction,
            workloadConversations,
            conversationsByStatus,
            totalConversationsToday
          };
        })
      );

      // Calculate today's totals by status
      const todaysTotalByStatus = {
        available: 0,
        busy: 0,
        away: 0
      };

      todayHistory.forEach(history => {
        const duration = history.duration || 0; // in seconds
        const minutes = Math.floor(duration / 60);
        todaysTotalByStatus[history.status] += minutes;
      });

      // Calculate system-wide conversation metrics
      const totalWorkloadConversations = agentSummaries.reduce((sum, agent) => sum + agent.workloadConversations, 0);
      const totalConversationsToday = agentSummaries.reduce((sum, agent) => sum + agent.totalConversationsToday, 0);
      
      // Calculate totals by status across all agents
      const systemConversationsByStatus = agentSummaries.reduce((totals, agent) => {
        Object.keys(agent.conversationsByStatus).forEach(status => {
          totals[status] = (totals[status] || 0) + agent.conversationsByStatus[status];
        });
        return totals;
      }, {});

      // Calculate comprehensive dashboard analytics
      let dashboardAnalytics = null;
      try {
        console.log('📊 Calculating comprehensive dashboard analytics...');
        dashboardAnalytics = await this.calculateDashboardAnalytics();
        console.log('📈 Dashboard analytics calculated successfully');
      } catch (error) {
        console.error('Error calculating dashboard analytics:', error);
      }

      return {
        agents: agentSummaries,
        pendingAuthorizations,
        todaysTotalByStatus,
        summary: {
          totalAgents: agentSummaries.length,
          available: agentSummaries.filter(a => a.currentStatus === 'available').length,
          busy: agentSummaries.filter(a => a.currentStatus === 'busy').length,
          away: agentSummaries.filter(a => a.currentStatus === 'away').length,
          withActiveChatActions: agentSummaries.filter(a => a.hasActiveChatsThatNeedAction).length,
          totalWorkloadConversations,
          totalConversationsToday,
          systemConversationsByStatus,
          avgResponseTimeMinutes: dashboardAnalytics?.responseTime.today.avgResponseTimeMinutes || 0,
          responseTimeTrend: dashboardAnalytics?.responseTime.trend || null,
          workloadTrend: dashboardAnalytics?.workload.trend || null,
          completedTrend: dashboardAnalytics?.completed.trend || null,
          escalationsTrend: dashboardAnalytics?.escalations.trend || null
        }
      };

    } catch (error) {
      console.error('Error getting supervisor dashboard:', error);
      throw error;
    }
  }

  // Find available agent for assignment (load balancing logic)
  async findAvailableAgent(departmentId: string = 'general'): Promise<any> {
    try {
      console.log(`🔍 Finding available agent for department: ${departmentId}`);

      // 1. Get agents from Supabase with department assignments
      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(
        process.env.SUPABASE_URL, 
        process.env.SUPABASE_ANON_KEY
      );

      // Query agents with their department assignments
      const { data: agentDepartments, error } = await supabase
        .from('agent_departments')
        .select(`
          agent_id,
          skill_level,
          is_primary,
          agents (
            id,
            name,
            email,
            role,
            agent_type,
            max_concurrent_sessions,
            is_active
          ),
          departments (
            id,
            name,
            is_active
          )
        `)
        .eq('departments.name', departmentId)
        .eq('departments.is_active', true)
        .eq('agents.is_active', true)
        // REMOVED: .eq('agents.status', 'available') - status is now in Firebase


      if (error) {
        console.warn(`Error querying department ${departmentId}:`, error);
        // Fallback to general department
        return this.findAvailableAgent('general');
      }

      if (!agentDepartments || agentDepartments.length === 0) {
        console.log(`No agents found for department ${departmentId}, trying general...`);
        if (departmentId !== 'general') {
          return this.findAvailableAgent('general');
        }
        return null;
      }

      // 2. Get unique agents from Supabase (config) - filter only by role and is_active
      const supabaseAgents = agentDepartments
        .map(ad => ad.agents)
        .filter(agent => 
          agent && 
          agent.is_active &&
          agent.role === 'agent' &&
          agent.agent_type === 'human' // 🚨 CRITICAL: Only human agents for automatic assignment (bots are assigned explicitly)
        );
        
      // Remove duplicates by agent ID
      const uniqueSupabaseAgents = supabaseAgents.filter((agent, index, self) => 
        index === self.findIndex(a => a.id === agent.id)
      );

      console.log(`🔍 [HYBRID] Found ${uniqueSupabaseAgents.length} unique active agents from Supabase`);

      if (uniqueSupabaseAgents.length === 0) {
        console.log('[HYBRID] No active agent-role users found');
        return null;
      }

      // 3. Get Firebase real-time status for each agent
      const agentStatusPromises = uniqueSupabaseAgents.map(async (agent) => {
        try {
          const agentStatus = await firebaseService.get(`agent_status/${agent.id}`);
          const currentSessions = agentStatus?.currentConversations?.length || 0;
          
          return {
            ...agent,
            // Firebase real-time fields
            firebaseStatus: agentStatus?.status || 'offline',
            isAvailable: agentStatus?.isAvailable || false,
            currentSessions: currentSessions,
            lastSeen: agentStatus?.lastSeen,
            // Calculated fields
            workloadPercentage: (currentSessions / agent.max_concurrent_sessions) * 100,
            availableSlots: agent.max_concurrent_sessions - currentSessions,
            hasCapacity: currentSessions < agent.max_concurrent_sessions
          };
        } catch (error) {
          console.warn(`[HYBRID] Failed to get Firebase status for agent ${agent.id}:`, error);
          return {
            ...agent,
            firebaseStatus: 'offline',
            isAvailable: false,
            currentSessions: 0,
            lastSeen: null,
            workloadPercentage: 0,
            availableSlots: agent.max_concurrent_sessions,
            hasCapacity: true
          };
        }
      });
      
      const agentsWithStatus = await Promise.all(agentStatusPromises);
      console.log(`🔍 [HYBRID] Enhanced ${agentsWithStatus.length} agents with Firebase status`);

      // 4. Filter by hybrid availability criteria (Firebase + Supabase)
      const availableAgents = agentsWithStatus.filter(agent => 
        agent.firebaseStatus === 'online' && 
        agent.isAvailable &&
        agent.hasCapacity
      );
      
      console.log(`🔍 [HYBRID] Found ${availableAgents.length} truly available agents`);

      if (availableAgents.length === 0) {
        console.log('[HYBRID] No available agents with capacity');
        return null;
      }

      // 5. Load balancing algorithm: Round Robin with workload consideration
      // Sort by: 1) Least busy (workload %), 2) Most available slots, 3) Random for fairness
      const selectedAgent = availableAgents.sort((a, b) => {
        if (a.workloadPercentage !== b.workloadPercentage) {
          return a.workloadPercentage - b.workloadPercentage; // Least busy first
        }
        if (a.availableSlots !== b.availableSlots) {
          return b.availableSlots - a.availableSlots; // Most slots first
        }
        return Math.random() - 0.5; // Random for fairness
      })[0];

      console.log(`✅ [HYBRID] Selected agent: ${selectedAgent.name} (${selectedAgent.currentSessions}/${selectedAgent.max_concurrent_sessions} sessions, ${selectedAgent.workloadPercentage.toFixed(1)}% load)`);
      
      return selectedAgent;

    } catch (error) {
      console.error('[HYBRID] Error finding available agent:', error);
      return null;
    }
  }

  // ========================================
  // ANALYTICS METHODS
  // ========================================

  async calculateDashboardAnalytics(): Promise<{ 
    responseTime: {
      today: {
        totalConversations: number;
        conversationsWithResponses: number; 
        avgResponseTimeMinutes: number;
        totalResponseTimes: number[];
      };
      yesterday: {
        totalConversations: number;
        conversationsWithResponses: number; 
        avgResponseTimeMinutes: number;
        totalResponseTimes: number[];
      };
      trend: {
        direction: 'up' | 'down' | 'neutral';
        differenceMinutes: string;
      };
    };
    workload: {
      today: { totalWorkload: number; breakdown: Record<string, number>; };
      yesterday: { totalWorkload: number; breakdown: Record<string, number>; };
      trend: { direction: 'up' | 'down' | 'neutral'; difference: string; };
    };
    completed: {
      today: { totalCompleted: number; };
      yesterday: { totalCompleted: number; };
      trend: { direction: 'up' | 'down' | 'neutral'; difference: string; };
    };
    escalations: {
      today: { totalEscalations: number; };
      yesterday: { totalEscalations: number; };
      trend: { direction: 'up' | 'down' | 'neutral'; difference: string; };
    };
  }> {
    try {
      console.log('📊 Starting comprehensive dashboard analytics calculation (today + yesterday)...');
      
      // Calculate all analytics in parallel for performance
      const [
        responseTimeToday,
        responseTimeYesterday,
        workloadToday,
        workloadYesterday,
        completedToday,
        completedYesterday,
        escalationsToday,
        escalationsYesterday
      ] = await Promise.all([
        this.calculateDayResponseTime('today'),
        this.calculateDayResponseTime('yesterday'),
        this.calculateDayWorkload('today'),
        this.calculateDayWorkload('yesterday'),
        this.calculateDayCompleted('today'),
        this.calculateDayCompleted('yesterday'),
        this.calculateDayEscalations('today'),
        this.calculateDayEscalations('yesterday')
      ]);
      
      // Calculate trends for all metrics
      const responseTimeTrend = {
        direction: this.calculateTrend(
          responseTimeToday.avgResponseTimeMinutes,
          responseTimeYesterday.avgResponseTimeMinutes,
          'min'
        ).direction,
        differenceMinutes: this.calculateTrend(
          responseTimeToday.avgResponseTimeMinutes,
          responseTimeYesterday.avgResponseTimeMinutes,
          'min'
        ).difference
      };
      
      const workloadTrend = this.calculateTrend(
        workloadToday.totalWorkload,
        workloadYesterday.totalWorkload,
        ''
      );
      
      const completedTrend = this.calculateTrend(
        completedToday.totalCompleted,
        completedYesterday.totalCompleted,
        ''
      );
      
      const escalationsTrend = this.calculateTrend(
        escalationsToday.totalEscalations,
        escalationsYesterday.totalEscalations,
        ''
      );
      
      console.log(`📊 Dashboard Analytics Summary:`);
      console.log(`   Response Time: Today ${responseTimeToday.avgResponseTimeMinutes.toFixed(2)}min, Trend: ${responseTimeTrend.differenceMinutes}`);
      console.log(`   Workload: Today ${workloadToday.totalWorkload}, Trend: ${workloadTrend.difference}`);
      console.log(`   Completed: Today ${completedToday.totalCompleted}, Trend: ${completedTrend.difference}`);
      console.log(`   Escalations: Today ${escalationsToday.totalEscalations}, Trend: ${escalationsTrend.difference}`);
      
      return {
        responseTime: {
          today: responseTimeToday,
          yesterday: responseTimeYesterday,
          trend: responseTimeTrend
        },
        workload: {
          today: workloadToday,
          yesterday: workloadYesterday,
          trend: workloadTrend
        },
        completed: {
          today: completedToday,
          yesterday: completedYesterday,
          trend: completedTrend
        },
        escalations: {
          today: escalationsToday,
          yesterday: escalationsYesterday,
          trend: escalationsTrend
        }
      };
      
    } catch (error) {
      console.error('Error calculating dashboard analytics:', error);
      return {
        responseTime: {
          today: { totalConversations: 0, conversationsWithResponses: 0, avgResponseTimeMinutes: 0, totalResponseTimes: [] },
          yesterday: { totalConversations: 0, conversationsWithResponses: 0, avgResponseTimeMinutes: 0, totalResponseTimes: [] },
          trend: { direction: 'neutral', differenceMinutes: '0.0min' }
        },
        workload: {
          today: { totalWorkload: 0, breakdown: {} },
          yesterday: { totalWorkload: 0, breakdown: {} },
          trend: { direction: 'neutral', difference: '0' }
        },
        completed: {
          today: { totalCompleted: 0 },
          yesterday: { totalCompleted: 0 },
          trend: { direction: 'neutral', difference: '0' }
        },
        escalations: {
          today: { totalEscalations: 0 },
          yesterday: { totalEscalations: 0 },
          trend: { direction: 'neutral', difference: '0' }
        }
      };
    }
  }

  // Helper method to calculate response time for a specific day
  private async calculateDayResponseTime(day: 'today' | 'yesterday'): Promise<{
    totalConversations: number;
    conversationsWithResponses: number;
    avgResponseTimeMinutes: number;
    totalResponseTimes: number[];
  }> {
    try {
      // Set date range based on day parameter
      const now = new Date();
      let startOfDay: Date, endOfDay: Date;
      
      if (day === 'today') {
        startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      } else { // yesterday
        const yesterday = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      }
      
      console.log(`📊 Analyzing ${day}: ${startOfDay.toLocaleDateString()} to ${endOfDay.toLocaleDateString()}`);
      
      // Get conversations for the specified day
      const conversationsQuery = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'createdAt', operator: '>=', value: startOfDay.getTime() },
        { field: 'createdAt', operator: '<', value: endOfDay.getTime() }
      ]);
      
      console.log(`📊 Found ${conversationsQuery.length} conversations for ${day}`);
      
      let allResponseTimes: number[] = [];
      let conversationsWithResponses = 0;
      
      // Analyze each conversation
      for (const conversation of conversationsQuery) {
        try {
          // Get messages for this conversation (messages are nested within conversations)
          const messagesRef = firebaseService.getRef(`${this.CONVERSATIONS_PATH}/${conversation.id}/messages`);
          const messagesSnapshot = await messagesRef.orderByKey().once('value');
          const messagesData = messagesSnapshot.val();
          
          if (!messagesData) continue;
          
          // Convert to array and sort by timestamp
          const messages = Object.values(messagesData).sort((a: any, b: any) => 
            new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
          );
          
          // Find response times between customer and agent messages
          let lastCustomerMessage: any = null;
          let conversationResponseTimes: number[] = [];
          
          for (const message of messages) {
            const msg = message as any;
            
            if (msg.senderType === 'customer') {
              lastCustomerMessage = msg;
            } else if (msg.senderType === 'agent' && lastCustomerMessage) {
              // Calculate response time
              const customerTime = new Date(lastCustomerMessage.timestamp).getTime();
              const agentTime = new Date(msg.timestamp).getTime();
              const responseTimeMinutes = (agentTime - customerTime) / (1000 * 60);
              
              // Filter out unreasonable response times (>60 minutes - likely different sessions)
              if (responseTimeMinutes > 0 && responseTimeMinutes <= 60) {
                conversationResponseTimes.push(responseTimeMinutes);
              }
              
              lastCustomerMessage = null; // Reset for next sequence
            }
          }
          
          if (conversationResponseTimes.length > 0) {
            allResponseTimes.push(...conversationResponseTimes);
            conversationsWithResponses++;
          }
          
        } catch (msgError) {
          console.warn(`Failed to analyze messages for conversation ${conversation.id}:`, msgError);
          continue;
        }
      }
      
      // Calculate average
      const avgResponseTimeMinutes = allResponseTimes.length > 0 
        ? allResponseTimes.reduce((sum, time) => sum + time, 0) / allResponseTimes.length
        : 0;
      
      console.log(`📊 ${day} analytics complete: ${allResponseTimes.length} response times, avg: ${avgResponseTimeMinutes.toFixed(2)}min`);
      
      return {
        totalConversations: conversationsQuery.length,
        conversationsWithResponses,
        avgResponseTimeMinutes: parseFloat(avgResponseTimeMinutes.toFixed(2)),
        totalResponseTimes: allResponseTimes
      };
      
    } catch (error) {
      console.error(`Error calculating ${day} response time analytics:`, error);
      return {
        totalConversations: 0,
        conversationsWithResponses: 0,
        avgResponseTimeMinutes: 0,
        totalResponseTimes: []
      };
    }
  }

  // Helper method to calculate workload for a specific day
  private async calculateDayWorkload(day: 'today' | 'yesterday'): Promise<{
    totalWorkload: number;
    breakdown: Record<string, number>;
  }> {
    try {
      // Set date range based on day parameter
      const now = new Date();
      let startOfDay: Date, endOfDay: Date;
      
      if (day === 'today') {
        startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      } else { // yesterday
        const yesterday = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      }

      // Get workload conversations (non-closed status)
      const workloadStatuses = ['active', 'supervised', 'escalated', 'pending_acceptance', 'transferring'];
      const workloadConversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'createdAt', operator: '>=', value: startOfDay.getTime() },
        { field: 'createdAt', operator: '<', value: endOfDay.getTime() }
      ]);

      // Filter by workload statuses and count breakdown
      const breakdown: Record<string, number> = {};
      let totalWorkload = 0;

      workloadConversations.forEach(conv => {
        if (workloadStatuses.includes(conv.status)) {
          breakdown[conv.status] = (breakdown[conv.status] || 0) + 1;
          totalWorkload++;
        }
      });

      console.log(`📊 ${day} workload: ${totalWorkload} conversations`);
      
      return { totalWorkload, breakdown };
      
    } catch (error) {
      console.error(`Error calculating ${day} workload:`, error);
      return { totalWorkload: 0, breakdown: {} };
    }
  }

  // Helper method to calculate completed conversations for a specific day
  private async calculateDayCompleted(day: 'today' | 'yesterday'): Promise<{
    totalCompleted: number;
  }> {
    try {
      // Set date range based on day parameter
      const now = new Date();
      let startOfDay: Date, endOfDay: Date;
      
      if (day === 'today') {
        startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      } else { // yesterday
        const yesterday = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      }

      // Get conversations that were completed (closed) on this day
      const completedConversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'closedAt', operator: '>=', value: startOfDay.getTime() },
        { field: 'closedAt', operator: '<', value: endOfDay.getTime() },
        { field: 'status', operator: '==', value: 'closed' }
      ]);

      const totalCompleted = completedConversations.length;
      console.log(`📊 ${day} completed: ${totalCompleted} conversations`);
      
      return { totalCompleted };
      
    } catch (error) {
      console.error(`Error calculating ${day} completed conversations:`, error);
      return { totalCompleted: 0 };
    }
  }

  // Helper method to calculate escalations for a specific day
  private async calculateDayEscalations(day: 'today' | 'yesterday'): Promise<{
    totalEscalations: number;
  }> {
    try {
      // Set date range based on day parameter
      const now = new Date();
      let startOfDay: Date, endOfDay: Date;
      
      if (day === 'today') {
        startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      } else { // yesterday
        const yesterday = new Date(now.getTime() - (24 * 60 * 60 * 1000));
        startOfDay = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        endOfDay = new Date(startOfDay.getTime() + (24 * 60 * 60 * 1000));
      }

      // Get conversations that were escalated on this day
      const escalatedConversations = await firebaseService.query(this.CONVERSATIONS_PATH, [
        { field: 'createdAt', operator: '>=', value: startOfDay.getTime() },
        { field: 'createdAt', operator: '<', value: endOfDay.getTime() },
        { field: 'status', operator: '==', value: 'escalated' }
      ]);

      const totalEscalations = escalatedConversations.length;
      console.log(`📊 ${day} escalations: ${totalEscalations} conversations`);
      
      return { totalEscalations };
      
    } catch (error) {
      console.error(`Error calculating ${day} escalations:`, error);
      return { totalEscalations: 0 };
    }
  }

  // Helper method to calculate trend between two values
  private calculateTrend(todayValue: number, yesterdayValue: number, unit: string = ''): {
    direction: 'up' | 'down' | 'neutral';
    difference: string;
  } {
    if (yesterdayValue === 0 && todayValue === 0) {
      return { direction: 'neutral', difference: `0${unit}` };
    }
    
    if (yesterdayValue === 0) {
      return { direction: 'up', difference: `+${todayValue}${unit}` };
    }
    
    const difference = todayValue - yesterdayValue;
    const direction = difference > 0 ? 'up' : difference < 0 ? 'down' : 'neutral';
    const formattedDiff = unit === 'min' 
      ? (difference >= 0 ? '+' : '') + difference.toFixed(1) + unit
      : (difference >= 0 ? '+' : '') + difference.toString() + unit;
    
    return { direction, difference: formattedDiff };
  }

  /**
   * Get agent profile by ID from Supabase
   */
  async getAgentProfile(agentId: string): Promise<any | null> {
    try {
      // We need access to supabase client. For now, let's use a simple approach
      const { createClient } = require('@supabase/supabase-js');
      
      const supabaseUrl = process.env.SUPABASE_URL;
      const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
      
      if (!supabaseUrl || !supabaseAnonKey) {
        console.error('Missing Supabase credentials for agent profile lookup');
        return null;
      }
      
      const supabase = createClient(supabaseUrl, supabaseAnonKey);
      
      const { data: agent, error } = await supabase
        .from('agents')
        .select('id, name, departments')
        .eq('id', agentId)
        .single();
        
      if (error) {
        console.error(`Error fetching agent profile for ${agentId}:`, error);
        return null;
      }
      
      return agent;
    } catch (error) {
      console.error(`Failed to get agent profile for ${agentId}:`, error);
      return null;
    }
  }

  /**
   * UNIFIED HYBRID: Get complete agent availability from both Supabase + Firebase
   */
  async getAgentAvailability(agentId: string): Promise<{
    isAvailable: boolean;
    reason?: string;
    supabaseData?: any;
    firebaseData?: any;
  }> {
    try {
      console.log(`🔄 [HYBRID] Checking availability for agent ${agentId}...`);
      
      // 1. Get persistent config from Supabase (role, max_sessions, departments)
      const { createClient } = require('@supabase/supabase-js');
      const supabase = createClient(
        process.env.SUPABASE_URL, 
        process.env.SUPABASE_ANON_KEY
      );

      const { data: supabaseAgent, error: supabaseError } = await supabase
        .from('agents')
        .select('id, name, role, max_concurrent_sessions, is_active')
        .eq('id', agentId)
        .single();
      
      if (supabaseError || !supabaseAgent) {
        return {
          isAvailable: false,
          reason: `Agent not found in Supabase: ${supabaseError?.message}`,
          supabaseData: null
        };
      }
      
      // 2. Get real-time status from Firebase (current_sessions, availability, online status)
      const firebaseStatus = await firebaseService.get(`${this.AGENT_STATUS_PATH}/${agentId}`);
      
      if (!firebaseStatus) {
        return {
          isAvailable: false,
          reason: 'Agent status not found in Firebase',
          supabaseData: supabaseAgent,
          firebaseData: null
        };
      }
      
      // 3. Apply business rules with data from both sources
      
      // Rule 1: Only 'agent' role can receive conversations
      if (supabaseAgent.role !== 'agent') {
        return {
          isAvailable: false,
          reason: `Role '${supabaseAgent.role}' cannot receive conversations`,
          supabaseData: supabaseAgent,
          firebaseData: firebaseStatus
        };
      }
      
      // Rule 2: Agent must be active in Supabase
      if (!supabaseAgent.is_active) {
        return {
          isAvailable: false,
          reason: 'Agent is inactive in system',
          supabaseData: supabaseAgent,
          firebaseData: firebaseStatus
        };
      }
      
      // Rule 3: Agent must be online and available in Firebase
      if (firebaseStatus.status !== 'online' || !firebaseStatus.isAvailable) {
        return {
          isAvailable: false,
          reason: `Firebase status: ${firebaseStatus.status}, available: ${firebaseStatus.isAvailable}`,
          supabaseData: supabaseAgent,
          firebaseData: firebaseStatus
        };
      }
      
      // Rule 4: Capacity check (use Firebase current + Supabase max)
      const currentSessions = firebaseStatus.currentConversations?.length || 0;
      const maxSessions = supabaseAgent.max_concurrent_sessions || 10;
      
      if (currentSessions >= maxSessions) {
        return {
          isAvailable: false,
          reason: `At capacity: ${currentSessions}/${maxSessions} sessions`,
          supabaseData: supabaseAgent,
          firebaseData: firebaseStatus
        };
      }
      
      // 5. Consistency validation (log warnings if data doesn't match)
      // Could add checks here for data consistency between bases
      
      console.log(`✅ [HYBRID] Agent ${agentId} (${supabaseAgent.name}) is available: ${currentSessions}/${maxSessions} sessions`);
      
      return {
        isAvailable: true,
        supabaseData: supabaseAgent,
        firebaseData: firebaseStatus
      };
      
    } catch (error) {
      console.error(`❌ [HYBRID] Error checking agent availability for ${agentId}:`, error);
      return {
        isAvailable: false,
        reason: `System error: ${error.message}`
      };
    }
  }

  /**
   * Check if an agent is available for assignment (LEGACY - uses hybrid function)
   */
  async isAgentAvailable(agentId: string): Promise<boolean> {
    const result = await this.getAgentAvailability(agentId);
    if (!result.isAvailable && result.reason) {
      console.log(`❌ Agent ${agentId} not available: ${result.reason}`);
    }
    return result.isAvailable;
  }
}

export const conversationService = new ConversationService();
export default conversationService;