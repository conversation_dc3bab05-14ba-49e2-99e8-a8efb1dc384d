import { config } from 'dotenv';
import path from 'path';

// Load .env from project root (not service directory)
config({ path: path.resolve(__dirname, '../../../.env') });

export const CONFIG = {
  PORT: process.env.CHAT_REALTIME_PORT || process.env.PORT || 3003,
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Firebase Configuration
  FIREBASE: {
    // Use emulator for local development
    USE_EMULATOR: process.env.NODE_ENV === 'development' || process.env.FIREBASE_USE_EMULATOR === 'true',
    EMULATOR_HOST: process.env.FIREBASE_EMULATOR_HOST || 'localhost:9000',
    
    // Production Firebase configuration  
    PROJECT_ID: process.env.GOOGLE_CLOUD_PROJECT_ID || 'cx-system-469120',
    DATABASE_URL: process.env.FIREBASE_DATABASE_URL || 'https://cx-system-dev-default-rtdb.firebaseio.com/',
    
    // Service account for production (optional for emulator)
    SERVICE_ACCOUNT_PATH: process.env.FIREBASE_SERVICE_ACCOUNT_PATH,
  },

  // Service Configuration
  SERVICE_NAME: 'chat-realtime',
  VERSION: '1.0.0',

  // Request Timeouts
  REQUEST_TIMEOUT: parseInt(process.env.REQUEST_TIMEOUT || '10000'), // 10 seconds

  // CORS Configuration
  CORS_ORIGIN: process.env.CORS_ORIGIN || '*',
};