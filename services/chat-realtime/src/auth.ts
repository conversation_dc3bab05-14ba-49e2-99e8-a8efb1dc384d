// Supabase Authentication Service
import { createClient, SupabaseClient, User } from '@supabase/supabase-js';
import { Request, Response, NextFunction } from 'express';
import { tokenCache, AgentProfile, AgentDepartment } from './tokenCache';

export interface AuthenticatedRequest extends Request {
  user?: User;
  userProfile?: AgentProfile;
}

// Re-export interfaces from tokenCache for convenience
export type { AgentProfile, AgentDepartment } from './tokenCache';

class AuthService {
  private supabase: SupabaseClient;
  private readonly SUPABASE_TIMEOUT = parseInt(process.env.SUPABASE_TIMEOUT || '5000');

  constructor() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Missing Supabase credentials in environment variables');
    }

    this.supabase = createClient(supabaseUrl, supabaseAnonKey, {
      global: {
        fetch: (url, options = {}) => {
          return fetch(url, {
            ...options,
            signal: AbortSignal.timeout(this.SUPABASE_TIMEOUT)
          });
        }
      }
    });
  }

  // Cache management methods
  private getCachedToken(token: string) {
    return tokenCache.get(token);
  }

  private setCachedToken(token: string, user: User, profile: AgentProfile): void {
    tokenCache.set(token, user, profile);
    console.log(`📄 Cached token for user ${user.id}`);
  }

  // Middleware to authenticate requests
  authenticate = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      console.log('🔍 [AUTH DEBUG] === AUTHENTICATION MIDDLEWARE START ===');
      console.log('🔍 [AUTH DEBUG] Request URL:', req.url);
      console.log('🔍 [AUTH DEBUG] Request method:', req.method);
      
      const authHeader = req.headers.authorization;
      console.log('🔍 [AUTH DEBUG] Auth header present:', !!authHeader);
      console.log('🔍 [AUTH DEBUG] Auth header starts with Bearer:', authHeader?.startsWith('Bearer '));
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        console.error('🔍 [AUTH DEBUG] ❌ MISSING OR INVALID AUTH HEADER');
        res.status(401).json({
          success: false,
          error: {
            code: 'MISSING_AUTH_TOKEN',
            message: 'Authorization header with Bearer token is required'
          },
          timestamp: Date.now()
        });
        return;
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      console.log('🔍 [AUTH DEBUG] Token extracted (first 20 chars):', token.substring(0, 20) + '...');
      
      // Try to get from cache first
      const cached = this.getCachedToken(token);
      console.log('🔍 [AUTH DEBUG] Token cache hit:', !!cached);
      
      let user: User;
      let agentProfile: AgentProfile | null;
      
      if (cached) {
        // Cache hit - use cached data
        user = cached.user;
        agentProfile = cached.profile;
        console.log('🔍 [AUTH DEBUG] 💨 Using cached token for user:', user.id);
        console.log('🔍 [AUTH DEBUG] Cached profile exists:', !!agentProfile);
      } else {
        // Cache miss - verify with Supabase
        console.log(`🔍 Cache miss - verifying token with Supabase`);
        
        try {
          // Verify the JWT token with Supabase with short timeout
          const { data: { user: supabaseUser }, error } = await Promise.race([
            this.supabase.auth.getUser(token),
            new Promise<any>((_, reject) => 
              setTimeout(() => reject(new Error('Supabase timeout')), this.SUPABASE_TIMEOUT)
            )
          ]);
          
          if (error || !supabaseUser) {
            res.status(401).json({
              success: false,
              error: {
                code: 'INVALID_AUTH_TOKEN',
                message: 'Invalid or expired authentication token'
              },
              timestamp: Date.now()
            });
            return;
          }

          user = supabaseUser;
          
          // Get agent profile from Supabase
          agentProfile = await this.getAgentProfile(user.id);
          
          if (!agentProfile) {
            res.status(403).json({
              success: false,
              error: {
                code: 'AGENT_PROFILE_NOT_FOUND',
                message: 'Agent profile not found or inactive'
              },
              timestamp: Date.now()
            });
            return;
          }
          
          // Cache the successful result
          this.setCachedToken(token, user, agentProfile);
          
        } catch (supabaseError: any) {
          // Handle Supabase connectivity issues
          console.error('⚠️ Supabase authentication error:', supabaseError.message);
          
          res.status(503).json({
            success: false,
            error: {
              code: 'SUPABASE_UNAVAILABLE',
              message: 'Authentication service temporarily unavailable'
            },
            timestamp: Date.now()
          });
          return;
        }
      }

      // Attach user and profile to request
      req.user = user;
      req.userProfile = agentProfile;
      
      next();
    } catch (error: any) {
      console.error('Authentication error:', error);
      
      // Handle specific connectivity errors
      if (error.message?.includes('timeout') || error.cause?.code === 'UND_ERR_CONNECT_TIMEOUT') {
        console.warn('⚠️ Supabase connectivity issue - using fallback authentication');
        res.status(503).json({
          success: false,
          error: {
            code: 'SUPABASE_UNAVAILABLE',
            message: 'Authentication service temporarily unavailable'
          },
          timestamp: Date.now()
        });
        return;
      }
      
      res.status(500).json({
        success: false,
        error: {
          code: 'AUTH_SERVICE_ERROR',
          message: 'Authentication service error'
        },
        timestamp: Date.now()
      });
      return;
    }
  };

  // Get agent profile from Supabase database
  private async getAgentProfile(userId: string): Promise<AgentProfile | null> {
    try {
      // Get agent basic data with timeout
      // HYBRID ARCHITECTURE: Only get config data from Supabase (status is now in Firebase)
      const agentPromise = this.supabase
        .from('agents')
        .select(`
          id,
          name,
          email,
          role,
          organization_id,
          skills,
          max_concurrent_sessions
        `)
        .eq('id', userId)
        .eq('is_active', true)
        .single();

      const { data: agentData, error: agentError } = await Promise.race([
        agentPromise,
        new Promise<any>((_, reject) => 
          setTimeout(() => reject(new Error('Agent profile query timeout')), this.SUPABASE_TIMEOUT)
        )
      ]);

      if (agentError || !agentData) {
        console.error('Agent profile query error:', agentError);
        return null;
      }

      // Get agent department relationships with timeout
      const departmentPromise = this.supabase
        .from('agent_departments')
        .select(`
          is_primary,
          skill_level,
          departments!inner (
            id,
            name,
            description,
            is_active
          )
        `)
        .eq('agent_id', userId)
        .eq('departments.is_active', true); // Only active departments

      const { data: departmentData, error: deptError } = await Promise.race([
        departmentPromise,
        new Promise<any>((_, reject) => 
          setTimeout(() => reject(new Error('Department query timeout')), this.SUPABASE_TIMEOUT)
        )
      ]);

      if (deptError) {
        console.error('Agent departments query error:', deptError);
        // Continue with basic profile but no departments
      }

      // Process department relationships
      const departmentDetails: AgentDepartment[] = [];
      const departments: string[] = [];

      if (departmentData && departmentData.length > 0) {
        departmentData.forEach((rel: any) => {
          const dept = rel.departments;
          departments.push(dept.name);
          departmentDetails.push({
            departmentId: dept.id,
            departmentName: dept.name,
            isPrimary: rel.is_primary,
            skillLevel: rel.skill_level
          });
        });
      }

      // Ensure agent has at least 'general' department as fallback
      if (!departments.includes('general')) {
        departments.push('general');
        // Try to find general department ID
        const { data: generalDept } = await this.supabase
          .from('departments')
          .select('id, name')
          .eq('name', 'general')
          .eq('is_active', true)
          .single();
        
        if (generalDept) {
          departmentDetails.push({
            departmentId: generalDept.id,
            departmentName: 'general',
            isPrimary: departments.length === 1, // Primary if it's the only one
            skillLevel: 3 // Default skill level
          });
        }
      }

      return {
        id: agentData.id,
        name: agentData.name,
        email: agentData.email,
        role: agentData.role,
        departments,
        maxConcurrentChats: agentData.max_concurrent_sessions || 5,
        departmentDetails,
        // Note: status removed - now handled in Firebase real-time data
        organizationId: agentData.organization_id
      };
    } catch (error) {
      console.error('Error fetching agent profile:', error);
      return null;
    }
  }

  // Login method for testing purposes
  async login(email: string, password: string) {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        throw error;
      }

      return {
        user: data.user,
        session: data.session,
        accessToken: data.session?.access_token
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Logout method
  async logout() {
    try {
      const { error } = await this.supabase.auth.signOut();
      if (error) {
        throw error;
      }
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  // Method to get current session
  async getCurrentSession() {
    try {
      const { data: { session }, error } = await this.supabase.auth.getSession();
      if (error) {
        throw error;
      }
      return session;
    } catch (error) {
      console.error('Get session error:', error);
      throw error;
    }
  }

  // Role-based middleware
  requireRole = (allowedRoles: string[]) => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.userProfile) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: 'Authentication required'
          },
          timestamp: Date.now()
        });
      }

      if (!allowedRoles.includes(req.userProfile.role)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: `Access denied. Required roles: ${allowedRoles.join(', ')}`
          },
          timestamp: Date.now()
        });
      }

      next();
    };
  };

  // Department access middleware
  requireDepartmentAccess = (departmentId?: string) => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.userProfile) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: 'Authentication required'
          },
          timestamp: Date.now()
        });
      }

      // Supervisors and admins have access to all departments
      if (['supervisor', 'admin'].includes(req.userProfile.role)) {
        return next();
      }

      // Check if agent has access to requested department
      const requestedDept = departmentId || req.params.departmentId || req.query.departmentId;
      
      if (requestedDept && !req.userProfile.departments.includes(requestedDept as string)) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'DEPARTMENT_ACCESS_DENIED',
            message: `Access denied to department: ${requestedDept}`
          },
          timestamp: Date.now()
        });
      }

      next();
    };
  };
}

export const authService = new AuthService();
export default authService;