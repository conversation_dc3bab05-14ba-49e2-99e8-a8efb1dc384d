/**
 * Redis Service
 * Shared Redis connection for conversation state management
 */

import Redis from 'ioredis';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  maxRetriesPerRequest: number;
}

export class RedisService {
  private static instance: RedisService;
  private redis: Redis;
  private isConnected: boolean = false;

  constructor(config?: Partial<RedisConfig>) {
    const defaultConfig: RedisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || 'cx-system-dev',
      db: parseInt(process.env.REDIS_DB || '0'),
      maxRetriesPerRequest: 3,
    };

    const finalConfig = { ...defaultConfig, ...config };

    this.redis = new Redis({
      host: finalConfig.host,
      port: finalConfig.port,
      password: finalConfig.password,
      db: finalConfig.db,
      maxRetriesPerRequest: finalConfig.maxRetriesPerRequest,
      lazyConnect: true,
    });

    this.setupEventHandlers();
  }

  /**
   * Get singleton instance
   */
  static getInstance(config?: Partial<RedisConfig>): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService(config);
    }
    return RedisService.instance;
  }

  private setupEventHandlers(): void {
    this.redis.on('connect', () => {
      console.log('🔗 Redis connecting...');
    });

    this.redis.on('ready', () => {
      console.log('✅ Redis connected and ready');
      this.isConnected = true;
    });

    this.redis.on('error', (err) => {
      console.error('❌ Redis error:', err.message);
      this.isConnected = false;
    });

    this.redis.on('close', () => {
      console.log('🔌 Redis connection closed');
      this.isConnected = false;
    });

    this.redis.on('reconnecting', (ms) => {
      console.log(`🔄 Redis reconnecting in ${ms}ms`);
    });
  }

  /**
   * Connect to Redis
   */
  async connect(): Promise<void> {
    try {
      await this.redis.connect();
      console.log('✅ Redis connection established');
    } catch (error) {
      console.error('❌ Failed to connect to Redis:', error);
      throw error;
    }
  }

  /**
   * Disconnect from Redis
   */
  async disconnect(): Promise<void> {
    try {
      await this.redis.disconnect();
      console.log('🔌 Redis disconnected');
      this.isConnected = false;
    } catch (error) {
      console.error('❌ Error disconnecting from Redis:', error);
    }
  }

  /**
   * Check if Redis is connected
   */
  isRedisConnected(): boolean {
    return this.isConnected && this.redis.status === 'ready';
  }

  /**
   * Get value from Redis
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.redis.get(key);
    } catch (error) {
      console.error(`❌ Redis GET error for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Set value in Redis with optional TTL
   */
  async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
    try {
      if (ttlSeconds) {
        await this.redis.setex(key, ttlSeconds, value);
      } else {
        await this.redis.set(key, value);
      }
    } catch (error) {
      console.error(`❌ Redis SET error for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Delete key from Redis
   */
  async delete(key: string): Promise<number> {
    try {
      return await this.redis.del(key);
    } catch (error) {
      console.error(`❌ Redis DELETE error for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`❌ Redis EXISTS error for key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Set object as JSON string
   */
  async setObject(key: string, obj: any, ttlSeconds?: number): Promise<void> {
    const jsonString = JSON.stringify(obj);
    await this.set(key, jsonString, ttlSeconds);
  }

  /**
   * Get object from JSON string
   */
  async getObject<T>(key: string): Promise<T | null> {
    const jsonString = await this.get(key);
    if (!jsonString) {
      return null;
    }

    try {
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`❌ Failed to parse JSON for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Get all keys matching pattern
   */
  async getKeys(pattern: string): Promise<string[]> {
    try {
      return await this.redis.keys(pattern);
    } catch (error) {
      console.error(`❌ Redis KEYS error for pattern ${pattern}:`, error);
      throw error;
    }
  }

  /**
   * Get Redis info
   */
  async info(section?: string): Promise<string> {
    try {
      return await this.redis.info(section);
    } catch (error) {
      console.error('❌ Redis INFO error:', error);
      throw error;
    }
  }

  /**
   * Ping Redis
   */
  async ping(): Promise<string> {
    try {
      return await this.redis.ping();
    } catch (error) {
      console.error('❌ Redis PING error:', error);
      throw error;
    }
  }

  /**
   * Flush all data (use with caution)
   */
  async flushall(): Promise<string> {
    try {
      return await this.redis.flushall();
    } catch (error) {
      console.error('❌ Redis FLUSHALL error:', error);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{
    connected: boolean;
    ping: boolean;
    error?: string;
  }> {
    try {
      const connected = this.isRedisConnected();
      if (!connected) {
        return {
          connected: false,
          ping: false,
          error: 'Not connected to Redis'
        };
      }

      const pingResult = await this.ping();
      const pingSuccess = pingResult === 'PONG';

      return {
        connected: true,
        ping: pingSuccess,
        error: pingSuccess ? undefined : `Unexpected ping response: ${pingResult}`
      };
    } catch (error) {
      return {
        connected: false,
        ping: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get the underlying Redis instance (use with caution)
   */
  getRedisInstance(): Redis {
    return this.redis;
  }
}