/**
 * PubSub Service for Chat Realtime
 * Handles outbound message publication to PubSub for Channel Router
 */

import { PubSub } from '@google-cloud/pubsub';

export interface OutboundMessage {
  to: string;           // Customer phone (whatsapp:+1234567890)
  body: string;         // Message content
  channel: string;      // 'whatsapp'
  conversationId: string;
  messageId: string;
  senderId: string;     // Agent ID or 'system'
  senderType: 'agent' | 'system';
  timestamp: string;    // ISO string to match Message contract
  metadata?: any;
}

export interface PubSubPublishResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export class ChatRealtimePubSubService {
  private pubSubClient: PubSub | null = null;
  private readonly projectId: string;
  private readonly outboundTopic: string;
  private readonly useEmulator: boolean;

  constructor() {
    this.projectId = process.env.GOOGLE_CLOUD_PROJECT_ID || 'cx-system-469120';
    this.outboundTopic = process.env.PUBSUB_OUTBOUND_TOPIC || 'outbound-messages';
    this.useEmulator = process.env.PUBSUB_USE_EMULATOR === 'true' || process.env.NODE_ENV === 'development';
    
    this.initializePubSub();
  }

  private initializePubSub() {
    try {
      if (this.useEmulator) {
        console.log('🔧 Initializing PubSub with Emulator');
        console.log(`   Project ID: ${this.projectId}`);
        console.log(`   Emulator Host: ${process.env.PUBSUB_EMULATOR_HOST || 'localhost:8085'}`);
        
        this.pubSubClient = new PubSub({ 
          projectId: this.projectId,
          // Emulator connection is handled by PUBSUB_EMULATOR_HOST env var
        });
      } else {
        console.log('🔧 Initializing PubSub for Production');
        this.pubSubClient = new PubSub({ 
          projectId: this.projectId 
        });
      }
      
      console.log(`✅ PubSub initialized for project: ${this.projectId}`);
      console.log(`📤 Outbound Topic: ${this.outboundTopic}`);
    } catch (error) {
      console.error('❌ Failed to initialize PubSub:', error);
      this.pubSubClient = null;
    }
  }

  /**
   * Publish outbound message to PubSub for Channel Router
   */
  async publishOutboundMessage(message: OutboundMessage): Promise<PubSubPublishResult> {
    try {
      if (!this.pubSubClient) {
        console.warn('⚠️ PubSub not initialized, message not sent to channel');
        return {
          success: false,
          error: 'PubSub client not initialized'
        };
      }

      const topic = this.pubSubClient.topic(this.outboundTopic);
      const messageBuffer = Buffer.from(JSON.stringify(message));
      
      const [messageId] = await topic.publish(messageBuffer);
      
      console.log('📤 Published OUTBOUND message to PubSub:', {
        topic: this.outboundTopic,
        messageId,
        conversationId: message.conversationId,
        to: message.to,
        preview: message.body.substring(0, 50) + '...'
      });

      return {
        success: true,
        messageId: messageId
      };
      
    } catch (error) {
      console.error('❌ Failed to publish outbound message:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown publish error'
      };
    }
  }

  /**
   * Check if PubSub is healthy and ready
   */
  isHealthy(): boolean {
    return this.pubSubClient !== null;
  }

  /**
   * Get service configuration info
   */
  getConfig() {
    return {
      projectId: this.projectId,
      outboundTopic: this.outboundTopic,
      useEmulator: this.useEmulator,
      emulatorHost: process.env.PUBSUB_EMULATOR_HOST || 'localhost:8085',
      healthy: this.isHealthy()
    };
  }
}

// Create singleton instance
export const pubsubService = new ChatRealtimePubSubService();