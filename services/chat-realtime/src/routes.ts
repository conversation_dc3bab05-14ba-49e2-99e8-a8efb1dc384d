import { Router, Request, Response } from 'express';
import { conversationService } from './conversationService';
import { ApiResponse } from './types';
import { authService, AuthenticatedRequest } from './auth';
import { tokenCache } from './tokenCache';
import { firebaseService } from './firebase';

const router = Router();

// Helper function for not implemented endpoints
const notImplemented = (endpoint: string) => {
  return (req: Request, res: Response) => {
    res.status(501).json({
      success: false,
      error: {
        code: 'NOT_IMPLEMENTED',
        message: `Endpoint ${endpoint} not yet implemented`,
      },
      timestamp: Date.now(),
    });
  };
};

// ========================================
// HEALTH CHECK
// ========================================

router.get('/health', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const isHealthy = await conversationService.healthCheck();
    
    res.json({
      success: true,
      data: {
        service: 'chat-realtime',
        status: isHealthy ? 'healthy' : 'unhealthy',
        firebase: isHealthy,
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Health check failed',
        details: error,
      },
      timestamp: Date.now(),
    });
  }
});

// Auth cache statistics (for monitoring)
router.get('/auth/cache/stats', (req: Request, res: Response<ApiResponse>) => {
  try {
    const stats = tokenCache.getStats();
    
    res.json({
      success: true,
      data: {
        cache: stats,
        description: 'Token cache statistics for performance monitoring'
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: 'CACHE_STATS_ERROR',
        message: error.message || 'Failed to get cache statistics',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// HYBRID AGENTS API
// ========================================

// Get all agents with hybrid data (Supabase config + Firebase real-time status)
router.get('/agents', async (req: Request, res: Response<ApiResponse>) => {
  try {
    console.log('🔄 [HYBRID] Getting agents with hybrid architecture...');
    
    // 1. Get agent config from Supabase
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(
      process.env.SUPABASE_URL, 
      process.env.SUPABASE_ANON_KEY
    );

    const { data: agents, error } = await supabase
      .from('agents')
      .select(`
        id,
        name,
        role,
        agent_type,
        max_concurrent_sessions,
        is_active,
        agent_departments (
          department_id,
          is_primary,
          departments (
            id,
            name
          )
        )
      `)
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Supabase error:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'SUPABASE_ERROR',
          message: 'Failed to fetch agents from database',
          details: error
        },
        timestamp: Date.now(),
      });
    }

    // 2. Get real-time status from Firebase for all agents
    const agentStatusPromises = (agents || []).map(async (agent: any) => {
      try {
        const agentStatus = await firebaseService.get(`agent_status/${agent.id}`);
        return {
          agentId: agent.id,
          status: agentStatus?.status || 'offline',
          isAvailable: agentStatus?.isAvailable || false,
          current_sessions: agentStatus?.currentConversations?.length || 0,
          lastSeen: agentStatus?.lastSeen || null
        };
      } catch (error) {
        console.warn(`Failed to get Firebase status for agent ${agent.id}:`, error);
        return {
          agentId: agent.id,
          status: 'offline',
          isAvailable: false,
          current_sessions: 0,
          lastSeen: null
        };
      }
    });

    const agentStatuses = await Promise.all(agentStatusPromises);
    const statusMap = agentStatuses.reduce((map: any, status: any) => {
      map[status.agentId] = status;
      return map;
    }, {});

    // 3. Transform agents data for UI compatibility (hybrid data)
    const transformedAgents = (agents || []).map((agent: any) => {
      const firebaseStatus = statusMap[agent.id];
      
      // Calculate availability based on business rules
      const isStatusAvailable = firebaseStatus.status === 'online' && firebaseStatus.isAvailable;
      const hasCapacity = firebaseStatus.current_sessions < agent.max_concurrent_sessions;
      
      // BUSINESS RULE: Only human agents can receive automatic transfers (bots are assigned explicitly)
      const canReceiveTransfers = agent.role === 'agent' && agent.agent_type === 'human';
      const isAvailable = isStatusAvailable && hasCapacity && canReceiveTransfers;

      // Extract departments
      const departments = (agent.agent_departments || []).map((ad: any) => ad.departments?.name || 'unknown');

      return {
        agentId: agent.id,
        name: agent.name,
        role: agent.role,
        agent_type: agent.agent_type, // NEW: Include agent_type for filtering
        status: {
          availability: firebaseStatus.status, // Firebase
          isAvailable: isAvailable,
          canReceiveTransfers: canReceiveTransfers,
          currentSessions: firebaseStatus.current_sessions, // Firebase
          maxSessions: agent.max_concurrent_sessions, // Supabase
          lastActivityAt: firebaseStatus.lastSeen // Firebase
        },
        departments: departments,
        isActive: agent.is_active
      };
    });

    // Filter available agents for transfers
    const availableAgents = transformedAgents
      .filter((agent: any) => agent.status.isAvailable)
      .filter((agent: any) => agent.role === 'agent');

    console.log(`📋 [HYBRID] Agents: ${availableAgents.length}/${transformedAgents.length} available for transfer`);

    return res.json({
      success: true,
      data: {
        agents: transformedAgents,
        availableAgents: availableAgents,
        totalAgents: transformedAgents.length,
        availableCount: availableAgents.length
      },
      timestamp: Date.now(),
    });

  } catch (error: any) {
    console.error('Error in hybrid agents API:', error);
    return res.status(500).json({
      success: false,
      error: {
        code: 'HYBRID_AGENTS_ERROR',
        message: 'Failed to get hybrid agent data',
        details: error.message
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// CONVERSATION CRUD OPERATIONS
// ========================================

// Create conversation
router.post('/conversations', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const conversation = await conversationService.createConversation(req.body);
    
    res.status(201).json({
      success: true,
      data: conversation,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_CREATE_FAILED',
        message: error.message || 'Failed to create conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get conversation by ID
router.get('/conversations/:id', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const conversation = await conversationService.getConversation(req.params.id);
    
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: `Conversation ${req.params.id} not found`,
        },
        timestamp: Date.now(),
      });
    }

    res.json({
      success: true,
      data: conversation,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_GET_FAILED',
        message: error.message || 'Failed to get conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Update conversation
router.put('/conversations/:id', async (req: Request, res: Response<ApiResponse>) => {
  try {
    await conversationService.updateConversation(req.params.id, req.body);
    
    res.json({
      success: true,
      data: { id: req.params.id, updated: true },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_UPDATE_FAILED',
        message: error.message || 'Failed to update conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Delete conversation
router.delete('/conversations/:id', async (req: Request, res: Response<ApiResponse>) => {
  try {
    await conversationService.deleteConversation(req.params.id);
    
    res.json({
      success: true,
      data: { id: req.params.id, deleted: true },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_DELETE_FAILED',
        message: error.message || 'Failed to delete conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// List conversations with filters (requires authentication)
router.get('/conversations', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    console.log('🔍 [CONVERSATIONS DEBUG] === START REQUEST ===');
    console.log('🔍 [CONVERSATIONS DEBUG] Headers:', JSON.stringify({
      authorization: req.headers.authorization ? `Bearer ${req.headers.authorization.substring(7, 20)}...` : 'MISSING',
      'content-type': req.headers['content-type'],
      'user-agent': req.headers['user-agent']
    }, null, 2));
    
    console.log('🔍 [CONVERSATIONS DEBUG] Query params:', JSON.stringify(req.query, null, 2));
    
    console.log('🔍 [CONVERSATIONS DEBUG] Authenticated user:', JSON.stringify({
      id: req.user?.id,
      email: req.user?.email,
      role: req.userProfile?.role,
      profileExists: !!req.userProfile
    }, null, 2));

    if (!req.user?.id) {
      console.error('🔍 [CONVERSATIONS DEBUG] ❌ CRITICAL: No authenticated user ID found');
      return res.status(403).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_FAILED',
          message: 'No authenticated user found - req.user.id is missing'
        },
        timestamp: Date.now(),
      });
    }

    // ✅ SECURITY: Auto-filter by authenticated agent - agents can only see their own conversations
    const filtersWithAgent = {
      ...req.query,
      assignedAgentId: req.user.id // Force filter by current agent's ID from Supabase Auth
    };
    
    console.log('🔍 [CONVERSATIONS DEBUG] Final filters:', JSON.stringify(filtersWithAgent, null, 2));
    console.log('🔍 [CONVERSATIONS DEBUG] Calling conversationService.listConversationsWithFilters...');
    
    const result = await conversationService.listConversationsWithFilters(filtersWithAgent);
    
    console.log('🔍 [CONVERSATIONS DEBUG] ✅ SUCCESS: Got result with', result?.conversations?.length || 0, 'conversations');
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATIONS_LIST_FAILED',
        message: error.message || 'Failed to list conversations',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// MESSAGE OPERATIONS
// ========================================

// Send message to conversation (requires authentication)
router.post('/conversations/:id/messages', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    const message = await conversationService.sendMessage(req.params.id, req.body);
    
    res.status(201).json({
      success: true,
      data: message,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'MESSAGE_SEND_FAILED',
        message: error.message || 'Failed to send message',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get messages for conversation
router.get('/conversations/:id/messages', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : undefined;
    const messages = await conversationService.getMessages(req.params.id, limit);
    
    res.json({
      success: true,
      data: {
        messages,
        count: messages.length,
        conversationId: req.params.id,
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'MESSAGE_GET_FAILED',
        message: error.message || 'Failed to get messages',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Mark message as read
router.put('/conversations/:conversationId/messages/:messageId/read', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { userId } = req.body;
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_USER_ID',
          message: 'userId is required',
        },
        timestamp: Date.now(),
      });
    }

    await conversationService.markMessageAsRead(req.params.conversationId, req.params.messageId, userId);
    
    res.json({
      success: true,
      data: { 
        conversationId: req.params.conversationId,
        messageId: req.params.messageId,
        readBy: userId,
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'MESSAGE_READ_FAILED',
        message: error.message || 'Failed to mark message as read',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Mark entire conversation as read
router.put('/conversations/:conversationId/read', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { userId } = req.body;
    if (!userId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_USER_ID',
          message: 'userId is required',
        },
        timestamp: Date.now(),
      });
    }

    await conversationService.markConversationAsRead(req.params.conversationId, userId);
    
    res.json({
      success: true,
      data: { 
        conversationId: req.params.conversationId,
        readBy: userId,
        readAt: Date.now(),
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_READ_FAILED',
        message: error.message || 'Failed to mark conversation as read',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// CONVERSATION ACTIONS (Used by other services)
// ========================================

// Transfer conversation (Used by Channel Router, Bot Human Router)
console.log('🛠️ ROUTER SETUP: Registering POST /conversations/:id/transfer route');
router.post('/conversations/:id/transfer', async (req: Request, res: Response<ApiResponse>) => {
  try {
    console.log('🌐 DEBUG ROUTER: Transfer endpoint called');
    console.log('🌐 DEBUG ROUTER: Params:', req.params);
    console.log('🌐 DEBUG ROUTER: Body:', req.body);
    
    const { targetAgentId, reason } = req.body;
    
    if (!targetAgentId || !reason) {
      console.log('❌ DEBUG ROUTER: Missing required fields');
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_REQUIRED_FIELDS',
          message: 'targetAgentId and reason are required',
        },
        timestamp: Date.now(),
      });
    }

    console.log('🚀 DEBUG ROUTER: Calling conversationService.transferConversation...');
    await conversationService.transferConversation(req.params.id, targetAgentId, reason);
    console.log('✅ DEBUG ROUTER: Transfer completed successfully');
    
    res.json({
      success: true,
      data: {
        conversationId: req.params.id,
        transferred: true,
        targetAgentId,
        reason,
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    console.error('❌ DEBUG ROUTER: Transfer error caught:', error);
    console.error('❌ DEBUG ROUTER: Error name:', error?.name);
    console.error('❌ DEBUG ROUTER: Error message:', error?.message);
    console.error('❌ DEBUG ROUTER: Error code:', error?.code);
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_TRANSFER_FAILED',
        message: error.message || 'Failed to transfer conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Assign conversation to available agent based on department and workload
router.post('/conversations/assign', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { conversationId, department, priority } = req.body;
    
    if (!conversationId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_CONVERSATION_ID',
          message: 'conversationId is required',
        },
        timestamp: Date.now(),
      });
    }

    // Find available agent using load balancing algorithm
    const availableAgent = await conversationService.findAvailableAgent(department || 'general');
    
    if (!availableAgent) {
      return res.status(503).json({
        success: false,
        error: {
          code: 'NO_AGENTS_AVAILABLE',
          message: `No agents available for department: ${department || 'general'}`,
        },
        timestamp: Date.now(),
      });
    }

    // Get the conversation and assign the agent
    const conversation = await conversationService.getConversation(conversationId);
    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: `Conversation ${conversationId} not found`,
        },
        timestamp: Date.now(),
      });
    }

    // Update conversation with agent assignment
    const updatedConversation = await conversationService.updateConversation(conversationId, {
      assignedTo: availableAgent.id,
      status: 'active'
    });

    // Update agent session count
    await conversationService.updateAgentStatus(availableAgent.id, {
      current_sessions: availableAgent.current_sessions + 1
    });

    res.json({
      success: true,
      data: {
        conversationId,
        assignedAgentId: availableAgent.id,
        assignedAgentName: availableAgent.name,
        department: department || 'general',
        reason: `Load balancing: ${availableAgent.utilization}% utilization, ${availableAgent.availableSlots - 1} remaining slots`,
        timestamp: new Date().toISOString()
      },
      timestamp: Date.now(),
    });

  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_ASSIGN_FAILED',
        message: error.message || 'Failed to assign conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Accept transfer (Used by Session Manager when agent accepts)
router.post('/conversations/:id/accept-transfer', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { agentId } = req.body;
    
    if (!agentId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_AGENT_ID',
          message: 'agentId is required',
        },
        timestamp: Date.now(),
      });
    }

    await conversationService.acceptTransfer(req.params.id, agentId);
    
    res.json({
      success: true,
      data: {
        conversationId: req.params.id,
        acceptedBy: agentId,
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'TRANSFER_ACCEPT_FAILED',
        message: error.message || 'Failed to accept transfer',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Close conversation (Used by Session Manager, Analytics)
router.post('/conversations/:id/close', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.closeConversationEnhanced(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_CLOSE_FAILED',
        message: error.message || 'Failed to close conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// TYPING INDICATORS
// ========================================

// Set typing indicator
router.post('/conversations/:id/typing', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { userId, userType, isTyping } = req.body;
    
    if (!userId || !userType || typeof isTyping !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_TYPING_DATA',
          message: 'userId, userType, and isTyping are required',
        },
        timestamp: Date.now(),
      });
    }

    await conversationService.setTypingIndicator(req.params.id, userId, userType, isTyping);
    
    res.json({
      success: true,
      data: {
        conversationId: req.params.id,
        userId,
        isTyping,
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: 'TYPING_INDICATOR_FAILED',
        message: 'Failed to set typing indicator',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get typing indicators
router.get('/conversations/:id/typing', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const indicators = await conversationService.getTypingIndicators(req.params.id);
    
    res.json({
      success: true,
      data: {
        conversationId: req.params.id,
        indicators,
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: 'TYPING_GET_FAILED',
        message: 'Failed to get typing indicators',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// AGENT MANAGEMENT (Used by Session Manager, Chat UI)
// ========================================

// Legacy agents endpoint removed - using hybrid architecture endpoint instead

// Get agent details
router.get('/agents/:agentId', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getAgentDetails(req.params.agentId);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    const statusCode = error.code === 'AGENT_NOT_FOUND' ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      error: {
        code: error.code || 'AGENT_DETAILS_FAILED',
        message: error.message || 'Failed to get agent details',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Update agent status (requires authentication)
router.put('/agents/:agentId/status', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    // Extract token from Authorization header for RLS queries
    const token = req.headers.authorization?.substring(7); // Remove 'Bearer ' prefix
    
    const result = await conversationService.updateAgentStatus(req.params.agentId, req.body, token);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    const statusCode = error.code === 'AGENT_NOT_FOUND' ? 404 : 400;
    res.status(statusCode).json({
      success: false,
      error: {
        code: error.code || 'AGENT_STATUS_UPDATE_FAILED',
        message: error.message || 'Failed to update agent status',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Set agent capacity
router.put('/agents/:agentId/capacity', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.setAgentCapacity(req.params.agentId, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    const statusCode = error.code === 'AGENT_NOT_FOUND' ? 404 : 400;
    res.status(statusCode).json({
      success: false,
      error: {
        code: error.code || 'AGENT_CAPACITY_UPDATE_FAILED',
        message: error.message || 'Failed to set agent capacity',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// SUPERVISOR AUTHORIZATION ENDPOINTS
// ========================================

// Create supervisor authorization (requires supervisor role)
router.post('/supervisor/authorizations', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.createSupervisorAuthorization(req.user.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    console.error('Create authorization error:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      error: {
        code: error.code || 'AUTHORIZATION_CREATE_FAILED',
        message: error.message || 'Failed to create authorization',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get agent status history
router.get('/agents/:agentId/status/history', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getAgentStatusHistory(req.params.agentId, req.query);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    console.error('Get status history error:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      error: {
        code: error.code || 'STATUS_HISTORY_FETCH_FAILED',
        message: error.message || 'Failed to fetch status history',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get supervisor dashboard data
router.get('/supervisor/dashboard', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getSupervisorDashboard(req.user.id);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    console.error('Get supervisor dashboard error:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      error: {
        code: error.code || 'SUPERVISOR_DASHBOARD_FAILED',
        message: error.message || 'Failed to get supervisor dashboard',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// INTERNAL NOTES
// ========================================

// Add note to conversation
router.post('/conversations/:id/notes', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const note = await conversationService.addNote(req.params.id, req.body);
    
    res.status(201).json({
      success: true,
      data: note,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'NOTE_ADD_FAILED',
        message: error.message || 'Failed to add note',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// DEPRECATED ENDPOINT - LOGIC MOVED TO BOT HUMAN ROUTER  
// ========================================

// DEPRECATED: Process message intelligently 
// Decision making logic moved to Bot Human Router service
router.post('/process-message', async (req: Request, res: Response<ApiResponse>) => {
  console.warn('⚠️ DEPRECATED: /process-message endpoint called - logic moved to Bot Human Router');
  
  // Return 501 Not Implemented to signal deprecation
  res.status(501).json({
    success: false,
    error: {
      code: 'ENDPOINT_DEPRECATED',
      message: 'The /process-message endpoint is deprecated. Decision making logic has been moved to Bot Human Router service.',
      details: {
        deprecatedAt: '2025-08-22',
        replacement: 'Logic is now handled by Bot Human Router → N8N/Chat Realtime flow',
        migration: 'Remove calls to this endpoint - use direct endpoint calls instead'
      }
    },
    timestamp: Date.now(),
  });
});

// ========================================
// AGENT ASSIGNMENT
// ========================================

// Assign agent to conversation
router.post('/conversations/:id/assign', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.assignAgent(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'AGENT_ASSIGNMENT_FAILED',
        message: error.message || 'Failed to assign agent',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Unassign agent from conversation
router.delete('/conversations/:id/assign', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.unassignAgent(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'AGENT_UNASSIGNMENT_FAILED',
        message: error.message || 'Failed to unassign agent',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// ADDITIONAL LIFECYCLE MANAGEMENT
// ========================================

// Update conversation status
router.put('/conversations/:id/status', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.updateConversationStatus(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'STATUS_UPDATE_FAILED',
        message: error.message || 'Failed to update status',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});


// ========================================
// TRANSFERS AND ESCALATION (EXTENDED)
// ========================================

// DEBUG: Test agent availability
router.post('/debug/agent-availability/:agentId', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { agentId } = req.params;
    console.log(`🔍 DEBUG: Testing availability for agent ${agentId}`);
    
    const isAvailable = await conversationService.isAgentAvailable(agentId);
    const agentProfile = await conversationService.getAgentProfile(agentId);
    
    // Also test findAvailableAgent
    const departmentId = agentProfile?.departments?.[0] || 'general';
    const foundAgent = await conversationService.findAvailableAgent(departmentId);
    
    res.json({
      success: true,
      data: {
        agentId,
        isAvailable,
        agentProfile,
        departmentId,
        foundAgent: foundAgent ? { id: foundAgent.id, name: foundAgent.name, role: foundAgent.role } : null
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    console.error('Error in debug agent availability:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DEBUG_ERROR',
        message: error.message,
      },
      timestamp: Date.now(),
    });
  }
});

// Reject transfer
router.post('/conversations/:id/reject-transfer', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.rejectTransfer(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'TRANSFER_REJECT_FAILED',
        message: error.message || 'Failed to reject transfer',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Cancel transfer (for originating agent)
router.post('/conversations/:id/cancel-transfer', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { agentId, reason } = req.body;
    
    if (!agentId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_AGENT_ID',
          message: 'agentId is required',
        },
        timestamp: Date.now(),
      });
    }

    const result = await conversationService.cancelTransfer(req.params.id, agentId, reason);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'TRANSFER_CANCEL_FAILED',
        message: error.message || 'Failed to cancel transfer',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Escalate conversation
router.post('/conversations/:id/escalate', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.escalateConversation(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'ESCALATION_FAILED',
        message: error.message || 'Failed to escalate conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Transfer to department
router.post('/conversations/:id/transfer-department', notImplemented('POST /conversations/:id/transfer-department'));

// Get transfer status
router.get('/conversations/:id/transfer/status', notImplemented('GET /conversations/:id/transfer/status'));

// ========================================
// SUPERVISION
// ========================================

// Start supervision
router.post('/conversations/:id/supervise', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.superviseConversation(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'SUPERVISION_START_FAILED',
        message: error.message || 'Failed to start supervision',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// End supervision
router.delete('/conversations/:id/supervise', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.endSupervision(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'SUPERVISION_END_FAILED',
        message: error.message || 'Failed to end supervision',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Request supervisor
router.post('/conversations/:id/supervisor-request', notImplemented('POST /conversations/:id/supervisor-request'));

// ========================================
// ADVANCED SUPERVISION
// ========================================

// Supervisor sends message in conversation
router.post('/conversations/:id/supervise/message', notImplemented('POST /conversations/:id/supervise/message'));

// Get supervision status
router.get('/conversations/:id/supervision/status', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getSupervisionStatus(req.params.id);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'SUPERVISION_STATUS_FAILED',
        message: error.message || 'Failed to get supervision status',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Change supervision mode (observe ↔ participate)
router.put('/conversations/:id/supervise/mode', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.changeSupervisionMode(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'SUPERVISION_MODE_CHANGE_FAILED',
        message: error.message || 'Failed to change supervision mode',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Send private coaching message to agent
router.post('/conversations/:id/supervise/coach', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.sendCoachingMessage(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'COACHING_MESSAGE_FAILED',
        message: error.message || 'Failed to send coaching message',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// List supervised conversations (dashboard)
router.get('/conversations/supervised', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const { supervisorId } = req.query;
    if (!supervisorId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_SUPERVISOR_ID',
          message: 'supervisorId query parameter is required',
        },
        timestamp: Date.now(),
      });
    }

    const result = await conversationService.getSupervisedConversations(supervisorId as string, req.query);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'SUPERVISED_CONVERSATIONS_FAILED',
        message: error.message || 'Failed to get supervised conversations',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// NOTES AND CONTEXT (EXTENDED)
// ========================================

// Edit note
router.put('/conversations/:id/notes/:noteId', notImplemented('PUT /conversations/:id/notes/:noteId'));

// Manage tags
router.put('/conversations/:id/tags', notImplemented('PUT /conversations/:id/tags'));

// ========================================
// CONVERSATION CLOSURE AND EXPORT
// ========================================

// Reopen conversation
router.post('/conversations/:id/reopen', notImplemented('POST /conversations/:id/reopen'));

// Export conversation
router.post('/conversations/:id/export', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.exportConversation(req.params.id, req.body);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    const statusCode = error.code === 'CONVERSATION_NOT_FOUND' ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_EXPORT_FAILED',
        message: error.message || 'Failed to export conversation',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// QUEUE MANAGEMENT
// ========================================

// Get all queues
router.get('/queues', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getAllQueues();
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'QUEUES_GET_FAILED',
        message: error.message || 'Failed to get queues',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get specific queue
router.get('/queues/:departmentId', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getQueueByDepartment(req.params.departmentId);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'QUEUE_GET_FAILED',
        message: error.message || 'Failed to get department queue',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// ANALYTICS & REPORTING
// ========================================

// Get conversation analytics
router.get('/analytics/conversations', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getConversationAnalytics(req.query);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'ANALYTICS_GET_FAILED',
        message: error.message || 'Failed to get conversation analytics',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// SYSTEM STATUS
// ========================================

// Get system status
router.get('/status', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getSystemStatus();
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'SYSTEM_STATUS_FAILED',
        message: error.message || 'Failed to get system status',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// STATES AND METRICS
// ========================================

// Get conversation history
router.get('/conversations/:id/history', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getConversationHistory(req.params.id);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    const statusCode = error.code === 'CONVERSATION_NOT_FOUND' ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_HISTORY_FAILED',
        message: error.message || 'Failed to get conversation history',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get conversation metrics
router.get('/conversations/:id/metrics', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getConversationMetrics(req.params.id);
    
    res.json({
      success: true,
      data: result,
      timestamp: Date.now(),
    });
  } catch (error: any) {
    const statusCode = error.code === 'CONVERSATION_NOT_FOUND' ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      error: {
        code: error.code || 'CONVERSATION_METRICS_FAILED',
        message: error.message || 'Failed to get conversation metrics',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// DYNAMIC QUEUE MANAGEMENT
// ========================================

// Sync department queues with Supabase (Admin endpoint)
router.post('/admin/sync-queues', async (req: Request, res: Response<ApiResponse>) => {
  try {
    await conversationService.syncDepartmentQueues();
    const activeQueues = await conversationService.getActiveQueues();
    
    res.json({
      success: true,
      data: {
        message: 'Department queues synchronized successfully',
        queuesCount: activeQueues.length,
        queues: activeQueues
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'QUEUE_SYNC_FAILED',
        message: error.message || 'Failed to synchronize department queues',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// Get active queues status
router.get('/admin/queues', async (req: Request, res: Response<ApiResponse>) => {
  try {
    const activeQueues = await conversationService.getActiveQueues();
    
    res.json({
      success: true,
      data: {
        queues: activeQueues,
        totalQueues: activeQueues.length
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'QUEUES_GET_FAILED',
        message: error.message || 'Failed to get queue status',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// AUTH TESTING ENDPOINT
// ========================================

// Get authenticated user profile with departments (for testing)
router.get('/auth/profile', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    res.json({
      success: true,
      data: {
        user: {
          id: req.user!.id,
          email: req.user!.email,
          role: req.user!.role
        },
        profile: req.userProfile,
        message: 'Agent profile loaded successfully with department relationships'
      },
      timestamp: Date.now(),
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: {
        code: error.code || 'AUTH_PROFILE_FAILED',
        message: error.message || 'Failed to get authenticated profile',
        details: error.details,
      },
      timestamp: Date.now(),
    });
  }
});

// ========================================
// SUPERVISOR QUEUE ENDPOINTS
// ========================================

// Get supervisor queue (escalated conversations)
router.get('/supervisor/queue', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getSupervisorQueue(req.query);
    res.json(result);
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'SUPERVISOR_QUEUE_ERROR',
        message: error.message || 'Failed to get supervisor queue',
        details: error.details
      },
      timestamp: Date.now()
    });
  }
});

// Get escalated conversations for supervisor dashboard
router.get('/supervisor/escalations', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    const result = await conversationService.getEscalatedConversations(req.query);
    res.json(result);
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'ESCALATIONS_ERROR',
        message: error.message || 'Failed to get escalated conversations',
        details: error.details
      },
      timestamp: Date.now()
    });
  }
});

// Resolve escalation - remove urgent priority and escalated status
router.post('/conversations/:conversationId/resolve-escalation', authService.authenticate, async (req: AuthenticatedRequest, res: Response<ApiResponse>) => {
  try {
    const { conversationId } = req.params;
    const { reason, resolvedBy } = req.body;
    
    if (!conversationId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_CONVERSATION_ID',
          message: 'conversationId is required'
        },
        timestamp: Date.now()
      });
    }

    const result = await conversationService.resolveEscalation(conversationId, {
      reason: reason || 'Escalación resuelta',
      resolvedBy: resolvedBy || req.user!.id,
      resolvedAt: Date.now()
    });
    
    res.json(result);
  } catch (error: any) {
    res.status(400).json({
      success: false,
      error: {
        code: error.code || 'RESOLVE_ESCALATION_ERROR',
        message: error.message || 'Failed to resolve escalation',
        details: error.details
      },
      timestamp: Date.now()
    });
  }
});

export default router;