#!/usr/bin/env node

/**
 * Centralized Firebase Configuration
 * 
 * This file provides consistent Firebase initialization across all helper scripts.
 * All scripts should import this configuration instead of creating their own.
 * 
 * Configuration is automatically pulled from environment variables,
 * ensuring consistency across development and production environments.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from project root
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

/**
 * Firebase Configuration Constants
 * All values come from environment variables with sensible defaults
 */
const FIREBASE_CONFIG = {
  PROJECT_ID: process.env.GOOGLE_CLOUD_PROJECT_ID || 'cx-system-469120',
  EMULATOR_HOST: '127.0.0.1:9000',
  USE_EMULATOR: process.env.NODE_ENV === 'development' || process.env.FIREBASE_USE_EMULATOR !== 'false',
  
  // Production configuration (when not using emulator)
  PRODUCTION_DATABASE_URL: process.env.FIREBASE_DATABASE_URL || 'https://cx-system-dev-default-rtdb.firebaseio.com/',
  SERVICE_ACCOUNT_PATH: process.env.FIREBASE_SERVICE_ACCOUNT_PATH
};

/**
 * Initialize Firebase Admin SDK with consistent configuration
 * This function should be called once at the start of each script
 * 
 * @param {Object} options - Optional configuration overrides
 * @returns {admin.app.App} Initialized Firebase app instance
 */
function initializeFirebase(options = {}) {
  // Check if Firebase is already initialized
  if (admin.apps.length > 0) {
    console.log('🔥 Firebase already initialized, reusing existing app');
    return admin.apps[0];
  }

  const config = { ...FIREBASE_CONFIG, ...options };
  
  if (config.USE_EMULATOR) {
    console.log('🔧 Initializing Firebase with Emulator Configuration');
    console.log(`   Project ID: ${config.PROJECT_ID}`);
    console.log(`   Emulator Host: ${config.EMULATOR_HOST}`);
    
    // Set emulator environment variables
    process.env.FIREBASE_AUTH_EMULATOR_HOST = '127.0.0.1:9099';
    process.env.FIRESTORE_EMULATOR_HOST = '127.0.0.1:8080';
    process.env.FIREBASE_DATABASE_EMULATOR_HOST = config.EMULATOR_HOST;
    
    // Use Firebase's official namespace format: {projectId}-default-rtdb
    const firebaseNamespace = `${config.PROJECT_ID}-default-rtdb`;
    
    const app = admin.initializeApp({
      projectId: config.PROJECT_ID,
      databaseURL: `http://${config.EMULATOR_HOST}/?ns=${firebaseNamespace}`
    });
    
    console.log(`✅ Firebase Emulator initialized for project: ${config.PROJECT_ID}`);
    console.log(`📊 Using Firebase namespace: ${firebaseNamespace}`);
    return app;
    
  } else {
    console.log('🚀 Initializing Firebase for Production');
    
    const credentials = config.SERVICE_ACCOUNT_PATH 
      ? admin.credential.cert(require(config.SERVICE_ACCOUNT_PATH))
      : admin.credential.applicationDefault();
    
    const app = admin.initializeApp({
      credential: credentials,
      databaseURL: config.PRODUCTION_DATABASE_URL,
      projectId: config.PROJECT_ID
    });
    
    console.log(`✅ Firebase Production initialized for project: ${config.PROJECT_ID}`);
    return app;
  }
}

/**
 * Get Firebase Database instance
 * Automatically initializes Firebase if not already done
 * 
 * @returns {admin.database.Database} Firebase Realtime Database instance
 */
function getFirebaseDatabase() {
  if (admin.apps.length === 0) {
    initializeFirebase();
  }
  
  return admin.database();
}

/**
 * Get Firebase Database reference
 * Convenience method for getting database references
 * 
 * @param {string} path - Database path
 * @returns {admin.database.Reference} Firebase Database reference
 */
function getFirebaseRef(path) {
  const db = getFirebaseDatabase();
  return db.ref(path);
}

/**
 * Helper function to get common agent/conversation constants
 * These IDs are used consistently across testing scripts
 */
const CONSTANTS = {
  // Test agent IDs
  JUAN_PEREZ_ID: '5f4fb378-908d-4b49-83ce-be4ce3b50c5d',
  SUPERVISOR_ID: 'supervisor-test-id-123',
  
  // Common conversation types for testing
  CONVERSATION_TYPES: {
    TWILIO_WA_DIRECT: 'twilio_wa_direct',
    TWILIO_CONVERSATIONS: 'twilio_conversations_api', 
    META_WA_BUSINESS: 'meta_wa_business',
    WHATSAPP_CLOUD: 'whatsapp_cloud_api'
  },
  
  // Department IDs (usando guión_bajo para consistencia)
  DEPARTMENTS: {
    TECHNICAL_SUPPORT: 'technical_support',
    SALES: 'sales',
    BILLING: 'billing',
    GENERAL: 'general'
  }
};

/**
 * Utility function to create consistent conversation ID
 * Based on the channel type and unique identifiers
 * 
 * @param {string} type - Conversation type (from CONSTANTS.CONVERSATION_TYPES)
 * @param {string} identifier - Unique identifier (phone, session ID, etc.)
 * @returns {string} Generated conversation ID
 */
function generateConversationId(type, identifier) {
  const timestamp = Date.now();
  return `${type}_${identifier}_${timestamp}`;
}

/**
 * Export all configuration and helper functions
 */
module.exports = {
  // Main configuration functions
  initializeFirebase,
  getFirebaseDatabase,
  getFirebaseRef,
  
  // Configuration constants
  FIREBASE_CONFIG,
  CONSTANTS,
  
  // Utility functions
  generateConversationId,
  
  // Direct exports for convenience
  admin // Re-export admin for scripts that need direct access
};

// Auto-initialize if this file is run directly (for testing)
if (require.main === module) {
  console.log('🧪 Testing Firebase Configuration...');
  console.log('Configuration:', FIREBASE_CONFIG);
  console.log('Constants:', CONSTANTS);
  
  try {
    const app = initializeFirebase();
    const db = getFirebaseDatabase();
    console.log('✅ Firebase configuration test successful');
    console.log(`📱 App: ${app.name}`);
    console.log(`💾 Database: Connected`);
  } catch (error) {
    console.error('❌ Firebase configuration test failed:', error.message);
  }
}