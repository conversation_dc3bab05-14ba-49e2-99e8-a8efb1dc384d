/**
 * Fix agent assignment for pending_acceptance conversation
 */

require('dotenv').config();
const { initializeFirebase, getFirebaseDatabase } = require('./firebase-config');

// Initialize Firebase
const app = initializeFirebase();
const db = getFirebaseDatabase();

async function fixAgentAssignment() {
  console.log('🔧 Fixing agent assignment for conversation...');
  
  const conversationId = 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaa10';
  const correctAgentId = '5f4fb378-908d-4b49-83ce-be4ce3b50c5d'; // <PERSON>
  
  try {
    // Update the conversation with correct agent assignment
    const updates = {
      [`conversations/${conversationId}/assignedAgentId`]: correctAgentId,
      [`conversations/${conversationId}/transferInfo/currentTransfer/toAgentId`]: correctAgentId,
      [`conversations/${conversationId}/transferInfo/transferHistory/0/toAgentId`]: correctAgentId,
    };
    
    await db.ref().update(updates);
    
    console.log('✅ Updated conversation assignment');
    console.log(`   Conversation: ${conversationId}`);
    console.log(`   Assigned to: ${correctAgentId} (<PERSON>)`);
    
    // Verify the update
    const conversationSnapshot = await db.ref(`conversations/${conversationId}`).once('value');
    const conversation = conversationSnapshot.val();
    
    console.log('\n📋 Verification:');
    console.log(`   Status: ${conversation.status}`);
    console.log(`   Assigned Agent: ${conversation.assignedAgentId}`);
    console.log(`   Transfer To: ${conversation.transferInfo.currentTransfer.toAgentId}`);
    console.log(`   Customer: ${conversation.customer.name}`);
    
  } catch (error) {
    console.error('❌ Error fixing assignment:', error);
  }
}

// Run the script
if (require.main === module) {
  fixAgentAssignment()
    .then(() => {
      console.log('\n✨ Assignment fixed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { fixAgentAssignment };