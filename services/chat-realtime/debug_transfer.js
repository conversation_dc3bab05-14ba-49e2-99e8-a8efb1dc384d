/**
 * Debug script para probar transferencias de conversaciones
 */

require('dotenv').config();
const { initializeFirebase, getFirebaseDatabase } = require('./firebase-config');

// Initialize Firebase
const app = initializeFirebase();
const db = getFirebaseDatabase();

async function debugTransfer() {
  console.log('🔍 Debug Transfer Script');
  
  const conversationId = 'CHaaaaaaaaaaaaaaaaaaaaaaatest01';
  const targetAgentId = '6e3ba129-1008-4d6a-a25d-bdce5d55082c';
  
  try {
    // Check if conversation exists
    console.log('\n📋 Checking conversation exists...');
    const conversationSnapshot = await db.ref(`conversations/${conversationId}`).once('value');
    const conversation = conversationSnapshot.val();
    
    if (!conversation) {
      console.error('❌ Conversation not found');
      return;
    }
    
    console.log('✅ Conversation found:', {
      id: conversation.id,
      status: conversation.status,
      assignedTo: conversation.assignedTo,
      assignedAgentId: conversation.assignedAgentId,
      customer: conversation.customer?.name
    });
    
    // Check current schema
    console.log('\n📊 Current schema details:');
    console.log('  assignedTo:', conversation.assignedTo, '(should be "human" or "bot")');
    console.log('  assignedAgentId:', conversation.assignedAgentId, '(should be agent UUID)');
    console.log('  assignedBotId:', conversation.assignedBotId, '(should be bot ID or null)');
    
    // Test the transfer logic manually
    console.log('\n🔄 Testing transfer logic...');
    
    const now = Date.now();
    const transferId = `transfer_${now}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log('Transfer details:');
    console.log('  From Agent ID:', conversation.assignedAgentId);
    console.log('  To Agent ID:', targetAgentId);
    console.log('  Transfer ID:', transferId);
    console.log('  Reason: Testing transfer functionality');
    
    // Check if we can create the transfer record
    const transferRecord = {
      id: transferId,
      fromAgentId: conversation.assignedAgentId,
      toAgentId: targetAgentId,
      reason: 'Testing transfer functionality',
      timestamp: now,
      status: 'pending'
    };
    
    console.log('\n✅ Transfer record created successfully:', transferRecord);
    
    // Test API call
    console.log('\n🌐 Testing API call...');
    
    const axios = require('axios');
    
    try {
      const baseUrl = process.env.CHAT_REALTIME_URL || 'http://localhost:3003';
      const response = await axios.post(`${baseUrl}/api/conversations/${conversationId}/transfer`, {
        targetAgentId: targetAgentId,
        reason: 'Testing transfer functionality'
      });
      
      console.log('✅ Transfer API successful:', response.data);
      
    } catch (error) {
      console.error('❌ Transfer API failed:');
      
      if (error.response) {
        console.error('  Status:', error.response.status);
        console.error('  Data:', error.response.data);
      } else {
        console.error('  Error:', error.message);
      }
      
      // Check if there are any additional error details
      if (error.response?.data?.error) {
        console.error('  Error Code:', error.response.data.error.code);
        console.error('  Error Message:', error.response.data.error.message);
        console.error('  Error Details:', error.response.data.error.details);
      }
    }
    
    // Check final state
    console.log('\n📊 Checking final conversation state...');
    const finalSnapshot = await db.ref(`conversations/${conversationId}`).once('value');
    const finalConversation = finalSnapshot.val();
    
    console.log('Final state:', {
      status: finalConversation.status,
      assignedTo: finalConversation.assignedTo,
      assignedAgentId: finalConversation.assignedAgentId,
      transferInfo: finalConversation.transferInfo ? 'Present' : 'Not present'
    });
    
  } catch (error) {
    console.error('❌ Debug script error:', error);
  }
}

// Run the debug
if (require.main === module) {
  debugTransfer()
    .then(() => {
      console.log('\n✨ Debug script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Debug script failed:', error);
      process.exit(1);
    });
}

module.exports = { debugTransfer };