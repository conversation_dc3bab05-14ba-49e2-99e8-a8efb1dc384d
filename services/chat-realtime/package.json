{"name": "@cx-system/chat-realtime", "version": "1.0.0", "private": true, "description": "Chat Realtime service for CX System - Exclusive Firebase Realtime Database interface", "main": "dist/server.js", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@google-cloud/pubsub": "^5.2.0", "@supabase/supabase-js": "^2.55.0", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.10", "dotenv": "^16.3.1", "express": "^4.18.2", "firebase": "^12.1.0", "firebase-admin": "^12.7.0", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "winston": "^3.17.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^20.11.0", "@types/supertest": "^2.0.16", "jest": "^29.7.0", "supertest": "^7.1.3", "ts-jest": "^29.4.1", "tsx": "^4.7.0", "typescript": "^5.3.3"}}