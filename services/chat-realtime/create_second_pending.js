/**
 * Create second pending_acceptance conversation for testing
 */

require('dotenv').config();
const { initializeFirebase, getFirebaseDatabase } = require('./firebase-config');

// Initialize Firebase
const app = initializeFirebase();
const db = getFirebaseDatabase();

async function createSecondPendingConversation() {
  console.log('🔧 Creating second pending_acceptance conversation...');
  
  const conversationId = 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaa10';
  const fromAgentId = 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'; // <PERSON>
  const toAgentId = '6e3ba129-1008-4d6a-a25d-bdce5d55082c';   // Your agent ID
  
  try {
    // Create conversation data
    const conversationData = {
      id: conversationId,
      status: 'pending_acceptance',
      assignedTo: 'human',
      assignedAgentId: toAgentId, // Assigned to you, waiting for acceptance
      customer: {
        id: 'whatsapp:+5000000100',
        name: '<PERSON>',
        phone: '+5000000100',
        email: '<EMAIL>'
      },
      metadata: {
        customerName: '<PERSON>',
        customerPhone: '+5000000100',
        customerEmail: '<EMAIL>',
        transferCount: 1,
        priority: 'medium'
      },
      priority: 'medium',
      createdAt: Date.now() - 600000, // 10 minutes ago
      updatedAt: Date.now() - 60000,  // 1 minute ago
      assignedAt: Date.now() - 120000, // 2 minutes ago
      unreadCount: 3,
      
      // Transfer information
      transferInfo: {
        currentTransfer: {
          id: `transfer_${Date.now()}_test02`,
          fromAgentId: fromAgentId,
          toAgentId: toAgentId,
          reason: 'Customer requesting billing support specialist',
          timestamp: Date.now() - 120000, // 2 minutes ago
          status: 'pending_acceptance'
        },
        transferHistory: [
          {
            id: `transfer_${Date.now() - 120000}_test02`,
            fromAgentId: fromAgentId,
            toAgentId: toAgentId,
            reason: 'Customer requesting billing support specialist',
            timestamp: Date.now() - 120000,
            status: 'pending_acceptance'
          }
        ]
      }
    };

    // Create conversation in Firebase
    await db.ref(`conversations/${conversationId}`).set(conversationData);
    console.log('✅ Created conversation:', conversationId);

    // Add messages to the conversation
    const messages = [
      {
        id: `msg-${Date.now()}-001`,
        conversationId: conversationId,
        senderId: 'customer-CHaaaaaaaaaaaaaaaaaaaaaaaaaaaa10',
        senderType: 'customer',
        content: 'Hello, I have an issue with my billing statement.',
        type: 'text',
        timestamp: new Date(Date.now() - 480000).toISOString() // 8 minutes ago
      },
      {
        id: `msg-${Date.now()}-002`,
        conversationId: conversationId,
        senderId: fromAgentId, // María González
        senderType: 'agent',
        content: 'Hi Carlos! I can help you with that. Could you tell me more about the billing issue?',
        type: 'text',
        timestamp: new Date(Date.now() - 420000).toISOString() // 7 minutes ago
      },
      {
        id: `msg-${Date.now()}-003`,
        conversationId: conversationId,
        senderId: 'customer-CHaaaaaaaaaaaaaaaaaaaaaaaaaaaa10',
        senderType: 'customer',
        content: 'I was charged twice for the same service last month. Can you check my account?',
        type: 'text',
        timestamp: new Date(Date.now() - 360000).toISOString() // 6 minutes ago
      },
      {
        id: `msg-${Date.now()}-004`,
        conversationId: conversationId,
        senderId: fromAgentId, // María González
        senderType: 'agent',
        content: 'I understand your concern about the double charge. Let me transfer you to our billing specialist who can investigate this thoroughly.',
        type: 'text',
        timestamp: new Date(Date.now() - 180000).toISOString() // 3 minutes ago
      },
      {
        id: `system-${Date.now()}`,
        conversationId: conversationId,
        senderId: 'system',
        senderType: 'system',
        content: 'Conversation transferred to billing specialist',
        type: 'system',
        timestamp: new Date(Date.now() - 120000).toISOString() // 2 minutes ago
      },
      {
        id: `msg-${Date.now()}-005`,
        conversationId: conversationId,
        senderId: 'customer-CHaaaaaaaaaaaaaaaaaaaaaaaaaaaa10',
        senderType: 'customer',
        content: 'Thank you, I\'ll wait for the billing specialist.',
        type: 'text',
        timestamp: new Date(Date.now() - 60000).toISOString() // 1 minute ago
      }
    ];
    
    // Add messages to Firebase
    const messagesRef = db.ref(`conversations/${conversationId}/messages`);
    
    for (const message of messages) {
      await messagesRef.child(message.id).set(message);
      console.log(`✅ Added message: ${message.senderType} - "${message.content.substring(0, 40)}..."`);
    }
    
    console.log(`\n🎉 Successfully created conversation ${conversationId} with ${messages.length} messages`);
    console.log('📋 Conversation details:');
    console.log(`   Customer: ${conversationData.customer.name}`);
    console.log(`   Phone: ${conversationData.customer.phone}`);
    console.log(`   Status: ${conversationData.status}`);
    console.log(`   Transfer reason: ${conversationData.transferInfo.currentTransfer.reason}`);
    console.log(`   Assigned to: ${toAgentId}`);
    
  } catch (error) {
    console.error('❌ Error creating conversation:', error);
  }
}

// Run the script
if (require.main === module) {
  createSecondPendingConversation()
    .then(() => {
      console.log('\n✨ Script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createSecondPendingConversation };