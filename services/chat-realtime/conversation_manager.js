#!/usr/bin/env node

/**
 * 🔧 CONVERSATION MANAGER - Herramienta poderosa para gestión de conversaciones
 * 
 * Funcionalidades:
 * - Crear N conversaciones para agente específico
 * - Transferir conversación específica a otro agente
 * - Cerrar conversación específica
 * - Eliminar conversaciones de agente específico
 * - Recrear conversaciones por defecto (modo compatibilidad)
 */

const { initializeFirebase, getFirebaseDatabase } = require('./firebase-config');
const axios = require('axios');
const { createClient } = require('@supabase/supabase-js');

// Initialize Firebase only if not already initialized
let app, db;
try {
  app = initializeFirebase();
  db = getFirebaseDatabase();
} catch (error) {
  if (error.message?.includes('already exists')) {
    console.log('ℹ️  Firebase ya inicializado, reutilizando conexión...');
    db = getFirebaseDatabase();
  } else {
    console.error('❌ Error inicializando Firebase:', error.message);
    process.exit(1);
  }
}

// Configuration
const CHAT_REALTIME_URL = process.env.CHAT_REALTIME_URL || 'http://localhost:3003';

// Load environment variables from project root
require('dotenv').config({ path: '../../.env' });

// Initialize Supabase client with environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;

let supabase = null;
if (supabaseUrl && supabaseKey) {
  supabase = createClient(supabaseUrl, supabaseKey);
}

// Cache for agents (loaded dynamically)
let AGENTS_CACHE = null;

// Customer pool for realistic conversations
const CUSTOMER_POOL = [
  { id: 'customer-001', name: 'Roberto Silva', phone: '+1234567001', email: '<EMAIL>' },
  { id: 'customer-002', name: 'Carmen López', phone: '+1234567002', email: '<EMAIL>' },
  { id: 'customer-003', name: 'Diego Martínez', phone: '+1234567003', email: '<EMAIL>' },
  { id: 'customer-004', name: 'Lucía Fernández', phone: '+1234567004', email: '<EMAIL>' },
  { id: 'customer-005', name: 'Patricia Morales', phone: '+1234567005', email: '<EMAIL>' },
  { id: 'customer-006', name: 'Fernando Castro', phone: '+1234567006', email: '<EMAIL>' },
  { id: 'customer-007', name: 'Isabel Jiménez', phone: '+1234567007', email: '<EMAIL>' },
  { id: 'customer-008', name: 'Miguel Vargas', phone: '+1234567008', email: '<EMAIL>' }
];

const CONVERSATION_TOPICS = [
  'Mi tarjeta no está funcionando correctamente',
  'Tengo una duda sobre mi facturación',
  'Quiero información sobre sus productos',
  'Necesito ayuda con mi cuenta online',
  'Hay un cargo que no reconozco',
  'Mi aplicación móvil no carga',
  'Quiero cambiar mis datos personales',
  'Necesito un reporte de mis transacciones'
];

// Load real agents from Supabase (NO FALLBACKS)
async function loadAgentsFromSupabase() {
  if (AGENTS_CACHE) {
    return AGENTS_CACHE;
  }
  
  if (!supabase) {
    throw new Error('❌ Supabase no está configurado. Verifica las variables de entorno SUPABASE_URL y SUPABASE_ANON_KEY');
  }
  
  try {
    console.log('🔍 Cargando agentes desde Supabase...');
    
    const { data: agents, error } = await supabase
      .from('agents')
      .select('id, name, email, is_active')
      .eq('is_active', true); // Only active agents
    
    if (error) {
      throw new Error(`❌ Error obteniendo agentes de Supabase: ${error.message || error}`);
    }
    
    if (!agents || agents.length === 0) {
      throw new Error('❌ No se encontraron agentes activos en Supabase');
    }
    
    return createAgentsCache(agents);
    
  } catch (error) {
    if (error.message.includes('❌')) {
      throw error; // Re-throw our custom errors
    }
    throw new Error(`❌ Error conectando con Supabase: ${error.message || error}`);
  }
}

// Helper function to create agents cache structure
function createAgentsCache(agents) {
  const agentsByName = {};
  const agentsById = {};
  
  agents.forEach(agent => {
    // Extract first name for easy lookup
    const firstName = agent.name.split(' ')[0].toLowerCase();
    agentsByName[firstName] = agent;
    agentsById[agent.id] = agent;
    
    // Also add full name lookup (normalized)
    const fullNameKey = agent.name.toLowerCase().replace(/\s+/g, '');
    agentsByName[fullNameKey] = agent;
  });
  
  AGENTS_CACHE = {
    byName: agentsByName,
    byId: agentsById,
    list: agents
  };
  
  console.log(`✅ ${agents.length} agentes disponibles`);
  agents.forEach(agent => {
    const firstName = agent.name.split(' ')[0].toLowerCase();
    console.log(`   ${firstName.padEnd(10)} ${agent.name} (${agent.id})`);
  });
  
  return AGENTS_CACHE;
}

// Find agent by name or ID
async function findAgent(identifier) {
  try {
    const agents = await loadAgentsFromSupabase();
    
    // Try by name first (most common usage)
    const normalizedName = identifier.toLowerCase();
    if (agents.byName[normalizedName]) {
      return agents.byName[normalizedName];
    }
    
    // Try by full ID
    if (agents.byId[identifier]) {
      return agents.byId[identifier];
    }
    
    // Not found
    return null;
    
  } catch (error) {
    console.error(error.message);
    console.error('');
    console.error('💡 Para solucionar:');
    console.error('   1. Verifica que Supabase esté disponible');
    console.error('   2. Revisa las variables de entorno en .env');
    console.error('   3. Asegúrate de tener agentes activos en la tabla "agents"');
    throw error;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

// Help message
function showHelp() {
  console.log('🔧 CONVERSATION MANAGER - Herramienta de gestión de conversaciones');
  console.log('');
  console.log('📋 COMANDOS DISPONIBLES:');
  console.log('');
  console.log('🆕 CREAR CONVERSACIONES:');
  console.log('  --create <agentId> <count>     Crear N conversaciones para agente específico');
  console.log('  --create-default               Recrear 4 conversaciones por defecto (compatibilidad)');
  console.log('');
  console.log('🔄 TRANSFERIR:');
  console.log('  --transfer <conversationId> <targetAgentId> [reason]');
  console.log('                                 Transferir conversación a otro agente');
  console.log('  --accept-transfer <conversationId> <agentId>');
  console.log('                                 Aceptar transfer pendiente como agente');
  console.log('');
  console.log('✅ CERRAR:');
  console.log('  --close <conversationId> [reason]');
  console.log('                                 Cerrar conversación específica');
  console.log('');
  console.log('🗑️  ELIMINAR:');
  console.log('  --delete-agent <agentId>       Eliminar todas las conversaciones del agente');
  console.log('  --delete-conversation <conversationId>   Eliminar conversación específica');
  console.log('  --delete-all                   Eliminar TODAS las conversaciones (⚠️  PELIGROSO!)');
  console.log('  --delete-all-agents            Eliminar TODOS los agentes de Firebase');
  console.log('  --recreate-agents              Eliminar y recrear agentes desde Supabase');
  console.log('');
  console.log('📊 INFO:');
  console.log('  --list-agents                  Mostrar agentes disponibles');
  console.log('  --list-conversations [agentId] Listar conversaciones (opcionalmente filtrar por agente)');
  console.log('  --help, -h                     Mostrar esta ayuda');
  console.log('');
  console.log('💡 EJEMPLOS:');
  console.log('');
  console.log('  # Crear 5 conversaciones para Juan');
  console.log('  node conversation_manager.js --create juan 5');
  console.log('');
  console.log('  # Transferir conversación específica a Carlos');
  console.log('  node conversation_manager.js --transfer CHaaa...test01 carlos "Load balancing"');
  console.log('');
  console.log('  # Aceptar transfer como Carlos');
  console.log('  node conversation_manager.js --accept-transfer CHaaa...test01 carlos');
  console.log('');
  console.log('  # Cerrar conversación');
  console.log('  node conversation_manager.js --close CHaaa...test01 "Issue resolved"');
  console.log('');
  console.log('  # Eliminar todas las conversaciones de María');
  console.log('  node conversation_manager.js --delete-agent maria');
  console.log('');
  console.log('  # Eliminar TODAS las conversaciones (PELIGROSO)');
  console.log('  node conversation_manager.js --delete-all');
  console.log('');
  console.log('🔑 AGENTES (se cargan dinámicamente desde Supabase):');
  console.log('   Usa nombres como: juan, carlos, maria');
  console.log('   O IDs completos: 5f4fb378-908d-4b49-83ce-be4ce3b50c5d');
}

// Generate unique conversation ID
function generateConversationId() {
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.random().toString(36).substring(2, 8);
  return `CHaaa${timestamp}${random}`.padEnd(32, 'a');
}

// 🔍 VALIDATION: TypeScript contract validation before creating conversation
async function validateConversationContract(conversationData) {
  // Note: We validate against the known TypeScript interface structure
  // since require('./src/types') doesn't work with .ts files in Node.js
  // This validation ensures test data matches the interfaces in src/types.ts
  
  // Check required fields from TypeScript interface
  const requiredFields = ['id', 'customerId', 'customer', 'channel', 'status', 'assignedTo', 'priority', 'createdAt', 'updatedAt', 'metadata'];
  const missingFields = requiredFields.filter(field => !(field in conversationData));
  
  if (missingFields.length > 0) {
    throw new Error(`❌ VALIDATION FAILED: Missing required TypeScript fields: ${missingFields.join(', ')}`);
  }
  
  // Validate customer object structure
  const requiredCustomerFields = ['id', 'name', 'phone', 'channel'];
  const missingCustomerFields = requiredCustomerFields.filter(field => !(field in conversationData.customer));
  
  if (missingCustomerFields.length > 0) {
    throw new Error(`❌ VALIDATION FAILED: Customer object missing fields: ${missingCustomerFields.join(', ')}`);
  }
  
  // Validate metadata structure
  const requiredMetadataFields = ['tags', 'transferCount', 'escalationLevel', 'messageCount'];
  const missingMetadataFields = requiredMetadataFields.filter(field => !(field in conversationData.metadata));
  
  if (missingMetadataFields.length > 0) {
    throw new Error(`❌ VALIDATION FAILED: Metadata missing required fields: ${missingMetadataFields.join(', ')}`);
  }
  
  // Validate priority values (must be 1|2|3|4 according to TypeScript)
  if (![1, 2, 3, 4].includes(conversationData.priority)) {
    throw new Error(`❌ VALIDATION FAILED: Priority must be 1|2|3|4, got: ${conversationData.priority}`);
  }
  
  // Validate status values according to ConversationStatus type
  const validStatuses = ['new', 'waiting', 'pending', 'pending_acceptance', 'assigned', 'active', 'transferring', 'supervised', 'escalated', 'closed', 'archived'];
  if (!validStatuses.includes(conversationData.status)) {
    throw new Error(`❌ VALIDATION FAILED: Invalid status '${conversationData.status}'. Must be one of: ${validStatuses.join(', ')}`);
  }
  
  // Validate assignedTo values
  if (!['bot', 'human'].includes(conversationData.assignedTo)) {
    throw new Error(`❌ VALIDATION FAILED: assignedTo must be 'bot' or 'human', got: ${conversationData.assignedTo}`);
  }
  
  console.log('✅ TypeScript contract validation passed');
  return true;
}

// Create conversation structure compatible with TypeScript
async function createConversation(agentId, customer, topic, index = 0) {
  const now = Date.now();
  const conversationId = generateConversationId();
  
  const conversation = {
    id: conversationId,
    customerId: customer.id,
    customer: {
      id: customer.id,
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      channel: 'whatsapp'
    },
    assignedTo: 'human',
    assignedAgentId: agentId,
    status: 'active',
    priority: 2, // TypeScript requires 1|2|3|4, not string
    channel: 'whatsapp',
    source: 'manual',
    departmentId: 'technical_support',
    
    // Timestamps as numbers (Firebase timestamp format)
    createdAt: now - (index * 60000),
    updatedAt: now - (index * 30000),
    assignedAt: now - (index * 60000),
    
    metadata: {
      subject: topic,
      tags: ['created-by-script'],
      notes: [], // Required InternalNote[] array
      transferCount: 0, // Required number
      escalationLevel: 0, // Required number
      messageCount: 0, // Will be updated after messages
      
      // Analytics fields
      firstResponseTime: null,
      avgResponseTime: null,
      
      // Customer info (for compatibility)
      customerName: customer.name,
      customerPhone: customer.phone,
      customerEmail: customer.email,
      
      // Bot tracking
      botAttempts: 0,
      assignmentHistory: [{
        agentId: agentId,
        assignedAt: now - (index * 60000),
        reason: 'Initial assignment by script'
      }]
    },
    
    routingInfo: {
      assignedDepartment: 'technical_support',
      aiAnalysisAttempts: 1,
      aiAnalysisHistory: [{
        attempt: 1,
        input: topic,
        result: 'technical_support',
        timestamp: now - (index * 60000)
      }],
      departmentAssignedAt: now - (index * 60000)
    }
  };
  
  // 🔍 VALIDATE against TypeScript contracts before saving
  try {
    await validateConversationContract(conversation);
  } catch (error) {
    console.error('❌ CRITICAL: Script data does not match TypeScript contracts!');
    console.error(error.message);
    console.error('');
    console.error('💡 This indicates the test script is out of sync with the codebase.');
    console.error('   Update the script to match the current TypeScript interfaces in src/types.ts');
    throw error;
  }
  
  // Save to Firebase
  await db.ref(`conversations/${conversationId}`).set(conversation);
  
  // Create initial messages
  const lastMessage = await createInitialMessages(conversationId, customer, topic, agentId, index);
  
  // Update conversation with lastMessage information
  await db.ref(`conversations/${conversationId}`).update({
    lastMessage: lastMessage,
    'metadata/messageCount': 4 // Customer message + welcome + assignment + follow-up
  });
  
  // Return updated conversation
  conversation.lastMessage = lastMessage;
  conversation.metadata.messageCount = 4;
  
  return conversation;
}

// 🔍 VALIDATION: Message contract validation
function validateMessageContract(messageData) {
  // Check required fields from TypeScript Message interface  
  const requiredFields = ['id', 'conversationId', 'senderId', 'senderType', 'content', 'type', 'timestamp', 'status'];
  const missingFields = requiredFields.filter(field => !(field in messageData));
  
  if (missingFields.length > 0) {
    throw new Error(`❌ MESSAGE VALIDATION FAILED: Missing required fields: ${missingFields.join(', ')}`);
  }
  
  // Validate senderType values according to TypeScript
  const validSenderTypes = ['customer', 'agent', 'system', 'bot', 'supervisor'];
  if (!validSenderTypes.includes(messageData.senderType)) {
    throw new Error(`❌ MESSAGE VALIDATION FAILED: Invalid senderType '${messageData.senderType}'. Must be one of: ${validSenderTypes.join(', ')}`);
  }
  
  // Validate message type values
  const validMessageTypes = ['text', 'image', 'file', 'system', 'note'];
  if (!validMessageTypes.includes(messageData.type)) {
    throw new Error(`❌ MESSAGE VALIDATION FAILED: Invalid type '${messageData.type}'. Must be one of: ${validMessageTypes.join(', ')}`);
  }
  
  // Validate status values according to MessageStatus type
  const validStatuses = ['sent', 'delivered', 'read', 'failed'];
  if (!validStatuses.includes(messageData.status)) {
    throw new Error(`❌ MESSAGE VALIDATION FAILED: Invalid status '${messageData.status}'. Must be one of: ${validStatuses.join(', ')}`);
  }
  
  // Validate timestamp format (should be ISO string)
  if (typeof messageData.timestamp !== 'string' || !messageData.timestamp.includes('T')) {
    throw new Error(`❌ MESSAGE VALIDATION FAILED: timestamp must be ISO string format, got: ${messageData.timestamp}`);
  }
  
  return true;
}

// Create initial messages for conversation
async function createInitialMessages(conversationId, customer, topic, agentId, index) {
  const now = Date.now();
  
  const messages = [
    {
      id: `msg_${conversationId}_001`,
      conversationId: conversationId,
      senderId: customer.id,
      senderType: 'customer',
      content: topic,
      type: 'text',
      timestamp: new Date(now - (index * 60000) + 10000).toISOString(),
      status: 'sent', // Required MessageStatus field
      readBy: {} // Required field for read tracking
    },
    {
      id: `msg_${conversationId}_002`,
      conversationId: conversationId,
      senderId: 'system',
      senderType: 'system',
      content: 'Hola! Un agente estará contigo pronto para ayudarte.',
      type: 'system',
      timestamp: new Date(now - (index * 60000) + 20000).toISOString(),
      status: 'sent',
      readBy: {},
      systemData: {
        action: 'welcome',
        details: {}
      }
    },
    {
      id: `msg_${conversationId}_003`,
      conversationId: conversationId,
      senderId: 'system',
      senderType: 'system',
      content: 'Un agente se ha unido a la conversación para ayudarte.',
      type: 'system',
      timestamp: new Date(now - (index * 60000) + 30000).toISOString(),
      status: 'sent',
      readBy: {},
      systemData: {
        action: 'agent_assigned',
        details: { agentId: agentId }
      }
    },
    {
      id: `msg_${conversationId}_004`,
      conversationId: conversationId,
      senderId: customer.id,
      senderType: 'customer',
      content: '¿Podrían ayudarme, por favor?',
      type: 'text',
      timestamp: new Date(now - (index * 60000) + 40000).toISOString(),
      status: 'sent',
      readBy: {}
    }
  ];
  
  // 🔍 VALIDATE all messages against TypeScript contracts
  messages.forEach((message, index) => {
    try {
      validateMessageContract(message);
    } catch (error) {
      console.error(`❌ CRITICAL: Message ${index + 1} validation failed!`);
      console.error(error.message);
      console.error('');
      console.error('💡 This indicates the test script message structure is out of sync with src/types.ts');
      throw error;
    }
  });
  
  const messagesRef = db.ref(`conversations/${conversationId}/messages`);
  for (const message of messages) {
    await messagesRef.child(message.id).set(message);
  }
  
  // Return the last message (customer follow-up) for conversation.lastMessage
  return {
    id: messages[3].id, // Customer follow-up message
    content: messages[3].content,
    timestamp: messages[3].timestamp,
    senderType: 'customer',
    type: 'text'
  };
}

// 🆕 CREATE CONVERSATIONS
async function createConversationsForAgent(agentKey, count) {
  console.log(`🚀 Creando ${count} conversaciones para agente: ${agentKey}`);
  
  try {
    const agent = await findAgent(agentKey);
    if (!agent) {
      console.error(`❌ Agente '${agentKey}' no encontrado en Supabase`);
      console.error('   Usa --list-agents para ver agentes disponibles');
      return;
    }
  
  const conversations = [];
  
  for (let i = 0; i < count; i++) {
    const customer = CUSTOMER_POOL[i % CUSTOMER_POOL.length];
    const topic = CONVERSATION_TOPICS[i % CONVERSATION_TOPICS.length];
    
    const conversation = await createConversation(agent.id, customer, topic, i);
    conversations.push(conversation);
    
    console.log(`  ✅ ${conversation.id}: ${customer.name} - "${topic}"`);
  }
  
    console.log(`\n🎉 ${conversations.length} conversaciones creadas exitosamente para ${agent.name}`);
    return conversations;
    
  } catch (error) {
    console.error(`❌ Error creando conversaciones: No se puede continuar sin conexión a Supabase`);
    return [];
  }
}

// 🔄 TRANSFER CONVERSATION
async function transferConversation(conversationId, targetAgentKey, reason = 'Transfer via script') {
  console.log(`🔄 Transfiriendo conversación ${conversationId} a ${targetAgentKey}...`);
  
  try {
    const targetAgent = await findAgent(targetAgentKey);
    if (!targetAgent) {
      console.error(`❌ Agente destino '${targetAgentKey}' no encontrado en Supabase`);
      console.error('   Usa --list-agents para ver agentes disponibles');
      return;
    }
  
  try {
    const response = await axios.post(
      `${CHAT_REALTIME_URL}/api/conversations/${conversationId}/transfer`,
      {
        targetAgentId: targetAgent.id,
        reason: reason
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      }
    );
    
    if (response.data.success) {
      console.log(`✅ Transferencia exitosa: ${conversationId} → ${targetAgent.name}`);
      console.log(`   Razón: ${reason}`);
    } else {
      console.error(`❌ Error en transferencia: ${response.data.error?.message || 'Error desconocido'}`);
    }
    } catch (error) {
      if (error.code === 'ECONNREFUSED') {
        console.error(`❌ No se pudo conectar a Chat Realtime (${CHAT_REALTIME_URL})`);
        console.error('   Asegúrate de que el servicio esté ejecutándose: cd services/chat-realtime && npm run dev');
      } else {
        console.error(`❌ Error de transferencia: ${error.response?.data?.error?.message || error.message}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ Error de transferencia: No se puede continuar sin conexión a Supabase`);
  }
}

// ✅ ACCEPT TRANSFER
async function acceptTransfer(conversationId, agentKey) {
  console.log(`✅ Aceptando transfer de conversación ${conversationId} como agente: ${agentKey}...`);
  
  try {
    const agent = await findAgent(agentKey);
    if (!agent) {
      console.error(`❌ Agente '${agentKey}' no encontrado en Supabase`);
      console.error('   Usa --list-agents para ver agentes disponibles');
      return;
    }

    const response = await axios.post(
      `${CHAT_REALTIME_URL}/api/conversations/${conversationId}/accept-transfer`,
      {
        agentId: agent.id
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      }
    );
    
    if (response.data.success) {
      console.log(`✅ Transfer aceptado exitosamente por ${agent.name}`);
      console.log(`   Conversación ${conversationId} ahora está activa`);
    } else {
      console.error(`❌ Error aceptando transfer: ${response.data.error?.message || 'Error desconocido'}`);
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error(`❌ No se pudo conectar a Chat Realtime (${CHAT_REALTIME_URL})`);
      console.error('   Asegúrate de que el servicio esté ejecutándose: cd services/chat-realtime && npm run dev');
    } else {
      console.error(`❌ Error aceptando transfer: ${error.response?.data?.error?.message || error.message}`);
    }
  }
}

// ✅ CLOSE CONVERSATION
async function closeConversation(conversationId, reason = 'Closed via script') {
  console.log(`✅ Cerrando conversación ${conversationId}...`);
  
  try {
    const response = await axios.post(
      `${CHAT_REALTIME_URL}/api/conversations/${conversationId}/close`,
      {
        reason: reason
      },
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 10000
      }
    );
    
    if (response.data.success) {
      console.log(`✅ Conversación cerrada exitosamente: ${conversationId}`);
      console.log(`   Razón: ${reason}`);
    } else {
      console.error(`❌ Error cerrando conversación: ${response.data.error?.message || 'Error desconocido'}`);
    }
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error(`❌ No se pudo conectar a Chat Realtime (${CHAT_REALTIME_URL})`);
      console.error('   Asegúrate de que el servicio esté ejecutándose: cd services/chat-realtime && npm run dev');
    } else {
      console.error(`❌ Error cerrando conversación: ${error.response?.data?.error?.message || error.message}`);
    }
  }
}

// 🗑️ DELETE CONVERSATIONS BY AGENT
async function deleteConversationsByAgent(agentKey) {
  console.log(`🗑️  Eliminando conversaciones del agente: ${agentKey}...`);
  
  try {
    const agent = await findAgent(agentKey);
    if (!agent) {
      console.error(`❌ Agente '${agentKey}' no encontrado en Supabase`);
      console.error('   Usa --list-agents para ver agentes disponibles');
      return;
    }
  
  try {
    // Get all conversations
    const snapshot = await db.ref('conversations').once('value');
    const conversations = snapshot.val() || {};
    
    const agentConversations = Object.values(conversations).filter(conv => 
      conv.agentId === agent.id
    );
    
    if (agentConversations.length === 0) {
      console.log(`ℹ️  No se encontraron conversaciones para el agente ${agent.name}`);
      return;
    }
    
    console.log(`🔍 Encontradas ${agentConversations.length} conversaciones para eliminar...`);
    
    // Delete each conversation and clean related structures
    for (const conv of agentConversations) {
      await deleteConversationComplete(conv.id, conv.metadata?.customerName || 'Sin nombre');
    }
    
    console.log(`✅ ${agentConversations.length} conversaciones eliminadas para ${agent.name}`);
    
    } catch (error) {
      console.error(`❌ Error eliminando conversaciones: ${error.message}`);
    }
    
  } catch (error) {
    console.error(`❌ Error eliminando conversaciones: No se puede continuar sin conexión a Supabase`);
  }
}

// 🗑️ DELETE SPECIFIC CONVERSATION
async function deleteConversation(conversationId) {
  console.log(`🗑️  Eliminando conversación: ${conversationId}...`);
  
  try {
    const snapshot = await db.ref(`conversations/${conversationId}`).once('value');
    if (!snapshot.exists()) {
      console.error(`❌ Conversación ${conversationId} no encontrada`);
      return;
    }
    
    const conversation = snapshot.val();
    const customerName = conversation.metadata?.customerName || conversation.customer?.name || 'Sin nombre';
    
    await deleteConversationComplete(conversationId, customerName);
  } catch (error) {
    console.error(`❌ Error eliminando conversación: ${error.message}`);
  }
}

// 🗑️ DELETE ALL CONVERSATIONS (DANGEROUS)
async function deleteAllConversations() {
  console.log(`🗑️  ELIMINANDO TODAS LAS CONVERSACIONES...`);
  console.log(`⚠️  ESTO ELIMINARÁ TODO EL CONTENIDO DE FIREBASE`);
  
  try {
    // Get all conversations first to see what we're deleting
    const snapshot = await db.ref('conversations').once('value');
    const conversations = snapshot.val() || {};
    const conversationList = Object.values(conversations);
    
    if (conversationList.length === 0) {
      console.log(`ℹ️  No se encontraron conversaciones para eliminar`);
      return;
    }
    
    console.log(`🔍 Encontradas ${conversationList.length} conversaciones para eliminar...`);
    
    // Delete each conversation completely
    for (const conv of conversationList) {
      // Skip conversations with invalid IDs
      if (!conv.id || conv.id === 'undefined') {
        console.log(`  ⚠️  Skipping conversation with invalid ID: ${conv.id}`);
        continue;
      }
      
      const customerName = conv.metadata?.customerName || conv.customer?.name || 'Sin nombre';
      await deleteConversationComplete(conv.id, customerName);
    }
    
    // Clean up conversations with invalid IDs (like undefined)
    console.log(`🧹 Limpiando conversaciones con IDs inválidos...`);
    const invalidKeys = ['undefined', 'null', ''];
    for (const key of invalidKeys) {
      await db.ref(`conversations/${key}`).remove();
    }
    
    // Also clean up any orphaned queue entries
    console.log(`🧹 Limpiando colas de departamentos...`);
    await db.ref('queues').remove();
    console.log(`✅ Colas limpiadas`);
    
    // Clean up supervisor queue
    console.log(`🧹 Limpiando cola de supervisores...`);
    await db.ref('supervisorQueue').remove();
    console.log(`✅ Cola de supervisores limpiada`);
    
    console.log(`✅ ${conversationList.length} conversaciones eliminadas completamente`);
    
  } catch (error) {
    console.error(`❌ Error eliminando todas las conversaciones: ${error.message}`);
  }
}

// 🧹 COMPLETE CONVERSATION DELETION (helper function)
async function deleteConversationComplete(conversationId, customerName = 'Sin nombre') {
  try {
    // 1. Get conversation details to know which queues to clean
    const snapshot = await db.ref(`conversations/${conversationId}`).once('value');
    const conversation = snapshot.exists() ? snapshot.val() : null;
    
    // 2. Remove from main conversations table
    await db.ref(`conversations/${conversationId}`).remove();
    
    // 3. Remove from department queues (search all departments)
    if (conversation?.departmentId) {
      await db.ref(`queues/${conversation.departmentId}/conversations/${conversationId}`).remove();
    }
    
    // 4. Remove from all possible queues (in case departmentId is missing or changed)
    const queuesSnapshot = await db.ref('queues').once('value');
    const queues = queuesSnapshot.val() || {};
    
    for (const departmentId of Object.keys(queues)) {
      const queueConvPath = `queues/${departmentId}/conversations/${conversationId}`;
      const queueConvSnapshot = await db.ref(queueConvPath).once('value');
      if (queueConvSnapshot.exists()) {
        await db.ref(queueConvPath).remove();
        console.log(`  🧹 Removed from ${departmentId} queue`);
      }
    }
    
    // 5. Remove from supervisor queue if present
    const supervisorQueuePath = `supervisorQueue/${conversationId}`;
    const supervisorQueueSnapshot = await db.ref(supervisorQueuePath).once('value');
    if (supervisorQueueSnapshot.exists()) {
      await db.ref(supervisorQueuePath).remove();
      console.log(`  🧹 Removed from supervisor queue`);
    }
    
    console.log(`  🗑️  ${conversationId}: ${customerName}`);
  } catch (error) {
    console.error(`❌ Error in complete deletion of ${conversationId}: ${error.message}`);
  }
}

// 📊 LIST AGENTS
async function listAgents() {
  console.log('🔑 AGENTES DISPONIBLES (desde Supabase):');
  console.log('');
  
  try {
    const agents = await loadAgentsFromSupabase();
    
    if (!agents.list || agents.list.length === 0) {
      console.log('❌ No se encontraron agentes en Supabase');
      return;
    }
    
    console.log(`📊 ${agents.list.length} agentes activos encontrados:\n`);
    
    agents.list.forEach(agent => {
      const firstName = agent.name.split(' ')[0].toLowerCase();
      console.log(`  ${firstName.padEnd(12)} ${agent.name}`);
      console.log(`  ${' '.repeat(12)} ${agent.email}`);
      console.log(`  ${' '.repeat(12)} ${agent.id}`);
      console.log('');
    });
    
  } catch (error) {
    console.error(`❌ Error listando agentes: No se puede continuar sin conexión a Supabase`);
  }
}

// 📊 LIST CONVERSATIONS
async function listConversations(agentKey = null) {
  console.log(`📋 LISTANDO CONVERSACIONES${agentKey ? ` PARA AGENTE: ${agentKey}` : ''}...`);
  
  try {
    const snapshot = await db.ref('conversations').once('value');
    const conversations = snapshot.val() || {};
    
    let filteredConversations = Object.values(conversations);
    
    if (agentKey) {
      try {
        const agent = await findAgent(agentKey);
        if (!agent) {
          console.error(`❌ Agente '${agentKey}' no encontrado en Supabase`);
          console.error('   Usa --list-agents para ver agentes disponibles');
          return;
        }
        filteredConversations = filteredConversations.filter(conv => conv.agentId === agent.id);
      } catch (error) {
        console.error(`❌ Error buscando agente: No se puede continuar sin conexión a Supabase`);
        return;
      }
    }
    
    if (filteredConversations.length === 0) {
      console.log('ℹ️  No se encontraron conversaciones');
      return;
    }
    
    console.log(`\n📊 ${filteredConversations.length} conversaciones encontradas:\n`);
    
    // Load agents for name lookup (or use fallback display)
    let agents = null;
    try {
      agents = await loadAgentsFromSupabase();
    } catch (error) {
      console.log('⚠️  No se pudo cargar información de agentes desde Supabase para mostrar nombres');
    }
    
    filteredConversations.forEach(conv => {
      const agent = agents?.byId[conv.agentId];
      const agentName = agent ? agent.name.split(' ')[0] : 'unknown';
      
      console.log(`  ${conv.id}`);
      console.log(`    👤 Cliente: ${conv.metadata?.customerName || 'Sin nombre'}`);
      console.log(`    👨‍💼 Agente: ${agentName} (${conv.agentId})`);
      console.log(`    📊 Estado: ${conv.status}`);
      
      // Show transfer status if exists
      if (conv.transferInfo) {
        if (conv.transferInfo.currentTransfer) {
          const target = conv.transferInfo.currentTransfer.targetAgentId || 'N/A';
          const status = conv.transferInfo.currentTransfer.status || 'N/A';
          console.log(`    🔄 Transfer: ${status} → ${target}`);
        }
        if (conv.transferInfo.transferHistory && conv.transferInfo.transferHistory.length > 0) {
          console.log(`    📋 Historial: ${conv.transferInfo.transferHistory.length} transfers`);
        }
      }
      
      console.log(`    🏷️  Tema: ${conv.metadata?.subject || 'Sin tema'}`);
      console.log(`    📅 Creado: ${conv.createdAt}`);
      console.log('');
    });
    
  } catch (error) {
    console.error(`❌ Error listando conversaciones: ${error.message}`);
  }
}

// 🔄 CREATE DEFAULT CONVERSATIONS (compatibility mode)
async function createDefaultConversations() {
  console.log('🔄 Creando conversaciones por defecto (modo compatibilidad)...');
  
  try {
    const agents = await loadAgentsFromSupabase();
    
    if (!agents.list || agents.list.length === 0) {
      console.error('❌ No se encontraron agentes en Supabase para crear conversaciones por defecto');
      return [];
    }
  
  const conversations = [];
  
  for (let i = 0; i < 4; i++) {
    const agent = agents.list[i % agents.list.length];
    const customer = CUSTOMER_POOL[i];
    const topic = CONVERSATION_TOPICS[i];
    
    const conversation = await createConversation(agent.id, customer, topic, i);
    conversations.push(conversation);
    
    const firstName = agent.name.split(' ')[0];
    console.log(`  ✅ ${conversation.id}: ${customer.name} → ${firstName}`);
  }
  
    console.log(`\n🎉 ${conversations.length} conversaciones por defecto creadas`);
    return conversations;
    
  } catch (error) {
    console.error(`❌ Error creando conversaciones por defecto: No se puede continuar sin conexión a Supabase`);
    return [];
  }
}

// Main execution
async function main() {
  if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
    showHelp();
    return;
  }
  
  try {
    // Parse commands
    if (args[0] === '--create' && args[1] && args[2]) {
      await createConversationsForAgent(args[1], parseInt(args[2]));
      
    } else if (args[0] === '--create-default') {
      await createDefaultConversations();
      
    } else if (args[0] === '--transfer' && args[1] && args[2]) {
      const reason = args[3] || 'Transfer via script';
      await transferConversation(args[1], args[2], reason);
      
    } else if (args[0] === '--accept-transfer' && args[1] && args[2]) {
      await acceptTransfer(args[1], args[2]);
      
    } else if (args[0] === '--close' && args[1]) {
      const reason = args[2] || 'Closed via script';
      await closeConversation(args[1], reason);
      
    } else if (args[0] === '--delete-agent' && args[1]) {
      await deleteConversationsByAgent(args[1]);
      
    } else if (args[0] === '--delete-conversation' && args[1]) {
      await deleteConversation(args[1]);
      
    } else if (args[0] === '--delete-all') {
      await deleteAllConversations();
      
    } else if (args[0] === '--delete-all-agents') {
      await deleteAllAgents();
      
    } else if (args[0] === '--recreate-agents') {
      await recreateAgentsFromSupabase();
      
    } else if (args[0] === '--list-agents') {
      await listAgents();
      
    } else if (args[0] === '--list-conversations') {
      await listConversations(args[1]);
      
    } else {
      console.error('❌ Comando no reconocido. Use --help para ver los comandos disponibles.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error(`❌ Error ejecutando comando: ${error.message}`);
    process.exit(1);
  }
}

// Run script
if (require.main === module) {
  main()
    .then(() => {
      console.log('\n✨ Comando completado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Error fatal:', error.message);
      process.exit(1);
    });
}

// Delete all agents from Firebase
async function deleteAllAgents() {
  try {
    console.log('🗑️ Eliminando todos los agentes de Firebase...');
    
    // Get all agents first to count them (check both possible paths)
    const agentsSnapshot = await db.ref('agents').once('value');
    const agentStatusSnapshot = await db.ref('agent_status').once('value');
    
    const agents = agentsSnapshot.val();
    const agentStatus = agentStatusSnapshot.val();
    
    const agentCount = agents ? Object.keys(agents).length : 0;
    const statusCount = agentStatus ? Object.keys(agentStatus).length : 0;
    
    if (agentCount === 0 && statusCount === 0) {
      console.log('ℹ️ No hay agentes que eliminar en Firebase');
      return;
    }
    
    // Delete from both paths to be thorough
    if (agentCount > 0) {
      await db.ref('agents').remove();
      console.log(`✅ ${agentCount} agentes eliminados de /agents/`);
    }
    
    if (statusCount > 0) {
      await db.ref('agent_status').remove();
      console.log(`✅ ${statusCount} agentes eliminados de /agent_status/`);
    }
    
    console.log(`✅ Total: ${agentCount + statusCount} agentes eliminados de Firebase`);
    
  } catch (error) {
    console.error('❌ Error eliminando agentes de Firebase:', error);
    throw error;
  }
}

// Recreate agents in Firebase from Supabase data
async function recreateAgentsFromSupabase() {
  try {
    console.log('🔄 Recreando agentes en Firebase desde Supabase...');
    
    // First delete all existing agents
    await deleteAllAgents();
    
    // Get agents from Supabase
    const agents = await loadAgentsFromSupabase();
    
    if (!agents.list || agents.list.length === 0) {
      console.error('❌ No se encontraron agentes en Supabase para recrear');
      return;
    }
    
    const now = Date.now();
    let recreatedCount = 0;
    
    // Create agent entries in Firebase
    for (const agent of agents.list) {
      const firebaseAgent = {
        id: agent.id,
        name: agent.name,
        email: agent.email,
        isActive: agent.is_active,
        // Add Firebase-specific fields
        status: 'offline',
        currentConversations: [],
        maxConcurrentChats: 5,
        isAvailable: false,
        lastSeen: now,
        createdAt: now,
        updatedAt: now
      };
      
      // Save to both locations for compatibility
      await db.ref(`agents/${agent.id}`).set(firebaseAgent);
      await db.ref(`agent_status/${agent.id}`).set({
        id: agent.id,
        status: 'offline',
        currentConversations: [],
        maxConcurrentChats: 5,
        isAvailable: false,
        lastSeen: now
      });
      
      recreatedCount++;
      console.log(`  ✅ ${agent.name} (${agent.id})`);
    }
    
    console.log(`✅ ${recreatedCount} agentes recreados en Firebase desde Supabase`);
    
  } catch (error) {
    console.error('❌ Error recreando agentes desde Supabase:', error);
    throw error;
  }
}

module.exports = {
  createConversationsForAgent,
  transferConversation,
  closeConversation,
  deleteConversationsByAgent,
  deleteConversation,
  deleteAllConversations,
  deleteConversationComplete,
  listConversations,
  createDefaultConversations,
  deleteAllAgents,
  recreateAgentsFromSupabase
};