// Standalone Authentication Test for Supabase Connection
import { testAuthService, TEST_USERS } from './auth';

async function testAuthentication() {
  console.log('🧪 Starting Authentication Test...\n');

  try {
    // Test 1: Show available agents
    console.log('1️⃣ Testing database connection and available agents...');
    const agents = await testAuthService.getAvailableAgents();
    console.log(`✅ Found ${agents.length} active agents:`);
    agents.forEach(agent => {
      console.log(`   - ${agent.name} (${agent.email}) - Role: ${agent.role} - Departments: [${agent.departments.join(', ')}]`);
    });
    console.log();

    // Test 2: Agent authentication
    console.log('2️⃣ Testing Agent authentication...');
    try {
      const agentAuth = await testAuthService.login(
        TEST_USERS.AGENT.email,
        TEST_USERS.AGENT.password
      );
      console.log(`✅ Agent login successful: ${agentAuth.profile.name}`);
      console.log(`   User ID: ${agentAuth.user.id}`);
      console.log(`   Role: ${agentAuth.profile.role}`);
      console.log(`   Token length: ${agentAuth.accessToken.length} chars`);
      console.log(`   Token expires: ${agentAuth.expiresAt ? new Date(agentAuth.expiresAt * 1000).toISOString() : 'No expiry'}`);
      
      // Verify token
      const isValid = await testAuthService.verifyToken(agentAuth.accessToken);
      console.log(`   Token verification: ${isValid ? '✅ Valid' : '❌ Invalid'}`);
      
      await testAuthService.logout();
      console.log('   🚪 Agent logout successful\n');
    } catch (error: any) {
      console.log(`❌ Agent authentication failed: ${error.message}\n`);
    }

    // Test 3: Supervisor authentication
    console.log('3️⃣ Testing Supervisor authentication...');
    try {
      const supervisorAuth = await testAuthService.login(
        TEST_USERS.SUPERVISOR.email,
        TEST_USERS.SUPERVISOR.password
      );
      console.log(`✅ Supervisor login successful: ${supervisorAuth.profile.name}`);
      console.log(`   User ID: ${supervisorAuth.user.id}`);
      console.log(`   Role: ${supervisorAuth.profile.role}`);
      console.log(`   Departments: [${supervisorAuth.profile.departments.join(', ')}]`);
      
      await testAuthService.logout();
      console.log('   🚪 Supervisor logout successful\n');
    } catch (error: any) {
      console.log(`❌ Supervisor authentication failed: ${error.message}\n`);
    }

    // Test 4: Invalid credentials
    console.log('4️⃣ Testing invalid credentials (should fail)...');
    try {
      await testAuthService.login('<EMAIL>', 'wrongpassword');
      console.log('❌ ERROR: Invalid credentials should have failed!');
    } catch (error: any) {
      console.log(`✅ Invalid credentials properly rejected: ${error.message}\n`);
    }

    console.log('🎉 Authentication testing completed successfully!');

  } catch (error: any) {
    console.error('💥 Authentication testing failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  testAuthentication()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('💥 Test failed:', error);
      process.exit(1);
    });
}

export default testAuthentication;