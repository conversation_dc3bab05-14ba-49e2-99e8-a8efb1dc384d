// Authentication Service for E2E Testing with Real Supabase Auth
import { createClient, SupabaseClient, User } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from project root
dotenv.config({ path: path.join(__dirname, '../../../.env') });

export interface AgentProfile {
  id: string;
  name: string;
  email: string;
  role: 'agent' | 'supervisor' | 'admin';
  departments: string[];
  status: 'active' | 'inactive';
  organizationId: string;
}

export interface AuthenticatedUser {
  user: User;
  profile: AgentProfile;
  accessToken: string;
  expiresAt?: number;
}

class TestAuthService {
  private supabase: SupabaseClient;

  constructor() {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Missing Supabase credentials. Make sure SUPABASE_URL and SUPABASE_ANON_KEY are set in .env');
    }

    this.supabase = createClient(supabaseUrl, supabaseAnonKey);
    console.log('🔐 Test Auth Service initialized with Supabase');
  }

  // Login with email and password, return real Supabase JWT token
  async login(email: string, password: string): Promise<AuthenticatedUser> {
    try {
      console.log(`🔑 Attempting login for: ${email}`);
      
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error('❌ Login failed:', error.message);
        throw new Error(`Login failed: ${error.message}`);
      }

      if (!data.user || !data.session) {
        throw new Error('Login successful but no user/session data received');
      }

      console.log('✅ Supabase authentication successful');
      
      // Get agent profile from Supabase
      const profile = await this.getAgentProfile(data.user.id);
      
      if (!profile) {
        throw new Error(`Agent profile not found for user: ${data.user.email}`);
      }

      console.log(`✅ Agent profile loaded: ${profile.name} (${profile.role})`);

      return {
        user: data.user,
        profile,
        accessToken: data.session.access_token,
        expiresAt: data.session.expires_at
      };
    } catch (error) {
      console.error('❌ Authentication error:', error);
      throw error;
    }
  }

  // Get agent profile from Supabase database
  private async getAgentProfile(userId: string): Promise<AgentProfile | null> {
    try {
      console.log(`📋 Fetching agent profile for user: ${userId}`);
      
      const { data, error } = await this.supabase
        .from('agents')
        .select(`
          id,
          name,
          email,
          role,
          status,
          organization_id
        `)
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Agent profile query error:', error);
        return null;
      }

      if (!data) {
        console.error('❌ No agent profile found');
        return null;
      }

      return {
        id: data.id,
        name: data.name,
        email: data.email,
        role: data.role,
        departments: [], // TODO: Add departments support when schema is ready
        status: data.status,
        organizationId: data.organization_id
      };
    } catch (error) {
      console.error('❌ Error fetching agent profile:', error);
      return null;
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      const { error } = await this.supabase.auth.signOut();
      if (error) {
        throw error;
      }
      console.log('🚪 Successfully logged out');
    } catch (error) {
      console.error('❌ Logout error:', error);
      throw error;
    }
  }

  // Verify token by making a test request
  async verifyToken(token: string): Promise<boolean> {
    try {
      const { data: { user }, error } = await this.supabase.auth.getUser(token);
      return !error && !!user;
    } catch {
      return false;
    }
  }

  // Get available test agents from database
  async getAvailableAgents(): Promise<AgentProfile[]> {
    try {
      console.log('🔍 Fetching available test agents...');
      
      const { data, error } = await this.supabase
        .from('agents')
        .select(`
          id,
          name,
          email,
          role,
          status,
          organization_id
        `)
        .eq('status', 'active')
        .order('role', { ascending: true });

      if (error) {
        console.error('❌ Error fetching agents:', error);
        throw error;
      }

      const agents = (data || []).map(agent => ({
        id: agent.id,
        name: agent.name,
        email: agent.email,
        role: agent.role,
        departments: [], // TODO: Add departments support when schema is ready
        status: agent.status,
        organizationId: agent.organization_id
      }));

      console.log(`✅ Found ${agents.length} active agents`);
      return agents;
    } catch (error) {
      console.error('❌ Error fetching available agents:', error);
      throw error;
    }
  }
}

export const testAuthService = new TestAuthService();

// Helper function to create Authorization header
export const createAuthHeader = (token: string): { Authorization: string } => ({
  Authorization: `Bearer ${token}`
});

// Predefined test users (these should exist in your Supabase database)
export const TEST_USERS = {
  AGENT: {
    email: '<EMAIL>',
    password: 'test123456',
    expectedRole: 'agent' as const
  },
  SUPERVISOR: {
    email: '<EMAIL>', 
    password: 'test123456',
    expectedRole: 'supervisor' as const
  },
  ADMIN: {
    email: '<EMAIL>',
    password: 'test123456', 
    expectedRole: 'admin' as const
  }
};