{"name": "chat-realtime-e2e-testing", "version": "1.0.0", "description": "E2E Testing Scripts for Chat Realtime Service with Real Supabase Auth", "main": "e2e-orchestrator.ts", "scripts": {"build": "tsc", "test": "npm run build && node dist/e2e-orchestrator.js", "test:agent": "npm run build && node dist/agent-journey.js", "test:supervisor": "npm run build && node dist/supervisor-journey.js", "test:auth": "npm run build && node dist/auth-test.js"}, "dependencies": {"@supabase/supabase-js": "^2.55.0", "axios": "^1.7.0", "dotenv": "^16.0.0"}, "devDependencies": {"@types/node": "^18.0.0", "typescript": "^5.0.0"}}