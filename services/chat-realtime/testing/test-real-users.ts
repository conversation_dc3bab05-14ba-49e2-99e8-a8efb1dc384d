#!/usr/bin/env ts-node

/**
 * Script para probar la autenticación con usuarios reales de Supabase
 * Verifica que los usuarios puedan hacer login y obtener tokens JWT válidos
 */

import { testAuthService } from './auth';

async function testRealUsers() {
  console.log('🔐 Testing Real User Authentication\n');

  // Usuarios reales encontrados en Supabase
  const testUsers = [
    {
      email: '<EMAIL>',
      name: 'Administrator',
      role: 'admin',
      password: 'admin123' // Asumiendo contraseña por defecto
    },
    {
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'supervisor', 
      password: 'supervisor123'
    },
    {
      email: '<EMAIL>',
      name: '<PERSON>',
      role: 'agent',
      password: 'agent123'
    }
  ];

  for (const user of testUsers) {
    console.log(`\n📧 Testing user: ${user.email} (${user.role})`);
    
    try {
      // Intentar login
      const authResult = await testAuthService.login(user.email, user.password);
      
      if (authResult.accessToken) {
        console.log(`✅ Login successful`);
        console.log(`   Token: ${authResult.accessToken.substring(0, 50)}...`);
        console.log(`   User ID: ${authResult.user?.id}`);
        console.log(`   Email: ${authResult.user?.email}`);
        console.log(`   Profile: ${authResult.profile?.name} (${authResult.profile?.role})`);
      } else {
        console.log(`❌ Login failed - no token received`);
      }
      
    } catch (error: any) {
      console.log(`❌ Login failed: ${error.message}`);
      
      // Si es error de credenciales, intentar diferentes contraseñas comunes
      if (error.message.includes('Invalid login credentials')) {
        console.log(`   Trying common passwords...`);
        
        const commonPasswords = ['password123', 'cxsystem123', '123456'];
        let loginSuccess = false;
        
        for (const pwd of commonPasswords) {
          try {
            const result = await testAuthService.login(user.email, pwd);
            if (result.accessToken) {
              console.log(`✅ Login successful with password: ${pwd}`);
              console.log(`   Token: ${result.accessToken.substring(0, 50)}...`);
              loginSuccess = true;
              break;
            }
          } catch (e) {
            // Continue to next password
          }
        }
        
        if (!loginSuccess) {
          console.log(`❌ Could not find valid password for ${user.email}`);
        }
      }
    }
  }

  console.log('\n🏁 Real user authentication test completed');
}

// Ejecutar el test
testRealUsers().catch(console.error);