// E2E Testing Orchestrator for Chat Realtime Service
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { testAuthService, createAuthHeader, AuthenticatedUser, TEST_USERS } from './auth';

// Configuration
const CHAT_REALTIME_BASE_URL = process.env.CHAT_REALTIME_URL || 'http://localhost:3003';

interface TestResult {
  testName: string;
  success: boolean;
  duration: number;
  error?: string;
  details?: any;
}

interface TestSuite {
  suiteName: string;
  results: TestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
}

class E2EOrchestrator {
  private client: AxiosInstance;
  private results: TestSuite[] = [];

  constructor() {
    this.client = axios.create({
      baseURL: CHAT_REALTIME_BASE_URL,
      timeout: 30000,
      validateStatus: () => true // Don't throw on HTTP errors, let tests handle them
    });

    console.log(`🧪 E2E Orchestrator initialized`);
    console.log(`🌐 Target service: ${CHAT_REALTIME_BASE_URL}`);
  }

  // Main testing entry point
  async runAllTests(): Promise<void> {
    console.log('🚀 Starting comprehensive E2E testing...\n');

    try {
      // 1. Health checks
      await this.runHealthTests();
      
      // 2. Authentication tests
      await this.runAuthenticationTests();
      
      // 3. Agent journey tests
      await this.runAgentJourneyTests();
      
      // 4. Supervisor journey tests  
      await this.runSupervisorJourneyTests();
      
      // 5. Generate final report
      this.generateFinalReport();
      
    } catch (error) {
      console.error('💥 Critical error during testing:', error);
      process.exit(1);
    }
  }

  // Health and connectivity tests
  private async runHealthTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'Health & Connectivity Tests',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    console.log(`🏥 Running ${suite.suiteName}...`);

    // Test service availability
    await this.runTest(
      suite,
      'Service Availability',
      async () => {
        const response = await this.client.get('/');
        if (response.status !== 200) {
          throw new Error(`Expected 200, got ${response.status}`);
        }
        return { serviceInfo: response.data };
      }
    );

    // Test health endpoint
    await this.runTest(
      suite,
      'Health Check Endpoint',
      async () => {
        const response = await this.client.get('/api/health');
        if (response.status !== 200) {
          throw new Error(`Expected 200, got ${response.status}`);
        }
        if (!response.data.success) {
          throw new Error(`Health check failed: ${JSON.stringify(response.data)}`);
        }
        return { healthData: response.data };
      }
    );

    // Test system status
    await this.runTest(
      suite,
      'System Status Endpoint',
      async () => {
        const response = await this.client.get('/api/status');
        if (response.status !== 200) {
          throw new Error(`Expected 200, got ${response.status}`);
        }
        return { statusData: response.data };
      }
    );

    this.results.push(suite);
    this.printSuiteResults(suite);
  }

  // Authentication flow tests
  private async runAuthenticationTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'Authentication Tests',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    console.log(`🔐 Running ${suite.suiteName}...`);

    // Test agent authentication
    await this.runTest(
      suite,
      'Agent Authentication',
      async () => {
        const authenticatedUser = await testAuthService.login(
          TEST_USERS.AGENT.email,
          TEST_USERS.AGENT.password
        );
        
        if (authenticatedUser.profile.role !== TEST_USERS.AGENT.expectedRole) {
          throw new Error(`Expected role ${TEST_USERS.AGENT.expectedRole}, got ${authenticatedUser.profile.role}`);
        }
        
        return {
          userId: authenticatedUser.user.id,
          email: authenticatedUser.user.email,
          role: authenticatedUser.profile.role,
          tokenLength: authenticatedUser.accessToken.length
        };
      }
    );

    // Test supervisor authentication
    await this.runTest(
      suite,
      'Supervisor Authentication', 
      async () => {
        const authenticatedUser = await testAuthService.login(
          TEST_USERS.SUPERVISOR.email,
          TEST_USERS.SUPERVISOR.password
        );
        
        if (authenticatedUser.profile.role !== TEST_USERS.SUPERVISOR.expectedRole) {
          throw new Error(`Expected role ${TEST_USERS.SUPERVISOR.expectedRole}, got ${authenticatedUser.profile.role}`);
        }
        
        return {
          userId: authenticatedUser.user.id,
          email: authenticatedUser.user.email,
          role: authenticatedUser.profile.role
        };
      }
    );

    // Test invalid credentials
    await this.runTest(
      suite,
      'Invalid Credentials Rejection',
      async () => {
        try {
          await testAuthService.login('<EMAIL>', 'wrongpassword');
          throw new Error('Should have failed with invalid credentials');
        } catch (error: any) {
          if (error.message.includes('Should have failed')) {
            throw error;
          }
          return { expectedError: error.message };
        }
      }
    );

    this.results.push(suite);
    this.printSuiteResults(suite);
  }

  // Agent workflow tests
  private async runAgentJourneyTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'Agent Journey Tests',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    console.log(`👤 Running ${suite.suiteName}...`);

    let agentAuth: AuthenticatedUser;

    // Login as agent
    try {
      agentAuth = await testAuthService.login(
        TEST_USERS.AGENT.email,
        TEST_USERS.AGENT.password
      );

      await this.runTest(
        suite,
        'Agent Login',
        async () => {
          return { agentId: agentAuth.profile.id, role: agentAuth.profile.role };
        }
      );
    } catch (error) {
      await this.runTest(
        suite,
        'Agent Login',
        async () => {
          throw error;
        }
      );
      
      console.log('❌ Cannot continue agent tests without authentication');
      this.results.push(suite);
      return;
    }

    const authHeaders = createAuthHeader(agentAuth.accessToken);

    // Test agent status update (requires auth)
    await this.runTest(
      suite,
      'Update Agent Status (Authenticated)',
      async () => {
        const response = await this.client.put(
          `/api/agents/${agentAuth!.profile.id}/status`,
          {
            availability: 'available',
            status: 'online'
          },
          { headers: authHeaders }
        );
        
        if (response.status !== 200) {
          throw new Error(`Expected 200, got ${response.status}: ${JSON.stringify(response.data)}`);
        }
        
        return { statusUpdate: response.data };
      }
    );

    // Test creating a conversation
    let conversationId: string | null = null;
    await this.runTest(
      suite,
      'Create New Conversation',
      async () => {
        const response = await this.client.post('/api/conversations', {
          customerId: 'test-customer-001',
          channel: 'whatsapp',
          customerData: {
            name: 'Test Customer',
            phone: '+1234567890'
          }
        });
        
        if (response.status !== 201) {
          throw new Error(`Expected 201, got ${response.status}: ${JSON.stringify(response.data)}`);
        }
        
        conversationId = response.data.data.id;
        return { conversationId, status: response.data.data.status };
      }
    );

    // Test sending messages in conversation  
    if (conversationId) {
      await this.runTest(
        suite,
        'Send Message to Conversation',
        async () => {
          const response = await this.client.post(`/api/conversations/${conversationId}/messages`, {
            senderId: agentAuth!.profile.id,
            senderType: 'agent',
            content: 'Hello, how can I help you today?',
            type: 'text'
          });
          
          if (response.status !== 201) {
            throw new Error(`Expected 201, got ${response.status}: ${JSON.stringify(response.data)}`);
          }
          
          return { messageId: response.data.data.id, content: response.data.data.content };
        }
      );

      // Test getting conversation messages
      await this.runTest(
        suite,
        'Retrieve Conversation Messages',
        async () => {
          const response = await this.client.get(`/api/conversations/${conversationId}/messages`);
          
          if (response.status !== 200) {
            throw new Error(`Expected 200, got ${response.status}: ${JSON.stringify(response.data)}`);
          }
          
          return { messageCount: response.data.data.count };
        }
      );
    }

    this.results.push(suite);
    this.printSuiteResults(suite);
  }

  // Supervisor workflow tests
  private async runSupervisorJourneyTests(): Promise<void> {
    const suite: TestSuite = {
      suiteName: 'Supervisor Journey Tests',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    console.log(`👥 Running ${suite.suiteName}...`);

    let supervisorAuth: AuthenticatedUser;

    // Login as supervisor
    try {
      supervisorAuth = await testAuthService.login(
        TEST_USERS.SUPERVISOR.email,
        TEST_USERS.SUPERVISOR.password
      );

      await this.runTest(
        suite,
        'Supervisor Login',
        async () => {
          return { supervisorId: supervisorAuth.profile.id, role: supervisorAuth.profile.role };
        }
      );
    } catch (error) {
      await this.runTest(
        suite,
        'Supervisor Login',
        async () => {
          throw error;
        }
      );
      
      console.log('❌ Cannot continue supervisor tests without authentication');
      this.results.push(suite);
      return;
    }

    // Test getting all agents (supervisor privilege)
    await this.runTest(
      suite,
      'View All Agents',
      async () => {
        const response = await this.client.get('/api/agents');
        
        if (response.status !== 200) {
          throw new Error(`Expected 200, got ${response.status}: ${JSON.stringify(response.data)}`);
        }
        
        return { agentCount: response.data.data.agents?.length || 0 };
      }
    );

    // Test getting all conversations
    await this.runTest(
      suite,
      'View All Conversations',
      async () => {
        const response = await this.client.get('/api/conversations');
        
        if (response.status !== 200) {
          throw new Error(`Expected 200, got ${response.status}: ${JSON.stringify(response.data)}`);
        }
        
        return { conversationCount: response.data.data.conversations?.length || 0 };
      }
    );

    // Test queue management
    await this.runTest(
      suite,
      'View Queue Status',
      async () => {
        const response = await this.client.get('/api/queues');
        
        if (response.status !== 200) {
          throw new Error(`Expected 200, got ${response.status}: ${JSON.stringify(response.data)}`);
        }
        
        return { queueData: response.data.data };
      }
    );

    this.results.push(suite);
    this.printSuiteResults(suite);
  }

  // Helper method to run individual tests
  private async runTest(
    suite: TestSuite,
    testName: string,
    testFunction: () => Promise<any>
  ): Promise<void> {
    suite.totalTests++;
    const startTime = Date.now();
    
    try {
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      suite.results.push({
        testName,
        success: true,
        duration,
        details: result
      });
      
      suite.passedTests++;
      suite.totalDuration += duration;
      
      console.log(`  ✅ ${testName} (${duration}ms)`);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      suite.results.push({
        testName,
        success: false,
        duration,
        error: error.message
      });
      
      suite.failedTests++;
      suite.totalDuration += duration;
      
      console.log(`  ❌ ${testName} (${duration}ms): ${error.message}`);
    }
  }

  // Print results for a test suite
  private printSuiteResults(suite: TestSuite): void {
    console.log(`\n📊 ${suite.suiteName} Results:`);
    console.log(`   Total: ${suite.totalTests} | Passed: ${suite.passedTests} | Failed: ${suite.failedTests}`);
    console.log(`   Duration: ${suite.totalDuration}ms\n`);
  }

  // Generate final comprehensive report
  private generateFinalReport(): void {
    console.log('📋 COMPREHENSIVE E2E TEST REPORT');
    console.log('='.repeat(50));
    
    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalDuration = 0;
    
    this.results.forEach(suite => {
      totalTests += suite.totalTests;
      totalPassed += suite.passedTests;
      totalFailed += suite.failedTests;
      totalDuration += suite.totalDuration;
      
      console.log(`\n📦 ${suite.suiteName}`);
      console.log(`   Tests: ${suite.totalTests} | ✅ ${suite.passedTests} | ❌ ${suite.failedTests} | ⏱️ ${suite.totalDuration}ms`);
      
      if (suite.failedTests > 0) {
        console.log('   Failed Tests:');
        suite.results
          .filter(r => !r.success)
          .forEach(r => console.log(`     - ${r.testName}: ${r.error}`));
      }
    });
    
    console.log('\n🏁 FINAL SUMMARY');
    console.log('='.repeat(30));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${totalPassed}`);
    console.log(`❌ Failed: ${totalFailed}`);
    console.log(`⏱️ Total Duration: ${totalDuration}ms`);
    console.log(`📊 Success Rate: ${totalTests > 0 ? Math.round((totalPassed / totalTests) * 100) : 0}%`);
    
    if (totalFailed === 0) {
      console.log('\n🎉 All tests passed! Service is ready for production.');
    } else {
      console.log(`\n⚠️ ${totalFailed} test(s) failed. Review and fix before proceeding.`);
    }
  }
}

// CLI entry point
if (require.main === module) {
  const orchestrator = new E2EOrchestrator();
  orchestrator.runAllTests()
    .then(() => {
      console.log('\n✅ E2E testing completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 E2E testing failed:', error);
      process.exit(1);
    });
}

export default E2EOrchestrator;