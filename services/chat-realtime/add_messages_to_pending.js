/**
 * Add test messages to pending_acceptance conversation
 */

require('dotenv').config();
const { initializeFirebase, getFirebaseDatabase } = require('./firebase-config');

// Initialize Firebase
const app = initializeFirebase();
const db = getFirebaseDatabase();

async function addMessagesToConversation() {
  console.log('🔧 Adding test messages to pending_acceptance conversation...');
  
  const conversationId = 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9';
  
  try {
    // Check if conversation exists
    const conversationSnapshot = await db.ref(`conversations/${conversationId}`).once('value');
    const conversation = conversationSnapshot.val();
    
    if (!conversation) {
      console.error('❌ Conversation not found');
      return;
    }
    
    console.log('✅ Conversation found:', {
      id: conversation.id,
      status: conversation.status,
      customer: conversation.customer?.name
    });
    
    // Add some test messages
    const messages = [
      {
        id: `msg-${Date.now()}-001`,
        conversationId: conversationId,
        senderId: 'customer-CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9',
        senderType: 'customer',
        content: 'Hi! I need help with my order.',
        type: 'text',
        timestamp: new Date(Date.now() - 300000).toISOString() // 5 minutes ago
      },
      {
        id: `msg-${Date.now()}-002`,
        conversationId: conversationId,
        senderId: '5f4fb378-908d-4b49-83ce-be4ce3b50c5d', // Previous agent (Juan)
        senderType: 'agent',
        content: 'Hello! Let me help you with that. Could you please provide your order number?',
        type: 'text',
        timestamp: new Date(Date.now() - 240000).toISOString() // 4 minutes ago
      },
      {
        id: `msg-${Date.now()}-003`,
        conversationId: conversationId,
        senderId: 'customer-CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9',
        senderType: 'customer',
        content: 'My order number is #12345. The delivery was delayed.',
        type: 'text',
        timestamp: new Date(Date.now() - 180000).toISOString() // 3 minutes ago
      },
      {
        id: `msg-${Date.now()}-004`,
        conversationId: conversationId,
        senderId: '5f4fb378-908d-4b49-83ce-be4ce3b50c5d', // Previous agent (Juan)
        senderType: 'agent',
        content: 'I understand your concern. Let me check the status and transfer you to our shipping specialist.',
        type: 'text',
        timestamp: new Date(Date.now() - 120000).toISOString() // 2 minutes ago
      },
      {
        id: `system-${Date.now()}`,
        conversationId: conversationId,
        senderId: 'system',
        senderType: 'system',
        content: 'Conversation transferred to shipping specialist',
        type: 'system',
        timestamp: new Date(Date.now() - 60000).toISOString() // 1 minute ago
      }
    ];
    
    // Add messages to Firebase
    const messagesRef = db.ref(`conversations/${conversationId}/messages`);
    
    for (const message of messages) {
      await messagesRef.child(message.id).set(message);
      console.log(`✅ Added message: ${message.senderType} - "${message.content.substring(0, 50)}..."`);
    }
    
    console.log(`\n🎉 Successfully added ${messages.length} messages to conversation ${conversationId}`);
    
  } catch (error) {
    console.error('❌ Error adding messages:', error);
  }
}

// Run the script
if (require.main === module) {
  addMessagesToConversation()
    .then(() => {
      console.log('\n✨ Script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { addMessagesToConversation };