{"name": "@cx-system/twilio", "version": "1.0.0", "private": true, "description": "<PERSON>wi<PERSON> webhook handler for CX System", "main": "dist/server.js", "scripts": {"build": "tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"dotenv": "^16.3.1", "express": "^4.18.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.11.0", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "tsx": "^4.7.0", "typescript": "^5.3.3"}}