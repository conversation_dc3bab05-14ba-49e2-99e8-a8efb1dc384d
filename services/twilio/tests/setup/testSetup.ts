// Test setup and configuration for Twilio Service
import { config } from '../../src/config';

// Test configuration
export const testConfig = {
  server: {
    port: 3000,
    webhookPath: '/webhook',
  },
  timeout: 5000, // 5 seconds timeout for tests
};

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.TWILIO_SERVICE_PORT = testConfig.server.port.toString();

// Global test setup
beforeAll(async () => {
  // Any global setup needed for tests
});

afterAll(async () => {
  // Any global cleanup needed for tests
});

// Mock console methods to reduce noise in test output unless explicitly needed
const originalConsole = global.console;

global.console = {
  ...console,
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
};

// Utility to restore console for specific tests
export const restoreConsole = () => {
  global.console = originalConsole;
};

// Utility to get console mock calls
export const getConsoleCalls = () => ({
  log: (global.console.log as jest.Mock).mock.calls,
  error: (global.console.error as jest.Mock).mock.calls,
  warn: (global.console.warn as jest.Mock).mock.calls,
  info: (global.console.info as jest.Mock).mock.calls,
});

export default testConfig;