import { Request, Response } from 'express';
import { WebhookHandler } from '../src/webhookHandler';
import { TwilioWebhookMessage } from '../src/types';

describe('WebhookHandler', () => {
  let webhookHandler: WebhookHandler;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    webhookHandler = new WebhookHandler();
    
    mockRes = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn(),
    };
    
    // Spy on console methods
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('handleIncomingMessage', () => {
    it('should process valid WhatsApp message successfully', () => {
      const twilioMessage: TwilioWebhookMessage = {
        MessageSid: 'SM1234567890',
        From: 'whatsapp:+1234567890',
        To: 'whatsapp:+0987654321',
        Body: 'Hello, this is a test message',
        NumMedia: '0',
      };

      mockReq = {
        body: twilioMessage,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.send).toHaveBeenCalledWith('OK');
      expect(console.log).toHaveBeenCalledWith('Received WhatsApp message:', {
        id: 'SM1234567890',
        from: '+1234567890',
        body: 'Hello, this is a test message',
      });
    });

    it('should process message with media correctly', () => {
      const twilioMessage: TwilioWebhookMessage = {
        MessageSid: 'SM1234567890',
        From: 'whatsapp:+1234567890',
        To: 'whatsapp:+0987654321',
        Body: '',
        NumMedia: '1',
        MediaUrl0: 'https://api.twilio.com/media/123.jpg',
        MediaContentType0: 'image/jpeg',
      };

      mockReq = {
        body: twilioMessage,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(mockRes.send).toHaveBeenCalledWith('OK');
    });

    it('should handle empty message body', () => {
      const twilioMessage: TwilioWebhookMessage = {
        MessageSid: 'SM1234567890',
        From: 'whatsapp:+1234567890',
        To: 'whatsapp:+0987654321',
        Body: '',
        NumMedia: '0',
      };

      mockReq = {
        body: twilioMessage,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(console.log).toHaveBeenCalledWith('Received WhatsApp message:', {
        id: 'SM1234567890',
        from: '+1234567890',
        body: '',
      });
    });

    it('should handle undefined message body', () => {
      const twilioMessage = {
        MessageSid: 'SM1234567890',
        From: 'whatsapp:+1234567890',
        To: 'whatsapp:+0987654321',
        NumMedia: '0',
        // Body is undefined
      } as TwilioWebhookMessage;

      mockReq = {
        body: twilioMessage,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(200);
      expect(console.log).toHaveBeenCalledWith('Received WhatsApp message:', {
        id: 'SM1234567890',
        from: '+1234567890',
        body: '',
      });
    });

    it('should clean phone numbers correctly', () => {
      const twilioMessage: TwilioWebhookMessage = {
        MessageSid: 'SM1234567890',
        From: 'whatsapp:+1234567890',
        To: 'whatsapp:+0987654321',
        Body: 'Test message',
        NumMedia: '0',
      };

      mockReq = {
        body: twilioMessage,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(console.log).toHaveBeenCalledWith('Received WhatsApp message:', {
        id: 'SM1234567890',
        from: '+1234567890',
        body: 'Test message',
      });
    });

    it('should handle malformed request gracefully', () => {
      mockReq = {
        body: null,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.send).toHaveBeenCalledWith('Error processing webhook');
      expect(console.error).toHaveBeenCalledWith('Error processing webhook:', expect.any(Error));
    });

    it('should handle missing required fields', () => {
      const malformedMessage = {
        // Missing MessageSid, From, To
        Body: 'Test message',
        NumMedia: '0',
      };

      mockReq = {
        body: malformedMessage,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(console.error).toHaveBeenCalled();
    });

    it('should handle request body parsing errors', () => {
      mockReq = {
        body: undefined,
      };

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.send).toHaveBeenCalledWith('Error processing webhook');
    });
  });

  describe('cleanPhoneNumber', () => {
    it('should remove whatsapp prefix from phone numbers', () => {
      // Access private method for testing
      const cleanPhoneNumber = (webhookHandler as any).cleanPhoneNumber.bind(webhookHandler);
      
      expect(cleanPhoneNumber('whatsapp:+1234567890')).toBe('+1234567890');
      expect(cleanPhoneNumber('whatsapp:+44123456789')).toBe('+44123456789');
    });

    it('should handle phone numbers without whatsapp prefix', () => {
      const cleanPhoneNumber = (webhookHandler as any).cleanPhoneNumber.bind(webhookHandler);
      
      expect(cleanPhoneNumber('+1234567890')).toBe('+1234567890');
      expect(cleanPhoneNumber('1234567890')).toBe('1234567890');
    });

    it('should handle empty or null phone numbers', () => {
      const cleanPhoneNumber = (webhookHandler as any).cleanPhoneNumber.bind(webhookHandler);
      
      expect(cleanPhoneNumber('')).toBe('');
      expect(cleanPhoneNumber('whatsapp:')).toBe('');
    });
  });

  describe('Message transformation', () => {
    it('should transform Twilio message to internal format correctly', () => {
      const twilioMessage: TwilioWebhookMessage = {
        MessageSid: 'SM9876543210',
        From: 'whatsapp:+1555123456',
        To: 'whatsapp:+1555987654',
        Body: 'Test transformation',
        NumMedia: '1',
        MediaUrl0: 'https://api.twilio.com/media/test.png',
        MediaContentType0: 'image/png',
      };

      mockReq = {
        body: twilioMessage,
      };

      // Mock Date.now to ensure consistent timestamp
      const mockTimestamp = '2023-01-01T12:00:00.000Z';
      jest.spyOn(Date.prototype, 'toISOString').mockReturnValue(mockTimestamp);

      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);

      expect(console.log).toHaveBeenCalledWith('Received WhatsApp message:', {
        id: 'SM9876543210',
        from: '+1555123456',
        body: 'Test transformation',
      });
    });

    it('should set correct timestamp for incoming messages', () => {
      const twilioMessage: TwilioWebhookMessage = {
        MessageSid: 'SM1234567890',
        From: 'whatsapp:+1234567890',
        To: 'whatsapp:+0987654321',
        Body: 'Timestamp test',
        NumMedia: '0',
      };

      mockReq = {
        body: twilioMessage,
      };

      const beforeTime = Date.now();
      webhookHandler.handleIncomingMessage(mockReq as Request, mockRes as Response);
      const afterTime = Date.now();

      expect(mockRes.status).toHaveBeenCalledWith(200);
      // The timestamp should be between beforeTime and afterTime
      // This is implicitly tested by the successful execution
    });
  });
});