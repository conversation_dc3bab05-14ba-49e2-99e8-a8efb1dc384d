import request from 'supertest';
import express from 'express';
import { TwilioWebhookMessage } from '../src/types';

// Mock the WebhookHandler
const mockWebhookHandler = {
  handleIncomingMessage: jest.fn(),
};

jest.mock('../src/webhookHandler', () => ({
  WebhookHandler: jest.fn().mockImplementation(() => mockWebhookHandler),
}));

// Mock config
jest.mock('../src/config', () => ({
  config: {
    port: 3001,
    webhookPath: '/webhook',
  },
}));

describe('Twilio Server Integration Tests', () => {
  let app: express.Application;

  beforeAll(() => {
    // Import the server setup after mocks are in place
    app = express();
    app.use(express.urlencoded({ extended: true }));
    app.use(express.json());

    // Setup routes manually since we can't import the full server
    app.post('/webhook', (req, res) => {
      mockWebhookHandler.handleIncomingMessage(req, res);
    });

    app.get('/health', (req, res) => {
      res.json({ status: 'ok', service: 'twilio' });
    });
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default mock implementation
    mockWebhookHandler.handleIncomingMessage.mockImplementation((req, res) => {
      res.status(200).send('OK');
    });
  });

  describe('POST /webhook', () => {
    it('should handle valid Twilio webhook with form data', async () => {
      const twilioFormData = {
        MessageSid: 'SM**********',
        From: 'whatsapp:+**********',
        To: 'whatsapp:+**********',
        Body: 'Hello from WhatsApp',
        NumMedia: '0',
      };

      const response = await request(app)
        .post('/webhook')
        .type('form')
        .send(twilioFormData);

      expect(response.status).toBe(200);
      expect(response.text).toBe('OK');
      expect(mockWebhookHandler.handleIncomingMessage).toHaveBeenCalled();
    });

    it('should handle Twilio webhook with JSON payload', async () => {
      const twilioMessage: TwilioWebhookMessage = {
        MessageSid: 'SM**********',
        From: 'whatsapp:+**********',
        To: 'whatsapp:+**********',
        Body: 'JSON payload test',
        NumMedia: '0',
      };

      const response = await request(app)
        .post('/webhook')
        .send(twilioMessage);

      expect(response.status).toBe(200);
      expect(mockWebhookHandler.handleIncomingMessage).toHaveBeenCalled();
    });

    it('should handle webhook with media message', async () => {
      const twilioFormData = {
        MessageSid: 'SM**********',
        From: 'whatsapp:+**********',
        To: 'whatsapp:+**********',
        Body: '',
        NumMedia: '1',
        MediaUrl0: 'https://api.twilio.com/media/123.jpg',
        MediaContentType0: 'image/jpeg',
      };

      const response = await request(app)
        .post('/webhook')
        .type('form')
        .send(twilioFormData);

      expect(response.status).toBe(200);
      expect(mockWebhookHandler.handleIncomingMessage).toHaveBeenCalled();
    });

    it('should handle webhook handler errors gracefully', async () => {
      mockWebhookHandler.handleIncomingMessage.mockImplementation((req, res) => {
        res.status(500).send('Error processing webhook');
      });

      const twilioFormData = {
        MessageSid: 'SM**********',
        From: 'whatsapp:+**********',
        To: 'whatsapp:+**********',
        Body: 'Error test',
        NumMedia: '0',
      };

      const response = await request(app)
        .post('/webhook')
        .type('form')
        .send(twilioFormData);

      expect(response.status).toBe(500);
      expect(response.text).toBe('Error processing webhook');
    });

    it('should handle malformed webhook data', async () => {
      mockWebhookHandler.handleIncomingMessage.mockImplementation((req, res) => {
        res.status(500).send('Error processing webhook');
      });

      const response = await request(app)
        .post('/webhook')
        .send('invalid-data');

      expect(response.status).toBe(500);
    });

    it('should handle empty webhook payload', async () => {
      const response = await request(app)
        .post('/webhook')
        .send({});

      expect(response.status).toBe(200);
      expect(mockWebhookHandler.handleIncomingMessage).toHaveBeenCalled();
    });
  });

  describe('GET /health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/health');

      expect(response.status).toBe(200);
      expect(response.body).toEqual({
        status: 'ok',
        service: 'twilio',
      });
    });

    it('should respond quickly to health checks', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/health');
      
      const duration = Date.now() - start;

      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(100); // Should respond in less than 100ms
    });
  });

  describe('Middleware functionality', () => {
    it('should parse URL-encoded form data correctly', async () => {
      const formData = {
        MessageSid: 'SM**********',
        From: 'whatsapp:+**********',
        To: 'whatsapp:+**********',
        Body: 'Form parsing test',
        NumMedia: '0',
      };

      let capturedBody: any;
      mockWebhookHandler.handleIncomingMessage.mockImplementation((req, res) => {
        capturedBody = req.body;
        res.status(200).send('OK');
      });

      await request(app)
        .post('/webhook')
        .type('form')
        .send(formData);

      expect(capturedBody).toMatchObject(formData);
    });

    it('should parse JSON data correctly', async () => {
      const jsonData = {
        MessageSid: 'SM**********',
        From: 'whatsapp:+**********',
        Body: 'JSON parsing test',
      };

      let capturedBody: any;
      mockWebhookHandler.handleIncomingMessage.mockImplementation((req, res) => {
        capturedBody = req.body;
        res.status(200).send('OK');
      });

      await request(app)
        .post('/webhook')
        .send(jsonData);

      expect(capturedBody).toMatchObject(jsonData);
    });
  });

  describe('Error scenarios', () => {
    it('should handle 404 for unknown endpoints', async () => {
      const response = await request(app)
        .get('/unknown-endpoint');

      expect(response.status).toBe(404);
    });

    it('should handle wrong HTTP method on webhook endpoint', async () => {
      const response = await request(app)
        .get('/webhook');

      expect(response.status).toBe(404);
    });

    it('should handle large payload sizes', async () => {
      const largeBody = 'x'.repeat(10000);
      const twilioFormData = {
        MessageSid: 'SM**********',
        From: 'whatsapp:+**********',
        To: 'whatsapp:+**********',
        Body: largeBody,
        NumMedia: '0',
      };

      const response = await request(app)
        .post('/webhook')
        .type('form')
        .send(twilioFormData);

      expect(response.status).toBe(200);
      expect(mockWebhookHandler.handleIncomingMessage).toHaveBeenCalled();
    });
  });
});