import express from 'express';
import { WebhookHandler } from './webhookHandler';
import { config } from './config';

const app = express();
const webhookHandler = new WebhookHandler();

// Middleware for parsing URL-encoded data (Twilio sends form data)
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Webhook endpoint
app.post(config.webhookPath, (req, res) => {
  webhookHandler.handleIncomingMessage(req, res);
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', service: 'twilio' });
});

// Start server
app.listen(config.port, () => {
  console.log(`Twilio service running on port ${config.port}`);
  console.log(`Webhook endpoint: ${config.webhookPath}`);
});