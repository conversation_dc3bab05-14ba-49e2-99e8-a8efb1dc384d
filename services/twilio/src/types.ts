// Traditional WhatsApp webhook format (legacy)
export interface TwilioWebhookMessage {
  MessageSid: string;
  From: string;  // whatsapp:+1234567890
  To: string;    // whatsapp:+0987654321
  Body: string;
  NumMedia: string;
  MediaUrl0?: string;
  MediaContentType0?: string;
}

// NEW: Twilio Conversations API webhook format (current)
export interface TwilioConversationsWebhook {
  RetryCount: string;
  EventType: string;              // "onMessageAdded", "onMessageUpdated", etc.
  Attributes: string;             // Custom attributes as JSON string
  DateCreated: string;            // ISO timestamp
  Author: string;                 // Phone number or participant identity
  Index: string;                  // Message index in conversation
  MessageSid: string;             // Unique message ID
  ParticipantSid: string;         // Participant who sent the message
  Body: string;                   // Message content
  AccountSid: string;             // Account ID
  Source: string;                 // "SMS", "API", "Chat"
  ConversationSid: string;        // Conversation ID
  // Optional fields
  From?: string;                  // For backward compatibility
  To?: string;                    // For backward compatibility
}

export interface WhatsAppMessage {
  id: string;
  from: string;
  to: string;
  body: string;
  mediaUrl?: string;
  mediaType?: string;
  timestamp: string;
}