import { Request, Response } from 'express';
import { TwilioWebhookMessage, WhatsAppMessage } from './types';
import axios from 'axios';

export class WebhookHandler {
  private readonly channelRouterUrl: string;
  
  constructor() {
    this.channelRouterUrl = process.env.CHANNEL_ROUTER_URL || 'http://localhost:3002';
  }
  
  async handleIncomingMessage(req: Request, res: Response): Promise<void> {
    try {
      const twilioPayload = req.body;
      
      console.log('📥 Received Twilio Conversations webhook:', {
        eventType: twilioPayload.EventType,
        conversationSid: twilioPayload.ConversationSid,
        author: twilioPayload.Author,
        bodyPreview: twilioPayload.Body?.substring(0, 50)
      });

      // Only process message-added events
      if (twilioPayload.EventType !== 'onMessageAdded') {
        console.log(`ℹ️ Ignoring event type: ${twilioPayload.EventType}`);
        res.status(200).send('OK - Event ignored');
        return;
      }

      // Convert Twilio Conversations format to internal format
      const incomingMessage = {
        id: twilioPayload.MessageSid,
        from: twilioPayload.Author || this.cleanPhoneNumber(twilioPayload.From || ''),
        to: this.cleanPhoneNumber(twilioPayload.To || ''),
        body: twilioPayload.Body || '',
        type: 'text' as const,
        channel: 'whatsapp' as const,
        timestamp: new Date(twilioPayload.DateCreated || Date.now()),
        metadata: {
          twilioMessageSid: twilioPayload.MessageSid,
          twilioConversationSid: twilioPayload.ConversationSid,
          messageIndex: twilioPayload.Index,
          participantSid: twilioPayload.ParticipantSid,
          source: twilioPayload.Source,
          eventType: twilioPayload.EventType,
          retryCount: twilioPayload.RetryCount,
          attributes: twilioPayload.Attributes
        }
      };

      console.log('🚀 Sending to Channel Router:', {
        messageId: incomingMessage.id,
        from: incomingMessage.from,
        channel: incomingMessage.channel,
        twilioConversationSid: incomingMessage.metadata.twilioConversationSid
      });

      // Send to Channel Router
      const channelRouterResponse = await axios.post(
        `${this.channelRouterUrl}/api/route`,
        { message: incomingMessage },
        { timeout: 5000 }
      );

      console.log('✅ Channel Router response:', channelRouterResponse.data);
      
      res.status(200).json({
        success: true,
        action: 'forwarded_to_channel_router',
        messageId: incomingMessage.id,
        channelRouterResponse: channelRouterResponse.data
      });
      
    } catch (error) {
      console.error('❌ Error processing Twilio webhook:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to process webhook',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  private cleanPhoneNumber(number: string): string {
    // Remove whatsapp: prefix
    return number.replace('whatsapp:', '');
  }
}