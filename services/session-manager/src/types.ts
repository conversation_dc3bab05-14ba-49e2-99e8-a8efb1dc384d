export interface Session {
  id: string;
  conversationId: string;
  customerId: string;
  agentId?: string;
  channel: 'whatsapp' | 'web' | 'sms';
  status: 'queued' | 'active' | 'closed';
  createdAt: string;
  updatedAt: string;
}

export interface CreateSessionRequest {
  conversationId: string;
  customerId: string;
  channel: 'whatsapp' | 'web' | 'sms';
}

export interface UpdateSessionRequest {
  agentId?: string;
  status?: 'queued' | 'active' | 'closed';
  updatedAt?: string;
}

export interface IncomingMessage {
  from: string;
  body: string;
  timestamp: string;
}

export interface MessageRequest {
  message: IncomingMessage;
  conversationId: string;
}