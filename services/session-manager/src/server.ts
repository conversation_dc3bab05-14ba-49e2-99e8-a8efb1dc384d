import express from 'express';
import { connectRedis } from './redis';
import { sessionRoutes } from './routes';
import { config } from './config';

const app = express();

app.use(express.json());
app.use('/api', sessionRoutes);

// Health check
app.get('/health', (_, res) => {
  res.json({ status: 'ok', service: 'session-manager' });
});

async function startServer() {
  try {
    await connectRedis();
    
    app.listen(config.port, () => {
      console.log(`Session Manager running on port ${config.port}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();