import { Request, Response, Router } from 'express';
import { SessionService } from './sessionService';

const router = Router();
const sessionService = new SessionService();

// Health check
router.get('/health', (req: Request, res: Response) => {
  res.json({ 
    status: 'ok', 
    service: 'session-manager',
    role: 'session management in Redis',
    timestamp: new Date().toISOString() 
  });
});

// Main endpoint for Channel Router - Get or create session ID
router.post('/session', async (req: Request, res: Response) => {
  try {
    const { phoneNumber, channel } = req.body;

    if (!phoneNumber) {
      return res.status(400).json({ 
        error: 'phoneNumber is required' 
      });
    }

    const result = await sessionService.getOrCreateSession(phoneNumber, channel || 'whatsapp');
    
    res.json({
      sessionId: result.sessionId,
      isNew: result.isNew,
      phoneNumber: phoneNumber,
      channel: channel || 'whatsapp',
      createdAt: result.createdAt,
      lastActivity: result.lastActivity
    });
    
  } catch (error) {
    console.error('Session endpoint error:', error);
    res.status(500).json({ 
      error: 'Failed to get or create session',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get session by ID (for debugging/monitoring)
router.get('/sessions/:id', async (req: Request, res: Response) => {
  try {
    const session = await sessionService.getSessionById(req.params.id);
    
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }
    
    res.json(session);
  } catch (error) {
    console.error('Get session error:', error);
    res.status(500).json({ error: 'Failed to get session' });
  }
});

// Update session activity (for keeping sessions alive)
router.post('/sessions/:id/activity', async (req: Request, res: Response) => {
  try {
    const updated = await sessionService.updateSessionActivity(req.params.id);
    
    if (!updated) {
      return res.status(404).json({ error: 'Session not found' });
    }
    
    res.json({ 
      sessionId: req.params.id,
      lastActivity: Date.now(),
      updated: true
    });
  } catch (error) {
    console.error('Update session activity error:', error);
    res.status(500).json({ error: 'Failed to update session activity' });
  }
});

export { router as sessionRoutes };
