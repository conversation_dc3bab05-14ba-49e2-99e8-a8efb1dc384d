import dotenv from 'dotenv';

dotenv.config({ path: '../../../.env' });

// Parse Redis URL if provided, otherwise use individual host/port
let redisHost = 'localhost';
let redisPort = 6379;

if (process.env.REDIS_URL) {
  const url = new URL(process.env.REDIS_URL);
  redisHost = url.hostname;
  redisPort = parseInt(url.port || '6379', 10);
} else {
  redisHost = process.env.REDIS_HOST || 'localhost';
  redisPort = parseInt(process.env.REDIS_PORT || '6379', 10);
}

export const config = {
  port: parseInt(process.env.SESSION_MANAGER_PORT || '3001', 10),
  redis: {
    host: redisHost,
    port: redisPort,
  }
};
