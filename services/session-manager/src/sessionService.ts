import { redisClient } from './redis';

const SESSION_KEY = 'session:phone:';
const SESSION_ID_KEY = 'session:id:';
const SESSION_TTL = 7200; // 2 hours

interface ConversationSession {
  sessionId: string;
  phoneNumber: string;
  channel: string;
  createdAt: number;
  lastActivity: number;
}

interface SessionResult {
  sessionId: string;
  isNew: boolean;
  createdAt: number;
  lastActivity: number;
}

export class SessionService {
  
  /**
   * Get or create session for a phone number
   * This is the main method called by Channel Router
   */
  async getOrCreateSession(phoneNumber: string, channel: string = 'whatsapp'): Promise<SessionResult> {
    const cleanPhone = this.cleanPhoneNumber(phoneNumber);
    const phoneKey = `${SESSION_KEY}${cleanPhone}`;
    
    // Try to get existing session
    const existingSessionData = await redisClient.get(phoneKey);
    
    if (existingSessionData) {
      const session: ConversationSession = JSON.parse(existingSessionData);
      
      // Update last activity
      session.lastActivity = Date.now();
      await this.saveSession(session);
      
      return {
        sessionId: session.sessionId,
        isNew: false,
        createdAt: session.createdAt,
        lastActivity: session.lastActivity
      };
    }
    
    // Create new session
    const sessionId = this.generateSessionId(cleanPhone);
    const now = Date.now();
    
    const newSession: ConversationSession = {
      sessionId,
      phoneNumber: cleanPhone,
      channel,
      createdAt: now,
      lastActivity: now
    };
    
    await this.saveSession(newSession);
    
    return {
      sessionId,
      isNew: true,
      createdAt: now,
      lastActivity: now
    };
  }

  /**
   * Get session by session ID
   */
  async getSessionById(sessionId: string): Promise<ConversationSession | null> {
    const sessionIdKey = `${SESSION_ID_KEY}${sessionId}`;
    const sessionData = await redisClient.get(sessionIdKey);
    
    return sessionData ? JSON.parse(sessionData) : null;
  }

  /**
   * Update session activity timestamp
   */
  async updateSessionActivity(sessionId: string): Promise<boolean> {
    const session = await this.getSessionById(sessionId);
    
    if (!session) {
      return false;
    }
    
    session.lastActivity = Date.now();
    await this.saveSession(session);
    
    return true;
  }

  /**
   * Save session to Redis with both phone and sessionId keys
   */
  private async saveSession(session: ConversationSession): Promise<void> {
    const sessionData = JSON.stringify(session);
    
    // Save by phone number
    const phoneKey = `${SESSION_KEY}${session.phoneNumber}`;
    await redisClient.setEx(phoneKey, SESSION_TTL, sessionData);
    
    // Save by session ID
    const sessionIdKey = `${SESSION_ID_KEY}${session.sessionId}`;
    await redisClient.setEx(sessionIdKey, SESSION_TTL, sessionData);
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(phoneNumber: string): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `sess_${phoneNumber}_${timestamp}_${random}`;
  }

  /**
   * Clean phone number for consistent storage
   */
  private cleanPhoneNumber(phoneNumber: string): string {
    // Remove all non-digits and ensure it starts with country code
    const digits = phoneNumber.replace(/\D/g, '');
    
    // If it doesn't start with country code, assume it's local (add default)
    if (digits.length === 10) {
      return `1${digits}`; // US default
    }
    
    return digits;
  }

  /**
   * Health check - verify Redis connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      await redisClient.ping();
      return true;
    } catch (error) {
      console.error('Redis health check failed:', error);
      return false;
    }
  }
}