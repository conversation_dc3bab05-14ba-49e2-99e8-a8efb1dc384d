import winston from 'winston';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logsDir = path.resolve(__dirname, '../../../logs/services/session-manager');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, service, sessionId, phoneNumber, stack, ...meta }) => {
    const logEntry: any = {
      timestamp,
      level,
      service: 'session-manager',
      message
    };
    
    if (sessionId) logEntry.sessionId = sessionId;
    if (phoneNumber) logEntry.phoneNumber = phoneNumber;
    if (stack) logEntry.stack = stack;
    
    // Add remaining metadata
    Object.keys(meta).forEach(key => {
      if (meta[key] !== undefined) {
        logEntry[key] = meta[key];
      }
    });
    
    return JSON.stringify(logEntry);
  })
);

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    // Console output for development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    
    // File output for all logs
    new winston.transports.File({
      filename: path.join(logsDir, 'session-manager.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true
    }),
    
    // Separate error log
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 3
    })
  ]
});

// Helper functions for structured logging
export const createLogger = (context: { sessionId?: string; phoneNumber?: string } = {}) => {
  return {
    info: (message: string, meta?: any) => logger.info(message, { ...context, ...meta }),
    warn: (message: string, meta?: any) => logger.warn(message, { ...context, ...meta }),
    error: (message: string, error?: any, meta?: any) => {
      const errorMeta = error instanceof Error ? { stack: error.stack, message: error.message } : { error };
      logger.error(message, { ...context, ...errorMeta, ...meta });
    },
    debug: (message: string, meta?: any) => logger.debug(message, { ...context, ...meta })
  };
};

export default logger;