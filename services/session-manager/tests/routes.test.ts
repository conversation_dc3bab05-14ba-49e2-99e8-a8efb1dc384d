import request from 'supertest';
import express from 'express';
import { Session, MessageRequest } from '../src/types';

// Create a separate express app for testing to avoid import issues
const app = express();
app.use(express.json());

// Mock SessionService directly in the route handler
const mockSessionService = {
  createSession: jest.fn(),
  getSession: jest.fn(),
  updateSession: jest.fn(),
  deleteSession: jest.fn(),
  handleMessage: jest.fn(),
};

// Define routes inline to avoid import/mock issues
app.post('/sessions', async (req, res) => {
  try {
    const session = await mockSessionService.createSession(req.body);
    res.status(201).json(session);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create session' });
  }
});

app.get('/sessions/:id', async (req, res) => {
  try {
    const session = await mockSessionService.getSession(req.params.id);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }
    res.json(session);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get session' });
  }
});

app.put('/sessions/:id', async (req, res) => {
  try {
    const session = await mockSessionService.updateSession(req.params.id, req.body);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }
    res.json(session);
  } catch (error) {
    res.status(500).json({ error: 'Failed to update session' });
  }
});

app.delete('/sessions/:id', async (req, res) => {
  try {
    const deleted = await mockSessionService.deleteSession(req.params.id);
    if (!deleted) {
      return res.status(404).json({ error: 'Session not found' });
    }
    res.status(204).send();
  } catch (error) {
    res.status(500).json({ error: 'Failed to delete session' });
  }
});

// New endpoint for handling incoming messages
app.post('/sessions/message', async (req, res) => {
  try {
    const result = await mockSessionService.handleMessage(req.body);
    
    if (result.success) {
      res.json(result);
    } else {
      res.status(500).json(result);
    }
  } catch (error) {
    console.error('Handle message error:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Failed to handle message' 
    });
  }
});

describe('Session Routes (Functional Tests)', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /sessions', () => {
    it('should create a session and return 201', async () => {
      const createRequest = {
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp' as const,
      };

      const mockSession: Session = {
        id: 'session-123',
        ...createRequest,
        status: 'queued',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T00:00:00.000Z',
      };

      mockSessionService.createSession.mockResolvedValue(mockSession);

      const response = await request(app)
        .post('/sessions')
        .send(createRequest);

      expect(response.status).toBe(201);
      expect(response.body).toEqual(mockSession);
      expect(mockSessionService.createSession).toHaveBeenCalledWith(createRequest);
    });

    it('should return 500 when session creation fails', async () => {
      const createRequest = {
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp' as const,
      };

      mockSessionService.createSession.mockRejectedValue(new Error('Database error'));

      const response = await request(app)
        .post('/sessions')
        .send(createRequest);

      expect(response.status).toBe(500);
      expect(response.body).toEqual({ error: 'Failed to create session' });
    });
  });

  describe('GET /sessions/:id', () => {
    it('should return session when it exists', async () => {
      const mockSession: Session = {
        id: 'session-123',
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp',
        status: 'active',
        agentId: 'agent-789',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T01:00:00.000Z',
      };

      mockSessionService.getSession.mockResolvedValue(mockSession);

      const response = await request(app)
        .get('/sessions/session-123');

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockSession);
      expect(mockSessionService.getSession).toHaveBeenCalledWith('session-123');
    });

    it('should return 404 when session does not exist', async () => {
      mockSessionService.getSession.mockResolvedValue(null);

      const response = await request(app)
        .get('/sessions/non-existent');

      expect(response.status).toBe(404);
      expect(response.body).toEqual({ error: 'Session not found' });
    });
  });

  describe('PUT /sessions/:id', () => {
    it('should update session and return updated data', async () => {
      const updateRequest = {
        agentId: 'agent-789',
        status: 'active' as const,
      };

      const mockUpdatedSession: Session = {
        id: 'session-123',
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp',
        status: 'active',
        agentId: 'agent-789',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T02:00:00.000Z',
      };

      mockSessionService.updateSession.mockResolvedValue(mockUpdatedSession);

      const response = await request(app)
        .put('/sessions/session-123')
        .send(updateRequest);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockUpdatedSession);
      expect(mockSessionService.updateSession).toHaveBeenCalledWith('session-123', updateRequest);
    });

    it('should return 404 when session does not exist', async () => {
      mockSessionService.updateSession.mockResolvedValue(null);

      const response = await request(app)
        .put('/sessions/non-existent')
        .send({ status: 'active' });

      expect(response.status).toBe(404);
      expect(response.body).toEqual({ error: 'Session not found' });
    });
  });

  describe('DELETE /sessions/:id', () => {
    it('should delete session and return 204', async () => {
      mockSessionService.deleteSession.mockResolvedValue(true);

      const response = await request(app)
        .delete('/sessions/session-123');

      expect(response.status).toBe(204);
      expect(response.body).toEqual({});
      expect(mockSessionService.deleteSession).toHaveBeenCalledWith('session-123');
    });

    it('should return 404 when session does not exist', async () => {
      mockSessionService.deleteSession.mockResolvedValue(false);

      const response = await request(app)
        .delete('/sessions/non-existent');

      expect(response.status).toBe(404);
      expect(response.body).toEqual({ error: 'Session not found' });
    });
  });

  describe('POST /sessions/message', () => {
    const mockMessageRequest: MessageRequest = {
      message: {
        from: '+**********',
        body: 'Hello, I need help with my account',
        timestamp: '2023-01-01T12:00:00.000Z',
      },
      conversationId: 'conv-123',
    };

    it('should handle message successfully and return success response', async () => {
      const mockSuccessResult = {
        success: true,
        sessionId: 'session-123',
        message: 'Message processed successfully',
      };

      mockSessionService.handleMessage.mockResolvedValue(mockSuccessResult);

      const response = await request(app)
        .post('/sessions/message')
        .send(mockMessageRequest);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockSuccessResult);
      expect(mockSessionService.handleMessage).toHaveBeenCalledWith(mockMessageRequest);
    });

    it('should return 500 when handleMessage returns failure', async () => {
      const mockFailureResult = {
        success: false,
        message: 'Failed to process message',
      };

      mockSessionService.handleMessage.mockResolvedValue(mockFailureResult);

      const response = await request(app)
        .post('/sessions/message')
        .send(mockMessageRequest);

      expect(response.status).toBe(500);
      expect(response.body).toEqual(mockFailureResult);
    });

    it('should handle service exceptions and return generic error', async () => {
      mockSessionService.handleMessage.mockRejectedValue(new Error('Redis connection failed'));

      const response = await request(app)
        .post('/sessions/message')
        .send(mockMessageRequest);

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        success: false,
        message: 'Failed to handle message',
      });
    });

    it('should handle malformed request data', async () => {
      const malformedRequest = {
        message: {
          from: '+**********',
          // Missing body and timestamp
        },
        conversationId: 'conv-123',
      };

      mockSessionService.handleMessage.mockResolvedValue({
        success: false,
        message: 'Invalid message data',
      });

      const response = await request(app)
        .post('/sessions/message')
        .send(malformedRequest);

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(mockSessionService.handleMessage).toHaveBeenCalledWith(malformedRequest);
    });

    it('should handle empty request body', async () => {
      const response = await request(app)
        .post('/sessions/message')
        .send({});

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
    });

    it('should process message with different channels correctly', async () => {
      const webMessageRequest = {
        message: {
          from: 'user-web-123',
          body: 'Web chat message',
          timestamp: '2023-01-01T12:00:00.000Z',
        },
        conversationId: 'web-conv-456',
      };

      const mockResult = {
        success: true,
        sessionId: 'web-session-456',
        message: 'Message processed successfully',
      };

      mockSessionService.handleMessage.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/sessions/message')
        .send(webMessageRequest);

      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockResult);
    });
  });
});