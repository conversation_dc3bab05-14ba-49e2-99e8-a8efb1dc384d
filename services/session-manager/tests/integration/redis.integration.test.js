"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const redis_1 = require("redis");
const sessionService_1 = require("../../src/sessionService");
// This test requires a real Redis instance running
// Skip this test in CI environments where Redis might not be available
const REDIS_AVAILABLE = process.env.REDIS_TEST_ENABLED === 'true';
describe('Redis Integration Tests', () => {
    let redisClient;
    let sessionService;
    beforeAll(async () => {
        if (!REDIS_AVAILABLE) {
            return;
        }
        // Connect to test Redis instance
        redisClient = (0, redis_1.createClient)({
            socket: {
                host: process.env.REDIS_HOST || 'localhost',
                port: parseInt(process.env.REDIS_PORT || '6379', 10),
            }
        });
        try {
            await redisClient.connect();
            sessionService = new sessionService_1.SessionService();
        }
        catch (error) {
            console.warn('Redis not available for integration tests:', error);
        }
    });
    afterAll(async () => {
        if (redisClient && redisClient.isReady) {
            await redisClient.quit();
        }
    });
    beforeEach(async () => {
        if (!REDIS_AVAILABLE || !redisClient?.isReady) {
            return;
        }
        // Clean up test data before each test
        const keys = await redisClient.keys('session:test-*');
        if (keys.length > 0) {
            await redisClient.del(keys);
        }
    });
    // Conditionally run tests based on Redis availability
    (REDIS_AVAILABLE ? describe : describe.skip)('SessionService with Real Redis', () => {
        it('should create and retrieve session from Redis', async () => {
            const createRequest = {
                conversationId: 'test-conv-123',
                customerId: 'test-customer-456',
                channel: 'whatsapp',
            };
            const createdSession = await sessionService.createSession(createRequest);
            expect(createdSession.id).toContain('test-conv-123');
            const retrievedSession = await sessionService.getSession(createdSession.id);
            expect(retrievedSession).toEqual(createdSession);
        });
        it('should update session in Redis', async () => {
            const createRequest = {
                conversationId: 'test-conv-update',
                customerId: 'test-customer-456',
                channel: 'web',
            };
            const createdSession = await sessionService.createSession(createRequest);
            const updatedSession = await sessionService.updateSession(createdSession.id, {
                agentId: 'test-agent-789',
                status: 'active',
            });
            expect(updatedSession).toMatchObject({
                ...createdSession,
                agentId: 'test-agent-789',
                status: 'active',
            });
            // Verify update persisted
            const retrievedSession = await sessionService.getSession(createdSession.id);
            expect(retrievedSession?.agentId).toBe('test-agent-789');
            expect(retrievedSession?.status).toBe('active');
        });
        it('should delete session from Redis', async () => {
            const createRequest = {
                conversationId: 'test-conv-delete',
                customerId: 'test-customer-456',
                channel: 'sms',
            };
            const createdSession = await sessionService.createSession(createRequest);
            const deleteResult = await sessionService.deleteSession(createdSession.id);
            expect(deleteResult).toBe(true);
            const retrievedSession = await sessionService.getSession(createdSession.id);
            expect(retrievedSession).toBeNull();
        });
        it('should handle Redis TTL correctly', async () => {
            const createRequest = {
                conversationId: 'test-conv-ttl',
                customerId: 'test-customer-456',
                channel: 'whatsapp',
            };
            const createdSession = await sessionService.createSession(createRequest);
            // Check TTL was set
            const ttl = await redisClient.ttl(`session:${createdSession.id}`);
            expect(ttl).toBeGreaterThan(3500); // Should be close to 3600 seconds
            expect(ttl).toBeLessThanOrEqual(3600);
        });
        it('should handle concurrent session operations', async () => {
            const requests = Array.from({ length: 5 }, (_, i) => ({
                conversationId: `test-conv-concurrent-${i}`,
                customerId: `test-customer-${i}`,
                channel: 'whatsapp',
            }));
            // Create multiple sessions concurrently
            const createdSessions = await Promise.all(requests.map(req => sessionService.createSession(req)));
            expect(createdSessions).toHaveLength(5);
            expect(new Set(createdSessions.map(s => s.id)).size).toBe(5); // All unique
            // Retrieve all sessions concurrently
            const retrievedSessions = await Promise.all(createdSessions.map(s => sessionService.getSession(s.id)));
            retrievedSessions.forEach((session, index) => {
                expect(session).toEqual(createdSessions[index]);
            });
        });
    });
    (REDIS_AVAILABLE ? describe : describe.skip)('Redis Connection Health', () => {
        it('should connect to Redis successfully', async () => {
            expect(redisClient.isReady).toBe(true);
        });
        it('should handle Redis ping/pong', async () => {
            const pong = await redisClient.ping();
            expect(pong).toBe('PONG');
        });
        it('should handle Redis info command', async () => {
            const info = await redisClient.info();
            expect(info).toContain('redis_version');
        });
    });
    // Always run this test to show that integration tests are skipped
    it('should skip Redis integration tests when Redis is not available', () => {
        if (!REDIS_AVAILABLE) {
            console.log('Redis integration tests skipped - set REDIS_TEST_ENABLED=true to enable');
        }
        expect(true).toBe(true); // Always passes
    });
});
