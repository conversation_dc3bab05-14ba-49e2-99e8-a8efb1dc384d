import { createClient, RedisClientType } from 'redis';
import { SessionService } from '../../src/sessionService';
import { CreateSessionRequest, MessageRequest } from '../../src/types';

// This test requires a real Redis instance running
// Skip this test in CI environments where Redis might not be available
const REDIS_AVAILABLE = process.env.REDIS_TEST_ENABLED === 'true';

describe('Redis Integration Tests', () => {
  let redisClient: RedisClientType;
  let sessionService: SessionService;

  beforeAll(async () => {
    if (!REDIS_AVAILABLE) {
      return;
    }

    // Connect to test Redis instance
    redisClient = createClient({
      socket: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
      }
    });

    try {
      await redisClient.connect();
      sessionService = new SessionService();
    } catch (error) {
      console.warn('Redis not available for integration tests:', error);
    }
  });

  afterAll(async () => {
    if (redisClient && redisClient.isReady) {
      await redisClient.quit();
    }
  });

  beforeEach(async () => {
    if (!REDIS_AVAILABLE || !redisClient?.isReady) {
      return;
    }

    // Clean up test data before each test
    const keys = await redisClient.keys('session:test-*');
    if (keys.length > 0) {
      await redisClient.del(keys);
    }
  });

  // Conditionally run tests based on Redis availability
  (REDIS_AVAILABLE ? describe : describe.skip)('SessionService with Real Redis', () => {
    it('should create and retrieve session from Redis', async () => {
      const createRequest: CreateSessionRequest = {
        conversationId: 'test-conv-123',
        customerId: 'test-customer-456',
        channel: 'whatsapp',
      };

      const createdSession = await sessionService.createSession(createRequest);
      expect(createdSession.id).toContain('test-conv-123');

      const retrievedSession = await sessionService.getSession(createdSession.id);
      expect(retrievedSession).toEqual(createdSession);
    });

    it('should update session in Redis', async () => {
      const createRequest: CreateSessionRequest = {
        conversationId: 'test-conv-update',
        customerId: 'test-customer-456',
        channel: 'web',
      };

      const createdSession = await sessionService.createSession(createRequest);
      
      const updatedSession = await sessionService.updateSession(createdSession.id, {
        agentId: 'test-agent-789',
        status: 'active',
      });

      expect(updatedSession).toMatchObject({
        ...createdSession,
        agentId: 'test-agent-789',
        status: 'active',
      });

      // Verify update persisted
      const retrievedSession = await sessionService.getSession(createdSession.id);
      expect(retrievedSession?.agentId).toBe('test-agent-789');
      expect(retrievedSession?.status).toBe('active');
    });

    it('should delete session from Redis', async () => {
      const createRequest: CreateSessionRequest = {
        conversationId: 'test-conv-delete',
        customerId: 'test-customer-456',
        channel: 'sms',
      };

      const createdSession = await sessionService.createSession(createRequest);
      
      const deleteResult = await sessionService.deleteSession(createdSession.id);
      expect(deleteResult).toBe(true);

      const retrievedSession = await sessionService.getSession(createdSession.id);
      expect(retrievedSession).toBeNull();
    });

    it('should handle Redis TTL correctly', async () => {
      const createRequest: CreateSessionRequest = {
        conversationId: 'test-conv-ttl',
        customerId: 'test-customer-456',
        channel: 'whatsapp',
      };

      const createdSession = await sessionService.createSession(createRequest);
      
      // Check TTL was set
      const ttl = await redisClient.ttl(`session:${createdSession.id}`);
      expect(ttl).toBeGreaterThan(3500); // Should be close to 3600 seconds
      expect(ttl).toBeLessThanOrEqual(3600);
    });

    it('should handle concurrent session operations', async () => {
      const requests: CreateSessionRequest[] = Array.from({ length: 5 }, (_, i) => ({
        conversationId: `test-conv-concurrent-${i}`,
        customerId: `test-customer-${i}`,
        channel: 'whatsapp' as const,
      }));

      // Create multiple sessions concurrently
      const createdSessions = await Promise.all(
        requests.map(req => sessionService.createSession(req))
      );

      expect(createdSessions).toHaveLength(5);
      expect(new Set(createdSessions.map(s => s.id)).size).toBe(5); // All unique

      // Retrieve all sessions concurrently
      const retrievedSessions = await Promise.all(
        createdSessions.map(s => sessionService.getSession(s.id))
      );

      retrievedSessions.forEach((session, index) => {
        expect(session).toEqual(createdSessions[index]);
      });
    });
  });

  (REDIS_AVAILABLE ? describe : describe.skip)('New Message Handling Integration', () => {
    beforeEach(async () => {
      // Mock console methods to prevent output during tests
      jest.spyOn(console, 'log').mockImplementation();
      jest.spyOn(console, 'error').mockImplementation();
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    it('should find existing session by conversation ID', async () => {
      // First create a session
      const createRequest: CreateSessionRequest = {
        conversationId: 'test-conv-find-123',
        customerId: 'test-customer-456',
        channel: 'whatsapp',
      };

      const createdSession = await sessionService.createSession(createRequest);
      
      // Then search for it by conversation ID
      const foundSession = await sessionService.getSessionByConversationId('test-conv-find-123');
      
      expect(foundSession).toEqual(createdSession);
    });

    it('should return null when searching for non-existent conversation ID', async () => {
      const foundSession = await sessionService.getSessionByConversationId('test-conv-not-exist');
      expect(foundSession).toBeNull();
    });

    it('should handle message for existing conversation', async () => {
      // Create initial session
      const createRequest: CreateSessionRequest = {
        conversationId: 'test-conv-message-exist',
        customerId: 'test-customer-789',
        channel: 'whatsapp',
      };

      const existingSession = await sessionService.createSession(createRequest);

      // Handle message for existing conversation
      const messageRequest: MessageRequest = {
        message: {
          from: 'test-customer-789',
          body: 'Hello, I have a question',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'test-conv-message-exist',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe(existingSession.id);
      expect(result.message).toBe('Message processed successfully');
    });

    it('should create new session when handling message for new conversation', async () => {
      const messageRequest: MessageRequest = {
        message: {
          from: '+1234567890',
          body: 'New conversation message',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'test-conv-message-new',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('test-conv-message-new');
      expect(result.message).toBe('Message processed successfully');

      // Verify session was actually created in Redis
      const createdSession = await sessionService.getSessionByConversationId('test-conv-message-new');
      expect(createdSession).toBeDefined();
      expect(createdSession?.customerId).toBe('+1234567890');
      expect(createdSession?.channel).toBe('whatsapp');
      expect(createdSession?.status).toBe('queued');
    });

    it('should assign agent to queued session during message handling', async () => {
      const messageRequest: MessageRequest = {
        message: {
          from: '+1111222333',
          body: 'I need help with billing',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'test-conv-agent-assign',
      };

      // First message creates session and potentially assigns agent
      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('test-conv-agent-assign');

      // Check if session was assigned an agent
      const session = await sessionService.getSessionByConversationId('test-conv-agent-assign');
      expect(session).toBeDefined();
      
      // If agent was assigned, session should be active with agentId
      if (session?.agentId) {
        expect(session.status).toBe('active');
        expect(['agent-001', 'agent-002', 'agent-003']).toContain(session.agentId);
      }
    });

    it('should handle multiple conversations concurrently', async () => {
      const messageRequests: MessageRequest[] = Array.from({ length: 3 }, (_, i) => ({
        message: {
          from: `+test-${i}`,
          body: `Message from user ${i}`,
          timestamp: new Date().toISOString(),
        },
        conversationId: `test-conv-concurrent-msg-${i}`,
      }));

      // Handle all messages concurrently
      const results = await Promise.all(
        messageRequests.map(req => sessionService.handleMessage(req))
      );

      // All should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.sessionId).toBeDefined();
      });

      // Verify all sessions exist
      const sessions = await Promise.all(
        messageRequests.map(req => sessionService.getSessionByConversationId(req.conversationId))
      );

      sessions.forEach((session, index) => {
        expect(session).toBeDefined();
        expect(session?.customerId).toBe(`+test-${index}`);
        expect(session?.conversationId).toBe(`test-conv-concurrent-msg-${index}`);
      });
    });

    it('should handle session search with multiple sessions', async () => {
      // Create multiple sessions
      const sessions = await Promise.all([
        sessionService.createSession({
          conversationId: 'test-conv-search-1',
          customerId: 'test-customer-1',
          channel: 'whatsapp',
        }),
        sessionService.createSession({
          conversationId: 'test-conv-search-2',
          customerId: 'test-customer-2',
          channel: 'web',
        }),
        sessionService.createSession({
          conversationId: 'test-conv-search-3',
          customerId: 'test-customer-3',
          channel: 'sms',
        }),
      ]);

      // Search for each conversation ID
      const foundSession1 = await sessionService.getSessionByConversationId('test-conv-search-1');
      const foundSession2 = await sessionService.getSessionByConversationId('test-conv-search-2');
      const foundSession3 = await sessionService.getSessionByConversationId('test-conv-search-3');

      expect(foundSession1).toEqual(sessions[0]);
      expect(foundSession2).toEqual(sessions[1]);
      expect(foundSession3).toEqual(sessions[2]);
    });

    it('should handle rapid message succession for same conversation', async () => {
      const conversationId = 'test-conv-rapid-messages';
      const messageRequests: MessageRequest[] = Array.from({ length: 5 }, (_, i) => ({
        message: {
          from: '+rapid-test-user',
          body: `Message ${i + 1}`,
          timestamp: new Date(Date.now() + i * 100).toISOString(),
        },
        conversationId,
      }));

      // Send messages rapidly
      const results = await Promise.all(
        messageRequests.map(req => sessionService.handleMessage(req))
      );

      // All should succeed and return the same session ID
      const sessionIds = results.map(r => r.sessionId);
      expect(sessionIds.every(id => id === sessionIds[0])).toBe(true);

      // Verify only one session exists
      const session = await sessionService.getSessionByConversationId(conversationId);
      expect(session).toBeDefined();
      expect(session?.customerId).toBe('+rapid-test-user');
    });

    it('should persist agent assignment across Redis operations', async () => {
      // Create a queued session
      const session = await sessionService.createSession({
        conversationId: 'test-conv-agent-persist',
        customerId: 'test-customer-persist',
        channel: 'whatsapp',
      });

      expect(session.status).toBe('queued');
      expect(session.agentId).toBeUndefined();

      // Handle message which should assign agent
      const messageRequest: MessageRequest = {
        message: {
          from: 'test-customer-persist',
          body: 'I need assistance',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'test-conv-agent-persist',
      };

      const result = await sessionService.handleMessage(messageRequest);
      expect(result.success).toBe(true);

      // Verify agent assignment persisted
      const updatedSession = await sessionService.getSessionByConversationId('test-conv-agent-persist');
      
      // Check if agent was assigned (mock implementation may or may not assign)
      if (updatedSession?.agentId) {
        expect(updatedSession.status).toBe('active');
        expect(['agent-001', 'agent-002', 'agent-003']).toContain(updatedSession.agentId);

        // Verify persistence by retrieving session directly
        const directSession = await sessionService.getSession(updatedSession.id);
        expect(directSession?.agentId).toBe(updatedSession.agentId);
        expect(directSession?.status).toBe('active');
      }
    });

    it('should handle Redis TTL refresh during updates', async () => {
      // Create session
      const session = await sessionService.createSession({
        conversationId: 'test-conv-ttl-refresh',
        customerId: 'test-customer-ttl',
        channel: 'whatsapp',
      });

      // Check initial TTL
      const initialTtl = await redisClient.ttl(`session:${session.id}`);
      expect(initialTtl).toBeGreaterThan(3500);

      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 100));

      // Handle message (which might update the session)
      const messageRequest: MessageRequest = {
        message: {
          from: 'test-customer-ttl',
          body: 'Testing TTL refresh',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'test-conv-ttl-refresh',
      };

      await sessionService.handleMessage(messageRequest);

      // Check TTL after potential update
      const updatedTtl = await redisClient.ttl(`session:${session.id}`);
      expect(updatedTtl).toBeGreaterThan(3500);
      
      // TTL should be close to original (within 2 seconds difference due to agent assignment update)
      expect(Math.abs(updatedTtl - initialTtl)).toBeLessThan(2);
    });
  });

  (REDIS_AVAILABLE ? describe : describe.skip)('Redis Connection Health', () => {
    it('should connect to Redis successfully', async () => {
      expect(redisClient.isReady).toBe(true);
    });

    it('should handle Redis ping/pong', async () => {
      const pong = await redisClient.ping();
      expect(pong).toBe('PONG');
    });

    it('should handle Redis info command', async () => {
      const info = await redisClient.info();
      expect(info).toContain('redis_version');
    });
  });

  // Always run this test to show that integration tests are skipped
  it('should skip Redis integration tests when Redis is not available', () => {
    if (!REDIS_AVAILABLE) {
      console.log('Redis integration tests skipped - set REDIS_TEST_ENABLED=true to enable');
    }
    expect(true).toBe(true); // Always passes
  });
});