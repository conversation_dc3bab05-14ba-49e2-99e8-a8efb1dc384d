"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const sessionService_1 = require("../src/sessionService");
// Mock the redis module with Jest factory function
jest.mock('../src/redis', () => ({
    redisClient: {
        setEx: jest.fn(),
        get: jest.fn(),
        del: jest.fn(),
    },
}));
// Get the mocked redis client
const redis_1 = require("../src/redis");
const mockRedisClient = redis_1.redisClient;
describe('SessionService', () => {
    let sessionService;
    beforeEach(() => {
        sessionService = new sessionService_1.SessionService();
        jest.clearAllMocks();
    });
    describe('createSession', () => {
        it('should create a new session with correct properties', async () => {
            const createRequest = {
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
            };
            mockRedisClient.setEx.mockResolvedValue('OK');
            const result = await sessionService.createSession(createRequest);
            expect(result).toMatchObject({
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
                status: 'queued',
            });
            expect(result.id).toContain('conv-123');
            expect(result.createdAt).toBeDefined();
            expect(result.updatedAt).toBeDefined();
        });
        it('should store session in Redis with correct TTL', async () => {
            const createRequest = {
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'web',
            };
            mockRedisClient.setEx.mockResolvedValue('OK');
            await sessionService.createSession(createRequest);
            expect(mockRedisClient.setEx).toHaveBeenCalledWith(expect.stringMatching(/^session:conv-123-/), 3600, expect.any(String));
        });
        it('should handle Redis errors during creation', async () => {
            const createRequest = {
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'sms',
            };
            mockRedisClient.setEx.mockRejectedValue(new Error('Redis error'));
            await expect(sessionService.createSession(createRequest)).rejects.toThrow('Redis error');
        });
    });
    describe('getSession', () => {
        it('should return session when it exists', async () => {
            const sessionData = {
                id: 'session-123',
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
                status: 'active',
                agentId: 'agent-789',
                createdAt: '2023-01-01T00:00:00.000Z',
                updatedAt: '2023-01-01T01:00:00.000Z',
            };
            mockRedisClient.get.mockResolvedValue(JSON.stringify(sessionData));
            const result = await sessionService.getSession('session-123');
            expect(result).toEqual(sessionData);
            expect(mockRedisClient.get).toHaveBeenCalledWith('session:session-123');
        });
        it('should return null when session does not exist', async () => {
            mockRedisClient.get.mockResolvedValue(null);
            const result = await sessionService.getSession('non-existent-session');
            expect(result).toBeNull();
        });
        it('should handle Redis errors during get', async () => {
            mockRedisClient.get.mockRejectedValue(new Error('Redis error'));
            await expect(sessionService.getSession('session-123')).rejects.toThrow('Redis error');
        });
        it('should handle invalid JSON in Redis', async () => {
            mockRedisClient.get.mockResolvedValue('invalid-json');
            await expect(sessionService.getSession('session-123')).rejects.toThrow();
        });
    });
    describe('updateSession', () => {
        const existingSession = {
            id: 'session-123',
            conversationId: 'conv-123',
            customerId: 'customer-456',
            channel: 'whatsapp',
            status: 'queued',
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z',
        };
        it('should update existing session with new values', async () => {
            const updates = {
                agentId: 'agent-789',
                status: 'active',
            };
            mockRedisClient.get.mockResolvedValue(JSON.stringify(existingSession));
            mockRedisClient.setEx.mockResolvedValue('OK');
            const result = await sessionService.updateSession('session-123', updates);
            expect(result).toMatchObject({
                id: 'session-123',
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
                createdAt: '2023-01-01T00:00:00.000Z',
                agentId: 'agent-789',
                status: 'active',
            });
            expect(result?.updatedAt).not.toBe(existingSession.updatedAt);
            expect(result?.updatedAt).toBeDefined();
        });
        it('should return null when session does not exist', async () => {
            mockRedisClient.get.mockResolvedValue(null);
            const result = await sessionService.updateSession('non-existent', { status: 'active' });
            expect(result).toBeNull();
            expect(mockRedisClient.setEx).not.toHaveBeenCalled();
        });
        it('should preserve existing values when partial update', async () => {
            const updates = {
                status: 'closed',
            };
            mockRedisClient.get.mockResolvedValue(JSON.stringify(existingSession));
            mockRedisClient.setEx.mockResolvedValue('OK');
            const result = await sessionService.updateSession('session-123', updates);
            expect(result?.conversationId).toBe(existingSession.conversationId);
            expect(result?.customerId).toBe(existingSession.customerId);
            expect(result?.status).toBe('closed');
        });
    });
    describe('deleteSession', () => {
        it('should return true when session is successfully deleted', async () => {
            mockRedisClient.del.mockResolvedValue(1);
            const result = await sessionService.deleteSession('session-123');
            expect(result).toBe(true);
            expect(mockRedisClient.del).toHaveBeenCalledWith('session:session-123');
        });
        it('should return false when session does not exist', async () => {
            mockRedisClient.del.mockResolvedValue(0);
            const result = await sessionService.deleteSession('non-existent');
            expect(result).toBe(false);
        });
        it('should handle Redis errors during deletion', async () => {
            mockRedisClient.del.mockRejectedValue(new Error('Redis error'));
            await expect(sessionService.deleteSession('session-123')).rejects.toThrow('Redis error');
        });
    });
});
