// Test setup and configuration for Session Manager
// import { config } from '../../src/config';

// Test configuration
export const testConfig = {
  redis: {
    host: 'localhost',
    port: 6379,
  },
  timeout: 10000, // 10 seconds timeout for tests
};

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.REDIS_HOST = testConfig.redis.host;
process.env.REDIS_PORT = testConfig.redis.port.toString();
process.env.SESSION_MANAGER_PORT = '3001';

// Global test setup
beforeAll(async () => {
  // Any global setup needed for tests
});

afterAll(async () => {
  // Any global cleanup needed for tests
});

// Mock console methods to reduce noise in test output
global.console = {
  ...console,
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
};

export default testConfig;