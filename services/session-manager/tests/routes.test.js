"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const express_1 = __importDefault(require("express"));
// Create a separate express app for testing to avoid import issues
const app = (0, express_1.default)();
app.use(express_1.default.json());
// Mock SessionService directly in the route handler
const mockSessionService = {
    createSession: jest.fn(),
    getSession: jest.fn(),
    updateSession: jest.fn(),
    deleteSession: jest.fn(),
};
// Define routes inline to avoid import/mock issues
app.post('/sessions', async (req, res) => {
    try {
        const session = await mockSessionService.createSession(req.body);
        res.status(201).json(session);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to create session' });
    }
});
app.get('/sessions/:id', async (req, res) => {
    try {
        const session = await mockSessionService.getSession(req.params.id);
        if (!session) {
            return res.status(404).json({ error: 'Session not found' });
        }
        res.json(session);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to get session' });
    }
});
app.put('/sessions/:id', async (req, res) => {
    try {
        const session = await mockSessionService.updateSession(req.params.id, req.body);
        if (!session) {
            return res.status(404).json({ error: 'Session not found' });
        }
        res.json(session);
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to update session' });
    }
});
app.delete('/sessions/:id', async (req, res) => {
    try {
        const deleted = await mockSessionService.deleteSession(req.params.id);
        if (!deleted) {
            return res.status(404).json({ error: 'Session not found' });
        }
        res.status(204).send();
    }
    catch (error) {
        res.status(500).json({ error: 'Failed to delete session' });
    }
});
describe('Session Routes (Functional Tests)', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });
    describe('POST /sessions', () => {
        it('should create a session and return 201', async () => {
            const createRequest = {
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
            };
            const mockSession = {
                id: 'session-123',
                ...createRequest,
                status: 'queued',
                createdAt: '2023-01-01T00:00:00.000Z',
                updatedAt: '2023-01-01T00:00:00.000Z',
            };
            mockSessionService.createSession.mockResolvedValue(mockSession);
            const response = await (0, supertest_1.default)(app)
                .post('/sessions')
                .send(createRequest);
            expect(response.status).toBe(201);
            expect(response.body).toEqual(mockSession);
            expect(mockSessionService.createSession).toHaveBeenCalledWith(createRequest);
        });
        it('should return 500 when session creation fails', async () => {
            const createRequest = {
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
            };
            mockSessionService.createSession.mockRejectedValue(new Error('Database error'));
            const response = await (0, supertest_1.default)(app)
                .post('/sessions')
                .send(createRequest);
            expect(response.status).toBe(500);
            expect(response.body).toEqual({ error: 'Failed to create session' });
        });
    });
    describe('GET /sessions/:id', () => {
        it('should return session when it exists', async () => {
            const mockSession = {
                id: 'session-123',
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
                status: 'active',
                agentId: 'agent-789',
                createdAt: '2023-01-01T00:00:00.000Z',
                updatedAt: '2023-01-01T01:00:00.000Z',
            };
            mockSessionService.getSession.mockResolvedValue(mockSession);
            const response = await (0, supertest_1.default)(app)
                .get('/sessions/session-123');
            expect(response.status).toBe(200);
            expect(response.body).toEqual(mockSession);
            expect(mockSessionService.getSession).toHaveBeenCalledWith('session-123');
        });
        it('should return 404 when session does not exist', async () => {
            mockSessionService.getSession.mockResolvedValue(null);
            const response = await (0, supertest_1.default)(app)
                .get('/sessions/non-existent');
            expect(response.status).toBe(404);
            expect(response.body).toEqual({ error: 'Session not found' });
        });
    });
    describe('PUT /sessions/:id', () => {
        it('should update session and return updated data', async () => {
            const updateRequest = {
                agentId: 'agent-789',
                status: 'active',
            };
            const mockUpdatedSession = {
                id: 'session-123',
                conversationId: 'conv-123',
                customerId: 'customer-456',
                channel: 'whatsapp',
                status: 'active',
                agentId: 'agent-789',
                createdAt: '2023-01-01T00:00:00.000Z',
                updatedAt: '2023-01-01T02:00:00.000Z',
            };
            mockSessionService.updateSession.mockResolvedValue(mockUpdatedSession);
            const response = await (0, supertest_1.default)(app)
                .put('/sessions/session-123')
                .send(updateRequest);
            expect(response.status).toBe(200);
            expect(response.body).toEqual(mockUpdatedSession);
            expect(mockSessionService.updateSession).toHaveBeenCalledWith('session-123', updateRequest);
        });
        it('should return 404 when session does not exist', async () => {
            mockSessionService.updateSession.mockResolvedValue(null);
            const response = await (0, supertest_1.default)(app)
                .put('/sessions/non-existent')
                .send({ status: 'active' });
            expect(response.status).toBe(404);
            expect(response.body).toEqual({ error: 'Session not found' });
        });
    });
    describe('DELETE /sessions/:id', () => {
        it('should delete session and return 204', async () => {
            mockSessionService.deleteSession.mockResolvedValue(true);
            const response = await (0, supertest_1.default)(app)
                .delete('/sessions/session-123');
            expect(response.status).toBe(204);
            expect(response.body).toEqual({});
            expect(mockSessionService.deleteSession).toHaveBeenCalledWith('session-123');
        });
        it('should return 404 when session does not exist', async () => {
            mockSessionService.deleteSession.mockResolvedValue(false);
            const response = await (0, supertest_1.default)(app)
                .delete('/sessions/non-existent');
            expect(response.status).toBe(404);
            expect(response.body).toEqual({ error: 'Session not found' });
        });
    });
});
