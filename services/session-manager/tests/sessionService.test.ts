import { SessionService } from '../src/sessionService';
import { Session, CreateSessionRequest, UpdateSessionRequest, MessageRequest } from '../src/types';

// Mock the redis module with Jest factory function
jest.mock('../src/redis', () => ({
  redisClient: {
    setEx: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
  },
}));

// Get the mocked redis client
import { redisClient } from '../src/redis';
const mockRedisClient = redisClient as jest.Mocked<typeof redisClient>;

describe('SessionService', () => {
  let sessionService: SessionService;

  beforeEach(() => {
    sessionService = new SessionService();
    jest.clearAllMocks();
    // Mock console methods to prevent output during tests
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('createSession', () => {
    it('should create a new session with correct properties', async () => {
      const createRequest: CreateSessionRequest = {
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp',
      };

      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.createSession(createRequest);

      expect(result).toMatchObject({
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp',
        status: 'queued',
      });
      expect(result.id).toContain('conv-123');
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('should store session in Redis with correct TTL', async () => {
      const createRequest: CreateSessionRequest = {
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'web',
      };

      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      await sessionService.createSession(createRequest);

      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        expect.stringMatching(/^session:conv-123-/),
        3600,
        expect.any(String)
      );
    });

    it('should handle Redis errors during creation', async () => {
      const createRequest: CreateSessionRequest = {
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'sms',
      };

      mockRedisClient.setEx.mockRejectedValue(new Error('Redis error'));

      await expect(sessionService.createSession(createRequest)).rejects.toThrow('Redis error');
    });
  });

  describe('getSession', () => {
    it('should return session when it exists', async () => {
      const sessionData: Session = {
        id: 'session-123',
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp',
        status: 'active',
        agentId: 'agent-789',
        createdAt: '2023-01-01T00:00:00.000Z',
        updatedAt: '2023-01-01T01:00:00.000Z',
      };

      mockRedisClient.get.mockResolvedValue(JSON.stringify(sessionData) as never);

      const result = await sessionService.getSession('session-123');

      expect(result).toEqual(sessionData);
      expect(mockRedisClient.get).toHaveBeenCalledWith('session:session-123');
    });

    it('should return null when session does not exist', async () => {
      mockRedisClient.get.mockResolvedValue(null as never);

      const result = await sessionService.getSession('non-existent-session');

      expect(result).toBeNull();
    });

    it('should handle Redis errors during get', async () => {
      mockRedisClient.get.mockRejectedValue(new Error('Redis error'));

      await expect(sessionService.getSession('session-123')).rejects.toThrow('Redis error');
    });

    it('should handle invalid JSON in Redis', async () => {
      mockRedisClient.get.mockResolvedValue('invalid-json' as never);

      await expect(sessionService.getSession('session-123')).rejects.toThrow();
    });
  });

  describe('updateSession', () => {
    const existingSession: Session = {
      id: 'session-123',
      conversationId: 'conv-123',
      customerId: 'customer-456',
      channel: 'whatsapp',
      status: 'queued',
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
    };

    it('should update existing session with new values', async () => {
      const updates: UpdateSessionRequest = {
        agentId: 'agent-789',
        status: 'active',
      };

      mockRedisClient.get.mockResolvedValue(JSON.stringify(existingSession) as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.updateSession('session-123', updates);

      expect(result).toMatchObject({
        id: 'session-123',
        conversationId: 'conv-123',
        customerId: 'customer-456',
        channel: 'whatsapp',
        createdAt: '2023-01-01T00:00:00.000Z',
        agentId: 'agent-789',
        status: 'active',
      });
      expect(result?.updatedAt).not.toBe(existingSession.updatedAt);
      expect(result?.updatedAt).toBeDefined();
    });

    it('should return null when session does not exist', async () => {
      mockRedisClient.get.mockResolvedValue(null as never);

      const result = await sessionService.updateSession('non-existent', { status: 'active' });

      expect(result).toBeNull();
      expect(mockRedisClient.setEx).not.toHaveBeenCalled();
    });

    it('should preserve existing values when partial update', async () => {
      const updates: UpdateSessionRequest = {
        status: 'closed',
      };

      mockRedisClient.get.mockResolvedValue(JSON.stringify(existingSession) as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.updateSession('session-123', updates);

      expect(result?.conversationId).toBe(existingSession.conversationId);
      expect(result?.customerId).toBe(existingSession.customerId);
      expect(result?.status).toBe('closed');
    });
  });

  describe('deleteSession', () => {
    it('should return true when session is successfully deleted', async () => {
      mockRedisClient.del.mockResolvedValue(1 as never);

      const result = await sessionService.deleteSession('session-123');

      expect(result).toBe(true);
      expect(mockRedisClient.del).toHaveBeenCalledWith('session:session-123');
    });

    it('should return false when session does not exist', async () => {
      mockRedisClient.del.mockResolvedValue(0 as never);

      const result = await sessionService.deleteSession('non-existent');

      expect(result).toBe(false);
    });

    it('should handle Redis errors during deletion', async () => {
      mockRedisClient.del.mockRejectedValue(new Error('Redis error'));

      await expect(sessionService.deleteSession('session-123')).rejects.toThrow('Redis error');
    });
  });

  describe('getSessionByConversationId', () => {
    const mockSession: Session = {
      id: 'session-123',
      conversationId: 'conv-456',
      customerId: 'customer-789',
      channel: 'whatsapp',
      status: 'active',
      agentId: 'agent-001',
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T01:00:00.000Z',
    };

    it('should return session when conversation ID exists', async () => {
      const mockKeys = ['session:session-123', 'session:session-456'];
      mockRedisClient.keys.mockResolvedValue(mockKeys as never);
      mockRedisClient.get
        .mockResolvedValueOnce(JSON.stringify(mockSession) as never)
        .mockResolvedValueOnce(JSON.stringify({ ...mockSession, conversationId: 'conv-other' }) as never);

      const result = await sessionService.getSessionByConversationId('conv-456');

      expect(result).toEqual(mockSession);
      expect(mockRedisClient.keys).toHaveBeenCalledWith('session:*');
    });

    it('should return null when conversation ID does not exist', async () => {
      const mockKeys = ['session:session-123'];
      mockRedisClient.keys.mockResolvedValue(mockKeys as never);
      mockRedisClient.get.mockResolvedValue(JSON.stringify({ ...mockSession, conversationId: 'conv-other' }) as never);

      const result = await sessionService.getSessionByConversationId('conv-not-found');

      expect(result).toBeNull();
    });

    it('should return null when no sessions exist', async () => {
      mockRedisClient.keys.mockResolvedValue([] as never);

      const result = await sessionService.getSessionByConversationId('conv-456');

      expect(result).toBeNull();
    });

    it('should handle Redis errors during search', async () => {
      mockRedisClient.keys.mockRejectedValue(new Error('Redis keys error'));

      await expect(sessionService.getSessionByConversationId('conv-456')).rejects.toThrow('Redis keys error');
    });

    it('should handle invalid JSON data in sessions during search', async () => {
      const mockKeys = ['session:session-123'];
      mockRedisClient.keys.mockResolvedValue(mockKeys as never);
      mockRedisClient.get.mockResolvedValue('invalid-json' as never);

      // Should not throw but continue to search other sessions
      const result = await sessionService.getSessionByConversationId('conv-456');

      expect(result).toBeNull();
      
      // Verify it logs the warning for corrupted data
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Skipping corrupted session data'),
        expect.any(SyntaxError)
      );
    });

    it('should skip null entries during search', async () => {
      const mockKeys = ['session:session-123', 'session:session-456'];
      mockRedisClient.keys.mockResolvedValue(mockKeys as never);
      mockRedisClient.get
        .mockResolvedValueOnce(null as never)
        .mockResolvedValueOnce(JSON.stringify(mockSession) as never);

      const result = await sessionService.getSessionByConversationId('conv-456');

      expect(result).toEqual(mockSession);
    });

    it('should handle large number of sessions efficiently', async () => {
      const mockKeys = Array.from({ length: 100 }, (_, i) => `session:session-${i}`);
      const targetSession = { ...mockSession, id: 'session-50' };
      
      mockRedisClient.keys.mockResolvedValue(mockKeys as never);
      
      // Mock all get calls to return different sessions, with the target at position 50
      mockRedisClient.get.mockImplementation(((key: string) => {
        if (key === 'session:session-50') {
          return Promise.resolve(JSON.stringify(targetSession));
        }
        return Promise.resolve(JSON.stringify({ ...mockSession, id: key.split(':')[1], conversationId: 'conv-other' }));
      }) as any);

      const result = await sessionService.getSessionByConversationId('conv-456');

      expect(result).toEqual(targetSession);
      expect(mockRedisClient.get).toHaveBeenCalledTimes(51); // Should stop when found
    });
  });

  describe('handleMessage', () => {
    const mockMessageRequest: MessageRequest = {
      message: {
        from: '+1234567890',
        body: 'Hello, I need help',
        timestamp: '2023-01-01T12:00:00.000Z',
      },
      conversationId: 'conv-123',
    };

    const existingSession: Session = {
      id: 'session-123',
      conversationId: 'conv-123',
      customerId: '+1234567890',
      channel: 'whatsapp',
      status: 'queued',
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
    };

    it('should use existing session when conversation ID exists', async () => {
      // Mock getSessionByConversationId to return existing session
      mockRedisClient.keys.mockResolvedValue(['session:session-123'] as never);
      mockRedisClient.get.mockResolvedValue(JSON.stringify(existingSession) as never);

      const result = await sessionService.handleMessage(mockMessageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe('session-123');
      expect(result.message).toBe('Message processed successfully');
    });

    it('should create new session when conversation ID does not exist', async () => {
      // Mock getSessionByConversationId to return null (no existing session)
      mockRedisClient.keys.mockResolvedValue([] as never);
      
      // Mock createSession
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.handleMessage(mockMessageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('conv-123');
      expect(result.message).toBe('Message processed successfully');
      expect(mockRedisClient.setEx).toHaveBeenCalled();
    });

    it('should update session timestamp when handling message', async () => {
      const queuedSession = { ...existingSession, status: 'queued' as const };
      
      // Mock finding queued session
      mockRedisClient.keys.mockResolvedValue(['session:session-123'] as never);
      mockRedisClient.get
        .mockResolvedValueOnce(JSON.stringify(queuedSession) as never) // for getSessionByConversationId
        .mockResolvedValueOnce(JSON.stringify(queuedSession) as never); // for updateSession
      
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.handleMessage(mockMessageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe('session-123');
      
      // Verify updateSession was called to keep session alive
      expect(mockRedisClient.setEx).toHaveBeenCalled();
    });

    it('should not reassign agent when session is already active', async () => {
      const activeSession = { ...existingSession, status: 'active' as const, agentId: 'agent-002' };
      
      // Mock finding active session
      mockRedisClient.keys.mockResolvedValue(['session:session-123'] as never);
      mockRedisClient.get.mockResolvedValue(JSON.stringify(activeSession) as never);

      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.handleMessage(mockMessageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe('session-123');
      expect(result.message).toBe('Message processed successfully');
    });

    it('should handle errors during message processing', async () => {
      mockRedisClient.keys.mockRejectedValue(new Error('Redis connection failed'));

      const result = await sessionService.handleMessage(mockMessageRequest);

      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to handle message');
    });

    it('should handle session creation errors', async () => {
      // Mock no existing session
      mockRedisClient.keys.mockResolvedValue([] as never);
      
      // Mock createSession to fail
      mockRedisClient.setEx.mockRejectedValue(new Error('Redis error'));

      const result = await sessionService.handleMessage(mockMessageRequest);

      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to handle message');
    });

    it('should handle different message formats', async () => {
      const smsMessageRequest: MessageRequest = {
        message: {
          from: '+9876543210',
          body: 'SMS message content',
          timestamp: '2023-01-01T13:00:00.000Z',
        },
        conversationId: 'sms-conv-456',
      };

      // Mock no existing session
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.handleMessage(smsMessageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('sms-conv-456');
      
      // Verify session was created with SMS message data
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        expect.stringMatching(/^session:sms-conv-456-/),
        3600,
        expect.stringContaining('"customerId":"+9876543210"')
      );
    });

    it('should handle empty message body', async () => {
      const emptyMessageRequest: MessageRequest = {
        message: {
          from: '+1234567890',
          body: '',
          timestamp: '2023-01-01T12:00:00.000Z',
        },
        conversationId: 'conv-empty',
      };

      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const result = await sessionService.handleMessage(emptyMessageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('conv-empty');
    });
  });

  describe('session lifecycle management', () => {
    it('should focus only on Redis session management', () => {
      // Session Manager now only manages Redis sessions, no agent assignment
      expect(sessionService).toBeDefined();
      expect(typeof sessionService.createSession).toBe('function');
      expect(typeof sessionService.updateSession).toBe('function');
      expect(typeof sessionService.deleteSession).toBe('function');
    });
  });
});