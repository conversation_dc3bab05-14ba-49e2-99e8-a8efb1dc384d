import { SessionService } from '../src/sessionService';
import { MessageRequest } from '../src/types';

// Mock the redis module with optimized mocks for performance testing
jest.mock('../src/redis', () => ({
  redisClient: {
    setEx: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    keys: jest.fn(),
  },
}));

import { redisClient } from '../src/redis';
const mockRedisClient = redisClient as jest.Mocked<typeof redisClient>;

describe('Performance and Edge Case Tests', () => {
  let sessionService: SessionService;

  beforeEach(() => {
    sessionService = new SessionService();
    jest.clearAllMocks();
    
    // Mock console methods to prevent output during tests
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Performance Tests', () => {
    it('should handle high volume message processing efficiently', async () => {
      const startTime = Date.now();
      const messageCount = 100;
      
      // Mock Redis responses for performance
      mockRedisClient.keys.mockResolvedValue([] as never); // No existing sessions
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequests: MessageRequest[] = Array.from({ length: messageCount }, (_, i) => ({
        message: {
          from: `+performance-${i}`,
          body: `Performance test message ${i}`,
          timestamp: new Date().toISOString(),
        },
        conversationId: `perf-conv-${i}`,
      }));

      // Process all messages
      const results = await Promise.all(
        messageRequests.map(req => sessionService.handleMessage(req))
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      // All should succeed
      expect(results.every(r => r.success)).toBe(true);
      
      // Should process at reasonable speed (less than 5 seconds for 100 messages)
      expect(duration).toBeLessThan(5000);
      
      // Each message should process in reasonable time (average < 50ms)
      const avgTimePerMessage = duration / messageCount;
      expect(avgTimePerMessage).toBeLessThan(50);

      console.log(`Processed ${messageCount} messages in ${duration}ms (avg: ${avgTimePerMessage}ms per message)`);
    });

    it('should handle rapid session lookups efficiently', async () => {
      const lookupCount = 1000;
      const sessionKeys = Array.from({ length: lookupCount }, (_, i) => `session:lookup-${i}`);
      
      // Mock keys to return all session keys
      mockRedisClient.keys.mockResolvedValue(sessionKeys as never);
      
      // Mock get to return different sessions
      mockRedisClient.get.mockImplementation(((key: string) => {
        const index = key.split('-')[1];
        return Promise.resolve(JSON.stringify({
          id: `lookup-${index}`,
          conversationId: `target-conv`, // One matching conversation
          customerId: `customer-${index}`,
          channel: 'whatsapp',
          status: 'queued',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));
      }) as any);

      const startTime = Date.now();
      
      // Search for conversation (should find first match quickly)
      const result = await sessionService.getSessionByConversationId('target-conv');
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(result).toBeDefined();
      expect(result?.conversationId).toBe('target-conv');
      
      // Should find quickly even with many sessions (< 100ms)
      expect(duration).toBeLessThan(100);
      
      // Should stop searching after finding the first match
      expect(mockRedisClient.get).toHaveBeenCalledTimes(1);

      console.log(`Found session among ${lookupCount} in ${duration}ms`);
    });

    it('should handle concurrent session operations without race conditions', async () => {
      const concurrency = 50;
      const conversationId = 'race-condition-test';
      
      // Mock finding no existing session initially
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequests: MessageRequest[] = Array.from({ length: concurrency }, (_, i) => ({
        message: {
          from: '+race-test',
          body: `Concurrent message ${i}`,
          timestamp: new Date(Date.now() + i).toISOString(),
        },
        conversationId,
      }));

      const startTime = Date.now();
      
      // Send all messages concurrently
      const results = await Promise.all(
        messageRequests.map(req => sessionService.handleMessage(req))
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      // All should succeed
      expect(results.every(r => r.success)).toBe(true);
      
      // Should handle concurrency efficiently (< 2 seconds for 50 concurrent operations)
      expect(duration).toBeLessThan(2000);

      console.log(`Handled ${concurrency} concurrent operations in ${duration}ms`);
    });
  });

  describe('Edge Case Tests', () => {
    it('should handle extremely long message bodies', async () => {
      const longMessage = 'x'.repeat(10000); // 10KB message
      
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequest: MessageRequest = {
        message: {
          from: '+long-message-test',
          body: longMessage,
          timestamp: new Date().toISOString(),
        },
        conversationId: 'long-message-conv',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('long-message-conv');
    });

    it('should handle special characters in message data', async () => {
      const specialMessage = '🚀 Héllo Wörld! @#$%^&*()_+{}|:"<>?[];\'\\,./`~';
      
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequest: MessageRequest = {
        message: {
          from: '+special-chars',
          body: specialMessage,
          timestamp: new Date().toISOString(),
        },
        conversationId: 'special-chars-conv',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('special-chars-conv');
    });

    it('should handle very long conversation IDs', async () => {
      const longConversationId = 'very-long-conversation-id-'.repeat(50); // ~1500 chars
      
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequest: MessageRequest = {
        message: {
          from: '+long-conv-id',
          body: 'Test message',
          timestamp: new Date().toISOString(),
        },
        conversationId: longConversationId,
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain(longConversationId.substring(0, 50));
    });

    it('should handle malformed timestamps gracefully', async () => {
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequest: MessageRequest = {
        message: {
          from: '+malformed-timestamp',
          body: 'Test message',
          timestamp: 'invalid-timestamp-format',
        },
        conversationId: 'malformed-timestamp-conv',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('malformed-timestamp-conv');
    });

    it('should handle empty strings in message data', async () => {
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequest: MessageRequest = {
        message: {
          from: '',
          body: '',
          timestamp: '',
        },
        conversationId: 'empty-strings-conv',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('empty-strings-conv');
      
      // Verify session was created with empty customer ID
      expect(mockRedisClient.setEx).toHaveBeenCalledWith(
        expect.stringMatching(/^session:empty-strings-conv-/),
        3600,
        expect.stringContaining('"customerId":""')
      );
    });

    it('should handle null-like values in message data', async () => {
      mockRedisClient.keys.mockResolvedValue([] as never);
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequest: MessageRequest = {
        message: {
          from: 'null',
          body: 'undefined',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'null-values-conv',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toContain('null-values-conv');
    });

    it('should handle Redis timeout scenarios', async () => {
      // Simulate slow Redis response
      mockRedisClient.keys.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([] as never), 100))
      );
      mockRedisClient.setEx.mockResolvedValue('OK' as never);

      const messageRequest: MessageRequest = {
        message: {
          from: '+timeout-test',
          body: 'Timeout test message',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'timeout-test-conv',
      };

      const startTime = Date.now();
      const result = await sessionService.handleMessage(messageRequest);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(endTime - startTime).toBeGreaterThan(100); // Should wait for Redis
    });

    it('should handle Redis connection failures gracefully', async () => {
      mockRedisClient.keys.mockRejectedValue(new Error('ECONNREFUSED'));

      const messageRequest: MessageRequest = {
        message: {
          from: '+connection-fail',
          body: 'Connection failure test',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'connection-fail-conv',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to handle message');
    });

    it('should handle corrupted session data during lookup', async () => {
      const corruptedSessionKeys = ['session:corrupt-1', 'session:corrupt-2'];
      mockRedisClient.keys.mockResolvedValue(corruptedSessionKeys as never);
      
      // First session returns corrupted JSON, second returns valid session
      mockRedisClient.get
        .mockResolvedValueOnce('{"invalid": json}' as never) // Invalid JSON
        .mockResolvedValueOnce(JSON.stringify({
          id: 'corrupt-2',
          conversationId: 'target-conv',
          customerId: 'customer-1',
          channel: 'whatsapp',
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }) as never);

      const result = await sessionService.getSessionByConversationId('target-conv');

      expect(result).toBeDefined();
      expect(result?.id).toBe('corrupt-2');
      expect(result?.conversationId).toBe('target-conv');
      
      // Verify it logs the warning for corrupted data
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Skipping corrupted session data'),
        expect.any(SyntaxError)
      );
    });

    it('should handle mixed valid and invalid session data', async () => {
      const mixedSessionKeys = ['session:valid-1', 'session:null-1', 'session:invalid-1'];
      mockRedisClient.keys.mockResolvedValue(mixedSessionKeys as never);
      
      mockRedisClient.get
        .mockResolvedValueOnce(JSON.stringify({
          id: 'valid-1',
          conversationId: 'other-conv', // Different conversation ID
          customerId: 'customer-1',
          channel: 'whatsapp',
          status: 'active',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }) as never)
        .mockResolvedValueOnce(null as never) // Null data
        .mockResolvedValueOnce('invalid json' as never); // Invalid JSON

      const result = await sessionService.getSessionByConversationId('target-conv');

      expect(result).toBeNull(); // Should not find the target conversation
      expect(mockRedisClient.get).toHaveBeenCalledTimes(3); // Should process all keys
      
      // Verify it logs the warning for invalid JSON
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Skipping corrupted session data'),
        expect.any(SyntaxError)
      );
    });
  });

  describe('Memory and Resource Tests', () => {
    it('should not leak memory during repeated operations', async () => {
      const iterations = 100;

      for (let i = 0; i < iterations; i++) {
        // Reset mocks for each iteration to prevent interference
        mockRedisClient.keys.mockResolvedValue([] as never);
        mockRedisClient.setEx.mockResolvedValue('OK' as never);
        
        const messageRequest: MessageRequest = {
          message: {
            from: `+memory-test-${i}`,
            body: `Memory test ${i}`,
            timestamp: new Date().toISOString(),
          },
          conversationId: `memory-conv-${i}`,
        };

        const result = await sessionService.handleMessage(messageRequest);
        expect(result.success).toBe(true);

        // Clear mocks to prevent memory buildup
        if (i % 10 === 0) {
          jest.clearAllMocks();
        }
      }

      // Test should complete without memory issues
      expect(true).toBe(true);
    });

    it('should handle cleanup of expired sessions concept', async () => {
      // This tests the concept - in real Redis, TTL handles expiration
      const expiredSessionId = 'expired-session-123';
      
      // Mock Redis to return null (expired)
      mockRedisClient.get.mockResolvedValue(null as never);

      const result = await sessionService.getSession(expiredSessionId);
      
      expect(result).toBeNull();
      expect(mockRedisClient.get).toHaveBeenCalledWith(`session:${expiredSessionId}`);
    });
  });

  describe('Agent Assignment Edge Cases', () => {
    it('should handle agent assignment when no agents available', async () => {
      const mockSession = {
        id: 'no-agent-session',
        conversationId: 'no-agent-conv',
        customerId: 'customer-123',
        channel: 'whatsapp' as const,
        status: 'queued' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Mock assignAgent to return null (no agents)
      jest.spyOn(sessionService as any, 'assignAgent').mockResolvedValue(null);

      mockRedisClient.keys.mockResolvedValue(['session:no-agent-session'] as never);
      mockRedisClient.get.mockResolvedValue(JSON.stringify(mockSession) as never);

      const messageRequest: MessageRequest = {
        message: {
          from: 'customer-123',
          body: 'Need help but no agents available',
          timestamp: new Date().toISOString(),
        },
        conversationId: 'no-agent-conv',
      };

      const result = await sessionService.handleMessage(messageRequest);

      expect(result.success).toBe(true);
      expect(result.sessionId).toBe('no-agent-session');
      // Session should remain queued when no agent is available
    });

    it('should handle rapid agent assignment calls', async () => {
      const sessions = Array.from({ length: 10 }, (_, i) => ({
        id: `rapid-agent-${i}`,
        conversationId: `rapid-conv-${i}`,
        customerId: `customer-${i}`,
        channel: 'whatsapp' as const,
        status: 'queued' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }));

      const assignAgent = (sessionService as any).assignAgent;
      
      // Call assignAgent rapidly
      const agentPromises = sessions.map(session => assignAgent(session));
      const assignedAgents = await Promise.all(agentPromises);

      // All should get an agent (even if same agent)
      assignedAgents.forEach(agent => {
        expect(['agent-001', 'agent-002', 'agent-003']).toContain(agent);
      });
    });
  });
});