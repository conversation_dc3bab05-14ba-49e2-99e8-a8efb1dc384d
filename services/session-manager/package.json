{"name": "@cx-system/session-manager", "version": "1.0.0", "private": true, "description": "Session Manager service for CX System", "main": "dist/server.js", "scripts": {"build": "npm install && tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration"}, "dependencies": {"dotenv": "^16.3.1", "express": "^4.18.2", "redis": "^5.8.1", "winston": "^3.17.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.11.0", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "redis-memory-server": "^0.10.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "tsx": "^4.7.0", "typescript": "^5.3.3"}}