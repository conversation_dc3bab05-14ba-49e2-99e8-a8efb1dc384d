import { RoutingService } from '../src/routingService';
import { IncomingMessage } from '../src/types';

// Mock axios for testing
jest.mock('axios', () => ({
  post: jest.fn()
}));

// Import axios after mocking
import axios from 'axios';
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('RoutingService', () => {
  let routingService: RoutingService;

  beforeEach(() => {
    routingService = new RoutingService();
    jest.clearAllMocks();
  });

  const mockMessage: IncomingMessage = {
    id: 'msg_12345',
    from: '+**********',
    to: '+**********',
    body: 'Mi tarjeta no funciona',
    channel: 'whatsapp',
    timestamp: new Date()
  };

  describe('routeMessage', () => {
    it('should route new message successfully', async () => {
      // Mock successful session manager response
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, sessionId: 'session_123' }
      });

      const result = await routingService.routeMessage(mockMessage);

      expect(result.success).toBe(true);
      expect(result.conversationId).toBe('new'); // New conversations return 'new'
      expect(result.routedTo).toBe('session_manager');
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/sessions/message'),
        expect.objectContaining({
          message: mockMessage,
          conversationId: undefined, // New conversation
          departmentInfo: expect.any(Object)
        }),
        expect.any(Object)
      );
    });

    it('should route existing conversation successfully', async () => {
      const existingConversationId = 'conv_existing_123';
      
      // Mock successful session manager response
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, sessionId: 'session_123' }
      });

      const result = await routingService.routeMessage(mockMessage, existingConversationId);

      expect(result.success).toBe(true);
      expect(result.conversationId).toBe(existingConversationId);
      expect(result.routedTo).toBe('session_manager');
      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/sessions/message'),
        expect.objectContaining({
          message: mockMessage,
          conversationId: existingConversationId,
          departmentInfo: null // No department analysis for existing conversations
        }),
        expect.any(Object)
      );
    });

    it('should handle session manager communication failure', async () => {
      // Mock failed session manager response
      mockedAxios.post.mockRejectedValueOnce(new Error('Connection failed'));

      const result = await routingService.routeMessage(mockMessage);

      expect(result.success).toBe(false);
      expect(result.message).toContain('Routing failed');
    });

    it('should handle technical support message with department assignment', async () => {
      const techMessage: IncomingMessage = {
        ...mockMessage,
        body: 'Error en la aplicación, no puedo acceder'
      };

      // Mock successful session manager response
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, sessionId: 'session_123' }
      });

      const result = await routingService.routeMessage(techMessage);

      expect(result.success).toBe(true);
      expect(result.conversationId).toBeDefined();
    });

    it('should handle vague message requiring more information', async () => {
      const vageMessage: IncomingMessage = {
        ...mockMessage,
        body: 'hola'
      };

      // Mock successful session manager response
      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, sessionId: 'session_123' }
      });

      const result = await routingService.routeMessage(vageMessage);

      expect(result.success).toBe(true);
      expect(result.routedTo).toBe('session_manager'); // All messages now go to session_manager
    });
  });

  it('should pass department information for new conversations', () => {
    // This is now tested in the first test case through the mock expectations
    expect(true).toBe(true); // Simple placeholder test
  });
});