import { DepartmentService } from '../src/departmentService';
import { AIAnalysisAttempt } from '../src/types';

describe('DepartmentService', () => {
  let departmentService: DepartmentService;

  beforeEach(() => {
    departmentService = new DepartmentService();
  });

  describe('analyzeMessageForDepartment', () => {
    it('should identify technical support messages', async () => {
      const result = await departmentService.analyzeMessageForDepartment('Mi tarjeta no funciona');
      
      expect(result.department).toBe('technical_support');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.needsMoreInfo).toBe(false);
    });

    it('should identify sales messages', async () => {
      const result = await departmentService.analyzeMessageForDepartment('Quiero comprar un producto nuevo');
      
      expect(result.department).toBe('sales');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.needsMoreInfo).toBe(false);
    });

    it('should identify billing messages', async () => {
      const result = await departmentService.analyzeMessageForDepartment('Tengo problemas con mi factura');
      
      expect(result.department).toBe('billing');
      expect(result.confidence).toBeGreaterThan(0.5);
      expect(result.needsMoreInfo).toBe(false);
    });

    it('should request more info for vague messages on first attempt', async () => {
      const result = await departmentService.analyzeMessageForDepartment('hola');
      
      expect(result.needsMoreInfo).toBe(true);
      expect(result.confidence).toBeLessThan(0.5);
    });

    it('should assign to general after multiple vague attempts', async () => {
      const previousAttempts: AIAnalysisAttempt[] = [
        {
          attempt: 1,
          input: 'hola',
          result: 'general',
          confidence: 0.3,
          timestamp: new Date()
        },
        {
          attempt: 2,
          input: 'ayuda',
          result: 'general',
          confidence: 0.3,
          timestamp: new Date()
        }
      ];

      const result = await departmentService.analyzeMessageForDepartment('help', previousAttempts);
      
      expect(result.department).toBe('general');
      expect(result.needsMoreInfo).toBe(false);
    });

    it('should handle complex technical messages', async () => {
      const result = await departmentService.analyzeMessageForDepartment(
        'Tengo un error en la aplicación cuando intento hacer un pago'
      );
      
      expect(result.department).toBe('technical_support');
      expect(result.confidence).toBeGreaterThan(0.5);
    });

    it('should default to general for unknown messages', async () => {
      const result = await departmentService.analyzeMessageForDepartment('mensaje completamente aleatorio');
      
      expect(result.department).toBe('general');
      expect(result.confidence).toBeLessThanOrEqual(0.5);
    });
  });

  describe('requestMoreInformation', () => {
    it('should return a clarification message', async () => {
      const message = await departmentService.requestMoreInformation('conv_12345');
      
      expect(typeof message).toBe('string');
      expect(message.length).toBeGreaterThan(10);
      expect(message).toContain('ayudarte');
    });
  });
});