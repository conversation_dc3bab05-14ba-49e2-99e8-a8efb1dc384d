import request from 'supertest';
import { app } from '../src/server';
import { IncomingMessage } from '../src/types';

// Mock axios to prevent real HTTP calls in tests
jest.mock('axios', () => ({
  post: jest.fn().mockResolvedValue({
    data: { success: true, sessionId: 'test_session' }
  })
}));

describe('Channel Router Routes', () => {
  const mockMessage: IncomingMessage = {
    id: 'msg_12345',
    from: '+**********',
    to: '+**********',
    body: 'Mi tarjeta no funciona',
    channel: 'whatsapp',
    timestamp: new Date()
  };

  describe('GET /api/health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('service', 'channel-router');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('POST /api/route', () => {
    it('should route a valid message successfully', async () => {
      const response = await request(app)
        .post('/api/route')
        .send({ message: mockMessage })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('conversationId');
      expect(response.body).toHaveProperty('routedTo');
    });

    it('should return 400 for invalid message format', async () => {
      const invalidMessage = { ...mockMessage };
      delete (invalidMessage as any).id;

      const response = await request(app)
        .post('/api/route')
        .send({ message: invalidMessage })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid message format');
      expect(response.body).toHaveProperty('required');
    });

    it('should handle missing message', async () => {
      const response = await request(app)
        .post('/api/route')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Invalid message format');
    });
  });

  describe('POST /api/send', () => {
    it('should handle outbound messages', async () => {
      const outboundMessage = {
        message: {
          id: 'out_12345',
          to: '+**********',
          body: 'Respuesta del agente',
          channel: 'whatsapp'
        },
        channel: 'whatsapp'
      };

      const response = await request(app)
        .post('/api/send')
        .send(outboundMessage)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('messageId');
      expect(response.body).toHaveProperty('sentAt');
    });

    it('should return 400 for missing required fields', async () => {
      const response = await request(app)
        .post('/api/send')
        .send({ message: 'test' })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Missing required fields');
    });
  });

  describe('POST /api/analyze-department', () => {
    it('should analyze technical support message correctly', async () => {
      const response = await request(app)
        .post('/api/analyze-department')
        .send({ message: 'Mi tarjeta no funciona' })
        .expect(200);

      expect(response.body).toHaveProperty('department', 'technical_support');
      expect(response.body).toHaveProperty('confidence');
      expect(response.body.confidence).toBeGreaterThan(0.5);
      expect(response.body).toHaveProperty('needsMoreInfo', false);
    });

    it('should request more info for vague messages', async () => {
      const response = await request(app)
        .post('/api/analyze-department')
        .send({ message: 'hola' })
        .expect(200);

      expect(response.body).toHaveProperty('needsMoreInfo', true);
      expect(response.body.confidence).toBeLessThan(0.5);
    });

    it('should analyze sales message correctly', async () => {
      const response = await request(app)
        .post('/api/analyze-department')
        .send({ message: 'Quiero comprar un producto' })
        .expect(200);

      expect(response.body).toHaveProperty('department', 'sales');
      expect(response.body).toHaveProperty('needsMoreInfo', false);
    });
  });

  describe('GET /', () => {
    it('should return service information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toHaveProperty('service', 'Channel Router');
      expect(response.body).toHaveProperty('version', '1.0.0');
      expect(response.body).toHaveProperty('status', 'running');
      expect(response.body).toHaveProperty('endpoints');
    });
  });
});