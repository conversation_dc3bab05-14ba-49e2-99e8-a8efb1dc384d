{"name": "@cx-system/channel-router", "version": "1.0.0", "private": true, "description": "Channel Router service for CX System - Routes messages between channels and manages conversation lifecycle", "main": "dist/server.js", "scripts": {"build": "npm install && tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@google-cloud/pubsub": "^5.2.0", "axios": "^1.11.0", "dotenv": "^16.3.1", "express": "^4.18.2", "winston": "^3.17.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.19.11", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "tsx": "^4.7.0", "typescript": "^5.9.2"}}