import express from 'express';
import { config } from './config';
import { router } from './routes';
import logger from './logger';

const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, res, next) => {
  const sessionId = req.headers['x-session-id'] as string;
  const startTime = Date.now();
  
  logger.info(`Incoming request: ${req.method} ${req.path}`, {
    method: req.method,
    path: req.path,
    sessionId,
    from: req.body?.from || req.query?.from,
    body: req.method === 'POST' ? req.body : undefined
  });
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    logger.info(`Request completed: ${req.method} ${req.path}`, {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      duration,
      sessionId
    });
  });
  
  next();
});

// CORS for local development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  if (req.method === 'OPTIONS') {
    res.sendStatus(200);
  } else {
    next();
  }
});

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Routes
app.use('/api', router);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'Channel Router',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      route: '/api/route',
      send: '/api/send',
      analyzeDepartment: '/api/analyze-department'
    }
  });
});

const server = app.listen(config.port, () => {
  console.log(`🚀 Channel Router service running on port ${config.port}`);
  console.log(`🔗 Chat Realtime URL: ${config.chatRealtimeUrl}`);
  console.log(`🧠 N8N Webhook URL: ${config.n8nWebhookUrl}`);
  console.log(`🎯 Mock N8N Enabled: ${config.departmentAssignment.enableMockN8n}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Process terminated');
  });
});

export { app };