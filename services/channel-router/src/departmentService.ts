import axios from 'axios';
import { config } from './config';
import { AIAnalysisAttempt } from './types';

export interface DepartmentAnalysisResponse {
  convId: string;
  department: string;
  reply?: string;
}

export class DepartmentService {
  
  async analyzeMessageForDepartment(
    convID: string,
    chatMessage: string
  ): Promise<DepartmentAnalysisResponse> {
    
    if (config.departmentAssignment.enableMockN8n) {
      return this.mockAIAnalysis(convID, chatMessage);
    }

    try {
      console.log('🔍 Calling N8N Department Analysis:', { convID, messagePreview: chatMessage.substring(0, 50) });
      
      const response = await axios.post(config.n8nWebhookUrl, {
        convID,
        chatMessage
      }, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ N8N Department Analysis response:', response.data);

      // N8N wraps response in 'output' object
      const outputData = response.data.output || response.data;
      
      // N8N always returns department as string, not array
      return {
        convId: outputData.convId || convID,
        department: outputData.department || 'general',
        reply: outputData.reply || ''
      };
      
    } catch (error) {
      console.warn('N8N analysis failed, using fallback:', error);
      return this.mockAIAnalysis(convID, chatMessage);
    }
  }

  private mockAIAnalysis(
    convID: string,
    chatMessage: string
  ): DepartmentAnalysisResponse {
    
    const lowerMessage = chatMessage.toLowerCase().trim();
    
    // Simple keyword-based mock analysis
    if (lowerMessage.includes('tarjeta') || lowerMessage.includes('no funciona') || 
        lowerMessage.includes('error') || lowerMessage.includes('problema técnico') ||
        lowerMessage.includes('aplicación')) {
      return { 
        convId: convID, 
        department: 'technical_support'
      };
    }
    
    if (lowerMessage.includes('comprar') || lowerMessage.includes('precio') || 
        lowerMessage.includes('cotización') || lowerMessage.includes('vender')) {
      return { 
        convId: convID, 
        department: 'sales'
      };
    }
    
    if (lowerMessage.includes('factura') || lowerMessage.includes('pago') || 
        lowerMessage.includes('cobro') || lowerMessage.includes('billing')) {
      return { 
        convId: convID, 
        department: 'billing'
      };
    }
    
    // Generic greetings or unclear messages - request more info
    if (lowerMessage.length < 10 || 
        ['hola', 'hi', 'hello', 'buenas', 'ayuda', 'help'].includes(lowerMessage)) {
      
      return { 
        convId: convID, 
        department: 'more_info',
        reply: 'Hola, con gusto te ayudamos, pero necesito un poco más de información para poder determinar el mejor experto para ayudarte. ¿Podrías contarme más detalles sobre tu consulta?'
      };
    }
    
    // Default to general department
    return { 
      convId: convID, 
      department: 'general'
    };
  }

  async requestMoreInformation(conversationId: string): Promise<string> {
    // This would typically send a message through the outbound channel
    // For now, we'll return the message that should be sent
    const clarificationMessages = [
      '¡Hola! ¿En qué puedo ayudarte hoy?',
      'Para poder dirigirte al departamento correcto, ¿podrías contarme más detalles sobre tu consulta?',
      'Perfecto, ¿podrías ser más específico sobre lo que necesitas? Así te conectaré con el agente adecuado.'
    ];
    
    // Return different message based on attempt number
    // In real implementation, this would track attempts per conversation
    return clarificationMessages[0];
  }
}