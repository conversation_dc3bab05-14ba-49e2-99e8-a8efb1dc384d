export interface IncomingMessage {
  id: string;
  from: string;
  to: string;
  body: string;
  type?: 'text' | 'media' | 'audio' | 'image' | 'video' | 'document';
  channel: 'whatsapp' | 'web' | 'sms' | 'simulation';
  timestamp: Date;
  mediaUrl?: string;
  mediaType?: string;
  metadata?: TwilioMetadata; // NEW: Twilio Conversations API metadata
}

// NEW: Twilio Conversations API metadata structure
export interface TwilioMetadata {
  twilioMessageSid?: string;      // Original Twilio message ID
  twilioConversationSid?: string; // Original Twilio conversation ID
  messageIndex?: string;          // Message sequence in conversation
  participantSid?: string;        // Twilio participant ID
  source?: string;                // "SMS", "API", "Chat"
  eventType?: string;             // "onMessageAdded", etc.
  retryCount?: string;            // Webhook retry count
  attributes?: string;            // Custom Twilio attributes (JSON string)
}

export interface OutgoingMessage {
  id: string;
  to: string;
  body: string;
  channel: 'whatsapp' | 'web' | 'sms' | 'simulation';
  conversationId: string;
  timestamp: Date;
  mediaUrl?: string;
  mediaType?: string;
}

export interface Conversation {
  id: string;
  customerId: string;
  channel: string;
  status: 'active' | 'waiting' | 'closed';
  assignedDepartment?: string;
  assignedAgent?: string;
  createdAt: Date;
  updatedAt: Date;
  lastMessageAt: Date;
  routingInfo?: RoutingInfo;
}

export interface RoutingInfo {
  assignedDepartment?: string;
  aiAnalysisAttempts: number;
  aiAnalysisHistory: AIAnalysisAttempt[];
  departmentAssignedAt?: Date;
  needsMoreInfo: boolean;
}

export interface AIAnalysisAttempt {
  attempt: number;
  input: string;
  result: string;
  confidence?: number;
  timestamp: Date;
}

export interface RouteMessageRequest {
  message: IncomingMessage;
  conversationId?: string;
}

export interface RouteMessageResponse {
  success: boolean;
  conversationId: string;
  routedTo: 'chat_realtime' | 'department_assignment' | 'pubsub_inbound' | 'error';
  message?: string;
}