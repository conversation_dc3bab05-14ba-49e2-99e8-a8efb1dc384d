import axios from 'axios';
import { config } from './config';
import { DepartmentService, DepartmentAnalysisResponse } from './departmentService';
import { ChatRealtimeService } from './chatRealtimeService';
import { PubSubService } from './pubsubService';
import { 
  IncomingMessage, 
  RouteMessageResponse 
} from './types';

export class RoutingService {
  private departmentService: DepartmentService;
  private chatRealtimeService: ChatRealtimeService;
  private pubsubService: PubSubService;

  constructor() {
    this.departmentService = new DepartmentService();
    this.chatRealtimeService = new ChatRealtimeService();
    this.pubsubService = new PubSubService();
  }

  async routeMessage(
    message: IncomingMessage, 
    existingConversationId?: string
  ): Promise<RouteMessageResponse> {
    
    try {
      if (existingConversationId) {
        // Existing conversation - publish directly to PubSub
        return await this.publishToPubSub(message, existingConversationId);
      }

      // New conversation - handle department analysis loop
      return await this.handleDepartmentAnalysisLoop(message);
      
    } catch (error) {
      console.error('❌ Routing error:', error);
      return {
        success: false,
        conversationId: existingConversationId || '',
        routedTo: 'error',
        message: `Routing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Handle department analysis loop until department is determined
   */
  private async handleDepartmentAnalysisLoop(message: IncomingMessage): Promise<RouteMessageResponse> {
    // Generate conversation ID
    const convId = `conv_${message.from.replace('+', '')}_${Date.now()}`;
    
    console.log('🔄 Starting department analysis loop for new conversation:', convId);

    let analysisResult = await this.departmentService.analyzeMessageForDepartment(
      convId,
      message.body
    );

    // Loop until department is determined (not more_info)
    while (analysisResult.department === 'more_info') {
      console.log('❓ Need more info from customer');
      
      // Send reply to customer asking for more info
      if (analysisResult.reply) {
        await this.pubsubService.sendToChannel({
          to: message.from,
          body: analysisResult.reply,
          channel: message.channel,
          conversationId: convId
        });
      }

      // For this implementation, we'll exit the loop after first more_info
      // In production, we'd wait for customer response and continue the loop
      console.log('⚠️ Department analysis needs more info - defaulting to general');
      analysisResult = {
        convId,
        department: 'general',
        reply: ''
      };
      break;
    }

    console.log(`✅ Department determined: ${analysisResult.department}`);

    // Create conversation in Chat Realtime
    const conversationResult = await this.chatRealtimeService.createConversation(
      convId,
      analysisResult.department,
      message.from,
      message.body
    );

    if (!conversationResult.success) {
      throw new Error(`Failed to create conversation: ${conversationResult.error}`);
    }

    // Publish to PubSub inbound topic for Bot Human Router
    return await this.publishToPubSub(message, convId);
  }

  /**
   * Publish message to PubSub inbound topic
   */
  private async publishToPubSub(message: IncomingMessage, conversationId: string): Promise<RouteMessageResponse> {
    // Get session ID (deterministic based on phone)
    const sessionInfo = await this.pubsubService.getSessionId(message.from);

    // Enrich message for PubSub
    const enrichedMessage = {
      ...message,
      sessionId: sessionInfo.sessionId,
      conversationId,
      timestamp: Date.now(),
      source: 'channel-router'
    };

    // Publish to inbound topic
    const pubsubResult = await this.pubsubService.publishToInbound(enrichedMessage);

    if (pubsubResult.success) {
      console.log('✅ Message published to PubSub inbound topic');
      return {
        success: true,
        conversationId,
        routedTo: 'pubsub_inbound',
        message: 'Message routed to Bot Human Router via PubSub'
      };
    } else {
      throw new Error(`PubSub publish failed: ${pubsubResult.error}`);
    }
  }

}