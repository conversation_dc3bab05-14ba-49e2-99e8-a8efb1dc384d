import axios from 'axios';
import { config } from './config';

export interface CreateConversationRequest {
  convId: string;
  department: string;
  customerInfo: {
    phone: string;
    channel: string;
  };
  initialMessage: {
    body: string;
    timestamp: number;
  };
}

export interface CreateConversationResponse {
  success: boolean;
  conversationId: string;
  error?: string;
}

export class ChatRealtimeService {
  private readonly chatRealtimeUrl: string;

  constructor() {
    this.chatRealtimeUrl = config.chatRealtimeUrl;
  }

  /**
   * Create new conversation in Chat Realtime
   * This is called after department is determined (not more_info)
   */
  async createConversation(
    convId: string,
    department: string,
    customerPhone: string,
    initialMessage: string
  ): Promise<CreateConversationResponse> {
    try {
      console.log('📋 Creating conversation in Chat Realtime:', {
        convId,
        department,
        customerPhone
      });

      const payload: CreateConversationRequest = {
        convId,
        department,
        customerInfo: {
          phone: customerPhone,
          channel: 'whatsapp'
        },
        initialMessage: {
          body: initialMessage,
          timestamp: Date.now()
        }
      };

      const response = await axios.post(
        `${this.chatRealtimeUrl}/api/conversations`,
        payload,
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('✅ Conversation created successfully:', response.data);

      return {
        success: true,
        conversationId: response.data.conversationId || convId
      };
      
    } catch (error) {
      console.error('❌ Failed to create conversation in Chat Realtime:', error);
      
      return {
        success: false,
        conversationId: convId,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Health check for Chat Realtime connection
   */
  async healthCheck(): Promise<{ available: boolean; error?: string }> {
    try {
      const response = await axios.get(`${this.chatRealtimeUrl}/api/health`, {
        timeout: 3000
      });
      
      return { available: response.status === 200 };
    } catch (error) {
      return { 
        available: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }
}