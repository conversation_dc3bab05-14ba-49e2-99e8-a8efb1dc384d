import { Router } from 'express';
import { PubSubService } from './pubsubService';
import { DepartmentService } from './departmentService';
import { IncomingMessage } from './types';

const router = Router();
const pubsubService = new PubSubService();
const departmentService = new DepartmentService();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'channel-router', 
    role: 'message router to PubSub',
    timestamp: new Date().toISOString() 
  });
});

// Main routing endpoint - receives messages from Twilio service
// Only validates and routes to PubSub INBOUND
router.post('/route', async (req, res) => {
  try {
    const { message }: { message: IncomingMessage } = req.body;

    // Basic validation
    if (!message || !message.id || !message.from || !message.body || !message.channel) {
      return res.status(400).json({
        error: 'Invalid message format',
        required: ['message.id', 'message.from', 'message.body', 'message.channel']
      });
    }

    // Filter messages - reject attachments for now (only plain text)
    if (message.type && message.type !== 'text') {
      console.log(`Rejecting message type: ${message.type} from ${message.from}`);
      return res.json({
        success: true,
        action: 'rejected',
        reason: 'Only text messages are supported',
        messageId: message.id
      });
    }

    // Call Session Manager to get/create sessionId
    const sessionData = await pubsubService.getSessionId(message.from);
    
    // Analyze message to determine department
    const departmentAnalysis = await departmentService.analyzeMessageForDepartment(
      sessionData.sessionId, 
      message.body
    );

    console.log('🏢 Department Analysis Result:', departmentAnalysis);

    // If more info is needed, send reply back to user and don't continue to PubSub
    if (departmentAnalysis.department === 'more_info') {
      console.log('📝 Requesting more information from user');
      
      // Send reply message back to Twilio (via outbound channel)
      const replyMessage = {
        id: `reply_${Date.now()}`,
        to: message.from,
        body: departmentAnalysis.reply || 'Necesito más información para ayudarte mejor.',
        channel: message.channel,
        conversationId: sessionData.sessionId,
        timestamp: new Date(),
        isSystemMessage: true
      };

      // Send reply directly to Twilio (immediate response)
      const outboundResult = await pubsubService.sendToChannel(replyMessage);
      
      return res.json({
        success: true,
        action: 'requested_more_info',
        messageId: message.id,
        sessionId: sessionData.sessionId,
        reply: departmentAnalysis.reply,
        outboundPublished: outboundResult.success
      });
    }

    // Enrich message with sessionId and assigned department
    const enrichedMessage = {
      ...message,
      sessionId: sessionData.sessionId,
      assignedDepartment: departmentAnalysis.department,
      timestamp: Date.now()
    };

    // Route to PubSub INBOUND with department assignment
    const result = await pubsubService.publishToInbound(enrichedMessage);
    
    if (result.success) {
      res.json({
        success: true,
        action: 'routed_to_pubsub',
        messageId: message.id,
        sessionId: sessionData.sessionId,
        assignedDepartment: departmentAnalysis.department,
        publishedAt: result.publishedAt
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to route message to PubSub',
        details: result.error
      });
    }
    
  } catch (error) {
    console.error('Route endpoint error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Endpoint for PubSub OUTBOUND Push (receives responses to send back to Twilio)
router.post('/pubsub/outbound', async (req, res) => {
  try {
    // Decode PubSub Push message
    const envelope = req.body;
    
    if (!envelope || !envelope.message) {
      return res.status(400).json({ error: 'Invalid PubSub message format' });
    }

    const pubsubMessage = envelope.message;
    let messageData;

    if (pubsubMessage.data) {
      const decodedData = Buffer.from(pubsubMessage.data, 'base64').toString('utf-8');
      messageData = JSON.parse(decodedData);
    } else {
      return res.status(400).json({ error: 'No message data' });
    }

    console.log('Received outbound message from PubSub:', messageData);

    // Route to appropriate channel service (Twilio for WhatsApp)
    const result = await pubsubService.sendToChannel(messageData);

    if (result.success) {
      // Return 204 to acknowledge PubSub message
      res.status(204).send();
    } else {
      res.status(500).json({ error: 'Failed to send message to channel' });
    }
    
  } catch (error) {
    console.error('PubSub outbound endpoint error:', error);
    res.status(500).json({
      error: 'Failed to process outbound message',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router };