import dotenv from 'dotenv';
import path from 'path';

// Load .env from project root, regardless of working directory
const envPath = path.resolve(__dirname, '../../../.env');
dotenv.config({ path: envPath });

const isProduction = process.env.NODE_ENV === 'production';

const n8nBaseUrl = isProduction 
  ? process.env.N8N_BASE_URL_PRODUCTION! 
  : process.env.N8N_BASE_URL_LOCAL!;
const n8nWebhookPath = isProduction 
  ? process.env.N8N_DEPARTMENT_WEBHOOK_PATH_PRODUCTION! 
  : process.env.N8N_DEPARTMENT_WEBHOOK_PATH_LOCAL!;

export const config = {
  port: parseInt(process.env.CHANNEL_ROUTER_PORT || '3002'),
  chatRealtimeUrl: process.env.CHAT_REALTIME_URL || 'http://localhost:3003',
  
  // N8N configuration for department assignment
  n8nWebhookUrl: `${n8nBaseUrl}${n8nWebhookPath}`,
  
  // Department assignment settings
  departmentAssignment: {
    enableMockN8n: process.env.ENABLE_MOCK_N8N === 'true',
    maxAttempts: 3,
    fallbackDepartment: 'general'
  },
  
  // PubSub configuration (for Cloud Run)
  pubsub: {
    projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || 'cx-system-469120',
    inboundTopic: process.env.PUBSUB_INBOUND_TOPIC || 'inbound-messages',
    outboundTopic: process.env.PUBSUB_OUTBOUND_TOPIC || 'outbound-messages'
  },
  
  // Channel services
  channels: {
    twilioUrl: process.env.TWILIO_URL || 'http://localhost:3005',
    clientSimulatorUrl: process.env.CLIENT_SIMULATOR_URL || 'http://localhost:3008'
  },
  
  // Message filtering
  messageFiltering: {
    allowedTypes: ['text'], // Only text messages for now
    maxMessageLength: 4000,
    blockAttachments: true
  }
};