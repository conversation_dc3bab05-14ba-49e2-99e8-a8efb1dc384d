import axios from 'axios';
import { config } from './config';

interface SessionResponse {
  sessionId: string;
  isNew: boolean;
}

interface PubSubPublishResult {
  success: boolean;
  messageId?: string;
  publishedAt?: string;
  error?: string;
}

interface ChannelSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export class PubSubService {
  private readonly chatRealtimeUrl: string;
  private readonly twilioUrl: string;
  private readonly clientSimulatorUrl: string;

  constructor() {
    this.chatRealtimeUrl = config.chatRealtimeUrl;
    this.twilioUrl = config.channels.twilioUrl;
    this.clientSimulatorUrl = config.channels.clientSimulatorUrl;
  }

  /**
   * Generate deterministic session ID for conversation continuity
   * Note: This bypasses Session Manager as per architecture decision
   */
  async getSessionId(phoneNumber: string): Promise<SessionResponse> {
    // Generate deterministic session ID based on phone number
    // This ensures same conversation continues across messages
    const sessionId = `whatsapp_${phoneNumber.replace('+', '')}`;
    
    return {
      sessionId,
      isNew: false // We don't track "new" since we're stateless
    };
  }

  /**
   * Publish message to PubSub INBOUND topic
   */
  async publishToInbound(enrichedMessage: any): Promise<PubSubPublishResult> {
    try {
      // Use PubSub emulator for development or real PubSub for production
      if (process.env.PUBSUB_USE_EMULATOR === 'true' || process.env.NODE_ENV === 'development') {
        const { PubSub } = require('@google-cloud/pubsub');
        
        const pubSubClient = new PubSub({ 
          projectId: config.pubsub.projectId,
          // Emulator connection is handled by PUBSUB_EMULATOR_HOST env var
        });
        
        const topic = pubSubClient.topic(config.pubsub.inboundTopic);
        const messageBuffer = Buffer.from(JSON.stringify(enrichedMessage));
        
        const [messageId] = await topic.publish(messageBuffer);
        
        console.log('📤 Published to INBOUND PubSub:', {
          topic: config.pubsub.inboundTopic,
          messageId,
          sessionId: enrichedMessage.sessionId,
          from: enrichedMessage.from
        });

        return {
          success: true,
          messageId: messageId,
          publishedAt: new Date().toISOString()
        };
      }

      // Production PubSub implementation
      const { PubSub } = require('@google-cloud/pubsub');
      const pubSubClient = new PubSub({ projectId: config.pubsub.projectId });
      const topic = pubSubClient.topic(config.pubsub.inboundTopic);
      const messageBuffer = Buffer.from(JSON.stringify(enrichedMessage));
      const [messageId] = await topic.publish(messageBuffer);

      return {
        success: true,
        messageId: messageId,
        publishedAt: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('Failed to publish to PubSub INBOUND:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown PubSub error'
      };
    }
  }

  /**
   * Send message to appropriate channel (Twilio for WhatsApp)
   */
  async sendToChannel(messageData: any): Promise<ChannelSendResult> {
    try {
      const { to, body, channel, conversationId, metadata } = messageData;

      if (channel === 'whatsapp') {
        const response = await axios.post(`${this.twilioUrl}/api/send`, {
          to,
          body,
          channel,
          conversationSid: conversationId, // Pass Twilio Conversation SID
          ...metadata // Include any additional metadata
        }, {
          timeout: 10000
        });

        return {
          success: true,
          messageId: response.data.messageId
        };
      }

      if (channel === 'simulation') {
        const response = await axios.post(`${this.clientSimulatorUrl}/api/webhook/response`, {
          id: `sim_response_${Date.now()}`,
          to,
          body,
          channel,
          conversationId,
          timestamp: new Date()
        }, {
          timeout: 10000
        });

        console.log('📤 Sent to Client Simulator:', { to, body, conversationId });

        return {
          success: true,
          messageId: `sim_${Date.now()}`
        };
      }

      // Other channels can be added here
      console.log(`Channel ${channel} not implemented yet`);
      
      return {
        success: false,
        error: `Channel ${channel} not supported`
      };
      
    } catch (error) {
      console.error('Failed to send message to channel:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Channel send error'
      };
    }
  }

  /**
   * Health check for dependencies
   */
  async healthCheck(): Promise<{ chatRealtime: boolean; pubsub: boolean }> {
    const results = {
      chatRealtime: false,
      pubsub: true // Mock is always "healthy"
    };

    // Check Chat Realtime Service
    try {
      const response = await axios.get(`${this.chatRealtimeUrl}/api/health`, {
        timeout: 3000
      });
      results.chatRealtime = response.status === 200;
    } catch (error) {
      console.error('Chat Realtime health check failed:', error);
    }

    return results;
  }
}