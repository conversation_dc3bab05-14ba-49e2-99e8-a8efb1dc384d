/**
 * Test Setup Configuration for Bot Human Router Service
 * Provides common test utilities, mocks, and fixtures
 */

import { IncomingMessage, ConversationContext, RoutingRequest } from '../../src/types';

// Mock environment variables for testing
export const mockEnvVars = {
  FORCE_HUMAN_ROUTING: 'false',
  ENABLE_MOCK_BOT: 'true',
  MAX_BOT_ATTEMPTS: '3',
  BOT_TIMEOUT: '5000',
  ESCALATION_KEYWORDS: 'supervisor,manager,human,agent,person',
  NEGATIVE_SENTIMENT_THRESHOLD: '0.7',
  SESSION_MANAGER_URL: 'http://localhost:3001',
  CHAT_REALTIME_URL: 'http://localhost:3004',
  N8N_BASE_URL_LOCAL: 'http://localhost:5678',
  N8N_DEPARTMENT_WEBHOOK_PATH_LOCAL: '/webhook/bot-handler',
  BOT_WEBHOOK_PATH: '/webhook/bot-handler',
};

// Test message fixtures
export const createMockMessage = (overrides: Partial<IncomingMessage> = {}): IncomingMessage => ({
  from: '+**********',
  body: 'Hello',
  timestamp: new Date().toISOString(),
  conversationId: 'conv-123',
  ...overrides,
});

// Test conversation context fixtures
export const createMockContext = (overrides: Partial<ConversationContext> = {}): ConversationContext => ({
  id: 'conv-123',
  customerId: 'customer-123',
  channel: 'whatsapp',
  department: undefined,
  botAttempts: 0,
  lastBotResponse: undefined,
  isFirstMessage: true,
  customerSentiment: 'neutral',
  ...overrides,
});

// Test routing request fixtures
export const createMockRoutingRequest = (
  messageOverrides: Partial<IncomingMessage> = {},
  contextOverrides: Partial<ConversationContext> = {}
): RoutingRequest => ({
  message: createMockMessage(messageOverrides),
  conversationContext: createMockContext(contextOverrides),
});

// Test scenarios for escalation keywords
export const escalationKeywordScenarios = [
  { keyword: 'supervisor', body: 'I need to speak with a supervisor' },
  { keyword: 'manager', body: 'Can I talk to your manager?' },
  { keyword: 'human', body: 'I want to speak with a human' },
  { keyword: 'agent', body: 'Connect me to an agent' },
  { keyword: 'person', body: 'I need to talk to a real person' },
  { keyword: 'SUPERVISOR', body: 'GET ME YOUR SUPERVISOR NOW' }, // Test case insensitive
];

// Test scenarios for complex issues
export const complexIssueScenarios = [
  { indicator: 'no funciona', body: 'Mi tarjeta no funciona' },
  { indicator: 'error', body: 'Tengo un error en mi cuenta' },
  { indicator: 'problema', body: 'Hay un problema con mi servicio' },
  { indicator: 'falla', body: 'La aplicación falla constantemente' },
  { indicator: 'bug', body: 'Encontré un bug' },
  { indicator: 'cancelar', body: 'Quiero cancelar mi suscripción' },
  { indicator: 'reembolso', body: 'Necesito un reembolso' },
  { indicator: 'queja', body: 'Tengo una queja formal' },
  { indicator: 'reclamo', body: 'Presento un reclamo' },
  { indicator: 'urgente', body: 'Es urgente por favor' },
  { indicator: 'importante', body: 'Esto es muy importante' },
  { indicator: 'inmediato', body: 'Necesito ayuda inmediata' },
];

// Test scenarios for bot mock responses
export const botMockScenarios = [
  {
    input: 'Hello',
    expectedResponse: '¡Hola! Soy tu asistente virtual. ¿En qué puedo ayudarte?',
    shouldHandoff: false,
    description: 'greeting response'
  },
  {
    input: 'I want to speak with a human',
    expectedResponse: 'Te conectaré con un agente humano.',
    shouldHandoff: true,
    description: 'human request handoff'
  },
  {
    input: 'I have a problem with my account',
    expectedResponse: 'Entiendo que tienes un problema. Te voy a conectar con un especialista.',
    shouldHandoff: true,
    description: 'complex issue handoff'
  },
  {
    input: 'What is your name?',
    expectedResponse: 'Gracias por tu mensaje. ¿Podrías darme más detalles para ayudarte mejor?',
    shouldHandoff: false,
    description: 'default response'
  },
];

// Mock axios for testing
export const createMockAxios = () => {
  const mockAxios = {
    post: jest.fn(),
    get: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  };
  
  return mockAxios;
};

// Performance test helpers
export const performanceTestConfig = {
  maxResponseTime: 100, // 100ms max for routing decisions
  maxBotTimeout: 5000,  // 5 seconds for bot responses
  concurrentRequests: 10,
};

// Error scenarios for testing
export const errorScenarios = [
  {
    name: 'network timeout',
    mockError: { code: 'ECONNABORTED', message: 'timeout of 5000ms exceeded' }
  },
  {
    name: 'service unavailable',
    mockError: { response: { status: 503, data: 'Service Unavailable' } }
  },
  {
    name: 'invalid response',
    mockError: { response: { status: 400, data: 'Bad Request' } }
  },
];

// Environment variable helper
export const setTestEnv = (vars: Record<string, string>) => {
  Object.entries(vars).forEach(([key, value]) => {
    process.env[key] = value;
  });
};

// Clean up environment after tests
export const cleanTestEnv = (vars: string[]) => {
  vars.forEach(key => {
    delete process.env[key];
  });
};

// Assert helpers for routing decisions
export const assertRoutingDecision = (
  decision: any,
  expectedRoute: 'bot' | 'human',
  expectedConfidence?: number,
  expectedReason?: string
) => {
  expect(decision.route).toBe(expectedRoute);
  
  if (expectedConfidence !== undefined) {
    expect(decision.confidence).toBeCloseTo(expectedConfidence, 1);
  }
  
  if (expectedReason) {
    expect(decision.reason).toContain(expectedReason);
  }
  
  // Validate decision structure
  expect(decision).toHaveProperty('route');
  expect(decision).toHaveProperty('confidence');
  expect(decision).toHaveProperty('reason');
  
  if (decision.route === 'bot') {
    expect(decision).toHaveProperty('botWebhookUrl');
  }
  
  if (decision.route === 'human') {
    expect(decision).toHaveProperty('agentRequired');
    expect(decision.agentRequired).toBe(true);
  }
};

// Assert helpers for bot responses
export const assertBotResponse = (
  response: any,
  shouldSucceed: boolean,
  shouldHandoff?: boolean,
  expectedResponse?: string
) => {
  expect(response.success).toBe(shouldSucceed);
  
  if (shouldHandoff !== undefined) {
    expect(response.handoffToHuman).toBe(shouldHandoff);
  }
  
  if (expectedResponse) {
    expect(response.response).toBe(expectedResponse);
  }
  
  // Validate response structure
  expect(response).toHaveProperty('success');
  
  if (response.success) {
    expect(response).toHaveProperty('response');
  }
  
  if (response.handoffToHuman) {
    expect(response).toHaveProperty('reason');
  }
};