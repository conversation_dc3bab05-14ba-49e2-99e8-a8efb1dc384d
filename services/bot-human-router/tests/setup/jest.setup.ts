/**
 * Jest Setup Configuration
 * Global setup for all tests including mocks and environment configuration
 */

import '@jest/globals';
import { setTestEnv } from './testSetup';

// Global test environment setup
beforeAll(() => {
  // Set default test environment variables
  setTestEnv({
    NODE_ENV: 'test',
    ENABLE_MOCK_BOT: 'true',
    MAX_BOT_ATTEMPTS: '3',
    BOT_TIMEOUT: '5000',
    FORCE_HUMAN_ROUTING: 'false',
    ESCALATION_KEYWORDS: 'supervisor,manager,human,agent,person',
    NEGATIVE_SENTIMENT_THRESHOLD: '0.7',
    SESSION_MANAGER_URL: 'http://localhost:3001',
    CHAT_REALTIME_URL: 'http://localhost:3004',
    N8N_BASE_URL_LOCAL: 'http://localhost:5678',
    N8N_DEPARTMENT_WEBHOOK_PATH_LOCAL: '/webhook/bot-handler',
    B<PERSON>_WEBHOOK_PATH: '/webhook/bot-handler',
    PORT: '3003',
  });
});

// Global mocks
jest.mock('axios', () => ({
  post: jest.fn(),
  get: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
}));

// Console suppression for cleaner test output (optional)
const originalConsole = console;

beforeEach(() => {
  // Uncomment to suppress console logs during tests
  // console.log = jest.fn();
  // console.error = jest.fn();
  // console.warn = jest.fn();
  // console.info = jest.fn();
});

afterEach(() => {
  // Restore console
  // console.log = originalConsole.log;
  // console.error = originalConsole.error;
  // console.warn = originalConsole.warn;
  // console.info = originalConsole.info;
});

// Increase timeout for integration tests
jest.setTimeout(10000);

// Global error handling for unhandled promises
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Global utilities available in tests
declare global {
  var testUtils: {
    waitFor: (ms: number) => Promise<void>;
    advanceTimersByTime: typeof jest.advanceTimersByTime;
    clearAllTimers: typeof jest.clearAllTimers;
    runAllTimers: typeof jest.runAllTimers;
  };
}

// Setup global test utilities
(global as any).testUtils = {
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Mock timer utilities
  advanceTimersByTime: jest.advanceTimersByTime,
  clearAllTimers: jest.clearAllTimers,
  runAllTimers: jest.runAllTimers,
};