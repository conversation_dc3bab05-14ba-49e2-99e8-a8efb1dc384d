/**
 * Smoke Test Script for Bot Human Router Service
 * Quick verification that the service starts and basic functionality works
 */

const { RoutingService } = require('../dist/routingService');
const { BotService } = require('../dist/botService');

async function runSmokeTest() {
  console.log('🧪 Starting Bot Human Router Smoke Test...\n');

  try {
    // Test 1: Routing Service Instantiation
    console.log('1️⃣ Testing RoutingService instantiation...');
    const routingService = new RoutingService();
    console.log('✅ RoutingService created successfully\n');

    // Test 2: Bot Service Instantiation  
    console.log('2️⃣ Testing BotService instantiation...');
    const botService = new BotService();
    console.log('✅ BotService created successfully\n');

    // Test 3: Basic Routing Decision
    console.log('3️⃣ Testing basic routing decision...');
    const basicRequest = {
      message: {
        from: '+**********',
        body: 'Hello, I need help',
        timestamp: new Date().toISOString(),
        conversationId: 'test-conv-1'
      },
      conversationContext: {
        id: 'test-conv-1',
        customerId: 'customer-1',
        channel: 'whatsapp',
        botAttempts: 0,
        isFirstMessage: true,
        customerSentiment: 'neutral'
      }
    };

    const decision = await routingService.makeRoutingDecision(basicRequest);
    console.log('📊 Routing Decision:', {
      route: decision.route,
      confidence: decision.confidence,
      reason: decision.reason,
      hasWebhookUrl: !!decision.botWebhookUrl
    });
    console.log('✅ Basic routing decision works\n');

    // Test 4: Escalation Keyword Detection
    console.log('4️⃣ Testing escalation keyword detection...');
    const escalationRequest = {
      ...basicRequest,
      message: {
        ...basicRequest.message,
        body: 'I want to speak with a supervisor'
      },
      conversationContext: {
        ...basicRequest.conversationContext,
        isFirstMessage: false
      }
    };

    const escalationDecision = await routingService.makeRoutingDecision(escalationRequest);
    console.log('📊 Escalation Decision:', {
      route: escalationDecision.route,
      confidence: escalationDecision.confidence,
      reason: escalationDecision.reason
    });
    console.log('✅ Escalation keyword detection works\n');

    // Test 5: Mock Bot Response
    console.log('5️⃣ Testing mock bot response...');
    const mockMessage = {
      from: '+**********',
      body: 'Hello',
      timestamp: new Date().toISOString(),
      conversationId: 'test-conv-2'
    };

    const botResponse = await botService.sendToBot(mockMessage, 'mock://bot-handler');
    console.log('📊 Bot Response:', {
      success: botResponse.success,
      hasResponse: !!botResponse.response,
      handoffToHuman: !!botResponse.handoffToHuman
    });
    console.log('✅ Mock bot response works\n');

    // Test 6: Bot Handoff Scenario
    console.log('6️⃣ Testing bot handoff scenario...');
    const handoffMessage = {
      from: '+**********',
      body: 'I need a human agent',
      timestamp: new Date().toISOString(),
      conversationId: 'test-conv-3'
    };

    const handoffResponse = await botService.sendToBot(handoffMessage, 'mock://bot-handler');
    console.log('📊 Handoff Response:', {
      success: handoffResponse.success,
      handoffToHuman: handoffResponse.handoffToHuman,
      reason: handoffResponse.reason
    });
    console.log('✅ Bot handoff scenario works\n');

    // Test 7: Future Methods (should not crash)
    console.log('7️⃣ Testing future extensibility methods...');
    const rules = await routingService.loadRoutingRules();
    const stats = await routingService.getRoutingStats();
    const botStatus = await botService.getBotStatus('test-bot');
    
    console.log('📊 Future Methods:', {
      rulesCount: rules.length,
      totalRequests: stats.totalRequests,
      botStatus: botStatus.status
    });
    console.log('✅ Future methods work without errors\n');

    // Final Results
    console.log('🎉 Smoke Test Results:');
    console.log('✅ All 7 test scenarios passed');
    console.log('✅ Service is functioning correctly');
    console.log('✅ Ready for production use\n');

  } catch (error) {
    console.error('❌ Smoke Test Failed:', error.message);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
}

// Only run if called directly (not imported)
if (require.main === module) {
  runSmokeTest();
}

module.exports = { runSmokeTest };