/**
 * Comprehensive Integration Tests for Bot Human Router Routes
 * Tests all API endpoints with real HTTP requests
 */

import request from 'supertest';
import express from 'express';
import { botHumanRoutes } from '../../src/routes';
import {
  createMockMessage,
  createMockContext,
  createMockRoutingRequest,
  escalationKeywordScenarios,
  setTestEnv,
  cleanTestEnv,
  performanceTestConfig,
} from '../setup/testSetup';

// Mock axios for bot service tests
jest.mock('axios');
import axios from 'axios';
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Bot Human Router Routes Integration', () => {
  let app: express.Application;
  const originalEnv = process.env;

  beforeAll(() => {
    app = express();
    app.use(express.json());
    app.use('/api', botHumanRoutes);
  });

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = { ...originalEnv };
    setTestEnv({ ENABLE_MOCK_BOT: 'true' });
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('POST /api/route - Routing Decision Endpoint', () => {
    
    it('should return routing decision for valid request', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Hello, I need help with general questions' }),
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision).toBeDefined();
      expect(response.body.decision.route).toMatch(/^(bot|human)$/);
      expect(response.body.decision.confidence).toBeGreaterThan(0);
      expect(response.body.decision.reason).toBeDefined();
    });

    it('should route to human when escalation keywords detected', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'I want to speak with a supervisor' }),
        conversationContext: createMockContext({ isFirstMessage: false })
      };

      const response = await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision.route).toBe('human');
      expect(response.body.decision.confidence).toBe(0.9);
      expect(response.body.decision.reason).toContain('Escalation keywords detected');
      expect(response.body.decision.agentRequired).toBe(true);
    });

    it('should route to human when bot attempt limit reached', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'I still need help' }),
        conversationContext: createMockContext({ 
          isFirstMessage: false, 
          botAttempts: 3 
        })
      };

      const response = await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision.route).toBe('human');
      expect(response.body.decision.reason).toContain('Maximum bot attempts reached');
    });

    it('should route to bot for first message with neutral content', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Hello there, I need information' }),
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision.route).toBe('bot');
      expect(response.body.decision.botWebhookUrl).toBeDefined();
      expect(response.body.decision.reason).toContain('First message');
    });

    it('should return 400 for missing message', async () => {
      const requestBody = {
        conversationContext: createMockContext()
      };

      const response = await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(400);

      expect(response.body.error).toBe('Message is required');
    });

    it('should handle request without conversation context', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Hello, general question' })
      };

      const response = await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision.route).toBe('bot');
    });

    it('should respect FORCE_HUMAN_ROUTING override', async () => {
      setTestEnv({ FORCE_HUMAN_ROUTING: 'true' });
      
      // Restart the application with new env
      jest.resetModules();
      const { botHumanRoutes: newRoutes } = require('../../src/routes');
      const newApp = express();
      newApp.use(express.json());
      newApp.use('/api', newRoutes);

      const requestBody = {
        message: createMockMessage({ body: 'Simple message' }),
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(newApp)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      expect(response.body.decision.route).toBe('human');
      expect(response.body.decision.confidence).toBe(1.0);
      expect(response.body.decision.reason).toContain('FORCE_HUMAN_ROUTING');
    });

    escalationKeywordScenarios.forEach(({ keyword, body }) => {
      it(`should handle escalation keyword: ${keyword}`, async () => {
        const requestBody = {
          message: createMockMessage({ body }),
          conversationContext: createMockContext({ isFirstMessage: false })
        };

        const response = await request(app)
          .post('/api/route')
          .send(requestBody)
          .expect(200);

        expect(response.body.decision.route).toBe('human');
        expect(response.body.decision.reason).toContain('Escalation keywords detected');
      });
    });

    it('should complete routing decision within performance threshold', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Performance test question' }),
        conversationContext: createMockContext()
      };

      const startTime = Date.now();

      await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(performanceTestConfig.maxResponseTime);
    });

    it('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/api/route')
        .send('invalid json')
        .expect(400);

      // Express should handle JSON parsing errors
      expect(response.body).toBeDefined();
    });

    it('should handle extremely large message bodies', async () => {
      const largeMessage = 'general question '.repeat(1000);
      const requestBody = {
        message: createMockMessage({ body: largeMessage }),
        conversationContext: createMockContext()
      };

      const response = await request(app)
        .post('/api/route')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('POST /api/bot/process - Bot Processing Endpoint', () => {
    
    it('should process message with mock bot successfully', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Hello' }),
        webhookUrl: 'mock://bot-handler'
      };

      const response = await request(app)
        .post('/api/bot/process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.botResponse).toBeDefined();
      expect(response.body.botResponse.success).toBe(true);
      expect(response.body.botResponse.response).toContain('¡Hola!');
    });

    it('should handle bot requesting human handoff', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'I want a human agent' }),
        webhookUrl: 'mock://bot-handler'
      };

      const response = await request(app)
        .post('/api/bot/process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.botResponse.handoffToHuman).toBe(true);
      expect(response.body.botResponse.reason).toBe('User requested human agent');
    });

    it('should use default webhook URL when not provided', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Test message' })
      };

      const response = await request(app)
        .post('/api/bot/process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.botResponse).toBeDefined();
    });

    it('should return 400 for missing message', async () => {
      const requestBody = {
        webhookUrl: 'mock://bot-handler'
      };

      const response = await request(app)
        .post('/api/bot/process')
        .send(requestBody)
        .expect(400);

      expect(response.body.error).toBe('Message is required');
    });

    it('should handle N8N webhook integration', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'false' });
      
      const requestBody = {
        message: createMockMessage({ body: 'Real bot test' }),
        webhookUrl: 'http://localhost:5678/webhook/bot'
      };

      const mockN8NResponse = {
        data: {
          response: 'N8N response',
          handoffToHuman: false
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockN8NResponse);

      const response = await request(app)
        .post('/api/bot/process')
        .send(requestBody)
        .expect(200);

      expect(response.body.botResponse.response).toBe('N8N response');
      expect(mockedAxios.post).toHaveBeenCalled();
    });

    it('should handle N8N service failure', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'false' });
      
      const requestBody = {
        message: createMockMessage({ body: 'Test' }),
        webhookUrl: 'http://localhost:5678/webhook/bot'
      };

      mockedAxios.post.mockRejectedValueOnce(new Error('Network error'));

      const response = await request(app)
        .post('/api/bot/process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.botResponse.success).toBe(false);
      expect(response.body.botResponse.handoffToHuman).toBe(true);
    });
  });

  describe('POST /api/route-and-process - Combined Endpoint', () => {
    
    it('should route to bot and process message successfully', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Hello, help me with general information' }),
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision.route).toBe('bot');
      expect(response.body.botResponse).toBeDefined();
      expect(response.body.botResponse.success).toBe(true);
    });

    it('should route to human and skip bot processing', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'I want to speak with supervisor' }),
        conversationContext: createMockContext({ isFirstMessage: false })
      };

      const response = await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision.route).toBe('human');
      expect(response.body.botResponse).toBeNull();
    });

    it('should handle bot handoff and update routing decision', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'I need help with general stuff' }), // Will route to bot first
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);

      // For neutral messages that go to bot, it should stay bot unless bot requests handoff
      // The test was expecting handoff but our mock bot doesn't trigger handoff for general messages
      if (response.body.botResponse && response.body.botResponse.handoffToHuman) {
        expect(response.body.decision.route).toBe('human');
        expect(response.body.decision.reason).toContain('Bot handoff');
      } else {
        expect(response.body.decision.route).toBe('bot');
      }
    });

    it('should test specific bot handoff scenario', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'I have a problema' }), // Should trigger bot handoff
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      
      // This message should trigger handoff due to "problema" keyword in mock bot
      expect(response.body.decision.route).toBe('human');
      expect(response.body.decision.reason).toContain('Bot handoff');
      expect(response.body.botResponse.handoffToHuman).toBe(true);
    });

    it('should handle bot processing without handoff', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'What is your name?' }), // Simple question
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.decision.route).toBe('bot');
      expect(response.body.botResponse.handoffToHuman).toBeFalsy();
    });

    it('should handle missing bot webhook URL gracefully', async () => {
      // Force a scenario where routing returns bot but no webhook URL
      const requestBody = {
        message: createMockMessage({ body: 'Test general question' }),
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const response = await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      // Should still process even if webhook URL is missing
    });

    it('should return 400 for missing message', async () => {
      const requestBody = {
        conversationContext: createMockContext()
      };

      const response = await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(400);

      expect(response.body.error).toBe('Message is required');
    });

    it('should complete combined operation within reasonable time', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Performance test for general help' }),
        conversationContext: createMockContext({ isFirstMessage: true })
      };

      const startTime = Date.now();

      await request(app)
        .post('/api/route-and-process')
        .send(requestBody)
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(200); // Allow more time for combined operation
    });
  });

  describe('GET /api/stats - Statistics Endpoint', () => {
    
    it('should return default statistics', async () => {
      const response = await request(app)
        .get('/api/stats')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.stats).toEqual({
        totalRequests: 0,
        botRouted: 0,
        humanRouted: 0,
        successRate: 0
      });
    });

    it('should handle stats request quickly', async () => {
      const startTime = Date.now();

      await request(app)
        .get('/api/stats')
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(50);
    });
  });

  describe('GET /api/rules - Rules Management Endpoints', () => {
    
    it('should return empty rules array', async () => {
      const response = await request(app)
        .get('/api/rules')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.rules).toEqual([]);
    });

    it('should handle rules loading quickly', async () => {
      const startTime = Date.now();

      await request(app)
        .get('/api/rules')
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(50);
    });
  });

  describe('POST /api/rules - Update Rules Endpoint', () => {
    
    it('should return failure for rule updates (not implemented)', async () => {
      const requestBody = {
        rules: [
          {
            id: 'rule-1',
            name: 'Test Rule',
            conditions: [],
            action: 'route_to_bot',
            priority: 1,
            isActive: true
          }
        ]
      };

      const response = await request(app)
        .post('/api/rules')
        .send(requestBody)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Failed to update routing rules');
    });

    it('should handle missing rules in request body', async () => {
      const requestBody = {};

      const response = await request(app)
        .post('/api/rules')
        .send(requestBody)
        .expect(500);

      expect(response.body.success).toBe(false);
    });
  });

  describe('GET /api/health - Health Check Endpoint', () => {
    
    it('should return healthy status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.status).toBe('ok');
      expect(response.body.service).toBe('bot-human-router');
      expect(response.body.timestamp).toBeDefined();
      
      // Validate timestamp format
      const timestamp = new Date(response.body.timestamp);
      expect(timestamp).toBeInstanceOf(Date);
      expect(timestamp.getTime()).not.toBeNaN();
    });

    it('should respond to health check within performance threshold', async () => {
      const startTime = Date.now();

      await request(app)
        .get('/api/health')
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(50);
    });

    it('should handle multiple concurrent health checks', async () => {
      const promises = Array.from({ length: 10 }, () =>
        request(app).get('/api/health')
      );

      const responses = await Promise.all(promises);

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.status).toBe('ok');
      });
    });
  });

  describe('Error Handling and Edge Cases', () => {
    
    it('should handle invalid HTTP methods gracefully', async () => {
      await request(app)
        .put('/api/route')
        .send({})
        .expect(404); // Method not allowed or not found
    });

    it('should handle non-existent endpoints', async () => {
      await request(app)
        .get('/api/nonexistent')
        .expect(404);
    });

    it('should handle request timeout scenarios', async () => {
      // This would require mocking long-running operations
      // For now, we test that normal operations complete quickly
      const requestBody = {
        message: createMockMessage({ body: 'Test neutral question' }),
        conversationContext: createMockContext()
      };

      const startTime = Date.now();
      
      await request(app)
        .post('/api/route')
        .send(requestBody)
        .timeout(1000) // 1 second timeout
        .expect(200);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(1000);
    });

    it('should handle Content-Type variations', async () => {
      const requestBody = {
        message: createMockMessage({ body: 'Test question' })
      };

      const response = await request(app)
        .post('/api/route')
        .set('Content-Type', 'application/json; charset=utf-8')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('should handle missing Content-Type header', async () => {
      const jsonString = JSON.stringify({
        message: createMockMessage({ body: 'Test' })
      });

      await request(app)
        .post('/api/route')
        .send(jsonString)
        .expect(400); // Should fail without proper content type
    });
  });

  describe('Load and Stress Testing', () => {
    
    it('should handle multiple concurrent routing requests', async () => {
      const requests = Array.from({ length: 20 }, (_, i) => ({
        message: createMockMessage({ body: `Concurrent test ${i} general question` }),
        conversationContext: createMockContext({ isFirstMessage: true })
      }));

      const promises = requests.map(requestBody =>
        request(app)
          .post('/api/route')
          .send(requestBody)
      );

      const responses = await Promise.all(promises);

      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    });

    it('should maintain performance under moderate load', async () => {
      const numberOfRequests = 50;
      const requestBody = {
        message: createMockMessage({ body: 'Load test general question' }),
        conversationContext: createMockContext()
      };

      const startTime = Date.now();

      const promises = Array.from({ length: numberOfRequests }, () =>
        request(app)
          .post('/api/route')
          .send(requestBody)
      );

      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;
      const avgResponseTime = totalTime / numberOfRequests;

      expect(responses).toHaveLength(numberOfRequests);
      expect(avgResponseTime).toBeLessThan(200); // Average under 200ms
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });
  });
});