/**
 * Comprehensive Unit Tests for BotService
 * Tests bot communication, mock responses, and error handling
 */

import axios from 'axios';
import { BotService } from '../../src/botService';
import { config } from '../../src/config';
import {
  createMockMessage,
  botMockScenarios,
  setTestEnv,
  cleanTestEnv,
  assertBotResponse,
  errorScenarios,
  performanceTestConfig,
} from '../setup/testSetup';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('BotService', () => {
  let botService: BotService;
  const originalEnv = process.env;

  beforeAll(() => {
    botService = new BotService();
  });

  beforeEach(() => {
    jest.resetModules();
    jest.clearAllMocks();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('sendToBot - Mock Bot Responses', () => {
    
    beforeEach(() => {
      setTestEnv({ ENABLE_MOCK_BOT: 'true' });
    });

    botMockScenarios.forEach(({ input, expectedResponse, shouldHandoff, description }) => {
      it(`should return correct mock response for ${description}`, async () => {
        const message = createMockMessage({ body: input });
        const webhookUrl = 'mock://bot-handler';

        const response = await botService.sendToBot(message, webhookUrl);

        assertBotResponse(response, true, shouldHandoff, expectedResponse);
      });
    });

    it('should handle mock URL webhook correctly', async () => {
      const message = createMockMessage({ body: 'Hello' });
      const webhookUrl = 'mock://any-path';

      const response = await botService.sendToBot(message, webhookUrl);

      assertBotResponse(response, true, false);
      expect(response.response).toContain('¡Hola!');
    });

    it('should handle case insensitive mock responses', async () => {
      const message = createMockMessage({ body: 'HUMAN AGENT PLEASE' });
      const webhookUrl = 'mock://bot-handler';

      const response = await botService.sendToBot(message, webhookUrl);

      assertBotResponse(response, true, true);
      expect(response.reason).toBe('User requested human agent');
    });

    it('should detect multiple trigger words in mock responses', async () => {
      const message = createMockMessage({ 
        body: 'I have a big problema and need human help' 
      });
      const webhookUrl = 'mock://bot-handler';

      const response = await botService.sendToBot(message, webhookUrl);

      // Should trigger on "problema" first (more specific)
      assertBotResponse(response, true, true);
      expect(response.reason).toBe('Complex issue detected');
    });

    it('should provide consistent mock responses', async () => {
      const message = createMockMessage({ body: 'Hello there' });
      const webhookUrl = 'mock://bot-handler';

      const response1 = await botService.sendToBot(message, webhookUrl);
      const response2 = await botService.sendToBot(message, webhookUrl);

      expect(response1).toEqual(response2);
    });
  });

  describe('sendToBot - Real N8N Integration', () => {
    
    beforeEach(() => {
      setTestEnv({ ENABLE_MOCK_BOT: 'false' });
    });

    it('should send correct payload to N8N webhook', async () => {
      const message = createMockMessage({
        body: 'Test message',
        from: '+1234567890',
        conversationId: 'conv-123',
        timestamp: '2024-08-15T12:00:00Z'
      });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      const mockN8NResponse = {
        data: {
          response: 'N8N bot response',
          handoffToHuman: false,
          reason: 'Bot handled successfully'
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockN8NResponse);

      const response = await botService.sendToBot(message, webhookUrl);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        webhookUrl,
        {
          message: 'Test message',
          from: '+1234567890',
          conversationId: 'conv-123',
          timestamp: '2024-08-15T12:00:00Z'
        },
        {
          timeout: config.botTimeout,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      assertBotResponse(response, true, false, 'N8N bot response');
      expect(response.reason).toBe('Bot handled successfully');
    });

    it('should handle N8N bot handoff response', async () => {
      const message = createMockMessage({ body: 'Complex issue' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      const mockN8NResponse = {
        data: {
          response: 'This is too complex for me',
          handoffToHuman: true,
          reason: 'Issue complexity exceeds bot capabilities'
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockN8NResponse);

      const response = await botService.sendToBot(message, webhookUrl);

      assertBotResponse(response, true, true, 'This is too complex for me');
      expect(response.reason).toBe('Issue complexity exceeds bot capabilities');
    });

    it('should handle N8N response without handoff flag', async () => {
      const message = createMockMessage({ body: 'Simple question' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      const mockN8NResponse = {
        data: {
          response: 'Here is your answer',
          // handoffToHuman is undefined
          reason: 'Standard bot response'
        }
      };
      mockedAxios.post.mockResolvedValueOnce(mockN8NResponse);

      const response = await botService.sendToBot(message, webhookUrl);

      assertBotResponse(response, true, false, 'Here is your answer');
    });

    it('should respect timeout configuration', async () => {
      setTestEnv({ BOT_TIMEOUT: '3000' });
      
      const botServiceWithCustomTimeout = new (require('../../src/botService')).BotService();
      const message = createMockMessage({ body: 'Test' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      mockedAxios.post.mockResolvedValueOnce({ data: { response: 'OK' } });

      await botServiceWithCustomTimeout.sendToBot(message, webhookUrl);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Object),
        expect.objectContaining({
          timeout: 3000
        })
      );
    });

    it('should complete within performance threshold for successful requests', async () => {
      const message = createMockMessage({ body: 'Quick test' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      mockedAxios.post.mockResolvedValueOnce({
        data: { response: 'Fast response' }
      });

      const startTime = Date.now();
      await botService.sendToBot(message, webhookUrl);
      const responseTime = Date.now() - startTime;

      expect(responseTime).toBeLessThan(100); // Should be very fast for mocked response
    });
  });

  describe('sendToBot - Error Handling', () => {
    
    beforeEach(() => {
      setTestEnv({ ENABLE_MOCK_BOT: 'false' });
    });

    errorScenarios.forEach(({ name, mockError }) => {
      it(`should handle ${name} and route to human`, async () => {
        const message = createMockMessage({ body: 'Test message' });
        const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

        mockedAxios.post.mockRejectedValueOnce(mockError);

        const response = await botService.sendToBot(message, webhookUrl);

        assertBotResponse(response, false, true);
        expect(response.reason).toBe('Bot service unavailable - routing to human');
      });
    });

    it('should handle network timeout gracefully', async () => {
      const message = createMockMessage({ body: 'Test message' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 10000ms exceeded'
      };
      mockedAxios.post.mockRejectedValueOnce(timeoutError);

      const response = await botService.sendToBot(message, webhookUrl);

      assertBotResponse(response, false, true);
      expect(response.reason).toContain('Bot service unavailable');
    });

    it('should handle malformed N8N response', async () => {
      const message = createMockMessage({ body: 'Test' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      // Response without expected structure
      mockedAxios.post.mockResolvedValueOnce({
        data: null
      });

      const response = await botService.sendToBot(message, webhookUrl);

      // Should handle gracefully but might fail due to accessing null.response
      expect(response).toBeDefined();
    });

    it('should handle partial N8N response data', async () => {
      const message = createMockMessage({ body: 'Test' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          // Missing response field
          handoffToHuman: false
        }
      });

      const response = await botService.sendToBot(message, webhookUrl);

      expect(response.success).toBe(true);
      expect(response.handoffToHuman).toBe(false);
      expect(response.response).toBeUndefined();
    });

    it('should handle HTTP error status codes', async () => {
      const message = createMockMessage({ body: 'Test' });
      const webhookUrl = 'http://localhost:5678/webhook/bot-handler';

      const httpError = {
        response: {
          status: 500,
          data: 'Internal Server Error'
        }
      };
      mockedAxios.post.mockRejectedValueOnce(httpError);

      const response = await botService.sendToBot(message, webhookUrl);

      assertBotResponse(response, false, true);
    });
  });

  describe('sendToBot - Configuration Scenarios', () => {
    
    it('should use mock when ENABLE_MOCK_BOT=true regardless of webhook URL', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'true' });
      
      const message = createMockMessage({ body: 'Hello' });
      const realWebhookUrl = 'http://real-n8n.com/webhook/bot';

      const response = await botService.sendToBot(message, realWebhookUrl);

      // Should use mock response, not call axios
      expect(mockedAxios.post).not.toHaveBeenCalled();
      assertBotResponse(response, true);
    });

    it('should call N8N when ENABLE_MOCK_BOT=false and webhook is not mock://', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'false' });
      
      const message = createMockMessage({ body: 'Test' });
      const webhookUrl = 'http://localhost:5678/webhook/bot';

      mockedAxios.post.mockResolvedValueOnce({
        data: { response: 'Real bot response' }
      });

      await botService.sendToBot(message, webhookUrl);

      expect(mockedAxios.post).toHaveBeenCalled();
    });

    it('should use mock for mock:// URLs even when ENABLE_MOCK_BOT=false', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'false' });
      
      const message = createMockMessage({ body: 'Hello' });
      const mockWebhookUrl = 'mock://bot-handler';

      const response = await botService.sendToBot(message, mockWebhookUrl);

      // Should use mock, not call axios
      expect(mockedAxios.post).not.toHaveBeenCalled();
      assertBotResponse(response, true);
    });
  });

  describe('Mock Response Logic Deep Dive', () => {
    
    it('should prioritize human/agent keywords over other patterns', async () => {
      const message = createMockMessage({ 
        body: 'hola, I have a problema but want human help'
      });
      const webhookUrl = 'mock://bot-handler';

      const response = await botService.sendToBot(message, webhookUrl);

      // Should match "human" first
      assertBotResponse(response, true, true);
      expect(response.reason).toBe('User requested human agent');
    });

    it('should handle greeting patterns correctly', async () => {
      const greetingVariations = ['hola', 'hello', 'Hello there', 'HOLA MUNDO'];
      
      for (const greeting of greetingVariations) {
        const message = createMockMessage({ body: greeting });
        const response = await botService.sendToBot(message, 'mock://bot');

        assertBotResponse(response, true, false);
        expect(response.response).toContain('¡Hola!');
      }
    });

    it('should handle problem/error patterns correctly', async () => {
      const problemVariations = ['problema', 'error', 'I have an error', 'big problema here'];
      
      for (const problem of problemVariations) {
        const message = createMockMessage({ body: problem });
        const response = await botService.sendToBot(message, 'mock://bot');

        assertBotResponse(response, true, true);
        expect(response.reason).toBe('Complex issue detected');
      }
    });

    it('should provide default response for unmatched patterns', async () => {
      const unknownMessages = [
        'random question',
        'what is the weather',
        'tell me a joke',
        'asdf qwerty'
      ];
      
      for (const unknownMsg of unknownMessages) {
        const message = createMockMessage({ body: unknownMsg });
        const response = await botService.sendToBot(message, 'mock://bot');

        assertBotResponse(response, true, false);
        expect(response.response).toContain('Gracias por tu mensaje');
      }
    });

    it('should handle empty message body in mock', async () => {
      const message = createMockMessage({ body: '' });
      const response = await botService.sendToBot(message, 'mock://bot');

      assertBotResponse(response, true, false);
    });

    it('should handle special characters in mock responses', async () => {
      const message = createMockMessage({ body: '¡Hola! ñañañá @#$%' });
      const response = await botService.sendToBot(message, 'mock://bot');

      expect(response.success).toBe(true);
      expect(response.response).toContain('¡Hola!');
    });
  });

  describe('Future Extensibility Methods', () => {
    
    it('should return true for registerBotWebhook (placeholder)', async () => {
      const result = await botService.registerBotWebhook('http://test.com', {});
      
      expect(result).toBe(true);
    });

    it('should return unknown status for getBotStatus (placeholder)', async () => {
      const status = await botService.getBotStatus('bot-123');
      
      expect(status).toEqual({ status: 'unknown' });
    });

    it('should return zero metrics for getBotMetrics (placeholder)', async () => {
      const metrics = await botService.getBotMetrics('bot-123');
      
      expect(metrics).toEqual({
        totalMessages: 0,
        successRate: 0,
        averageResponseTime: 0
      });
    });
  });

  describe('Concurrent Bot Requests', () => {
    
    it('should handle multiple concurrent bot requests', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'true' });
      
      const messages = Array.from({ length: 10 }, (_, i) =>
        createMockMessage({ body: `Test message ${i}` })
      );

      const promises = messages.map(message => 
        botService.sendToBot(message, 'mock://bot-handler')
      );

      const responses = await Promise.all(promises);

      expect(responses).toHaveLength(10);
      responses.forEach(response => {
        expect(response.success).toBe(true);
        expect(response.response).toBeDefined();
      });
    });

    it('should handle mixed success/failure scenarios concurrently', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'false' });
      
      const requests = [
        { message: createMockMessage({ body: 'Success 1' }), shouldSucceed: true },
        { message: createMockMessage({ body: 'Success 2' }), shouldSucceed: true },
        { message: createMockMessage({ body: 'Fail 1' }), shouldSucceed: false },
        { message: createMockMessage({ body: 'Success 3' }), shouldSucceed: true },
        { message: createMockMessage({ body: 'Fail 2' }), shouldSucceed: false },
      ];

      // Setup mocks
      mockedAxios.post
        .mockResolvedValueOnce({ data: { response: 'OK 1' } })
        .mockResolvedValueOnce({ data: { response: 'OK 2' } })
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ data: { response: 'OK 3' } })
        .mockRejectedValueOnce(new Error('Service error'));

      const promises = requests.map(({ message }) => 
        botService.sendToBot(message, 'http://localhost:5678/webhook/bot')
      );

      const responses = await Promise.all(promises);

      expect(responses).toHaveLength(5);
      
      // Check success/failure pattern
      expect(responses[0].success).toBe(true);
      expect(responses[1].success).toBe(true);
      expect(responses[2].success).toBe(false);
      expect(responses[3].success).toBe(true);
      expect(responses[4].success).toBe(false);
    });
  });

  describe('Performance and Load Testing', () => {
    
    it('should handle high-frequency requests without degradation', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'true' });
      
      const numberOfRequests = 100;
      const message = createMockMessage({ body: 'Performance test' });
      
      const startTime = Date.now();
      
      const promises = Array.from({ length: numberOfRequests }, () =>
        botService.sendToBot(message, 'mock://bot-handler')
      );

      const responses = await Promise.all(promises);
      
      const totalTime = Date.now() - startTime;
      const avgResponseTime = totalTime / numberOfRequests;

      expect(responses).toHaveLength(numberOfRequests);
      expect(avgResponseTime).toBeLessThan(10); // Should be very fast for mock responses
      
      responses.forEach(response => {
        expect(response.success).toBe(true);
      });
    });
  });
});