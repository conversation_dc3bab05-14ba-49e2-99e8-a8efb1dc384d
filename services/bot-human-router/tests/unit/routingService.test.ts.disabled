/**
 * Comprehensive Unit Tests for RoutingService
 * Tests all critical routing decision logic and edge cases
 */

import { RoutingService } from '../../src/routingService';
import { config } from '../../src/config';
import {
  createMockMessage,
  createMockContext,
  createMockRoutingRequest,
  escalationKeywordScenarios,
  complexIssueScenarios,
  setTestEnv,
  cleanTestEnv,
  assertRoutingDecision,
  performanceTestConfig,
} from '../setup/testSetup';

describe('RoutingService', () => {
  let routingService: RoutingService;
  const originalEnv = process.env;

  beforeAll(() => {
    routingService = new RoutingService();
  });

  beforeEach(() => {
    // Reset environment for each test
    jest.resetModules();
    process.env = { ...originalEnv };
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('makeRoutingDecision - Core Logic', () => {
    
    it('should handle valid routing request with message and context', async () => {
      const request = createMockRoutingRequest(
        { body: 'Hello, I need help' },
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'bot', 0.8, 'First message');
      expect(decision.botWebhookUrl).toBeDefined();
    });

    it('should handle request without conversation context', async () => {
      const request = {
        message: createMockMessage({ body: 'Hello' }),
        conversationContext: undefined
      };

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'bot', 0.8, 'First message');
    });

    it('should handle errors gracefully and default to human', async () => {
      const invalidRequest = {
        message: null,
        conversationContext: null
      } as any;

      const decision = await routingService.makeRoutingDecision(invalidRequest);

      assertRoutingDecision(decision, 'human', 0.5, 'Error in routing logic');
    });

    it('should complete routing decision within performance threshold', async () => {
      const request = createMockRoutingRequest();
      const startTime = Date.now();

      await routingService.makeRoutingDecision(request);

      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(performanceTestConfig.maxResponseTime);
    });
  });

  describe('FORCE_HUMAN_ROUTING Override', () => {
    
    it('should override ALL logic when FORCE_HUMAN_ROUTING=true', async () => {
      setTestEnv({ FORCE_HUMAN_ROUTING: 'true' });
      
      // Recreate routing service with new config
      const routingServiceWithOverride = new (require('../../src/routingService')).RoutingService();
      
      const request = createMockRoutingRequest(
        { body: 'hello simple message' }, // Should normally go to bot
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingServiceWithOverride.makeRoutingDecision(request);

      expect(decision.route).toBe('human');
      expect(decision.confidence).toBe(1.0);
      expect(decision.reason).toContain('FORCE_HUMAN_ROUTING environment variable enabled');
      expect(decision.agentRequired).toBe(true);
    });

    it('should override escalation keywords when FORCE_HUMAN_ROUTING=true', async () => {
      setTestEnv({ FORCE_HUMAN_ROUTING: 'true' });
      
      const routingServiceWithOverride = new (require('../../src/routingService')).RoutingService();
      
      const request = createMockRoutingRequest(
        { body: 'supervisor manager human' },
        { botAttempts: 5, isFirstMessage: false }
      );

      const decision = await routingServiceWithOverride.makeRoutingDecision(request);

      expect(decision.route).toBe('human');
      expect(decision.confidence).toBe(1.0);
      expect(decision.reason).toContain('FORCE_HUMAN_ROUTING');
    });

    it('should NOT force human routing when FORCE_HUMAN_ROUTING=false', async () => {
      setTestEnv({ FORCE_HUMAN_ROUTING: 'false' });
      
      const request = createMockRoutingRequest(
        { body: 'hello' },
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      expect(decision.route).toBe('bot');
      expect(decision.reason).not.toContain('FORCE_HUMAN_ROUTING');
    });
  });

  describe('Escalation Keywords Detection', () => {
    
    escalationKeywordScenarios.forEach(({ keyword, body }) => {
      it(`should detect escalation keyword: "${keyword}" and route to human`, async () => {
        const request = createMockRoutingRequest(
          { body },
          { isFirstMessage: false, botAttempts: 1 }
        );

        const decision = await routingService.makeRoutingDecision(request);

        assertRoutingDecision(decision, 'human', 0.9, 'Escalation keywords detected');
      });
    });

    it('should handle multiple escalation keywords in same message', async () => {
      const request = createMockRoutingRequest(
        { body: 'I need to speak with a supervisor or manager or human agent' },
        { isFirstMessage: false, botAttempts: 1 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.9, 'Escalation keywords detected');
    });

    it('should be case insensitive for escalation keywords', async () => {
      const request = createMockRoutingRequest(
        { body: 'I WANT TO TALK TO YOUR SUPERVISOR RIGHT NOW!' },
        { isFirstMessage: false, botAttempts: 1 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.9, 'Escalation keywords detected');
    });

    it('should not trigger false positives for partial word matches', async () => {
      const request = createMockRoutingRequest(
        { body: 'I am a supervisor in my company' }, // Contains "supervisor" but different context
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      // Should still catch it due to current implementation (contains match)
      expect(decision.route).toBe('human');
    });
  });

  describe('Bot Attempt Limits', () => {
    
    it('should route to human when bot attempts >= maxBotAttempts', async () => {
      const request = createMockRoutingRequest(
        { body: 'I still need help' },
        { 
          isFirstMessage: false, 
          botAttempts: 3, // Equal to MAX_BOT_ATTEMPTS default
          lastBotResponse: 'Previous bot response'
        }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.8, 'Maximum bot attempts reached');
      expect(decision.reason).toContain('3');
    });

    it('should route to human when bot attempts exceed maxBotAttempts', async () => {
      const request = createMockRoutingRequest(
        { body: 'Help me please' },
        { 
          isFirstMessage: false, 
          botAttempts: 5, // Exceeds MAX_BOT_ATTEMPTS
          lastBotResponse: 'Previous bot response'
        }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.8, 'Maximum bot attempts reached');
    });

    it('should continue with bot when attempts under limit', async () => {
      const request = createMockRoutingRequest(
        { body: 'I need more help' },
        { 
          isFirstMessage: false, 
          botAttempts: 2, // Under limit
          lastBotResponse: 'Previous bot response'
        }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'bot', 0.6, 'No escalation triggers');
    });

    it('should respect custom MAX_BOT_ATTEMPTS configuration', async () => {
      setTestEnv({ MAX_BOT_ATTEMPTS: '2' });
      
      const routingServiceWithCustomLimit = new (require('../../src/routingService')).RoutingService();
      
      const request = createMockRoutingRequest(
        { body: 'Still confused' },
        { isFirstMessage: false, botAttempts: 2 }
      );

      const decision = await routingServiceWithCustomLimit.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.8, 'Maximum bot attempts reached');
      expect(decision.reason).toContain('2');
    });
  });

  describe('Complex Issue Detection', () => {
    
    complexIssueScenarios.forEach(({ indicator, body }) => {
      it(`should detect complex issue indicator: "${indicator}" and route to human`, async () => {
        const request = createMockRoutingRequest(
          { body },
          { isFirstMessage: false, botAttempts: 1 }
        );

        const decision = await routingService.makeRoutingDecision(request);

        assertRoutingDecision(decision, 'human', 0.7, 'Complex issue detected');
      });
    });

    it('should handle multiple complexity indicators in same message', async () => {
      const request = createMockRoutingRequest(
        { body: 'Tengo un problema urgente, hay un error que no funciona' },
        { isFirstMessage: false, botAttempts: 1 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.7, 'Complex issue detected');
    });

    it('should prioritize escalation keywords over complex issues', async () => {
      const request = createMockRoutingRequest(
        { body: 'I have a problema but I want to speak with a human' },
        { isFirstMessage: false, botAttempts: 1 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      // Should route to human with higher confidence due to escalation keyword
      assertRoutingDecision(decision, 'human', 0.9, 'Escalation keywords detected');
    });
  });

  describe('First Message Logic', () => {
    
    it('should route first message to bot by default', async () => {
      const request = createMockRoutingRequest(
        { body: 'Hello, I need help with something neutral' },
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'bot', 0.8, 'First message - trying bot first');
      expect(decision.botWebhookUrl).toBeDefined();
    });

    it('should route first message to human if escalation keywords present', async () => {
      const request = createMockRoutingRequest(
        { body: 'Hello, I want to speak with a supervisor' },
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.9, 'Escalation keywords detected');
    });

    it('should route first message to human if complex issue detected', async () => {
      const request = createMockRoutingRequest(
        { body: 'Hola, tengo un problema urgente' },
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'human', 0.7, 'Complex issue detected');
    });
  });

  describe('Default Routing Logic', () => {
    
    it('should continue with bot when no escalation triggers', async () => {
      const request = createMockRoutingRequest(
        { body: 'Can you help me understand this?' },
        { 
          isFirstMessage: false, 
          botAttempts: 1,
          lastBotResponse: 'Previous response' 
        }
      );

      const decision = await routingService.makeRoutingDecision(request);

      assertRoutingDecision(decision, 'bot', 0.6, 'No escalation triggers - continuing with bot');
      expect(decision.botWebhookUrl).toBeDefined();
    });

    it('should provide bot webhook URL for bot routing decisions', async () => {
      const request = createMockRoutingRequest(
        { body: 'Tell me more about this service' },
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      expect(decision.route).toBe('bot');
      expect(decision.botWebhookUrl).toBeDefined();
    });
  });

  describe('getBotWebhookUrl method', () => {
    
    it('should return mock URL when ENABLE_MOCK_BOT=true', async () => {
      setTestEnv({ ENABLE_MOCK_BOT: 'true' });
      
      const request = createMockRoutingRequest(
        { body: 'Hello general question' },
        { isFirstMessage: true }
      );

      const decision = await routingService.makeRoutingDecision(request);

      expect(decision.botWebhookUrl).toBe('mock://bot-handler');
    });

    it('should return N8N URL when ENABLE_MOCK_BOT=false', async () => {
      setTestEnv({ 
        ENABLE_MOCK_BOT: 'false',
        N8N_BASE_URL: 'http://n8n-test:5678',
        BOT_WEBHOOK_PATH: '/webhook/test-bot'
      });
      
      const routingServiceWithRealBot = new (require('../../src/routingService')).RoutingService();
      
      const request = createMockRoutingRequest(
        { body: 'Hello neutral question' },
        { isFirstMessage: true }
      );

      const decision = await routingServiceWithRealBot.makeRoutingDecision(request);

      expect(decision.botWebhookUrl).toBe('http://n8n-test:5678/webhook/test-bot');
    });
  });

  describe('Rule Priority Logic', () => {
    
    it('should prioritize FORCE_HUMAN_ROUTING over all other rules', async () => {
      setTestEnv({ FORCE_HUMAN_ROUTING: 'true' });
      
      const routingServiceWithOverride = new (require('../../src/routingService')).RoutingService();
      
      const request = createMockRoutingRequest(
        { body: 'simple message' },
        { isFirstMessage: true, botAttempts: 0 }
      );

      const decision = await routingServiceWithOverride.makeRoutingDecision(request);

      expect(decision.route).toBe('human');
      expect(decision.confidence).toBe(1.0);
    });

    it('should prioritize escalation keywords over bot attempt limits', async () => {
      const request = createMockRoutingRequest(
        { body: 'I want a human agent' },
        { isFirstMessage: false, botAttempts: 2 } // Under limit but has keyword
      );

      const decision = await routingService.makeRoutingDecision(request);

      expect(decision.route).toBe('human');
      expect(decision.confidence).toBe(0.9); // Escalation confidence
      expect(decision.reason).toContain('Escalation keywords detected');
    });

    it('should prioritize bot attempt limits over complex issues', async () => {
      const request = createMockRoutingRequest(
        { body: 'Tengo un problema' }, // Complex issue
        { isFirstMessage: false, botAttempts: 3 } // At limit
      );

      const decision = await routingService.makeRoutingDecision(request);

      expect(decision.route).toBe('human');
      expect(decision.confidence).toBe(0.8); // Bot limit confidence
      expect(decision.reason).toContain('Maximum bot attempts reached');
    });
  });

  describe('Edge Cases', () => {
    
    it('should handle empty message body', async () => {
      const request = createMockRoutingRequest(
        { body: '' },
        { isFirstMessage: true }
      );

      const decision = await routingService.makeRoutingDecision(request);

      // Should still make a decision
      expect(['bot', 'human']).toContain(decision.route);
      expect(decision.confidence).toBeGreaterThan(0);
    });

    it('should handle extremely long message', async () => {
      const longMessage = 'help with general questions '.repeat(100); // Long but neutral
      const request = createMockRoutingRequest(
        { body: longMessage },
        { isFirstMessage: false, botAttempts: 1 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      expect(['bot', 'human']).toContain(decision.route);
      expect(decision.confidence).toBeGreaterThan(0);
    });

    it('should handle special characters in message', async () => {
      const request = createMockRoutingRequest(
        { body: '¡Hola! @#$%^&*()_+ supervisor néëd hëlp' },
        { isFirstMessage: false, botAttempts: 1 }
      );

      const decision = await routingService.makeRoutingDecision(request);

      // Should detect "supervisor" despite special characters
      expect(decision.route).toBe('human');
    });

    it('should handle null/undefined conversation context gracefully', async () => {
      const request = {
        message: createMockMessage({ body: 'Hello neutral question' }),
        conversationContext: null as any
      };

      const decision = await routingService.makeRoutingDecision(request);

      // Should treat as first message
      expect(decision.route).toBe('bot');
      expect(decision.reason).toContain('First message');
    });
  });

  describe('Future Extensibility Methods', () => {
    
    it('should return empty array for loadRoutingRules (not implemented)', async () => {
      const rules = await routingService.loadRoutingRules();
      
      expect(Array.isArray(rules)).toBe(true);
      expect(rules).toHaveLength(0);
    });

    it('should return false for updateRoutingRules (not implemented)', async () => {
      const updated = await routingService.updateRoutingRules([]);
      
      expect(updated).toBe(false);
    });

    it('should return default stats for getRoutingStats (not implemented)', async () => {
      const stats = await routingService.getRoutingStats();
      
      expect(stats).toEqual({
        totalRequests: 0,
        botRouted: 0,
        humanRouted: 0,
        successRate: 0
      });
    });
  });

  describe('Concurrent Routing Decisions', () => {
    
    it('should handle multiple concurrent routing decisions', async () => {
      const requests = Array.from({ length: performanceTestConfig.concurrentRequests }, (_, i) =>
        createMockRoutingRequest(
          { body: `Test message ${i} about general help` },
          { isFirstMessage: true, botAttempts: 0 }
        )
      );

      const promises = requests.map(request => 
        routingService.makeRoutingDecision(request)
      );

      const decisions = await Promise.all(promises);

      expect(decisions).toHaveLength(performanceTestConfig.concurrentRequests);
      decisions.forEach(decision => {
        expect(['bot', 'human']).toContain(decision.route);
        expect(decision.confidence).toBeGreaterThan(0);
      });
    });
  });
});