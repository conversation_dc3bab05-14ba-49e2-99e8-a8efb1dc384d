{"name": "@cx-system/bot-human-router", "version": "1.0.0", "private": true, "description": "Bot Human Router service for CX System", "main": "dist/server.js", "scripts": {"build": "npm install && tsc", "dev": "tsx watch src/server.ts", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --selectProjects unit", "test:smoke": "npm run build && node tests/smoke-test.js"}, "dependencies": {"@google-cloud/pubsub": "^5.2.0", "@types/ioredis": "^4.28.10", "axios": "^1.11.0", "dotenv": "^16.3.1", "express": "^4.18.2", "ioredis": "^5.7.0", "winston": "^3.17.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.11.0", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "tsx": "^4.7.0", "typescript": "^5.3.3"}}