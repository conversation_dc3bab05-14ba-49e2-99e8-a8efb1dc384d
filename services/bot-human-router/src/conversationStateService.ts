/**
 * Conversation State Service
 * Manages conversation assignment state for Bot Human Router using Redis
 */

import { RedisService } from './redisService';

export interface ConversationState {
  conversationId: string;
  assignedTo: 'n8n' | 'human' | 'unassigned';
  assignedAt: number; // timestamp
  department: string;
  lastUpdated: number; // timestamp
  updatedBy: string; // service that updated the state
}

export class ConversationStateService {
  private redisService: RedisService;
  private readonly KEY_PREFIX = 'conversation:';
  private readonly DEFAULT_TTL = 7200; // 2 hours in seconds

  constructor() {
    this.redisService = RedisService.getInstance();
  }

  /**
   * Initialize Redis connection
   */
  async initialize(): Promise<void> {
    try {
      await this.redisService.connect();
      console.log('✅ ConversationStateService initialized with Redis');
    } catch (error) {
      console.error('❌ Failed to initialize ConversationStateService:', error);
      throw error;
    }
  }

  /**
   * Get Redis key for conversation
   */
  private getKey(conversationId: string): string {
    return `${this.KEY_PREFIX}${conversationId}`;
  }

  /**
   * Get conversation state
   */
  async getConversationState(conversationId: string): Promise<ConversationState> {
    try {
      const key = this.getKey(conversationId);
      const existing = await this.redisService.getObject<ConversationState>(key);
      
      if (existing) {
        console.log(`📋 Found existing state for ${conversationId}: ${existing.assignedTo} (updated by ${existing.updatedBy})`);
        return existing;
      }

      // Default state for new conversations
      const defaultState: ConversationState = {
        conversationId,
        assignedTo: 'unassigned',
        assignedAt: Date.now(),
        department: 'general', // Will be updated by Channel Router
        lastUpdated: Date.now(),
        updatedBy: 'bot-human-router'
      };

      console.log(`🆕 Creating default state for ${conversationId}: unassigned`);
      return defaultState;
    } catch (error) {
      console.error(`❌ Error getting conversation state for ${conversationId}:`, error);
      // Fallback to default state
      return {
        conversationId,
        assignedTo: 'unassigned',
        assignedAt: Date.now(),
        department: 'general',
        lastUpdated: Date.now(),
        updatedBy: 'bot-human-router'
      };
    }
  }

  /**
   * Set conversation state
   */
  async setConversationState(
    conversationId: string, 
    assignedTo: 'n8n' | 'human',
    department?: string,
    updatedBy: string = 'bot-human-router'
  ): Promise<void> {
    try {
      const state: ConversationState = {
        conversationId,
        assignedTo,
        assignedAt: Date.now(),
        department: department || 'general',
        lastUpdated: Date.now(),
        updatedBy
      };

      const key = this.getKey(conversationId);
      await this.redisService.setObject(key, state, this.DEFAULT_TTL);
      
      console.log(`💾 Saved state for ${conversationId}: ${assignedTo} (department: ${state.department}, by: ${updatedBy})`);
    } catch (error) {
      console.error(`❌ Error setting conversation state for ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Update department for existing conversation
   */
  async updateDepartment(conversationId: string, department: string): Promise<void> {
    try {
      const key = this.getKey(conversationId);
      const existing = await this.redisService.getObject<ConversationState>(key);
      
      if (existing) {
        existing.department = department;
        existing.lastUpdated = Date.now();
        existing.updatedBy = 'bot-human-router';
        
        await this.redisService.setObject(key, existing, this.DEFAULT_TTL);
        console.log(`🏢 Updated department for ${conversationId}: ${department}`);
      } else {
        console.warn(`⚠️ Cannot update department for ${conversationId} - conversation state not found`);
      }
    } catch (error) {
      console.error(`❌ Error updating department for ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Clear conversation state (for testing/cleanup)
   */
  async clearConversationState(conversationId: string): Promise<void> {
    try {
      const key = this.getKey(conversationId);
      await this.redisService.delete(key);
      console.log(`🗑️ Cleared state for ${conversationId}`);
    } catch (error) {
      console.error(`❌ Error clearing conversation state for ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Get all conversation states (for debugging)
   */
  async getAllStates(): Promise<ConversationState[]> {
    try {
      const pattern = `${this.KEY_PREFIX}*`;
      const keys = await this.redisService.getKeys(pattern);
      
      const states: ConversationState[] = [];
      for (const key of keys) {
        const state = await this.redisService.getObject<ConversationState>(key);
        if (state) {
          states.push(state);
        }
      }
      
      return states;
    } catch (error) {
      console.error('❌ Error getting all conversation states:', error);
      return [];
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<{ 
    redis: { connected: boolean; ping: boolean; error?: string };
    totalConversations: number; 
    unassigned: number; 
    n8n: number; 
    human: number; 
  }> {
    try {
      const redisHealth = await this.redisService.healthCheck();
      
      if (!redisHealth.connected) {
        return {
          redis: redisHealth,
          totalConversations: 0,
          unassigned: 0,
          n8n: 0,
          human: 0
        };
      }

      const states = await this.getAllStates();
      
      return {
        redis: redisHealth,
        totalConversations: states.length,
        unassigned: states.filter(s => s.assignedTo === 'unassigned').length,
        n8n: states.filter(s => s.assignedTo === 'n8n').length,
        human: states.filter(s => s.assignedTo === 'human').length
      };
    } catch (error) {
      console.error('❌ Error in health check:', error);
      return {
        redis: { connected: false, ping: false, error: 'Health check failed' },
        totalConversations: 0,
        unassigned: 0,
        n8n: 0,
        human: 0
      };
    }
  }
}