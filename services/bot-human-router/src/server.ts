import express from 'express';
import { botHumanRoutes } from './routes';
import { config } from './config';
import { DecisionEngine } from './decisionEngine';

const app = express();
const decisionEngine = new DecisionEngine();

app.use(express.json());
app.use('/api', botHumanRoutes);

// Health check at root level
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    service: 'bot-human-router',
    role: 'PubSub router to Chat Realtime',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

async function startServer() {
  try {
    // Initialize Redis connection first
    console.log('🔄 Initializing Bot Human Router...');
    await decisionEngine.initialize();
    
    app.listen(config.port, () => {
      console.log(`🔄 Bot Human Router running on port ${config.port}`);
      console.log(`📡 Chat Realtime URL: ${config.chatRealtimeUrl}`);
      console.log(`📨 PubSub Project: ${config.pubsub.projectId}`);
      console.log(`📥 Inbound Topic: ${config.pubsub.inboundTopic}`);
      console.log(`📤 Outbound Topic: ${config.pubsub.outboundTopic}`);
      console.log(`🔗 Redis connection: established`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();