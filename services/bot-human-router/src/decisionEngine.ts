import { N8nService } from './n8nService';
import { ChatRealtimeService } from './chatRealtimeService';
import { ConversationStateService, ConversationState } from './conversationStateService';

export interface DecisionResult {
  success: boolean;
  assignedTo: 'n8n' | 'human';
  assignedBotId?: string;
  assignedAgentId?: string;
  conversationId: string; // NEW: Conversation ID from Chat Realtime - ALWAYS required
  departmentId: string;
  reason: string;
  emergencyEscalated?: boolean;
  response?: any; // Response to send back to customer
  error?: string;
}

export interface MessageData {
  id: string;
  from: string;
  body: string;
  channel: string;
  sessionId: string;
  assignedDepartment: string; // From Channel Router - REQUIRED
  metadata?: TwilioMetadata; // NEW: Twilio metadata from Channel Router - OPTIONAL
}

// NEW: Import Twilio metadata structure
export interface TwilioMetadata {
  twilioMessageSid?: string;      // Original Twilio message ID
  twilioConversationSid?: string; // Original Twilio conversation ID
  messageIndex?: string;          // Message sequence in conversation
  participantSid?: string;        // Twilio participant ID
  source?: string;                // "SMS", "API", "Chat"
  eventType?: string;             // "onMessageAdded", etc.
  retryCount?: string;            // Webhook retry count
  attributes?: string;            // Custom Twilio attributes (JSON string)
}

export class DecisionEngine {
  private n8nService: N8nService;
  private chatRealtimeService: ChatRealtimeService;
  private conversationStateService: ConversationStateService;

  constructor() {
    this.n8nService = new N8nService();
    this.chatRealtimeService = new ChatRealtimeService();
    this.conversationStateService = new ConversationStateService();
  }

  /**
   * Initialize Decision Engine with Redis connection
   */
  async initialize(): Promise<void> {
    try {
      await this.conversationStateService.initialize();
      console.log('✅ Decision Engine initialized');
    } catch (error) {
      console.error('❌ Failed to initialize Decision Engine:', error);
      throw error;
    }
  }

  /**
   * Main processing logic for inbound messages
   * Checks conversation state and routes accordingly
   */
  async processInboundMessage(messageData: MessageData): Promise<DecisionResult> {
    try {
      console.log('🧠 Decision Engine processing message:', {
        sessionId: messageData.sessionId,
        from: messageData.from,
        messagePreview: messageData.body?.substring(0, 50) + '...'
      });

      // Step 1: ALWAYS check conversation state first
      const conversationState = await this.conversationStateService.getConversationState(
        messageData.sessionId
      );

      console.log(`📋 Conversation state: ${conversationState.assignedTo}`);

      // Step 2: Route based on state
      if (conversationState.assignedTo === 'unassigned') {
        // 🆕 First time - apply Decision Engine
        return await this.makeInitialDecision(messageData, conversationState);
      } 
      else if (conversationState.assignedTo === 'n8n') {
        // 🤖 Passthrough to N8N - already decided
        return await this.forwardToN8n(messageData, conversationState);
      } 
      else if (conversationState.assignedTo === 'human') {
        // 👤 Passthrough to Chat Realtime - human assigned
        return await this.forwardToChatRealtime(messageData, conversationState);
      }

      // Should never reach here
      throw new Error(`Unknown conversation state: ${conversationState.assignedTo}`);

    } catch (error) {
      console.error('❌ Decision Engine processing failed:', error);
      // Create emergency conversation ID for error cases
      const emergencyConversationId = `ERROR_${Date.now()}_${messageData.sessionId}`;
      return {
        success: false,
        assignedTo: 'human',
        conversationId: emergencyConversationId,
        departmentId: 'general',
        reason: 'Decision engine error - defaulting to human',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Make initial decision for unassigned conversations
   * UNIFIED ARCHITECTURE: Always register customer message first, then decide N8N vs Human
   */
  private async makeInitialDecision(
    messageData: MessageData, 
    conversationState: ConversationState
  ): Promise<DecisionResult> {
    try {
      console.log('🆕 Making initial decision for new conversation (UNIFIED FLOW)');

      // STEP 1: ALWAYS register customer message first (creates conversation)
      console.log('📝 Registering customer message in Chat Realtime...');
      const chatResult = await this.chatRealtimeService.processMessage(messageData);
      
      if (!chatResult.success || !chatResult.conversationId) {
        console.error('❌ Failed to register message in Chat Realtime');
        const emergencyConversationId = `ERROR_${Date.now()}_${messageData.sessionId}`;
        return {
          success: false,
          assignedTo: 'human',
          conversationId: emergencyConversationId,
          departmentId: conversationState.department,
          reason: 'Failed to register customer message',
          error: chatResult.error || 'No conversationId returned'
        };
      }

      // STEP 2: Ask N8N for bot analysis  
      if (!chatResult.conversationId) {
        throw new Error('Chat Realtime did not return conversationId');
      }
      console.log('🤖 Analyzing with N8N for bot capability...');
      const botAnalysis = await this.n8nService.analyzeBot(
        chatResult.conversationId, // 🐛 FIX: Use conversationId from Chat Realtime, not sessionId
        messageData.body,
        messageData.channel,
        messageData.from,
        messageData.assignedDepartment,
        messageData.metadata
      );

      // STEP 3: Update assignment based on N8N response
      if (botAnalysis.success && botAnalysis.handled) {
        // ✅ N8N can handle - assign to N8N bot agent
        console.log('✅ N8N will handle - assigning to bot agent');
        
        // Update conversation state to N8N
        await this.conversationStateService.setConversationState(
          messageData.sessionId, 
          'n8n',
          conversationState.department
        );

        // TODO: Update Chat Realtime assignment to N8N agent
        // await this.chatRealtimeService.assignConversation(chatResult.conversationId, N8N_AGENT_ID);
        
        return {
          success: true,
          assignedTo: 'n8n',
          assignedBotId: '577cdef7-2f2a-4185-861e-6f07a7819f1b', // N8N Agent UUID
          conversationId: chatResult.conversationId,
          departmentId: conversationState.department,
          reason: 'Message registered, N8N bot will handle responses'
        };
      } else {
        // ❌ N8N cannot handle - keep human assignment from Chat Realtime
        console.log('❌ N8N cannot handle - keeping human agent assignment');
        
        await this.conversationStateService.setConversationState(
          messageData.sessionId, 
          'human',
          conversationState.department
        );

        return {
          success: true,
          assignedTo: 'human',
          assignedAgentId: chatResult.assignedAgentId,
          conversationId: chatResult.conversationId,
          departmentId: conversationState.department,
          reason: 'Message registered, N8N unavailable - human agent assigned',
          response: chatResult.response
        };
      }
    } catch (error) {
      console.error('❌ Failed to make initial decision:', error);
      
      // Fallback: conversation stays with human
      await this.conversationStateService.setConversationState(
        messageData.sessionId, 
        'human',
        conversationState.department
      );

      const emergencyConversationId = `ERROR_${Date.now()}_${messageData.sessionId}`;
      return {
        success: false,
        assignedTo: 'human',
        conversationId: emergencyConversationId,
        departmentId: conversationState.department,
        reason: 'Initial assignment error: Message registered, defaulted to human agent',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Forward message to N8N (passthrough mode)
   * UNIFIED ARCHITECTURE: Always register message first, then forward to N8N
   */
  private async forwardToN8n(
    messageData: MessageData, 
    conversationState: ConversationState
  ): Promise<DecisionResult> {
    try {
      console.log('🤖 Forwarding to N8N (passthrough mode)');

      // STEP 1: ALWAYS register customer message first (maintains conversation)
      console.log('📝 Registering customer message in Chat Realtime (passthrough)...');
      const chatResult = await this.chatRealtimeService.processMessage(messageData);
      
      if (!chatResult.success || !chatResult.conversationId) {
        console.error('❌ Failed to register message in Chat Realtime (passthrough)');
        const emergencyConversationId = `ERROR_${Date.now()}_${messageData.sessionId}`;
        return {
          success: false,
          assignedTo: 'n8n',
          conversationId: emergencyConversationId,
          departmentId: conversationState.department,
          reason: 'Failed to register customer message in passthrough mode',
          error: chatResult.error || 'No conversationId returned'
        };
      }

      // STEP 2: Forward to N8N for response decision
      const botAnalysis = await this.n8nService.analyzeBot(
        chatResult.conversationId, // 🐛 FIX: Use conversationId from Chat Realtime, not sessionId
        messageData.body,
        messageData.channel,
        messageData.from,
        messageData.assignedDepartment,
        messageData.metadata
      );

      // STEP 3: Check if N8N still handles it (201 status)
      if (botAnalysis.success && botAnalysis.handled) {
        console.log('✅ N8N continues handling (passthrough mode)');
        return {
          success: true,
          assignedTo: 'n8n',
          assignedBotId: '577cdef7-2f2a-4185-861e-6f07a7819f1b', // N8N Agent UUID
          conversationId: chatResult.conversationId,
          departmentId: conversationState.department,
          reason: 'Message registered, N8N continues handling (passthrough mode)'
        };
      } else {
        // N8N returned ≠201 - transfer to human (N8N cedes control)
        console.log('⚠️ N8N returned non-201 - transferring to human (N8N CEDES CONTROL)');
        
        await this.conversationStateService.setConversationState(
          messageData.sessionId, 
          'human',
          conversationState.department
        );

        // TODO: Transfer conversation from N8N agent to human agent
        // await this.chatRealtimeService.transferConversation(chatResult.conversationId, humanAgentId);
        
        return {
          success: true,
          assignedTo: 'human',
          assignedAgentId: chatResult.assignedAgentId || 'unknown',
          conversationId: chatResult.conversationId,
          departmentId: conversationState.department,
          reason: 'Transfer: N8N ceded control - conversation transferred to human agent',
          response: chatResult.response
        };
      }
    } catch (error) {
      console.error('❌ Failed to forward to N8N:', error);
      
      const emergencyConversationId = `ERROR_${Date.now()}_${messageData.sessionId}`;
      return {
        success: false,
        assignedTo: 'n8n',
        conversationId: emergencyConversationId,
        departmentId: conversationState.department,
        reason: 'Error forwarding to N8N',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Forward message to Chat Realtime (passthrough mode)
   */
  private async forwardToChatRealtime(
    messageData: MessageData, 
    conversationState: ConversationState
  ): Promise<DecisionResult> {
    try {
      console.log('👤 Forwarding to Chat Realtime (passthrough mode)');

      // Direct forward to Chat Realtime - human has control
      const result = await this.chatRealtimeService.processMessage(messageData);

      return {
        success: result.success,
        assignedTo: 'human',
        assignedAgentId: result.assignedAgentId || 'unknown',
        conversationId: result.conversationId || `ERROR_${Date.now()}_${messageData.sessionId}`,
        departmentId: conversationState.department,
        reason: 'Message forwarded to human agent (passthrough mode)',
        response: result.response,
        error: result.error
      };
    } catch (error) {
      console.error('❌ Failed to forward to Chat Realtime:', error);
      
      const emergencyConversationId = `ERROR_${Date.now()}_${messageData.sessionId}`;
      return {
        success: false,
        assignedTo: 'human',
        conversationId: emergencyConversationId,
        departmentId: conversationState.department,
        reason: 'Error forwarding to Chat Realtime',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Health check for Decision Engine components
   */
  async healthCheck(): Promise<{ 
    n8n: any; 
    chatRealtime: any;
    conversationState: any;
    overallStatus: 'healthy' | 'degraded' | 'unhealthy';
  }> {
    const n8nHealth = await this.n8nService.healthCheck();
    const chatRealtimeHealth = await this.chatRealtimeService.healthCheck();
    const conversationStateHealth = await this.conversationStateService.healthCheck();

    const overallStatus = 
      n8nHealth.authConfigured && chatRealtimeHealth.chatRealtime ? 'healthy' :
      n8nHealth.authConfigured || chatRealtimeHealth.chatRealtime ? 'degraded' :
      'unhealthy';

    return {
      n8n: n8nHealth,
      chatRealtime: chatRealtimeHealth,
      conversationState: conversationStateHealth,
      overallStatus
    };
  }
}