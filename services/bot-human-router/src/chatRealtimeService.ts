import axios from 'axios';
import { config } from './config';

interface ProcessMessageResponse {
  success: boolean;
  response?: {
    to: string;
    body: string;
    channel: string;
    sessionId: string;
  };
  error?: string;
  // NEW: Properties needed by Decision Engine
  conversationId?: string;
  assignedAgentId?: string;
  assignedBotId?: string;
}

interface PubSubPublishResult {
  success: boolean;
  messageId?: string;
  publishedAt?: string;
  error?: string;
}

export class ChatRealtimeService {
  private readonly chatRealtimeUrl: string;

  constructor() {
    this.chatRealtimeUrl = config.chatRealtimeUrl;
  }

  /**
   * Send message to Chat Realtime /process-message endpoint
   */
  async processMessage(messageData: any): Promise<ProcessMessageResponse> {
    try {
      const response = await axios.post(`${this.chatRealtimeUrl}/api/process-message`, 
        messageData,
        {
          timeout: 10000,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      // Chat Realtime should return structured response
      const responseData = response.data;
      
      return {
        success: true,
        response: responseData.response || null,
        // Extract assignment info from Chat Realtime response
        conversationId: responseData.conversationId,
        assignedAgentId: responseData.assignedAgentId,
        assignedBotId: responseData.assignedBotId
      };
      
    } catch (error) {
      console.error('Chat Realtime /process-message failed:', error);
      
      if (axios.isAxiosError(error) && error.response?.status === 501) {
        // Endpoint not implemented yet - this is expected during development
        return {
          success: true,
          response: undefined // No response to send back
        };
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Chat Realtime call failed'
      };
    }
  }

  /**
   * Publish response message to PubSub OUTBOUND
   */
  async publishToOutbound(responseData: any): Promise<PubSubPublishResult> {
    try {
      // Use PubSub emulator for development or real PubSub for production
      if (process.env.PUBSUB_USE_EMULATOR === 'true' || process.env.NODE_ENV === 'development') {
        const { PubSub } = require('@google-cloud/pubsub');
        
        const pubSubClient = new PubSub({ 
          projectId: config.pubsub.projectId,
          // Emulator connection is handled by PUBSUB_EMULATOR_HOST env var
        });
        
        const topic = pubSubClient.topic(config.pubsub.outboundTopic);
        const messageBuffer = Buffer.from(JSON.stringify(responseData));
        
        const [messageId] = await topic.publish(messageBuffer);
        
        console.log('📤 Published to OUTBOUND PubSub:', {
          topic: config.pubsub.outboundTopic,
          messageId,
          to: responseData.to,
          channel: responseData.channel
        });

        return {
          success: true,
          messageId: messageId,
          publishedAt: new Date().toISOString()
        };
      }

      // Production PubSub implementation
      const { PubSub } = require('@google-cloud/pubsub');
      const pubSubClient = new PubSub({ projectId: config.pubsub.projectId });
      const topic = pubSubClient.topic(config.pubsub.outboundTopic);
      const messageBuffer = Buffer.from(JSON.stringify(responseData));
      const [messageId] = await topic.publish(messageBuffer);

      return {
        success: true,
        messageId: messageId,
        publishedAt: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('Failed to publish to PubSub OUTBOUND:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'PubSub publish failed'
      };
    }
  }

  /**
   * Health check for dependencies
   */
  async healthCheck(): Promise<{ chatRealtime: boolean; pubsub: boolean }> {
    const results = {
      chatRealtime: false,
      pubsub: true // Mock is always "healthy"
    };

    // Check Chat Realtime
    try {
      const response = await axios.get(`${this.chatRealtimeUrl}/api/health`, {
        timeout: 3000
      });
      results.chatRealtime = response.status === 200;
    } catch (error) {
      console.error('Chat Realtime health check failed:', error);
    }

    return results;
  }
}
