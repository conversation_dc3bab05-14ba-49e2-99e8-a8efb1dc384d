import axios from 'axios';

export interface BotAnalysisRequest {
  conversationId: string;          // "CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6" - REQUIRED, never empty
  message: string;                 // Customer message content - REQUIRED, never empty
  channel: string;                 // "whatsapp", "web", "sms" - REQUIRED, never empty
  customerPhone: string;           // "+1234567890" - Twilio format - REQUIRED, never empty
  department: string;              // Department assigned by Channel Router - REQUIRED
  
  // Configuration for N8N to make Chat Realtime API calls
  chatRealtimeConfig: {
    baseUrl: string;               // "http://localhost:3003/api" 
    endpoints: {
      getConversation: string;     // "/conversations/{id}"
      getMessages: string;         // "/conversations/{id}/messages"
      sendMessage: string;         // "/conversations/{id}/messages"  
      updateStatus: string;        // "/conversations/{id}/status"
      assignAgent: string;         // "/conversations/{id}/assign"
      escalate: string;           // "/conversations/{id}/escalate"
      transfer: string;           // "/conversations/{id}/transfer"
    }
  };
  
  // NEW: Twilio Conversations API metadata
  metadata?: {
    twilioMessageSid?: string;      // Original Twilio message ID
    twilioConversationSid?: string; // Original Twilio conversation ID
    messageIndex?: string;          // Message sequence in conversation
    participantSid?: string;        // Twilio participant ID
    source?: string;                // "SMS", "API", "Chat"
    eventType?: string;             // "onMessageAdded", etc.
    retryCount?: string;            // Webhook retry count
    attributes?: string;            // Custom Twilio attributes (JSON string)
  };
}

export interface BotAnalysisResponse {
  success: boolean;
  handled: boolean;   // 201 = true, ≠201 = false
  error?: string;
}

export interface EmergencyEscalationResponse {
  success: boolean;
  escalated?: boolean;
  message?: string;
  error?: string;
}

export class N8nService {
  private readonly authToken: string;
  private readonly timeout: number;
  private readonly baseUrl: string;
  private readonly isProduction: boolean;
  private readonly chatRealtimeUrl: string;

  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.baseUrl = this.isProduction 
      ? process.env.N8N_BASE_URL_PRODUCTION! 
      : process.env.N8N_BASE_URL_LOCAL!;
    this.authToken = this.isProduction ? (process.env.N8N_AUTH_TOKEN_PRODUCTION || '') : '';
    this.timeout = parseInt(process.env.N8N_WEBHOOK_TIMEOUT || '5000');
    this.chatRealtimeUrl = process.env.CHAT_REALTIME_URL || 'http://localhost:3003/api';

    if (this.isProduction && !this.authToken) {
      console.warn('N8N_AUTH_TOKEN_PRODUCTION not configured - production webhooks will fail');
    }
  }


  /**
   * Call N8N Bot Analysis webhook with enriched payload
   */
  async analyzeBot(
    conversationId: string, 
    message: string, 
    channel: string, 
    customerPhone: string,
    department: string = 'general',
    metadata?: any
  ): Promise<BotAnalysisResponse> {
    // Validate required parameters
    if (!conversationId || conversationId.trim() === '') {
      console.error('N8N Bot analysis: conversationId is required');
      return { success: false, handled: false, error: 'conversationId is required and cannot be empty' };
    }
    
    if (!message || message.trim() === '') {
      console.error('N8N Bot analysis: message is required');
      return { success: false, handled: false, error: 'message is required and cannot be empty' };
    }
    
    if (!channel || channel.trim() === '') {
      console.error('N8N Bot analysis: channel is required');
      return { success: false, handled: false, error: 'channel is required and cannot be empty' };
    }
    
    if (!customerPhone || customerPhone.trim() === '') {
      console.error('N8N Bot analysis: customerPhone is required');
      return { success: false, handled: false, error: 'customerPhone is required and cannot be empty' };
    }

    const webhookPath = this.isProduction 
      ? process.env.N8N_BOT_WEBHOOK_PATH_PRODUCTION
      : process.env.N8N_BOT_WEBHOOK_PATH_LOCAL;
    
    if (!webhookPath) {
      console.error('N8N Bot webhook path not configured');
      return { success: false, handled: false, error: 'Bot webhook path not configured' };
    }

    const webhookUrl = `${this.baseUrl}${webhookPath}`;

    // Build enriched payload with Chat Realtime API config
    const payload: BotAnalysisRequest = {
      conversationId,
      message,
      channel,
      customerPhone,
      department,
      chatRealtimeConfig: {
        baseUrl: this.chatRealtimeUrl,
        endpoints: {
          getConversation: `/conversations/${conversationId}`,
          getMessages: `/conversations/${conversationId}/messages`,
          sendMessage: `/conversations/${conversationId}/messages`,
          updateStatus: `/conversations/${conversationId}/status`,
          assignAgent: `/conversations/${conversationId}/assign`,
          escalate: `/conversations/${conversationId}/escalate`,
          transfer: `/conversations/${conversationId}/transfer`
        }
      },
      metadata: metadata || undefined
    };

    try {
      console.log('🤖 Calling N8N Bot Analysis:', { 
        conversationId, 
        channel,
        customerPhone,
        messagePreview: message.substring(0, 50) 
      });
      
      const response = await axios.post(webhookUrl, payload, {
        timeout: this.timeout,
        headers: {
          ...(this.isProduction && { 'Authorization': `Bearer ${this.authToken}` }),
          'Content-Type': 'application/json'
        }
      });

      // Check status code: 201 = bot handled, ≠201 = assign to human
      const handled = response.status === 201;
      
      console.log(`✅ Bot analysis response: ${response.status} → ${handled ? 'BOT' : 'HUMAN'}`);
      
      return {
        success: true,
        handled
      };
      
    } catch (error) {
      const statusCode = axios.isAxiosError(error) ? error.response?.status : 'unknown';
      console.log(`⚠️ Bot analysis failed (${statusCode}) → HUMAN assignment`);
      
      // Any non-201 response = assign to human
      return {
        success: false,
        handled: false,
        error: 'N8N unavailable - routing to human'
      };
    }
  }

  /**
   * Call N8N Emergency Escalation webhook
   */
  async triggerEmergencyEscalation(conversationId: string): Promise<EmergencyEscalationResponse> {
    const webhookPath = this.isProduction 
      ? process.env.N8N_EMERGENCY_WEBHOOK_PATH_PRODUCTION
      : process.env.N8N_EMERGENCY_WEBHOOK_PATH_LOCAL;
    
    if (!webhookPath) {
      console.error('N8N Emergency webhook path not configured');
      return { success: false, error: 'Emergency webhook path not configured' };
    }

    const webhookUrl = `${this.baseUrl}${webhookPath}`;

    try {
      console.log('🚨 Triggering N8N Emergency Escalation:', { conversationId });
      
      const response = await axios.post(webhookUrl, 
        {
          conversationId
        },
        {
          timeout: this.timeout,
          headers: {
            ...(this.isProduction && { 'Authorization': `Bearer ${this.authToken}` }),
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('✅ Emergency escalation response:', response.data);
      
      return {
        success: true,
        escalated: response.data.escalated || false,
        message: response.data.message
      };
      
    } catch (error) {
      console.error('❌ Emergency escalation webhook failed:', 
        axios.isAxiosError(error) ? error.response?.status : error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Emergency escalation failed'
      };
    }
  }

  /**
   * Health check for N8N webhooks
   * NOTE: Department Analysis webhook removed - Channel Router handles it
   */
  async healthCheck(): Promise<{ 
    bot: boolean; 
    emergency: boolean;
    authConfigured: boolean;
    baseUrl: string;
    environment: string;
  }> {
    const botPath = this.isProduction 
      ? process.env.N8N_BOT_WEBHOOK_PATH_PRODUCTION
      : process.env.N8N_BOT_WEBHOOK_PATH_LOCAL;
    const emergencyPath = this.isProduction 
      ? process.env.N8N_EMERGENCY_WEBHOOK_PATH_PRODUCTION
      : process.env.N8N_EMERGENCY_WEBHOOK_PATH_LOCAL;

    return {
      bot: !!botPath,
      emergency: !!emergencyPath,
      authConfigured: this.isProduction ? !!this.authToken : true,
      baseUrl: this.baseUrl,
      environment: this.isProduction ? 'production' : 'development'
    };
  }
}