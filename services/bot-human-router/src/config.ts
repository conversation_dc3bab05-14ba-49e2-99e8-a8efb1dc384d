import dotenv from 'dotenv';

dotenv.config({ path: '../../../.env' });

export const config = {
  port: process.env.BOT_HUMAN_ROUTER_PORT || process.env.PORT || 3004,
  
  // Service URLs
  chatRealtimeUrl: process.env.CHAT_REALTIME_URL || 'http://localhost:3003',
  
  // PubSub configuration (for Cloud Run)
  pubsub: {
    projectId: process.env.GOOGLE_CLOUD_PROJECT_ID || 'cx-system-469120',
    inboundTopic: process.env.PUBSUB_INBOUND_TOPIC || 'inbound-messages',
    outboundTopic: process.env.PUBSUB_OUTBOUND_TOPIC || 'outbound-messages'
  },
  
  // Processing Configuration
  messageTimeout: parseInt(process.env.MESSAGE_TIMEOUT || '10000'), // 10 seconds
};