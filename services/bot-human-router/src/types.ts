export interface IncomingMessage {
  from: string;
  body: string;
  timestamp: string;
  conversationId: string;
}

export interface RoutingDecision {
  route: 'bot' | 'human';
  confidence: number;
  reason: string;
  botWebhookUrl?: string;
  agentRequired?: boolean;
}

export interface BotResponse {
  success: boolean;
  response?: string;
  handoffToHuman?: boolean;
  reason?: string;
}

export interface RoutingRequest {
  message: IncomingMessage;
  conversationContext?: ConversationContext;
}

export interface ConversationContext {
  id: string;
  customerId: string;
  channel: 'whatsapp' | 'web' | 'sms';
  department?: string;
  botAttempts: number;
  lastBotResponse?: string;
  isFirstMessage: boolean;
  customerSentiment?: 'positive' | 'neutral' | 'negative';
}

export interface RoutingRule {
  id: string;
  name: string;
  conditions: RuleCondition[];
  action: 'route_to_bot' | 'route_to_human' | 'escalate';
  priority: number;
  isActive: boolean;
}

export interface RuleCondition {
  field: string; // 'message_body', 'department', 'bot_attempts', etc.
  operator: 'contains' | 'equals' | 'greater_than' | 'less_than';
  value: string | number;
}