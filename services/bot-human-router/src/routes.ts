import { Request, Response, Router } from 'express';
import { ChatRealtimeService } from './chatRealtimeService';
import { DecisionEngine } from './decisionEngine';
import { EmergencyNotificationService } from './emergencyNotificationService';

const router = Router();
const chatRealtimeService = new ChatRealtimeService();
const decisionEngine = new DecisionEngine();
const emergencyNotificationService = new EmergencyNotificationService();

// Health check
router.get('/health', async (req: Request, res: Response) => {
  try {
    const decisionEngineHealth = await decisionEngine.healthCheck();
    const emergencyNotificationHealth = await emergencyNotificationService.healthCheck();
    
    res.json({ 
      status: 'ok', 
      service: 'bot-human-router',
      role: 'Decision Engine for Bot vs Human routing',
      timestamp: new Date().toISOString(),
      components: {
        decisionEngine: decisionEngineHealth,
        emergencyNotifications: emergencyNotificationHealth
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      service: 'bot-human-router',
      error: error instanceof Error ? error.message : 'Health check failed',
      timestamp: new Date().toISOString()
    });
  }
});

// PubSub INBOUND Push endpoint - receives messages from Channel Router
router.post('/pubsub/inbound', async (req: Request, res: Response) => {
  try {
    // Decode PubSub Push message
    const envelope = req.body;
    
    if (!envelope || !envelope.message) {
      return res.status(400).json({ error: 'Invalid PubSub message format' });
    }

    const pubsubMessage = envelope.message;
    let messageData;

    if (pubsubMessage.data) {
      const decodedData = Buffer.from(pubsubMessage.data, 'base64').toString('utf-8');
      messageData = JSON.parse(decodedData);
    } else {
      return res.status(400).json({ error: 'No message data' });
    }

    console.log('📨 Received INBOUND message from PubSub:', {
      sessionId: messageData.sessionId,
      from: messageData.from,
      messagePreview: messageData.body?.substring(0, 50) + '...'
    });

    // 🧠 NEW: Use Decision Engine to determine bot vs human routing
    const decisionResult = await decisionEngine.processInboundMessage(messageData);
    
    if (decisionResult.success) {
      console.log('✅ Decision Engine result:', {
        assignedTo: decisionResult.assignedTo,
        assignedBotId: decisionResult.assignedBotId,
        assignedAgentId: decisionResult.assignedAgentId,
        departmentId: decisionResult.departmentId,
        reason: decisionResult.reason,
        emergencyEscalated: decisionResult.emergencyEscalated
      });

      // Check if Decision Engine wants to send a response
      if (decisionResult.response) {
        // Send response to PubSub OUTBOUND
        await chatRealtimeService.publishToOutbound(decisionResult.response);
      }
      
      // Return 204 to acknowledge PubSub message
      res.status(204).send();
    } else {
      console.error('Decision Engine processing failed:', decisionResult.error);
      res.status(500).json({ error: 'Failed to process message via Decision Engine' });
    }
    
  } catch (error) {
    console.error('PubSub inbound endpoint error:', error);
    res.status(500).json({
      error: 'Failed to process inbound message',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Test endpoint for emergency notifications
router.post('/test/emergency', async (req: Request, res: Response) => {
  try {
    console.log('🧪 Testing emergency notification system...');
    
    const result = await emergencyNotificationService.testNotifications();
    
    res.json({
      message: 'Emergency notification test completed',
      result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Emergency notification test failed:', error);
    res.status(500).json({
      error: 'Emergency notification test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
});

export { router as botHumanRoutes };