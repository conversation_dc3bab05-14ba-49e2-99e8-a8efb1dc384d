import { N8nService } from './n8nService';

export interface EmergencyNotificationResult {
  success: boolean;
  notificationsSent: number;
  methods: string[];
  error?: string;
}

export class EmergencyNotificationService {
  private n8nService: N8nService;
  private isEnabled: boolean;

  constructor() {
    this.n8nService = new N8nService();
    this.isEnabled = process.env.EMERGENCY_NOTIFICATION_ENABLED === 'true';
    
    if (!this.isEnabled) {
      console.warn('Emergency notifications are DISABLED - set EMERGENCY_NOTIFICATION_ENABLED=true to enable');
    }
  }

  /**
   * Trigger emergency notification when no agents are available
   */
  async notifyNoAgentsAvailable(conversationId: string, customerInfo?: any): Promise<EmergencyNotificationResult> {
    if (!this.isEnabled) {
      console.log('⚠️ Emergency notification skipped (disabled)');
      return {
        success: true,
        notificationsSent: 0,
        methods: [],
        error: 'Emergency notifications disabled'
      };
    }

    try {
      console.log(`🚨 Triggering emergency notification for conversation: ${conversationId}`);
      
      const methods: string[] = [];
      let notificationsSent = 0;

      // Method 1: N8N Emergency Webhook (primary)
      try {
        const webhookResult = await this.n8nService.triggerEmergencyEscalation(conversationId);
        
        if (webhookResult.success) {
          methods.push('n8n-webhook');
          notificationsSent++;
          console.log('✅ N8N emergency webhook triggered successfully');
        } else {
          console.warn('⚠️ N8N emergency webhook failed:', webhookResult.error);
        }
      } catch (error) {
        console.error('❌ N8N emergency webhook error:', error);
      }

      // Method 2: Console Alert (always available)
      this.logEmergencyAlert(conversationId, customerInfo);
      methods.push('console-alert');
      notificationsSent++;

      // Method 3: Future - Email notifications
      // Method 4: Future - SMS notifications  
      // Method 5: Future - Slack notifications

      const result: EmergencyNotificationResult = {
        success: notificationsSent > 0,
        notificationsSent,
        methods
      };

      console.log(`📡 Emergency notifications sent: ${notificationsSent} methods (${methods.join(', ')})`);
      
      return result;

    } catch (error) {
      console.error('❌ Emergency notification service failed:', error);
      
      // Fallback: At least log the emergency
      this.logEmergencyAlert(conversationId, customerInfo);
      
      return {
        success: false,
        notificationsSent: 1,
        methods: ['console-alert-fallback'],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Log emergency alert to console (always available fallback)
   */
  private logEmergencyAlert(conversationId: string, customerInfo?: any): void {
    const timestamp = new Date().toISOString();
    const alert = `
🚨🚨🚨 EMERGENCY ALERT 🚨🚨🚨
Timestamp: ${timestamp}
Conversation ID: ${conversationId}
Issue: NO AGENTS AVAILABLE
Customer: ${customerInfo?.name || 'Unknown'} (${customerInfo?.phone || 'No phone'})
Channel: ${customerInfo?.channel || 'Unknown'}
Action Required: Manual intervention needed
🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨
`;
    
    console.error(alert);
    
    // Also log to error stream for monitoring systems
    process.stderr.write(alert + '\n');
  }

  /**
   * Notify when conversation has been queued for too long
   */
  async notifyLongQueueTime(conversationId: string, queueTimeMinutes: number): Promise<EmergencyNotificationResult> {
    if (!this.isEnabled) {
      return {
        success: false,
        notificationsSent: 0,
        methods: [],
        error: 'Emergency notifications disabled'
      };
    }

    console.log(`⏰ Long queue time notification: ${conversationId} (${queueTimeMinutes} minutes)`);
    
    // Log warning for monitoring
    const alert = `
⚠️ QUEUE TIME ALERT ⚠️
Conversation ID: ${conversationId}
Queue Time: ${queueTimeMinutes} minutes
Action: Consider escalating or adding more agents
`;
    
    console.warn(alert);
    
    return {
      success: true,
      notificationsSent: 1,
      methods: ['console-alert']
    };
  }

  /**
   * Test emergency notification system
   */
  async testNotifications(): Promise<EmergencyNotificationResult> {
    const testConversationId = `test_emergency_${Date.now()}`;
    
    console.log('🧪 Testing emergency notification system...');
    
    return await this.notifyNoAgentsAvailable(testConversationId, {
      name: 'Test Customer',
      phone: '+**********',
      channel: 'whatsapp'
    });
  }

  /**
   * Health check for emergency notification system
   */
  async healthCheck(): Promise<{
    enabled: boolean;
    n8nWebhookReady: boolean;
    consoleAlertsReady: boolean;
    overallStatus: 'ready' | 'degraded' | 'disabled';
  }> {
    const n8nHealth = await this.n8nService.healthCheck();
    const n8nReady = n8nHealth.emergency && n8nHealth.authConfigured;
    const consoleReady = true; // Always available
    
    const overallStatus = 
      !this.isEnabled ? 'disabled' :
      n8nReady && consoleReady ? 'ready' :
      consoleReady ? 'degraded' : 'disabled';

    return {
      enabled: this.isEnabled,
      n8nWebhookReady: n8nReady,
      consoleAlertsReady: consoleReady,
      overallStatus
    };
  }
}