# CLAUDE.md - CX System Guide

## Project Overview
Customer Experience (CX) system with microservices for Cloud Run deployment:
- **twilio**: WhatsApp integration via webhooks
- **channel-router**: Message routing + conversation lifecycle
- **session-manager**: Session management using Redis
- **bot-human-router**: AI/Human routing via n8n webhooks  
- **chat-realtime**: Exclusive Firebase Realtime Database interface
- **chat-ui**: Next.js frontend with offline resilience
- **analytics**: Metrics collection using Supabase

## Architecture
```
WhatsApp → Twilio → Channel Router → Session Manager (Redis)
                         ↓              ↓
                   PubSub (inbound/outbound)
                         ↓
                Bot Human Router → Decision: Bot (n8n) / Human
                         ↓
                Chat Realtime ← → Firebase Realtime Database
                         ↓
                   Chat UI (Next.js)
                         ↓
                 Analytics → Supabase
```

## 🚨 DEVELOPMENT RULES - CRITICAL

### Service Management 
- **NEVER restart or kill services** - Always ask user to restart when needed
- **NEVER use pkill, killall, or restart commands**
- **Background processes**: Never start services - let user manage lifecycle

### Frontend Architecture Rules (POST-REFACTOR)
- **Supervisor Mode**: OBLIGATORIO usar TanStack Query hooks (`useQuery`, `useMutation`)
- **Agent Mode**: PRESERVAR optimistic UI custom para real-time chat experience  
- **NO Mixed Patterns**: Never mix useEffect/useCallback polling con TanStack Query
- **Memory Safety**: Zero setInterval usage, usar `refetchInterval` de TanStack Query
- **Mutation Pattern**: Usar `useConversationMutations` y `useMessageMutations` para UI updates

### API Contracts & Data Consistency
- **Check TypeScript types** in `src/types/api.ts` before implementing
- **Transform database responses** to match UI contracts exactly
- **Firebase dot notation fix**: Use `metadata: { field: value }` NOT `'metadata.field': value`
- **Contract violations**: Never create database fields that don't match TypeScript interfaces

## Database Architecture

### Firebase Realtime Database (Real-time Operations)
- Conversations, messages, agent status, transfer states
- **EPHEMERAL DATA** - destroyed on emulator restart
- Structure: `/conversations/{id}/` with metadata, messages, assignment, transfer

### Supabase (PostgreSQL) (Configuration & Analytics)  
- Organizations, agents, customers, departments
- Analytics events, audit logs, integrations
- **Multi-tenancy**: Single project with `organizations` table + RLS policies

## 🔥 CRITICAL: Firebase Emulator Development

### Data Recreation Protocol (MANDATORY after restart)
```bash
# 1. Start services
./scripts/start-all-emulators-improved.sh
cd services/chat-realtime && npm run dev
cd services/chat-ui && npm run dev -- --port 3007

# 2. Recreate test data (ALWAYS)
cd services/chat-realtime
node conversation_manager.js --create juan 5
```

### Required Test Data
- 5 clean conversations with Twilio format (CHaaaa...)  
- Agent: Juan Pérez (5f4fb378-908d-4b49-83ce-be4ce3b50c5d)
- Customer names: Roberto Silva, Carmen López, Diego Martínez, Lucía Fernández, Patricia Morales
- System messages: welcome, agent_assigned, agent_transferred, conversation_ended, session_ended

## Current System Status (2025-08-26)

### ✅ PRODUCTION-READY Services
- **Chat Realtime**: Port 3003 - Firebase CRUD + PubSub + Transfer API
- **Channel Router**: Port 3002 - Message routing operational  
- **Bot Human Router**: Port 3004 - Decision engine functional
- **Chat UI**: Port 3007 - **Hybrid TanStack + Optimistic Architecture**
- **Session Manager**: Clean TypeScript compilation
- **Agent Assignment**: Load balancing by utilization working

### ✅ Completed Features
- **Agent Status Management**: available/busy/away states
- **Load Balancing**: Utilization-based algorithm (sessions/max_sessions)
- **Conversation Transfer**: Complete API `/conversations/:id/transfer`
- **PubSub Integration**: Bidirectional Chat UI ↔ Channel Router
- **Real-time Messaging**: Firebase + optimistic UI
- **Department Routing**: AI-driven assignment via n8n mocks
- **📊 HYBRID ARCHITECTURE**: TanStack Query (supervisor) + Optimistic UI (agent)

### 🛠️ MAJOR REFACTOR (2025-08-25) — TanStack Query Migration
- **COMPLETE Supervisor Hooks Migration**: Eliminados 800+ líneas de hooks problemáticos con useCallback/useEffect
- **Infinite Re-render Fix**: Supervisor dashboard estable, sin ciclos infinitos de re-renders
- **Hybrid Architecture Implemented**: 
  - **Supervisor Mode**: TanStack Query con polling 30s, memory-safe caching
  - **Agent Mode**: Custom optimistic UI preservado para real-time chat performance
- **Optimistic Updates Extended**: Priority updates, instant filtering, conversation animations
- **Message Handling Migrated**: ChatArea + ConversationList usando TanStack mutations
- **Performance Gains**: Eliminados memory leaks, caching inteligente, 10s staleTime

## Development Workflow

### Session Management
- **Feature branches**: One per session (not per service)
- **Incremental progress**: Functional work by session end
- **Session closure**: Code review → Testing → Git commit → Service cleanup

### Code Standards
- **MVP first**: Implement only what's explicitly requested
- **TypeScript throughout**: Proper type safety
- **Simple & readable**: No complex code
- **100% testable**: Clean architecture patterns
- **🚨 NO MOCK DATA**: NUNCA usar mock data en implementación real - implementar funcionalidad real SIEMPRE

## Quick Start Commands

### Start Development Session
```bash
./scripts/start-all-emulators-improved.sh
cd services/chat-realtime && npm run dev &
cd services/chat-ui && npm run dev -- --port 3007 &
cd services/chat-realtime && node conversation_manager.js --create juan 5
```

### Verify System
- Firebase UI: http://127.0.0.1:4000
- Chat Realtime API: http://localhost:3003/api/health  
- Chat UI: http://localhost:3007/agent
- PubSub Emulator: localhost:8085

## 🚨 Troubleshooting

### Chat UI Cache Issues (FREQUENT PROBLEM)
1. **Hard refresh**: Cmd+Shift+R → "Empty Cache and Hard Reload"
2. **Clear storage**: `localStorage.clear(); sessionStorage.clear(); location.reload(true);`
3. **Next.js cache**: `rm -rf .next && npm run build && npm run dev -- --port 3007`
4. **Nuclear reset**: Kill all processes + restart emulators + recreate data

### Debug Tools
```javascript
// Chat UI console debugging
window.cxDebug.clearCache()
window.cxDebug.showCacheState() 
localStorage.clear()
```

## Environment
- **Development**: Single `.env` file in project root
- **Production**: Google Secret Manager for sensitive variables
- **Firebase**: Emulator for development (127.0.0.1:9000)
- **Supabase**: Dev instance for local development

## Key URLs
- **Chat UI**: http://localhost:3007/agent
- **Firebase Console**: http://127.0.0.1:4000
- **Chat Realtime Health**: http://localhost:3003/api/health

## 🛠️ Conversation Manager Tool

### Overview
`services/chat-realtime/conversation_manager.js` - Herramienta CLI completa para gestión de conversaciones Firebase.

### Available Commands

#### 🆕 CREATE CONVERSATIONS
```bash
node conversation_manager.js --create <agentId> <count>     # Crear N conversaciones para agente específico
node conversation_manager.js --create-default              # Recrear 4 conversaciones por defecto (compatibilidad)
```

#### 🔄 TRANSFER OPERATIONS
```bash
node conversation_manager.js --transfer <conversationId> <targetAgentId> [reason]  # Transferir conversación
node conversation_manager.js --accept-transfer <conversationId> <agentId>           # Aceptar transfer pendiente
```

#### ✅ CLOSE CONVERSATIONS
```bash
node conversation_manager.js --close <conversationId> [reason]  # Cerrar conversación específica
```

#### 🗑️ DELETE OPERATIONS
```bash
node conversation_manager.js --delete-agent <agentId>           # Eliminar todas las conversaciones del agente
node conversation_manager.js --delete-conversation <conversationId>  # Eliminar conversación específica  
node conversation_manager.js --delete-all                      # ⚠️ ELIMINAR TODAS las conversaciones (PELIGROSO!)
```

#### 📊 INFO COMMANDS
```bash
node conversation_manager.js --list-agents                     # Mostrar agentes disponibles
node conversation_manager.js --list-conversations [agentId]    # Listar conversaciones (filtrar por agente)
node conversation_manager.js --help                           # Mostrar ayuda completa
```

### Key Features
- **Complete Firebase cleanup**: Elimina de `/conversations` y `/queues/{dept}/conversations`
- **Agent lookup**: Integración dinámica con Supabase para resolución de nombres a IDs
- **Queue management**: Limpieza automática de colas de departamentos
- **Invalid ID cleanup**: Remueve entradas corruptas como `undefined`
- **Realistic test data**: Nombres y mensajes realistas para testing

### Usage Examples
```bash
# Setup inicial completo
node conversation_manager.js --delete-all
node conversation_manager.js --create juan 5

# Transfer workflow
node conversation_manager.js --transfer CHaaa...123 carlos "Load balancing"
node conversation_manager.js --accept-transfer CHaaa...123 carlos

# Cleanup por agente
node conversation_manager.js --delete-agent maria
```

## 🔑 Lecciones Aprendidas Críticas

### RLS + JWT Tokens en Backend
**PROBLEMA**: Backend usando solo `SUPABASE_ANON_KEY` → RLS bloquea queries  
**SOLUCIÓN**: Pasar JWT token del usuario autenticado desde frontend al backend

```javascript
// ❌ Solo anon key (RLS falla)
const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY)

// ✅ Anon key + JWT token (RLS funciona)  
const userSupabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  global: {
    headers: {
      Authorization: `Bearer ${userJwtToken}` // Token del frontend
    }
  }
})
```

**REGLA**: Para cualquier operación backend que requiera RLS, siempre pasar el JWT token del usuario autenticado.

### Interferencia Manual con Librerías Auto-gestionadas
**PROBLEMA**: Agregamos `localStorage.setItem('token', data.session.access_token)` manualmente porque "no vimos el token"  
**CAUSA**: Supabase maneja automáticamente con `persistSession: true` pero interferimos con su mecanismo interno  
**RESULTADO**: Ciclo `SIGNED_IN` → `SIGNED_OUT` inmediato, tokens guardados en keys incorrectas, API calls sin auth

```javascript
// ❌ Interferencia manual innecesaria
if (data.session) {
  localStorage.setItem('custom-auth-token', data.session.access_token); // ROMPE el funcionamiento
}

// ✅ Dejar que Supabase maneje automáticamente
// persistSession: true (default) - Supabase maneja todo
const token = await supabase.auth.getSession(); // API calls correctos
```

**REGLA**: Cuando una librería documenta que maneja algo automáticamente, NO interferir manualmente. Usar sus APIs oficiales.


<!-- LAST-SESSION:START -->
## Última Sesión — 2025-08-27 (Sesión #1)

## Resumen ejecutivo — 2025-08-27 (Sesión #1)
Sesión crítica enfocada en la eliminación sistemática de código muerto post-refactor y la implementación completa del sistema de autorización supervisor para cambios de estado de agentes. Se identificaron y corrigieron múltiples inconsistencias TypeScript, se eliminó código obsoleto, y se implementó un modal completo para solicitudes de autorización, reemplazando IDs hardcodeados por un flujo real de autorización.

## Decisiones del día (máx. 5)
1. **Sistema de autorización real implementado** — Reemplazar ID hardcodeado con modal + API flow completo — Permite solicitudes de autorización apropiadas y mantenibles
2. **Código muerto eliminado sistemáticamente** — Fallback obsoleto a user.status de Supabase removido — Evita confusiones y errores por datos inexistentes
3. **Tipos corregidos para AgentStatusDetails** — Status ahora es objeto complejo de Firebase, no string simple — Consistencia entre backend y frontend
4. **AuthorizationRequestModal component creado** — UI completa para solicitar autorización con razón y duración — UX apropiada para agentes solicitando permisos
5. **NavigationCounts properties fixed** — Propiedades corregidas para evitar errores TypeScript — Sistema de badges del supervisor funcional

## Estado actual
- **Sistema de autorización**: Completamente implementado end-to-end (modal → API → backend → supervisor approval)
- **AuthorizationRequestModal**: Nuevo componente con UI completa, estados de loading/success, validación
- **AgentHeader**: ID hardcodeado eliminado, lógica de autorización real implementada
- **TypeScript errors**: Todos los errores críticos corregidos (AuthContext, SupervisorLayout, Supabase)
- **Backend APIs**: Sistema de autorización ya existía y funciona correctamente

## Próximos pasos (Top 3–5) — listos para ejecutar
1. **Testing del flujo completo** — Probar agente solicita busy → supervisor aprueba → status cambia — Validar que la implementación funciona end-to-end
2. **Real-time notifications** — Implementar notificaciones en tiempo real cuando llega nueva solicitud — Mejorar UX del supervisor
3. **Status change confirmations** — Mostrar notificaciones de confirmación al agente — Mejor feedback de estado
4. **Authorization history** — Mostrar historial de autorizaciones en supervisor dashboard — Auditabilidad y seguimiento
5. **Cleanup unused authorization logic** — Revisar y limpiar lógica obsoleta de autorización — Mantenimiento de código

### Quick Links
- Sesión completa: `docs-locales/sesiones/session-2025-08-27.md`
- Nuevo component: `services/chat-ui/src/components/agent/AuthorizationRequestModal.tsx`
- Sistema híbrido: Firebase (real-time) + Supabase (config) completamente funcional

<!-- SYNC-HASH: ed8019e2f14ec2068acd6b2dcdc31e42b4336a9f87b7b683e4c0778d67563bce -->
<!-- LAST-SESSION:END -->

---
**Remember**: Always verify branch, recreate Firebase data, clear browser cache when starting new session.
- recuerda NUNCA borrar SESSION_GUIDE.md si existe
# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

      
      IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.
