{"permissions": {"allow": ["Bash(git init:*)", "Bash(git remote add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git checkout:*)", "mcp__supabase__list_tables", "mcp__supabase__apply_migration", "Bash(rm:*)", "Bash(npm install)", "Bash(npm run build:*)", "Bash(redis-cli:*)", "<PERSON><PERSON>(docker run:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(git add:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(timeout 5s npm start)", "Bash(npm test:*)", "Bash(npm run test:unit:*)", "<PERSON><PERSON>(mv:*)", "Bash(NODE_ENV=test npm test)", "Bash(ENABLE_MOCK_N8N=true npm run dev)", "Bash(npm run test:coverage:*)", "Bash(node:*)", "<PERSON><PERSON>(sed:*)", "Bash(git commit:*)", "Bash(npm run test:smoke:*)", "<PERSON><PERSON>(timeout 5 npm start)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(unset:*)", "Bash(npm ls:*)", "Bash(npm run lint)", "Bash(find:*)", "Bash(npm install:*)", "Bash(cp:*)", "Bash(git merge:*)", "Bash(git fetch:*)", "<PERSON><PERSON>(npx jest:*)", "Bash(npm run test:*)", "<PERSON><PERSON>(timeout 5 npm run dev)", "Bash(gtimeout 5 npm run dev)", "<PERSON><PERSON>(gcloud:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./scripts/start-pubsub-emulator.sh:*)", "Bash(nc:*)", "Bash(PUBSUB_EMULATOR_HOST=localhost:8085 node scripts/setup-pubsub-topics.js)", "Bash(firebase --version)", "Bash(firebase init:*)", "Bash(./scripts/start-firebase-emulator.sh:*)", "<PERSON><PERSON>(cat:*)", "Bash(grep:*)", "Bash(timeout 10s npm run dev)", "Bash(/dev/null)", "Bash(echo $?)", "Bash(git branch:*)", "Bash(ssh:*)", "Bash(npx create-next-app:*)", "Bash(npx:*)", "Bash(ln:*)", "mcp__supabase__execute_sql", "Bash(./scripts/start-all-emulators.sh:*)", "Bash(git restore:*)", "mcp__upstash-context-7-mcp__resolve-library-id", "mcp__smithery-ai-brave-search__brave_web_search", "WebSearch", "WebFetch(domain:www.twilio.com)", "WebFetch(domain:stackoverflow.com)", "Bash(./migrate_all_conversations.sh:*)", "Bash(firebase emulators:start:*)", "Bash(firebase emulators:exec:*)", "Bash(lsof:*)", "<PERSON><PERSON>(killall:*)", "Bash(sudo lsof:*)", "Bash(for script in assign_conversations_to_juan.js create_and_assign_conversations.js create_juan_agent.js debug_firebase_structure.js debug_juan_status.js direct_assign_juan.js recreate_conversations_for_juan.js verify_firebase_data.js)", "Bash(do echo \"=== $script ===\")", "Bash(done)", "Bash(npm start)", "Bash(kill:*)", "Bash(./scripts/clean-logs.sh:*)", "Bash(firebase emulators:list:*)", "Bash(./export-firebase-data.sh:*)", "Bash(./manage-services.sh:*)", "Bash(for port in {3008..3020})", "Bash(do if ! lsof -i :$port)", "Bash(then echo \"Port $port is free\")", "<PERSON><PERSON>(break)", "Bash(fi)", "Bash(./scripts/start-logs-proxy.sh:*)", "Bash(./scripts/manage-services.sh:*)", "Bash(for service in bot-human-router channel-router chat-realtime chat-ui session-manager twilio)", "Bash(do mv $service.log $service/)", "Bash(./scripts/stop-logs-proxy.sh:*)", "Bash(npm uninstall:*)", "Bash(./scripts/manage-services.sh status:*)", "Bash(./scripts/manage-services.sh:*)", "Bash(./scripts/validate-optional-logs-proxy.sh:*)", "Bash(npm audit:*)", "Bash(npm view:*)", "Bash(npm outdated)", "Bash(npm update:*)", "<PERSON><PERSON>(time curl:*)", "Bash(git tag:*)", "<PERSON><PERSON>(touch:*)", "Bash(src/components/supervisor/dashboard/DailyMetricsPanel.tsx)", "mcp__upstash-context-7-mcp__get-library-docs", "<PERSON><PERSON>(echo:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker rm:*)", "WebFetch(domain:docs.n8n.io)", "WebFetch(domain:github.com)", "Bash(./create-workflow.sh:*)", "Bash(./scripts/mcp-workflow-helper.sh status:*)", "Bash(./scripts/mcp-workflow-helper.sh:*)", "Bash(PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics create inbound-messages --project=cx-system-469120)", "Bash(git ls-tree:*)", "Bash(./scripts/manage-emulators.sh redis:*)", "Bash(./scripts/manage-emulators.sh:*)", "Bash(./scripts/start-all-emulators-improved.sh:*)", "Bash(rg:*)", "Bash(git log:*)", "Read(//var/folders/x4/nbp8q1c97tl4lksl2dkkt7440000gq/T/TemporaryItems/NSIRD_screencaptureui_Alkah0/**)"], "deny": [], "ask": [], "defaultMode": "acceptEdits"}, "enabledMcpjsonServers": ["supabase", "github"], "enableAllProjectMcpServers": true}