Entiendo tu problema perfectamente. Las desconexiones de PubSub en Cloud Run son un desafío común debido a la naturaleza efímera de las instancias. Voy a investigar las mejores prácticas y soluciones actuales para esta integración.Basándome en mi investigación, entiendo perfectamente tu problema con las desconexiones de PubSub en Cloud Run. Este es un desafío común debido a la naturaleza efímera de Cloud Run. Te voy a proporcionar las mejores soluciones para mantener una conexión estable:

## 📌 El Problema Principal

Cloud Run está diseñado para ser **stateless** y las instancias se reciclan automáticamente por varias razones:
- Después de 15 minutos de inactividad (aunque tengas min instances)
- Durante actualizaciones del servicio
- Por mantenimiento de la plataforma
- Si las instancias exceden límites de memoria/CPU

## ✅ Soluciones Recomendadas

### **1. Usar Push Subscriptions (La Solución Oficial de Google)**

Esta es la arquitectura recomendada por Google para integrar PubSub con Cloud Run:

```python
from flask import Flask, request
import base64
import json

app = Flask(__name__)

@app.route("/", methods=["POST"])
def pubsub_push():
    """Recibe mensajes de PubSub via Push"""
    envelope = request.get_json()
    
    if not envelope:
        return "Bad Request: no Pub/Sub message received", 400
    
    if not isinstance(envelope, dict) or "message" not in envelope:
        return "Bad Request: invalid Pub/Sub message format", 400
    
    pubsub_message = envelope["message"]
    
    # Decodificar el mensaje
    if isinstance(pubsub_message, dict) and "data" in pubsub_message:
        data = base64.b64decode(pubsub_message["data"]).decode("utf-8")
        
        # Procesar tu mensaje aquí
        process_message(data)
        
        # Retornar 204 para hacer ACK del mensaje
        return "", 204
    
    return "Bad Request", 400

def process_message(data):
    """Tu lógica de procesamiento aquí"""
    print(f"Procesando: {data}")
```

**Configuración de la suscripción Push:**

```bash
# 1. Crear cuenta de servicio para invocar Cloud Run
gcloud iam service-accounts create pubsub-cloud-run-invoker \
    --display-name "PubSub Cloud Run Invoker"

# 2. Dar permisos para invocar el servicio
gcloud run services add-iam-policy-binding YOUR_SERVICE_NAME \
    --member=serviceAccount:pubsub-cloud-run-invoker@PROJECT_ID.iam.gserviceaccount.com \
    --role=roles/run.invoker \
    --region=YOUR_REGION

# 3. Habilitar token creation
PROJECT_NUMBER=$(gcloud projects describe PROJECT_ID --format="value(projectNumber)")

gcloud projects add-iam-policy-binding PROJECT_ID \
    --member=serviceAccount:service-$<EMAIL> \
    --role=roles/iam.serviceAccountTokenCreator

# 4. Crear la suscripción Push
gcloud pubsub subscriptions create YOUR_SUBSCRIPTION \
    --topic=YOUR_TOPIC \
    --push-endpoint=https://YOUR-SERVICE-URL.run.app/ \
    --push-auth-service-account=pubsub-cloud-run-invoker@PROJECT_ID.iam.gserviceaccount.com \
    --ack-deadline=600
```

### **2. Configurar Cloud Run para Mayor Estabilidad**

```yaml
# service.yaml
apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: your-service
spec:
  template:
    metadata:
      annotations:
        # Mantener CPU siempre asignada
        run.googleapis.com/cpu-throttling: "false"
        # Timeout más largo si es necesario
        run.googleapis.com/execution-environment: "gen2"
    spec:
      containerConcurrency: 1000
      timeoutSeconds: 600
      containers:
      - image: gcr.io/PROJECT_ID/your-image
        resources:
          limits:
            cpu: "2"
            memory: "2Gi"
      # Configuración de instancias mínimas
      serviceAccountName: your-service@PROJECT_ID.iam.gserviceaccount.com
  traffic:
  - percent: 100
    latestRevision: true
  # Mantener instancias mínimas activas
  scaling:
    minInstances: 1
    maxInstances: 100
```

### **3. Usar Eventarc (Alternativa Moderna)**

Eventarc es la nueva forma de integrar eventos con Cloud Run:

```bash
# Crear trigger de Eventarc
gcloud eventarc triggers create pubsub-trigger \
    --location=YOUR_REGION \
    --destination-run-service=YOUR_SERVICE \
    --destination-run-region=YOUR_REGION \
    --event-filters="type=google.cloud.pubsub.topic.v1.messagePublished" \
    --transport-topic=projects/PROJECT_ID/topics/YOUR_TOPIC \
    --service-account=<EMAIL>
```

### **4. Patrón Worker con Pull (Si Necesitas Control Total)**

Si absolutamente necesitas mantener una conexión Pull persistente, considera usar **Cloud Run Jobs** o **GKE Autopilot**:

```python
# worker.py para Cloud Run Jobs
from google.cloud import pubsub_v1
import concurrent.futures
import os

subscriber = pubsub_v1.SubscriberClient()
subscription_path = subscriber.subscription_path(
    os.environ['PROJECT_ID'], 
    os.environ['SUBSCRIPTION_NAME']
)

def callback(message):
    try:
        # Procesar mensaje
        print(f"Procesando: {message.data.decode('utf-8')}")
        message.ack()
    except Exception as e:
        print(f"Error: {e}")
        message.nack()

def main():
    flow_control = pubsub_v1.types.FlowControl(max_messages=100)
    
    with subscriber:
        streaming_pull_future = subscriber.subscribe(
            subscription_path,
            callback=callback,
            flow_control=flow_control
        )
        
        # Ejecutar por tiempo limitado
        timeout = 540  # 9 minutos (menos que el timeout de Cloud Run)
        try:
            streaming_pull_future.result(timeout=timeout)
        except concurrent.futures.TimeoutError:
            streaming_pull_future.cancel()
            streaming_pull_future.result()

if __name__ == "__main__":
    main()
```

### **5. Manejo de Errores y Reintentos**

```python
# Configuración robusta para reconexión
class ResilientPubSubHandler:
    def __init__(self):
        self.subscriber = None
        self.future = None
        self.setup_subscriber()
    
    def setup_subscriber(self):
        """Configurar suscriptor con políticas de reintento"""
        from google.api_core import retry
        
        self.subscriber = pubsub_v1.SubscriberClient()
        # Configurar política de reintentos
        self.subscriber._subscriber._policy = retry.Retry(
            initial=0.1,
            maximum=60.0,
            multiplier=1.3,
            deadline=600.0
        )
    
    def handle_disconnect(self):
        """Manejar desconexiones gracefully"""
        if self.future:
            self.future.cancel()
        self.setup_subscriber()
        self.start_listening()
```

## 🎯 Recomendación Final

**Para tu caso específico en Cloud Run, te recomiendo fuertemente usar Push Subscriptions (#1)** porque:

1. **No mantiene conexiones persistentes** - Cada mensaje es una petición HTTP independiente
2. **Se integra naturalmente con Cloud Run** - Aprovecha el auto-scaling
3. **Es resiliente por diseño** - PubSub reintenta automáticamente si falla
4. **No se ve afectado por reciclaje de instancias** - Cada petición es independiente

Si necesitas absolutamente mantener una conexión Pull persistente, considera migrar a:
- **GKE Autopilot** para contenedores de larga duración
- **Compute Engine** con instancias administradas
- **Cloud Run Jobs** para procesamiento batch periódico

¿Cuál de estas soluciones se adapta mejor a tu caso de uso? ¿Necesitas ayuda implementando alguna específica?