# Flujos de Conversación - Sistema de Chat

## 📋 Índice

- [Estado Post-Migración](#estado-post-migración)
- [Flujo de Transferencia Bot→Humano](#flujo-de-transferencia-bot-humano)
- [Flujo de Asignación Manual](#flujo-de-asignación-manual)
- [Flujo de Transferencia Entre Agentes](#flujo-de-transferencia-entre-agentes)
- [Flujo de Cierre de Conversación](#flujo-de-cierre-de-conversación)
- [Flujo de Escalación](#flujo-de-escalación)
- [Flujo de Pausa y Reanudación](#flujo-de-pausa-y-reanudación)

---

## 🔄 **Estado Post-Migración**

### **⚡ Migración Smart API Router → API Directa (Enero 2025)**

Los flujos documentados a continuación ahora utilizan **conexión directa** entre `chat-ui` y `chat-realtime-backend`, eliminando la capa intermedia del Smart API Router para mejor performance.

**🔧 Implementación Actual**:

```typescript
// chat-ui/src/context/ConversationContext.tsx
import { conversationsService } from "@/lib/api";

// Todas las operaciones ahora van directamente al backend
await conversationsService.closeConversation(conversationId);
await conversationsService.transferConversation(
  conversationId,
  agentId,
  reason
);
await conversationsService.sendMessage(conversationId, text, "agent");
```

**📊 Tiempo Real**: Los flujos mantienen sincronización en tiempo real via **Firebase Realtime Database** (no WebSocket).

---

## 🤖➡️👤 Flujo de Transferencia Bot→Humano

### **Descripción**

Proceso automático cuando un bot transfiere una conversación a un agente humano.

### **Diagrama de Flujo**

```mermaid
graph TD
    A[Bot detecta necesidad de transferencia] --> B[POST /api/v1/bot/transfer]
    B --> C{¿Datos válidos?}
    C -->|No| D[Error 400: Validación]
    C -->|Sí| E[Crear conversación en Firebase]
    E --> F[Buscar agente disponible]
    F --> G{¿Agente disponible?}
    G -->|Sí| H[Asignación directa]
    G -->|No| I[Agregar a cola]
    H --> J[Notificar agente]
    I --> K[Calcular tiempo estimado]
    J --> L{¿Agente acepta?}
    L -->|Sí| M[Conversación activa]
    L -->|No| N[Reasignar a otro agente]
    K --> O[Notificar cuando agente disponible]
    N --> F
    O --> J
```

### **Pasos Detallados**

#### **1. Detección de Transferencia**

```javascript
// El bot detecta que necesita transferir
if (needsHumanAgent) {
  await transferToHuman({
    customer: customerData,
    messages: conversationHistory,
    transferReason: "Cliente requiere asistencia especializada",
  });
}
```

#### **2. Validación de Datos**

- Verificar datos obligatorios del cliente
- Validar formato de mensajes
- Confirmar canal de comunicación

#### **3. Creación de Conversación**

```json
{
  "id": "bot-transfer-1748490346954-0fedfb9c",
  "source": "bot_transfer",
  "customer": {
    "name": "María González",
    "phone": "+1234567890",
    "channel": "whatsapp"
  },
  "status": "waiting",
  "priority": 1,
  "transferReason": "Cliente requiere asistencia humana",
  "createdAt": 1748490346954
}
```

#### **4. Búsqueda de Agente**

Criterios de asignación:

- **Disponibilidad**: Estado "online" y capacidad libre
- **Habilidades**: Match con tipo de consulta
- **Carga de trabajo**: Número de chats activos
- **Tiempo de respuesta**: Historial de performance

#### **5. Asignación o Cola**

```javascript
if (availableAgent) {
  // Asignación directa
  await assignConversation(conversationId, agentId);
  return { status: "assigned", assignedAgent: agent };
} else {
  // Agregar a cola
  await addToQueue(conversationId, queueId);
  return { status: "waiting", queuePosition: position };
}
```

### **Estados Posibles**

- `waiting` - En cola de espera
- `pending_acceptance` - Ofrecida a agente
- `assigned` - Asignada directamente
- `active` - Conversación activa

### **💻 Implementación Frontend**

**⚠️ IMPORTANTE**: El frontend **NO maneja directamente** este flujo. Es completamente manejado por el backend.

```typescript
// El frontend solo RECIBE la conversación via Firebase
const { items: conversations, loading } =
  useFirebaseListener<ApiConversation[]>("/conversations");

// Cuando llega una nueva conversación de bot transfer:
useEffect(() => {
  const botTransfers = conversations.filter(
    (conv) =>
      conv.source === "bot_transfer" && conv.assignedTo === currentAgent.id
  );

  // Mostrar notificación al agente
  if (botTransfers.length > 0) {
    showBotTransferNotification(botTransfers[0]);
  }
}, [conversations]);
```

---

## 👤➡️👤 Flujo de Asignación Manual

### **Descripción**

Proceso cuando un supervisor asigna manualmente una conversación a un agente.

### **Diagrama de Flujo**

```mermaid
graph TD
    A[Supervisor selecciona conversación] --> B[Elegir agente destino]
    B --> C[POST /conversations/:id/assign]
    C --> D{¿Agente disponible?}
    D -->|No| E[Error: Agente no disponible]
    D -->|Sí| F[Verificar capacidad]
    F --> G{¿Capacidad libre?}
    G -->|No| H[Advertencia: Sobrecarga]
    G -->|Sí| I[Asignar conversación]
    H --> J{¿Forzar asignación?}
    J -->|No| K[Cancelar asignación]
    J -->|Sí| I
    I --> L[Notificar agente]
    L --> M[Actualizar métricas]
    M --> N[Conversación asignada]
```

### **Pasos Detallados**

#### **1. Selección de Conversación**

```typescript
// Supervisor ve lista de conversaciones
const conversations = await conversationsService.getConversations({
  status: "waiting",
  sortBy: "waitTime",
});
```

#### **2. Asignación Manual**

```typescript
const assignment = {
  conversationId: "conv123",
  agentId: "agent456",
  assignedBy: "supervisor123",
  reason: "manual_assignment",
  priority: "high",
};
```

#### **3. Validaciones**

- Agente debe estar "online"
- Verificar capacidad máxima de chats
- Confirmar habilidades requeridas

### **💻 Implementación Frontend**

```typescript
// Supervisor asigna conversación
const assignConversation = async (conversationId: string, agentId: string) => {
  try {
    await conversationsService.transferConversation(
      conversationId,
      agentId,
      "manual_assignment"
    );

    // Firebase actualiza automáticamente la UI
    toast.success("Conversación asignada exitosamente");
  } catch (error) {
    toast.error("Error al asignar conversación");
  }
};

// Agente recibe notificación automática via Firebase
```

---

## 🔄 Flujo de Transferencia Entre Agentes

### **Descripción**

Proceso cuando un agente transfiere una conversación a otro agente o departamento.

### **Diagrama de Flujo**

```mermaid
graph TD
    A[Agente inicia transferencia] --> B[Seleccionar destino]
    B --> C[POST /conversations/:id/transfer]
    C --> D[Validar permisos]
    D --> E{¿Transferencia válida?}
    E -->|No| F[Error: Transferencia no permitida]
    E -->|Sí| G[Buscar agente destino]
    G --> H{¿Agente disponible?}
    H -->|No| I[Agregar a cola departamento]
    H -->|Sí| J[Transferir directamente]
    I --> K[Notificar departamento]
    J --> L[Crear mensaje transferencia]
    L --> M[Actualizar asignación]
    M --> N[Notificar nuevo agente]
    N --> O[Liberar agente origen]
```

### **Tipos de Transferencia**

#### **1. Transferencia por Especialización**

```typescript
// Transferir a especialista
await conversationsService.transferConversation(
  conversationId,
  specialistId,
  "Cliente requiere especialista en facturación"
);
```

#### **2. Transferencia por Sobrecarga**

```typescript
// Transferir por alta carga de trabajo
await conversationsService.transferConversation(
  conversationId,
  availableAgentId,
  "Redistribución de carga de trabajo"
);
```

#### **3. Transferencia Departamental**

```typescript
// Transferir a otro departamento
await conversationsService.transferToDepartment(
  conversationId,
  "technical_support",
  "Problema técnico requiere soporte especializado"
);
```

### **💻 Implementación Frontend**

```typescript
// Componente TransferChatModal.tsx
const handleTransfer = async (transferData: TransferData) => {
  try {
    setIsTransferring(true);

    await conversationsService.transferConversation(
      conversationId,
      transferData.targetAgentId,
      transferData.reason
    );

    // Firebase actualiza automáticamente:
    // - Estado de la conversación
    // - Asignación del nuevo agente
    // - Historial de transferencias

    toast.success("Conversación transferida exitosamente");
    onClose();
  } catch (error) {
    toast.error("Error al transferir conversación");
  } finally {
    setIsTransferring(false);
  }
};
```

---

## ❌ Flujo de Cierre de Conversación

### **Descripción**

Proceso para finalizar una conversación, ya sea por resolución del problema o por otros motivos.

### **Diagrama de Flujo**

```mermaid
graph TD
    A[Agente inicia cierre] --> B[Seleccionar motivo]
    B --> C[PUT /conversations/:id/close]
    C --> D[Validar estado actual]
    D --> E{¿Puede cerrarse?}
    E -->|No| F[Error: Estado inválido]
    E -->|Sí| G[Crear mensaje de cierre]
    G --> H[Actualizar estado a 'closed']
    H --> I[Liberar capacidad agente]
    I --> J[Actualizar métricas]
    J --> K[Archivar conversación]
    K --> L[Notificar cierre]
```

### **Motivos de Cierre**

- `resolved` - Problema resuelto
- `no_response` - Cliente no responde
- `duplicate` - Conversación duplicada
- `spam` - Mensaje spam
- `transferred` - Transferida a otro canal

### **💻 Implementación Frontend**

```typescript
// Cierre manual por agente
const closeConversation = async (
  conversationId: string,
  reason: string,
  notes?: string
) => {
  try {
    await conversationsService.closeConversation(conversationId);

    // Firebase actualiza automáticamente:
    // - Estado de la conversación
    // - Métricas del agente
    // - Disponibilidad para nuevos chats

    toast.success("Conversación cerrada exitosamente");

    // Redirigir a lista de conversaciones
    router.push("/dashboard/conversations");
  } catch (error) {
    toast.error("Error al cerrar conversación");
  }
};

// Hook para manejar cierre automático
useEffect(() => {
  const handleAutoClose = () => {
    if (
      conversation?.status === "inactive" &&
      inactivityTime > 30 * 60 * 1000
    ) {
      closeConversation(conversation.id, "no_response");
    }
  };

  const interval = setInterval(handleAutoClose, 60000); // Check every minute
  return () => clearInterval(interval);
}, [conversation, inactivityTime]);
```

---

## 📈 Flujo de Escalación

### **Descripción**

Proceso para escalar una conversación a un supervisor o nivel superior cuando se requiere autorización adicional.

### **💻 Implementación Frontend**

```typescript
// Escalar conversación a supervisor
const escalateConversation = async (conversationId: string, reason: string) => {
  try {
    await conversationsService.escalateConversation(conversationId, reason);

    toast.success("Conversación escalada a supervisor");
  } catch (error) {
    toast.error("Error al escalar conversación");
  }
};
```

---

## ⏸️ Flujo de Pausa y Reanudación

### **Descripción**

Proceso para pausar temporalmente una conversación y reanudarla posteriormente.

### **💻 Implementación Frontend**

```typescript
// Pausar conversación
const pauseConversation = async (conversationId: string, reason: string) => {
  try {
    await conversationsService.pauseConversation(conversationId, reason);

    toast.success("Conversación pausada");
  } catch (error) {
    toast.error("Error al pausar conversación");
  }
};

// Reanudar conversación
const resumeConversation = async (conversationId: string) => {
  try {
    await conversationsService.resumeConversation(conversationId);

    toast.success("Conversación reanudada");
  } catch (error) {
    toast.error("Error al reanudar conversación");
  }
};
```

---

## 🔄 **Sincronización en Tiempo Real**

Todos los flujos mantienen sincronización automática via Firebase:

```typescript
// Escuchar cambios en tiempo real
const { items: conversations, loading } = useFirebaseListener<
  ApiConversation[]
>("/conversations", {
  limit: 50,
  orderBy: "updatedAt",
  orderDirection: "last",
});

// Los cambios se reflejan automáticamente en la UI:
// - Nuevas asignaciones
// - Cambios de estado
// - Transferencias
// - Cierres
// - Mensajes nuevos
```

---

_Última actualización: Enero 2025 (Post-migración Smart API Router → API Directa)_
