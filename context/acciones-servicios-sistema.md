# Acciones y Servicios del Sistema CX - Lista Completa

## 1. GESTIÓN DE CONVERSACIONES (Core Platform)

### Acciones del Agente

- ✅ **Aceptar/Rechazar conversación nueva** - Control del flujo de trabajo y capacidad del agente
- ✅ **Transferir conversación a otro agente** - Especialización y distribución de carga
- ✅ **Transferir conversación a supervisor** - Escalación de casos complejos
- ✅ **Transferir conversación entre departamentos** - Routing especializado (ventas, soporte, cobranza)
- ✅ **Poner conversación en pausa** - Gestión de tiempo para investigar casos
- ✅ **Reanudar conversación pausada** - Continuación de casos pausados con historial
- ✅ **Finalizar conversación** - Cierre formal con métricas de tiempo (manual y por bot)
- ✅ **Exportar transcript de conversación** - Compliance, auditoría y seguimiento legal
- ✅ **Agregar notas internas a conversación** - Contexto para futuros agentes
- ✅ **Gestión de auxiliares fuera de línea** - Lunch, Break, Restroom, admin, coaching para gestión de actividades asignadas durante sus horarios
- ✅ **Métricas de chats individuales** - Visualización de chats gestionados durante el turno, tiempo de gestión, chats abandonados
- ✅ **Solicitar supervisor en conversación activa** - Escalación inmediata con contexto y razón
- ✅ **Aceptar transferencia pendiente** - Confirmar recepción de conversación transferida
- ✅ **Rechazar transferencia con motivo** - Declinar transferencia con razón estructurada
- ✅ **Ver historial de transferencias** - Tracking completo de movimientos de conversación
- 📋 **Sistema de prioridades básico** - Manejo de prioridades en cola (low, medium, high, urgent)
- 📋 **Marcar conversación como urgente/VIP dinámicamente** - Cambio de prioridad en tiempo real
- 📋 **Programar follow-up automático** - Seguimiento proactivo de casos
- 📋 **Enviar archivos adjuntos** - Envío de documentos, imágenes, contratos
- 📋 **Solicitar información adicional del cliente** - Formularios estructurados dentro del chat
- 📋 **Iniciar videollamada/screenshare** - Soporte técnico avanzado
- ++ **Solicitud de encuestas de satisfacción finalización de chat** - Gestión de métricas de satisfacción, contact resolution y NPS para el cliente

### Acciones del Supervisor

- ✅ **Monitorear conversaciones en tiempo real** - Quality assurance y coaching
- ✅ **Intervenir en conversación activa** - Soporte directo al agente con modos observación/participación
- ✅ **Abandonar intervención activa** - Salir de conversación intervenida manteniendo historial
- ✅ **Responder solicitudes de supervisor** - Atender escalaciones de agentes con contexto
- ✅ **Establecer límites de conversaciones por agente** - Control de productividad
- ✅ **Gestionar solicitudes de auxiliares** - Aprobar/rechazar breaks, lunch, etc.
- ✅ **Dashboard básico de supervisión** - Vista de agentes activos, conversaciones y métricas simples
- ✅ **Gestión de transferencias entre agentes** - Supervisar y facilitar reasignaciones
- ✅ **Dashboard de intervenciones activas** - Vista consolidada de supervisiones en curso
- 📋 **Generación de reportes de desempeño detallados** - Evaluación avanzada de tendencias, tiempos y productividad
- 📋 **Reasignar conversaciones masivamente** - Balanceo de carga dinámico
- ++ **Pausar/Reanudar cola de conversaciones** - Control de flujo durante mantenimiento
- 📋 **Crear grupos de agentes especializados** - Segmentación por expertise
- 📋 **Definir reglas de routing automático** - Optimización de asignaciones
- 📋 **Activar/Desactivar canales temporalmente** - Mantenimiento y control de capacidad
- ✅ **Cierre de chats y desconexiones de agentes**
- ++ **Gestión de productividad, reporte de horas de conexión, abandonos, hora de log in y hora de toma de auxiliares**
- ✅ **Comunicación directa con un agente desde consola**

### Gestión de Notas y Contexto

- ✅ **Crear notas internas por categoría** - Clasificación estructurada (técnica, comercial, seguimiento)
- ✅ **Asignar prioridad a notas** - Niveles de importancia para revisión (low, medium, high, urgent)
- ✅ **Buscar notas en conversaciones** - Sistema de búsqueda básica con filtros
- ✅ **Ver estadísticas básicas de notas** - Contadores simples por agente
- ✅ **Editar/Eliminar notas propias** - Control de autoría con historial
- 📋 **Configurar visibilidad avanzada de notas** - Privadas, equipo, supervisores con permisos granulares
- 📋 **Sistema de alertas por notas urgentes** - Notificaciones automáticas para prioridades altas

## 2. GESTIÓN DE CLIENTES Y CRM

### Información del Cliente

- 📋 **Obtener perfil completo del cliente** - Contexto inmediato para personalización
- 📋 **Consultar historial de conversaciones** - Continuidad en el servicio
- 📋 **Ver últimas transacciones financieras** - Contexto para resolver dudas de pagos
- 📋 **Consultar estado de cuenta actual** - Información financiera en tiempo real
- 📋 **Verificar identidad del cliente** - Seguridad y compliance
- 📋 **Consultar productos/servicios contratados** - Upselling y cross-selling
- 📋 **Ver tickets de soporte previos** - Evitar repetir información
- 📋 **Consultar score crediticio** - Decisiones de productos financieros
- 📋 **Ver alertas de seguridad del cliente** - Detección de fraude
- 📋 **Consultar preferencias de comunicación** - Respeto a canales preferidos

### Gestión de Datos

- 📋 **Actualizar información de contacto** - Mantenimiento de base de datos
- 📋 **Crear nuevo registro de cliente** - Onboarding de nuevos usuarios
- 📋 **Fusionar perfiles duplicados** - Limpieza de base de datos
- 📋 **Exportar datos del cliente (GDPR)** - Compliance con regulaciones
- 📋 **Eliminar datos del cliente (GDPR)** - Derecho al olvido
- 📋 **Etiquetar cliente por segmento** - Segmentación para marketing
- 📋 **Registrar queja formal** - Procesos regulatorios de fintech
- 📋 **Actualizar nivel de riesgo del cliente** - Compliance AML/KYC
- 📋 **Bloquear/Desbloquear cliente** - Protección contra spam o abuso

## 3. AUTOMATIZACIÓN Y BOT

### Gestión del Bot

- ✅ **Transferencia de bot a humano** - Sistema completo de handoff implementado
- ✅ **Gestión de handoffs automáticos** - Detección automática de necesidad de transferencia
- ✅ **Procesamiento de transferencias en lote** - Migración masiva de conversaciones bot
- 📋 **Entrenar bot con nuevos intents** - Mejora continua de la automatización
- 📋 **Revisar conversaciones no entendidas** - Identificación de gaps en conocimiento
- 📋 **Aprobar/Rechazar respuestas sugeridas** - Control de calidad del bot
- 📋 **Configurar horarios de disponibilidad del bot** - Gestión de expectativas
- 📋 **Activar/Desactivar bot por canal** - Control granular de automatización
- 📋 **Definir reglas de escalación a humano** - Optimización de hand-off
- 📋 **Configurar respuestas automáticas por idioma** - Soporte multilenguaje
- 📋 **Importar/Exportar knowledge base** - Gestión de contenido del bot
- 📋 **Simular conversaciones de prueba** - Testing de flujos automatizados
- 📋 **Configurar integraciones del bot con APIs** - Conexión con sistemas backend

### Flujos Automatizados

- 📋 **Activar flujos de onboarding automático** - Guiar nuevos clientes
- 📋 **Configurar alertas proactivas** - Notificaciones de vencimientos, pagos
- 📋 **Programar campañas de mensajería** - Marketing automation
- 📋 **Activar encuestas de satisfacción automáticas** - Medición de CSAT/NPS
- 📋 **Configurar respuestas automáticas fuera de horario** - Expectativas claras
- 📋 **Establecer flujos de recuperación de abandono** - Re-engagement
- 📋 **Activar notificaciones de transacciones** - Confirmaciones automáticas
- 📋 **Configurar flujos de cobranza automatizada** - Recordatorios de pago

## 4. TRANSACCIONES Y SERVICIOS FINANCIEROS

### Consultas Financieras

- 📋 **Consultar saldo de cuenta** - Información básica más solicitada
- 📋 **Ver movimientos recientes** - Clarificación de transacciones
- 📋 **Consultar límites de tarjeta/cuenta** - Información de productos
- 📋 **Ver fecha de próximo pago** - Gestión de vencimientos
- 📋 **Consultar estado de transferencias** - Seguimiento de operaciones
- 📋 **Ver historial de pagos** - Registro de cumplimiento
- 📋 **Consultar puntos/beneficios acumulados** - Programas de fidelización
- 📋 **Ver comisiones aplicadas** - Transparencia en costos

### Acciones Transaccionales

- 📋 **Bloquear/Desbloquear tarjeta** - Seguridad inmediata
- 📋 **Solicitar reposición de tarjeta** - Gestión de incidentes
- 📋 **Cambiar límites de tarjeta temporalmente** - Flexibilidad para el cliente
- 📋 **Programar transferencias recurrentes** - Automatización de pagos
- 📋 **Cancelar transferencias pendientes** - Control de errores
- 📋 **Solicitar certificados bancarios** - Documentación formal
- 📋 **Actualizar datos para transferencias** - Mantenimiento de información
- 📋 **Reportar transacciones fraudulentas** - Protección al cliente

## 5. ANALYTICS Y REPORTES

### Métricas en Tiempo Real

- ✅ **Ver conversaciones activas** - Monitoring operacional básico
- ✅ **Ver agentes disponibles/ocupados** - Gestión de recursos básica
- ✅ **Ver cola de espera actual** - Monitoreo de queue simple
- ✅ **Dashboard básico de métricas** - Agentes activos, chats activos, satisfacción básica
- ✅ **Dashboard de auxiliares en tiempo real** - Estados de break, lunch, etc.
- ✅ **Métricas básicas de transferencias** - Contadores simples
- 📋 **Consultar tiempo de espera promedio** - SLA monitoring avanzado
- 📋 **Monitorear volumen por canal** - Distribución de carga detallada
- 📋 **Ver rate de resolución del bot** - Efectividad de automatización
- 📋 **Consultar satisfaction score avanzado** - Análisis de calidad detallado
- 📋 **Monitorear picos de demanda** - Capacidad y staffing inteligente
- 📋 **Ver alertas de SLA** - Cumplimiento de objetivos automatizado

### Exportación y Transcripts

- ✅ **Exportar transcript individual** - JSON, PDF, HTML, TXT, CSV
- ✅ **Exportar conversación con filtros** - Incluir/excluir notas internas
- ✅ **Exportación con métricas básicas** - Contadores de mensajes y tiempos
- 📋 **Exportar transcripts masivos por período** - Análisis en lote
- 📋 **Exportar datos para compliance** - Auditorías regulatorias
- 📋 **Exportación programada automática** - Backups y archivos

### Reportes y Analytics Avanzados

- 📋 **Generar reporte de productividad por agente** - Evaluación de performance detallada
- 📋 **Exportar métricas de satisfacción** - Análisis de calidad histórico
- 📋 **Crear reporte de temas más consultados** - Identificación de pain points
- 📋 **Generar análisis de sentimiento** - Medición de experiencia emocional
- 📋 **Crear dashboard ejecutivo** - Visión estratégica del negocio
- 📋 **Generar reporte de ROI del bot** - Justificación de inversión
- 📋 **Analytics de auxiliares por período** - Eficiencia, overtime, patrones de uso
- 📋 **Reportes de transferencias detallados** - Tasas de éxito, motivos, tiempos
- 📋 **Métricas de intervención de supervisores** - Frecuencia, duración, efectividad
- 📋 **Reportes de escalación** - Solicitudes de supervisor, análisis de resolución

### Funcionalidades Técnicas Implementadas

- ✅ **Migración de datos entre sistemas** - Transferencia incremental y completa (servicio analytics)
- ✅ **Métricas básicas de auxiliares** - Tiempo real vs estimado, contadores simples
- ✅ **Sistema de health checks** - Verificación básica de servicios
- 📋 **Analytics de handoffs bot-humano** - Tasas de transferencia detalladas, análisis de motivos
- 📋 **Análisis de patrones de transferencia** - Identificación de cuellos de botella
- 📋 **Dashboard de salud del sistema avanzado** - Monitoreo completo de microservicios

## 6. ADMINISTRACIÓN Y CONFIGURACIÓN

### Gestión de Usuarios

- 📋 **Crear/Editar perfiles de agente** - Onboarding de personal
- 📋 **Asignar roles y permisos** - Control de acceso granular
- 📋 **Configurar horarios de trabajo** - Gestión de disponibilidad
- 📋 **Establecer metas por agente** - KPIs personalizados
- 📋 **Crear grupos de trabajo** - Organización por especialidad
- 📋 **Configurar notificaciones por usuario** - Personalización de alertas
- 📋 **Gestionar acceso a integraciones** - Seguridad de datos
- 📋 **Auditar actividad de usuarios** - Compliance y seguridad

### Configuración del Sistema

- 📋 **Configurar parámetros de SLA** - Objetivos de servicio
- 📋 **Establecer reglas de routing** - Optimización de asignaciones
- 📋 **Configurar integraciones con terceros** - Conectividad externa
- 📋 **Gestionar templates de mensajes** - Estandarización de comunicación
- 📋 **Configurar webhooks** - Automatización de procesos
- 📋 **Establecer políticas de retención** - Compliance de datos
- 📋 **Configurar backup automático** - Continuidad del negocio
- 📋 **Gestionar certificados SSL** - Seguridad de comunicaciones

## 7. INTEGRACIONES Y APIs

### Integraciones CRM/ERP

- 📋 **Sincronizar datos con Zoho CRM** - Unificación de información de cliente
- 📋 **Consultar Oracle Fusion ERP** - Datos transaccionales en tiempo real
- 📋 **Crear tickets en Jira** - Escalación técnica estructurada
- 📋 **Actualizar estados en sistemas core** - Consistencia de datos
- 📋 **Sincronizar contactos** - Actualización bidireccional
- 📋 **Exportar leads calificados** - Seguimiento comercial
- 📋 **Importar políticas de productos** - Información actualizada
- 📋 **Sincronizar calendarios de agentes** - Gestión de disponibilidad
- 📋 **Crear ticket en sistema externo** - Integración con helpdesk/CRM

### APIs Externas

- 📋 **Validar identidad con proveedores externos** - KYC automatizado
- 📋 **Consultar bureaus de crédito** - Evaluación crediticia
- 📋 **Integrar con pasarelas de pago** - Procesamiento de transacciones
- 📋 **Conectar con proveedores de SMS/Email** - Diversificación de canales
- 📋 **Integrar con sistemas de fraude** - Detección en tiempo real
- 📋 **Conectar con servicios de geolocalización** - Verificación de ubicación
- 📋 **Integrar con servicios de traducción** - Soporte multiidioma
- 📋 **Conectar con servicios de análisis de sentimiento** - Medición automática

## 8. DEFLECTION Y GESTIÓN DE LLAMADAS

### Call Deflection

- 📋 **Configurar deflection por tipo de llamada** - Optimización de recursos
- 📋 **Establecer mensajes de deflection personalizados** - Experiencia coherente
- 📋 **Monitorear tasa de éxito de deflection** - Efectividad de la estrategia
- 📋 **Configurar fallbacks por canal** - Redundancia en comunicación
- 📋 **Establecer prioridad para llamadas deflectadas** - Atención preferencial
- 📋 **Configurar horarios de deflection** - Gestión por disponibilidad
- 📋 **Personalizar templates de WhatsApp** - Compliance con Meta
- 📋 **Integrar con IVR existente** - Aprovechamiento de infraestructura

## 9. CAPACITACIÓN Y KNOWLEDGE MANAGEMENT

### Gestión del Conocimiento

- 📋 **Crear/Editar artículos de knowledge base** - Centralización de información
- 📋 **Categorizar contenido por temas** - Organización eficiente
- 📋 **Establecer permisos de edición** - Control de calidad
- 📋 **Versionar documentos** - Trazabilidad de cambios
- 📋 **Aprobar contenido antes de publicación** - Quality assurance
- 📋 **Medir efectividad de artículos** - Optimización de contenido
- 📋 **Sincronizar con sistemas externos** - Fuente única de verdad
- 📋 **Traducir contenido automáticamente** - Soporte multiidioma

### Training y Desarrollo

- 📋 **Crear módulos de entrenamiento** - Onboarding estructurado
- 📋 **Asignar cursos por rol** - Especialización por función
- 📋 **Monitorear progreso de entrenamiento** - Seguimiento de competencias
- 📋 **Generar certificaciones** - Validación de conocimientos
- 📋 **Crear simulaciones de conversaciones** - Práctica segura
- 📋 **Establecer evaluaciones periódicas** - Mejora continua
- 📋 **Compartir mejores prácticas** - Desarrollo colectivo
- 📋 **Medir impacto del entrenamiento en KPIs** - ROI de capacitación

## 10. SEGURIDAD Y COMPLIANCE

### Seguridad

- 📋 **Encriptar conversaciones sensibles** - Protección de datos financieros
- 📋 **Aplicar políticas de retención de datos** - Compliance regulatorio
- 📋 **Auditar accesos a información sensible** - Trazabilidad de seguridad
- 📋 **Detectar patrones de fraude en conversaciones** - Prevención proactiva
- 📋 **Configurar alertas de seguridad** - Respuesta rápida a incidentes
- 📋 **Gestionar tokens y certificados** - Seguridad de integraciones
- 📋 **Implementar autenticación multifactor** - Acceso seguro
- 📋 **Configurar IP whitelisting** - Control de acceso por ubicación

### Compliance

- 📋 **Generar reportes regulatorios** - Cumplimiento normativo
- 📋 **Aplicar políticas de consentimiento** - GDPR y regulaciones locales
- 📋 **Gestionar solicitudes de datos (SAR)** - Derechos del titular
- 📋 **Implementar políticas de anonimización** - Protección de privacidad
- 📋 **Auditar cumplimiento de SLA** - Contratos de servicio
- 📋 **Documentar procesos para auditorías** - Preparación regulatoria
- 📋 **Gestionar consentimientos de marketing** - Comunicación permitida
- 📋 **Implementar controles de AML/KYC** - Prevención de lavado de dinero

## 11. SISTEMA DE AUXILIARES (Nuevo)

### Gestión de Solicitudes

- ✅ **Crear solicitud de auxiliar** - Break, lunch, restroom, training, admin
- ✅ **Aprobar/Rechazar solicitudes** - Workflow de supervisión
- ✅ **Iniciar auxiliar aprobado** - Control de estados en tiempo real
- ✅ **Completar auxiliar** - Tracking de tiempo real vs estimado
- ✅ **Ver historial de auxiliares** - Registro completo por agente
- ✅ **Notificaciones de auxiliares** - Alertas para agentes y supervisores

### Analytics de Auxiliares

- ✅ **Dashboard de auxiliares** - Vista consolidada de estados
- ✅ **Métricas de eficiencia básicas** - Tiempo real vs estimado, contadores simples
- 📋 **Reportes de patrones de uso** - Identificación de tendencias avanzadas
- 📋 **Analytics detallados por tipo de auxiliar** - Break, lunch, etc. con insights
- 📋 **Métricas de aprobación avanzadas** - Tasas y tiempos de supervisión con analytics

## 12. SISTEMA DE NOTIFICACIONES (Nuevo)

### Gestión de Notificaciones

- ✅ **Notificaciones en tiempo real** - Transferencias, solicitudes, escalaciones
- ✅ **Marcar notificaciones como leídas** - Control de estado
- ✅ **Configuración básica de notificaciones** - Preferencias por usuario
- ✅ **Conteo de notificaciones no leídas** - Badges y contadores
- ✅ **Historial de notificaciones** - Registro de actividad
- ✅ **Notificaciones por rol** - Agente, supervisor, admin

---

## Leyenda de Estados

- ✅ = **Implementado y funcional**
- ++ = **Pendiente de implementación (va al roadmap)**
- 📋 = **Funcionalidad futura** (no implementada, no va al roadmap por ahora)

---

## Importancia Estratégica

Cada una de estas acciones/servicios es crítica porque:

1. **Eficiencia Operacional**: Reducen tiempo de resolución y aumentan productividad de agentes
2. **Experiencia del Cliente**: Proporcionan respuestas rápidas y personalizadas
3. **Compliance**: Cumplen con regulaciones financieras estrictas
4. **Escalabilidad**: Permiten manejar volúmenes crecientes sin degradar calidad
5. **Inteligencia de Negocio**: Generan insights para mejora continua
6. **Seguridad**: Protegen datos sensibles financieros
7. **Automatización**: Reducen costos operativos y errores humanos
8. **Integración**: Aprovechan sistemas existentes sin duplicar datos
9. **Personalización**: Adaptan el servicio a cada cliente específico
10. **Medición**: Permiten optimización basada en datos reales


## Notas 1 de julio
1. Session manager dispara evento de pub sub
2. Servicio que exporta transcript y lo procesa, pero usa el evento disparado por pub sub
3. Que servicios deben pegarse al evento de cierre de session
4. La conversacion debe tener un status que indique pendiente de limpieza, esto debe aplicarse como filtro al momento de buscar la conversacion
5. Nuevo servicio de transcripts y extraccion de datos
6. Tool de envios de transcripts