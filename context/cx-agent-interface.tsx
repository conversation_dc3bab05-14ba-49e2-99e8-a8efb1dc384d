import React, { useState } from 'react';
import { 
  Search, 
  ChevronDown, 
  MessageSquare, 
  User, 
  FileText, 
  Globe, 
  Send, 
  Paperclip, 
  Smile, 
  MoreHorizontal, 
  Phone, 
  LogOut, 
  Filter, 
  Bell,
  Clock,
  Tag,
  PanelRight,
  ArrowUpRight,
  X,
  CheckCircle,
  Inbox,
  List,
  ClipboardList,
  Info
} from 'lucide-react';

const AgentInterface = () => {
  // Sample data for demonstration
  const [activeChat, setActiveChat] = useState(1);
  const [showCannedResponses, setShowCannedResponses] = useState(false);
  const [messageInput, setMessageInput] = useState('');
  const [showPanel, setShowPanel] = useState(true);
  
  const chats = [
    { 
      id: 1, 
      name: '<PERSON>', 
      lastMessage: 'I need help with my account', 
      time: '2 min ago',
      channel: 'WhatsApp',
      unread: false,
      status: 'typing...',
      priority: 'medium',
      waitingForAgent: true
    },
    { 
      id: 2, 
      name: '<PERSON>', 
      lastMessage: 'My payment is not showing up', 
      time: '5 min ago',
      channel: 'Web Chat',
      unread: true,
      status: 'waiting',
      priority: 'high',
      waitingForAgent: true
    },
    { 
      id: 3, 
      name: '<PERSON> <PERSON>', 
      lastMessage: 'Thanks for the help', 
      time: '12 min ago',
      channel: 'SMS',
      unread: false,
      status: 'idle',
      priority: 'low',
      waitingForAgent: false
    },
    { 
      id: 4, 
      name: '<PERSON> Williams', 
      lastMessage: 'I lost my card, what should I do?', 
      time: '15 min ago',
      channel: 'WhatsApp',
      unread: false,
      status: 'waiting',
      priority: 'medium',
      waitingForAgent: true
    }
  ];

  const messages = [
    { id: 1, chatId: 1, sender: 'customer', text: 'Hello, I need help with my account', time: '10:32 AM' },
    { id: 2, chatId: 1, sender: 'bot', text: 'Hi there! I\'m the virtual assistant. What specific account issue are you having?', time: '10:32 AM' },
    { id: 3, chatId: 1, sender: 'customer', text: 'I can\'t see my recent transactions', time: '10:33 AM' },
    { id: 4, chatId: 1, sender: 'bot', text: 'I understand you\'re having trouble viewing your recent transactions. Let me connect you with an agent who can help.', time: '10:33 AM' },
    { id: 5, chatId: 1, sender: 'system', text: 'Chat transferred to agent', time: '10:34 AM' },
    { id: 6, chatId: 1, sender: 'agent', text: 'Hi John, I\'m Sophia. I understand you\'re having trouble viewing your recent transactions. When did you last see them?', time: '10:35 AM' },
    { id: 7, chatId: 1, sender: 'customer', text: 'Thanks Sophia. I checked yesterday and they were there, but today nothing shows up for the past week.', time: '10:36 AM' }
  ];

  const cannedResponses = [
    { id: 1, category: 'Greetings', text: 'Hello, thank you for contacting us. How may I assist you today?' },
    { id: 2, category: 'Account Issues', text: 'I understand you\'re having trouble with your account. Let me check that for you right away.' },
    { id: 3, category: 'Transfers', text: 'To transfer funds, you can use our mobile app or website. Would you like me to guide you through the process?' },
    { id: 4, category: 'Card Issues', text: 'I\'m sorry to hear about the issue with your card. Let me help you resolve this.' },
    { id: 5, category: 'Closing', text: 'Is there anything else I can help you with today?' },
    { id: 6, category: 'Closing', text: 'Thank you for contacting us. Have a great day!' }
  ];

  const customerInfo = {
    name: 'John Smith',
    email: '<EMAIL>',
    phone: '+****************',
    accountType: 'Premium',
    accountNumber: '****3456',
    joinDate: 'May 15, 2023',
    lastContact: 'May 30, 2023',
    notes: 'Customer reported login issues last month.'
  };

  const recentTransactions = [
    { id: 1, date: 'June 2, 2023', description: 'Coffee Shop', amount: '-$4.50', status: 'Completed' },
    { id: 2, date: 'June 1, 2023', description: 'Salary Deposit', amount: '+$3,500.00', status: 'Completed' },
    { id: 3, date: 'May 31, 2023', description: 'Utility Bill', amount: '-$120.75', status: 'Completed' },
    { id: 4, date: 'May 30, 2023', description: 'Grocery Store', amount: '-$85.20', status: 'Completed' }
  ];

  const tags = [
    { id: 1, name: 'Account Issue' },
    { id: 2, name: 'Transaction Problem' },
    { id: 3, name: 'Billing Question' },
    { id: 4, name: 'Technical Support' }
  ];

  const knowledgeArticles = [
    { id: 1, title: 'Viewing Transactions', excerpt: 'How to view account transactions in the app and web portal...' },
    { id: 2, title: 'Transaction History Limits', excerpt: 'Understanding transaction history viewing limits and archives...' },
    { id: 3, title: 'Missing Transactions', excerpt: 'Troubleshooting steps for transactions not appearing in your history...' }
  ];

  const handleSendMessage = () => {
    if (messageInput.trim() === '') return;
    // In a real app, you would add the message to the chat and clear the input
    setMessageInput('');
  };

  const toggleCannedResponses = () => {
    setShowCannedResponses(!showCannedResponses);
  };

  const insertCannedResponse = (text) => {
    setMessageInput(text);
    setShowCannedResponses(false);
  };

  const togglePanel = () => {
    setShowPanel(!showPanel);
  };

  // Get chat class for styling
  const getChatClass = (chat) => {
    let classes = 'border-b border-gray-200 hover:bg-gray-50 cursor-pointer p-3 transition-colors ';
    
    if (chat.id === activeChat) {
      classes += 'bg-blue-50 border-l-4 border-l-blue-500 ';
    }
    
    if (chat.unread) {
      classes += 'font-semibold ';
    }
    
    if (chat.waitingForAgent) {
      classes += 'border-r-4 border-r-yellow-400 ';
    }
    
    return classes;
  };

  // Get priority indicator class
  const getPriorityClass = (priority) => {
    switch(priority) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Get channel indicator
  const getChannelIcon = (channel) => {
    switch(channel) {
      case 'WhatsApp':
        return <div className="text-green-600 text-xs flex items-center"><MessageSquare size={12} className="mr-1" /> WA</div>;
      case 'Web Chat':
        return <div className="text-blue-600 text-xs flex items-center"><Globe size={12} className="mr-1" /> Web</div>;
      case 'SMS':
        return <div className="text-purple-600 text-xs flex items-center"><MessageSquare size={12} className="mr-1" /> SMS</div>;
      default:
        return <div className="text-gray-600 text-xs flex items-center"><MessageSquare size={12} className="mr-1" /> {channel}</div>;
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-100 overflow-hidden">
      {/* Header */}
      <header className="bg-white shadow-sm h-16 flex items-center justify-between px-4 z-10">
        <div className="flex items-center">
          <img src="/api/placeholder/40/40" alt="Logo" className="h-8 w-8 mr-2" />
          <h1 className="text-xl font-semibold text-gray-800">CX Gateway</h1>
        </div>
        
        <div className="flex items-center">
          <div className="relative mr-2">
            <select className="bg-green-100 text-green-800 text-sm rounded-full px-3 py-1 appearance-none cursor-pointer">
              <option>Available</option>
              <option>Away</option>
              <option>Busy</option>
              <option>Offline</option>
            </select>
          </div>
          <button className="p-2 rounded-full hover:bg-gray-100 relative">
            <Bell size={20} />
            <span className="absolute top-1 right-1 bg-red-500 rounded-full w-2 h-2"></span>
          </button>
          <div className="flex items-center ml-4 text-sm">
            <span className="mr-2">Sophia Chen</span>
            <img src="/api/placeholder/32/32" alt="User" className="h-8 w-8 rounded-full" />
          </div>
        </div>
      </header>
      
      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Chat List Panel */}
        <div className="w-80 bg-white border-r overflow-hidden flex flex-col">
          <div className="p-3 border-b">
            <div className="relative flex items-center">
              <Search size={16} className="absolute left-3 text-gray-500" />
              <input 
                type="text" 
                placeholder="Search conversations..." 
                className="bg-gray-100 w-full pl-10 pr-3 py-2 rounded-lg text-sm"
              />
            </div>
            <div className="flex items-center justify-between mt-3">
              <div className="flex space-x-1">
                <button className="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">
                  All
                </button>
                <button className="bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">
                  Assigned to me
                </button>
              </div>
              <button className="text-gray-500 hover:text-gray-700">
                <Filter size={16} />
              </button>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto">
            {chats.map(chat => (
              <div 
                key={chat.id} 
                className={getChatClass(chat)}
                onClick={() => setActiveChat(chat.id)}
              >
                <div className="flex items-start">
                  <div className={`mt-1 w-2 h-2 rounded-full mr-2 flex-shrink-0 ${getPriorityClass(chat.priority)}`}></div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <span className="font-medium text-gray-900 truncate block">{chat.name}</span>
                      <span className="text-xs text-gray-500">{chat.time}</span>
                    </div>
                    
                    <div className="flex items-center justify-between mt-1">
                      <p className="text-sm text-gray-600 truncate">{chat.lastMessage}</p>
                      {getChannelIcon(chat.channel)}
                    </div>
                    
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-gray-500 italic">{chat.status}</span>
                      {chat.unread && (
                        <span className="bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                          1
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="p-3 border-t bg-gray-50 flex justify-between items-center">
            <div>
              <span className="text-xs text-gray-500">Active chats: <b>4</b></span>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-1 text-gray-500 hover:bg-gray-200 rounded">
                <Inbox size={18} />
              </button>
              <button className="p-1 text-gray-500 hover:bg-gray-200 rounded">
                <List size={18} />
              </button>
              <button className="p-1 text-gray-500 hover:bg-gray-200 rounded">
                <ClipboardList size={18} />
              </button>
            </div>
          </div>
        </div>
        
        {/* Chat Content Area */}
        <div className="flex-1 flex flex-col bg-gray-50">
          <div className="bg-white border-b p-3 flex justify-between items-center">
            <div className="flex items-center">
              <div className="font-medium">{customerInfo.name}</div>
              <div className="text-xs text-gray-500 ml-3 flex items-center">
                {getChannelIcon(chats.find(c => c.id === activeChat)?.channel || 'Chat')}
                <span className="ml-2">ID: #10234</span>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button className="text-gray-500 hover:bg-gray-100 p-1 rounded" onClick={togglePanel}>
                <PanelRight size={18} />
              </button>
              <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs rounded px-2 py-1 flex items-center">
                <Tag size={14} className="mr-1" />
                Tag
              </button>
              <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs rounded px-2 py-1 flex items-center">
                <ArrowUpRight size={14} className="mr-1" />
                Transfer
              </button>
              <button className="bg-red-100 hover:bg-red-200 text-red-700 text-xs rounded px-2 py-1 flex items-center">
                <X size={14} className="mr-1" />
                End Chat
              </button>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            {messages
              .filter(msg => msg.chatId === activeChat)
              .map(message => (
                <div key={message.id} className={`mb-4 ${message.sender === 'agent' ? 'text-right' : ''}`}>
                  {message.sender === 'system' ? (
                    <div className="bg-gray-200 text-gray-600 text-xs py-1 px-3 rounded inline-block my-2 mx-auto">
                      {message.text}
                    </div>
                  ) : (
                    <div className={`flex ${message.sender === 'agent' ? 'justify-end' : 'justify-start'}`}>
                      {message.sender === 'customer' && (
                        <div className="bg-gray-300 rounded-full w-8 h-8 mr-2 flex-shrink-0 overflow-hidden">
                          <img src="/api/placeholder/32/32" alt="Customer" className="w-full h-full object-cover" />
                        </div>
                      )}
                      
                      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.sender === 'agent' ? 'bg-blue-500 text-white' :
                        message.sender === 'bot' ? 'bg-purple-100 text-gray-800' :
                        'bg-white text-gray-800 border'
                      }`}>
                        {message.sender === 'bot' && (
                          <div className="text-xs text-purple-700 font-medium mb-1">AI Assistant</div>
                        )}
                        <p className="text-sm">{message.text}</p>
                        <div className={`text-xs mt-1 ${message.sender === 'agent' ? 'text-blue-200' : 'text-gray-500'}`}>
                          {message.time}
                        </div>
                      </div>
                      
                      {message.sender === 'agent' && (
                        <div className="bg-blue-600 rounded-full w-8 h-8 ml-2 flex-shrink-0 overflow-hidden">
                          <img src="/api/placeholder/32/32" alt="Agent" className="w-full h-full object-cover" />
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
          </div>
          
          <div className="p-3 bg-white border-t relative">
            {showCannedResponses && (
              <div className="absolute bottom-full bg-white border rounded-lg shadow-lg mb-2 w-2/3 max-h-72 overflow-y-auto">
                <div className="p-2 border-b text-sm font-medium flex justify-between items-center">
                  <span>Canned Responses</span>
                  <button onClick={() => setShowCannedResponses(false)} className="text-gray-500 hover:text-gray-700">
                    <X size={16} />
                  </button>
                </div>
                <div className="p-1">
                  {cannedResponses.map(response => (
                    <div 
                      key={response.id} 
                      className="p-2 hover:bg-gray-100 cursor-pointer text-sm rounded"
                      onClick={() => insertCannedResponse(response.text)}
                    >
                      <div className="text-xs text-gray-500 mb-1">{response.category}</div>
                      <div>{response.text}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <div className="flex items-end">
              <button className="text-gray-500 hover:text-gray-700 p-2" title="Canned responses" onClick={toggleCannedResponses}>
                <FileText size={20} />
              </button>
              <button className="text-gray-500 hover:text-gray-700 p-2" title="Attach file">
                <Paperclip size={20} />
              </button>
              <div className="flex-1 mx-2">
                <textarea 
                  className="w-full border rounded-lg p-2 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Type your message..."
                  rows="2"
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                  }}
                ></textarea>
                <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
                  <div>
                    <span>Customer is typing...</span>
                  </div>
                  <div>
                    Press <kbd className="bg-gray-200 px-1 rounded">Shift</kbd> + <kbd className="bg-gray-200 px-1 rounded">Enter</kbd> for new line
                  </div>
                </div>
              </div>
              <button 
                className={`p-2 rounded-full ${messageInput.trim() ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'}`}
                onClick={handleSendMessage}
                disabled={!messageInput.trim()}
              >
                <Send size={20} />
              </button>
            </div>
          </div>
        </div>
        
        {/* Context Panel */}
        {showPanel && (
          <div className="w-80 bg-white border-l overflow-hidden flex flex-col">
            <div className="p-3 border-b flex justify-between items-center">
              <div className="font-medium">Customer Info</div>
              <button className="text-gray-500 hover:text-gray-700" onClick={togglePanel}>
                <X size={16} />
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {/* Tabs for the right panel */}
              <div className="flex border-b">
                <button className="flex-1 py-2 px-3 text-sm border-b-2 border-blue-500 text-blue-500 font-medium">
                  Profile
                </button>
                <button className="flex-1 py-2 px-3 text-sm text-gray-500">
                  Notes
                </button>
                <button className="flex-1 py-2 px-3 text-sm text-gray-500">
                  Knowledge
                </button>
              </div>
              
              {/* Customer Profile Section */}
              <div className="p-3">
                <div className="flex items-center mb-4">
                  <div className="bg-gray-200 rounded-full w-12 h-12 flex-shrink-0 overflow-hidden">
                    <img src="/api/placeholder/48/48" alt="Customer" className="w-full h-full object-cover" />
                  </div>
                  <div className="ml-3">
                    <div className="font-medium">{customerInfo.name}</div>
                    <div className="text-sm text-gray-500">{customerInfo.accountType} Member</div>
                    <div className="flex items-center mt-1">
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                        Active
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-3 mt-3">
                  <h3 className="text-sm font-medium mb-2">Contact Information</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-start">
                      <div className="text-gray-500 w-20">Email:</div>
                      <div className="flex-1">{customerInfo.email}</div>
                    </div>
                    <div className="flex items-start">
                      <div className="text-gray-500 w-20">Phone:</div>
                      <div className="flex-1">{customerInfo.phone}</div>
                    </div>
                    <div className="flex items-start">
                      <div className="text-gray-500 w-20">Account:</div>
                      <div className="flex-1">{customerInfo.accountNumber}</div>
                    </div>
                    <div className="flex items-start">
                      <div className="text-gray-500 w-20">Joined:</div>
                      <div className="flex-1">{customerInfo.joinDate}</div>
                    </div>
                    <div className="flex items-start">
                      <div className="text-gray-500 w-20">Last Contact:</div>
                      <div className="flex-1">{customerInfo.lastContact}</div>
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-3 mt-3">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-sm font-medium">Recent Transactions</h3>
                    <button className="text-xs text-blue-500 hover:underline">View All</button>
                  </div>
                  <div className="space-y-2 text-sm">
                    {recentTransactions.map(transaction => (
                      <div key={transaction.id} className="p-2 bg-gray-50 rounded">
                        <div className="flex justify-between">
                          <div className="font-medium">{transaction.description}</div>
                          <div className={transaction.amount.startsWith('+') ? 'text-green-600' : 'text-red-600'}>
                            {transaction.amount}
                          </div>
                        </div>
                        <div className="flex justify-between mt-1 text-xs text-gray-500">
                          <div>{transaction.date}</div>
                          <div>{transaction.status}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="border-t pt-3 mt-3">
                  <h3 className="text-sm font-medium mb-2">Knowledge Base</h3>
                  <div className="space-y-2 text-sm">
                    {knowledgeArticles.map(article => (
                      <div key={article.id} className="p-2 border border-gray-200 rounded hover:bg-gray-50 cursor-pointer">
                        <div className="font-medium">{article.title}</div>
                        <div className="text-xs text-gray-500 mt-1 line-clamp-2">{article.excerpt}</div>
                        <button className="text-xs text-blue-500 hover:underline mt-1">
                          Insert into chat
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="border-t pt-3 mt-3">
                  <h3 className="text-sm font-medium mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {tags.map(tag => (
                      <span 
                        key={tag.id} 
                        className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full cursor-pointer hover:bg-gray-200"
                      >
                        {tag.name}
                      </span>
                    ))}
                    <button className="text-blue-500 text-xs hover:underline">+ Add Tag</button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="p-3 border-t">
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 rounded flex items-center justify-center text-sm">
                <Info size={16} className="mr-2" />
                View Full Customer Profile
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentInterface;
