# Acciones que Afectan Conversaciones - Guía Detallada

## 📋 Índice

- [Introducción](#introducción)
- [1. Gestión del Ciclo de Vida](#1-gestión-del-ciclo-de-vida)
- [2. Transferencias y Escalación](#2-transferencias-y-escalación)
- [3. Mensajería y Comunicación](#3-mensajería-y-comunicación)
- [4. Cierre y Finalización](#4-cierre-y-finalización)
- [5. Gestión de Agentes](#5-gestión-de-agentes)
- [6. Análisis y Métricas](#6-análisis-y-métricas)
- [7. Administración Avanzada](#7-administración-avanzada)

---

## 🎯 Introducción

Este documento detalla **todas las acciones** que pueden afectar el estado, contenido o flujo de una conversación en el sistema de chat. Cada acción incluye:

- **Descripción funcional**
- **Implementación frontend**
- **Casos de uso prácticos**
- **Estados involucrados**
- **Efectos en tiempo real**

**📊 Estado Post-Migración**: Todas las acciones utilizan conexión directa con `chat-realtime-backend` a través de `conversationsService`.

---

## 1. Gestión del Ciclo de Vida

### 1.1 Crear Conversación

**🎯 Propósito**: Inicializar una nueva conversación en el sistema.

**📡 Endpoint**: `POST /conversations`
**🔗 Frontend**: `conversationsService.createConversation()`

#### **Implementación**

```typescript
const createConversation = async (conversationData: CreateConversationData) => {
  try {
    const newConversation = await conversationsService.createConversation({
      customerId: conversationData.customer.id,
      customer: {
        name: conversationData.customer.name,
        email: conversationData.customer.email,
        phone: conversationData.customer.phone,
      },
      channel: conversationData.channel, // 'whatsapp', 'web', 'email'
      priority: conversationData.priority, // 1-3
      initialMessage: conversationData.initialMessage,
      source: "manual", // 'manual', 'bot_transfer', 'api'
    });

    // Firebase actualiza automáticamente la UI
    toast.success("Conversación creada exitosamente");
    return newConversation;
  } catch (error) {
    toast.error("Error al crear conversación");
    throw error;
  }
};
```

#### **Estados Generados**

- **Inicial**: `new` - Conversación recién creada
- **Siguiente**: `waiting` - En cola para asignación

#### **Casos de Uso**

1. **Supervisor crea conversación manual**: Para casos especiales
2. **Integración API**: Sistemas externos crean conversaciones
3. **Importación de datos**: Migración de conversaciones históricas

#### **Efectos en Tiempo Real**

- ✅ Aparece en lista de conversaciones pendientes
- ✅ Notificación a supervisores
- ✅ Actualización de métricas de cola

---

### 1.2 Asignar Conversación

**🎯 Propósito**: Asignar una conversación específica a un agente.

**📡 Endpoint**: `POST /conversations/:id/assign`
**🔗 Frontend**: `conversationsService.transferConversation()`

#### **Implementación**

```typescript
const assignConversation = async (
  conversationId: string,
  agentId: string,
  reason: string = "manual_assignment"
) => {
  try {
    await conversationsService.transferConversation(
      conversationId,
      agentId,
      reason
    );

    // El sistema maneja automáticamente:
    // - Validación de disponibilidad del agente
    // - Actualización de capacidad
    // - Notificación al agente
    // - Actualización en Firebase

    toast.success(`Conversación asignada a ${agentName}`);
  } catch (error) {
    if (error.code === "AGENT_NOT_AVAILABLE") {
      toast.error("El agente no está disponible");
    } else if (error.code === "AGENT_OVERLOADED") {
      toast.warning("El agente tiene muchas conversaciones activas");
    } else {
      toast.error("Error al asignar conversación");
    }
  }
};
```

#### **Validaciones del Sistema**

1. **Disponibilidad**: Agente debe estar `online` o `away`
2. **Capacidad**: No exceder límite de chats simultáneos
3. **Habilidades**: Match con requerimientos de la conversación
4. **Permisos**: Usuario debe tener rol `supervisor` o superior

#### **Estados Involucrados**

- **Desde**: `waiting`, `new`
- **Hacia**: `pending_acceptance`, `assigned`

#### **Casos de Uso**

1. **Asignación manual por supervisor**: Control directo de distribución
2. **Reasignación tras rechazo**: Cuando agente rechaza conversación
3. **Balanceo de carga**: Redistribuir conversaciones entre agentes

---

### 1.3 Aceptar Conversación

**🎯 Propósito**: Agente acepta una conversación que le fue ofrecida.

**📡 Endpoint**: `POST /conversations/:id/accept`
**🔗 Frontend**: `conversationsService.acceptConversation()`

#### **Implementación**

```typescript
const acceptConversation = async (conversationId: string) => {
  try {
    await conversationsService.acceptConversation(conversationId);

    // Efectos automáticos:
    // - Estado cambia a 'active'
    // - Se actualiza capacidad del agente
    // - Se notifica al cliente (si está configurado)
    // - Se inicia timer de respuesta

    toast.success("Conversación aceptada");

    // Redirigir a la conversación
    router.push(`/dashboard/conversations/${conversationId}`);
  } catch (error) {
    toast.error("Error al aceptar conversación");
  }
};
```

#### **Estados Involucrados**

- **Desde**: `pending_acceptance`
- **Hacia**: `active`

#### **Casos de Uso**

1. **Flujo normal de asignación**: Agente acepta conversación ofrecida
2. **Recuperación tras timeout**: Agente acepta antes del timeout
3. **Priorización**: Agente elige qué conversación atender primero

#### **Efectos en Tiempo Real**

- ✅ Conversación aparece en panel activo del agente
- ✅ Cliente recibe notificación de agente asignado
- ✅ Se inicia tracking de métricas de respuesta

---

### 1.4 Rechazar Conversación

**🎯 Propósito**: Agente rechaza una conversación ofrecida.

**📡 Endpoint**: `POST /conversations/:id/reject`
**🔗 Frontend**: `conversationsService.rejectConversation()`

#### **Implementación**

```typescript
const rejectConversation = async (conversationId: string, reason: string) => {
  try {
    await conversationsService.rejectConversation(conversationId, reason);

    // El sistema automáticamente:
    // - Busca otro agente disponible
    // - Si no encuentra, regresa a cola
    // - Registra el rechazo para métricas
    // - Puede afectar score del agente

    toast.info("Conversación rechazada - buscando otro agente");
  } catch (error) {
    toast.error("Error al rechazar conversación");
  }
};
```

#### **Motivos de Rechazo**

- `overloaded` - Agente con demasiadas conversaciones
- `break_time` - Agente en descanso
- `skill_mismatch` - No tiene habilidades requeridas
- `priority_conflict` - Atendiendo caso más urgente
- `technical_issue` - Problemas técnicos

#### **Estados Involucrados**

- **Desde**: `pending_acceptance`
- **Hacia**: `waiting` (regresa a cola)

#### **Efectos en Métricas**

- ❌ Incrementa ratio de rechazo del agente
- 📊 Afecta tiempo promedio de asignación
- 🔄 Puede triggerar reasignación automática

---

## 2. Transferencias y Escalación

### 2.1 Transferir Entre Agentes

**🎯 Propósito**: Mover una conversación de un agente a otro.

**📡 Endpoint**: `POST /conversations/:id/transfer`
**🔗 Frontend**: `conversationsService.transferConversation()`

#### **Implementación**

```typescript
const transferConversation = async (transferData: TransferData) => {
  try {
    await conversationsService.transferConversation(
      transferData.conversationId,
      transferData.targetAgentId,
      transferData.reason
    );

    // Efectos automáticos:
    // - Mensaje de transferencia automático
    // - Contexto preservado
    // - Historial de transferencias actualizado
    // - Notificación al nuevo agente

    toast.success(`Conversación transferida a ${transferData.targetAgentName}`);

    // Opcional: Redirigir a lista de conversaciones
    if (transferData.closeCurrentView) {
      router.push("/dashboard/conversations");
    }
  } catch (error) {
    if (error.code === "TARGET_AGENT_UNAVAILABLE") {
      toast.error("El agente destino no está disponible");
    } else {
      toast.error("Error al transferir conversación");
    }
  }
};
```

#### **Tipos de Transferencia**

**1. Por Especialización**

```typescript
await conversationsService.transferConversation(
  conversationId,
  specialistId,
  "Cliente requiere especialista en facturación"
);
```

**2. Por Sobrecarga**

```typescript
await conversationsService.transferConversation(
  conversationId,
  availableAgentId,
  "Redistribución de carga de trabajo"
);
```

**3. Por Idioma**

```typescript
await conversationsService.transferConversation(
  conversationId,
  bilingualAgentId,
  "Cliente requiere atención en inglés"
);
```

#### **Estados Involucrados**

- **Desde**: `active`
- **Durante**: `transferring`
- **Hacia**: `pending_acceptance` o `active`

#### **Mensaje Automático Generado**

```
🔄 Conversación transferida desde [Agente Origen] a [Agente Destino]
Motivo: [Razón de transferencia]
Hora: [Timestamp]
```

---

### 2.2 Transferir a Departamento

**🎯 Propósito**: Transferir conversación a una cola departamental.

**📡 Endpoint**: `POST /conversations/:id/department-transfer`
**🔗 Frontend**: `conversationsService.transferToDepartment()`

#### **Implementación**

```typescript
const transferToDepartment = async (
  conversationId: string,
  departmentId: string,
  reason: string
) => {
  try {
    await conversationsService.transferToDepartment(
      conversationId,
      departmentId,
      reason
    );

    // El sistema:
    // - Agrega a cola del departamento
    // - Preserva contexto y prioridad
    // - Notifica al departamento
    // - Libera al agente actual

    toast.success(`Conversación transferida a ${departmentName}`);
  } catch (error) {
    toast.error("Error al transferir a departamento");
  }
};
```

#### **Departamentos Disponibles**

- `technical_support` - Soporte técnico
- `billing` - Facturación
- `sales` - Ventas
- `complaints` - Quejas y reclamos
- `vip_support` - Soporte VIP

#### **Estados Involucrados**

- **Desde**: `active`
- **Hacia**: `waiting` (en cola departamental)

---

### 2.3 Escalar Conversación

**🎯 Propósito**: Elevar conversación a nivel superior (supervisor/gerente).

**📡 Endpoint**: `POST /conversations/:id/escalate`
**🔗 Frontend**: `conversationsService.escalateConversation()`

#### **Implementación**

```typescript
const escalateConversation = async (
  conversationId: string,
  escalationLevel: number,
  reason: string
) => {
  try {
    await conversationsService.escalateConversation(
      conversationId,
      escalationLevel,
      reason
    );

    // Efectos:
    // - Prioridad aumenta automáticamente
    // - Notificación urgente a supervisores
    // - Se registra en historial de escalaciones
    // - Puede activar alertas especiales

    toast.success("Conversación escalada exitosamente");
  } catch (error) {
    toast.error("Error al escalar conversación");
  }
};
```

#### **Niveles de Escalación**

1. **Nivel 1**: Agente senior del mismo departamento
2. **Nivel 2**: Supervisor de departamento
3. **Nivel 3**: Gerente de área
4. **Nivel 4**: Director de atención al cliente

#### **Criterios de Escalación Automática**

- ⏰ Tiempo sin resolver > 30 minutos
- 🔑 Palabras clave: "supervisor", "gerente", "queja"
- 😤 Sentimiento negativo alto detectado
- 📈 Cliente VIP o premium

---

## 3. Mensajería y Comunicación

### 3.1 Enviar Mensaje

**🎯 Propósito**: Enviar un mensaje dentro de una conversación.

**📡 Endpoint**: `POST /conversations/:id/messages`
**🔗 Frontend**: `conversationsService.sendMessage()`

#### **Implementación Completa**

```typescript
const sendMessage = async (messageData: SendMessageData) => {
  try {
    // Validaciones previas
    if (!messageData.text.trim()) {
      toast.error("El mensaje no puede estar vacío");
      return;
    }

    if (messageData.text.length > 4000) {
      toast.error("El mensaje es demasiado largo");
      return;
    }

    await conversationsService.sendMessage(
      messageData.conversationId,
      messageData.text,
      "agent", // sender type
      "text", // message type
      messageData.formatting, // bold, italic, etc.
      messageData.richContent, // links, mentions, etc.
      messageData.linkPreviews,
      messageData.mentions
    );

    // Efectos automáticos:
    // - Mensaje aparece en tiempo real
    // - Se actualiza timestamp de última actividad
    // - Se reinicia timer de inactividad
    // - Se registra para métricas de respuesta

    // Limpiar campo de texto
    setMessageText("");

    // Scroll al último mensaje
    scrollToBottom();
  } catch (error) {
    toast.error("Error al enviar mensaje");
  }
};
```

#### **Tipos de Mensaje**

- `text` - Mensaje de texto normal
- `system` - Mensaje del sistema (transferencias, etc.)
- `file` - Archivo adjunto
- `image` - Imagen
- `audio` - Nota de voz
- `template` - Mensaje de plantilla

#### **Formateo de Texto**

```typescript
const formatting = {
  bold: [[0, 4]], // "Hola" en negrita
  italic: [[6, 12]], // "cliente" en cursiva
  code: [[14, 20]], // código monospace
  strikethrough: [[22, 30]],
};
```

#### **Contenido Rico**

```typescript
const richContent = {
  linkPreviews: [
    {
      url: "https://example.com",
      title: "Título del enlace",
      description: "Descripción",
      image: "https://example.com/image.jpg",
    },
  ],
  mentions: [
    {
      userId: "agent123",
      displayName: "@juan.perez",
      position: [15, 26], // posición en el texto
    },
  ],
};
```

---

### 3.2 Marcar Mensajes como Leídos

**🎯 Propósito**: Indicar que el agente ha leído mensajes específicos.

**📡 Endpoint**: `PUT /conversations/:id/read`
**🔗 Frontend**: `conversationsService.markAsRead()`

#### **Implementación**

```typescript
const markMessagesAsRead = async (
  conversationId: string,
  messageIds: string[]
) => {
  try {
    await conversationsService.markAsRead(conversationId, messageIds);

    // Efectos visuales:
    // - Mensajes muestran indicador de "leído"
    // - Se actualiza contador de mensajes no leídos
    // - Cliente puede ver que agente leyó mensajes
  } catch (error) {
    console.error("Error al marcar mensajes como leídos:", error);
  }
};

// Auto-marcar como leído cuando conversación está visible
useEffect(() => {
  if (isConversationVisible && unreadMessages.length > 0) {
    const messageIds = unreadMessages.map((msg) => msg.id);
    markMessagesAsRead(conversationId, messageIds);
  }
}, [isConversationVisible, unreadMessages]);
```

#### **Estados de Lectura**

- `sent` - Mensaje enviado
- `delivered` - Mensaje entregado
- `read` - Mensaje leído por destinatario

---

### 3.3 Indicador de Escritura

**🎯 Propósito**: Mostrar cuando el agente está escribiendo un mensaje.

**📡 Endpoint**: `POST /conversations/:id/typing`
**🔗 Frontend**: `conversationsService.setTyping()`

#### **Implementación**

```typescript
const handleTypingIndicator = () => {
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const setTyping = async (conversationId: string, typing: boolean) => {
    try {
      await conversationsService.setTyping(conversationId, typing, "agent");

      // Actualización local inmediata para UI
      setTypingIndicators((prev) => ({
        ...prev,
        [conversationId]: {
          ...prev[conversationId],
          agentTyping: typing,
        },
      }));
    } catch (error) {
      console.error("Error al actualizar indicador de escritura:", error);
    }
  };

  const handleInputChange = (text: string) => {
    setMessageText(text);

    if (text.length > 0 && !isTyping) {
      // Iniciar indicador de escritura
      setIsTyping(true);
      setTyping(conversationId, true);
    }

    // Resetear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Detener indicador después de 3 segundos sin escribir
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      setTyping(conversationId, false);
    }, 3000);
  };

  return { handleInputChange, isTyping };
};
```

#### **Duración del Indicador**

- ⏱️ **Activación**: Inmediata al empezar a escribir
- ⏱️ **Duración**: 3 segundos sin actividad
- ⏱️ **Desactivación**: Al enviar mensaje o timeout

---

## 4. Cierre y Finalización

### 4.1 Cerrar Conversación

**🎯 Propósito**: Finalizar una conversación marcándola como resuelta.

**📡 Endpoint**: `PUT /conversations/:id/close`
**🔗 Frontend**: `conversationsService.closeConversation()`

#### **Implementación Completa**

```typescript
const closeConversation = async (
  conversationId: string,
  closeData: CloseConversationData
) => {
  try {
    // Validaciones previas
    if (!closeData.reason) {
      toast.error("Debe especificar un motivo de cierre");
      return;
    }

    await conversationsService.closeConversation(conversationId, {
      reason: closeData.reason,
      resolution: closeData.resolution,
      notes: closeData.notes,
      customerSatisfied: closeData.customerSatisfied,
      followUpRequired: closeData.followUpRequired,
      followUpDate: closeData.followUpDate,
    });

    // Efectos automáticos:
    // - Estado cambia a 'closed'
    // - Se libera capacidad del agente
    // - Se calculan métricas de resolución
    // - Se puede enviar encuesta de satisfacción
    // - Se archiva la conversación

    toast.success("Conversación cerrada exitosamente");

    // Redirigir a lista de conversaciones
    router.push("/dashboard/conversations");
  } catch (error) {
    if (error.code === "CANNOT_CLOSE_ACTIVE_CONVERSATION") {
      toast.error(
        "No se puede cerrar una conversación con mensajes pendientes"
      );
    } else {
      toast.error("Error al cerrar conversación");
    }
  }
};
```

#### **Motivos de Cierre**

- `resolved` - Problema resuelto satisfactoriamente
- `no_response` - Cliente no responde
- `duplicate` - Conversación duplicada
- `spam` - Mensaje spam o irrelevante
- `transferred` - Transferida a otro canal
- `customer_request` - Cliente solicita cierre
- `technical_issue` - Problema técnico irresolvable

#### **Datos de Cierre**

```typescript
interface CloseConversationData {
  reason: string;
  resolution?: string; // Descripción de la solución
  notes?: string; // Notas internas
  customerSatisfied?: boolean; // Satisfacción del cliente
  followUpRequired?: boolean; // Requiere seguimiento
  followUpDate?: Date; // Fecha de seguimiento
  tags?: string[]; // Etiquetas finales
}
```

#### **Estados Involucrados**

- **Desde**: `active`, `paused`
- **Hacia**: `closed`

#### **Métricas Generadas**

- ⏱️ Tiempo total de resolución
- 📊 Satisfacción del cliente
- 🎯 Efectividad de resolución
- 📈 Productividad del agente

---

### 4.2 Reabrir Conversación

**🎯 Propósito**: Reactivar una conversación previamente cerrada.

**📡 Endpoint**: `POST /conversations/:id/reopen`
**🔗 Frontend**: `conversationsService.reopenConversation()`

#### **Implementación**

```typescript
const reopenConversation = async (
  conversationId: string,
  reopenReason: string
) => {
  try {
    await conversationsService.reopenConversation(conversationId, {
      reason: reopenReason,
      reopenedBy: currentAgent.id,
      notes: "Conversación reabierta por solicitud del cliente",
    });

    // Efectos:
    // - Estado cambia a 'active'
    // - Se reasigna al agente original o disponible
    // - Se registra evento de reapertura
    // - Se notifica al agente asignado

    toast.success("Conversación reabierta exitosamente");

    // Navegar a la conversación
    router.push(`/dashboard/conversations/${conversationId}`);
  } catch (error) {
    toast.error("Error al reabrir conversación");
  }
};
```

#### **Motivos de Reapertura**

- `customer_request` - Cliente solicita información adicional
- `follow_up` - Seguimiento programado
- `issue_not_resolved` - Problema no resuelto completamente
- `new_related_issue` - Nuevo problema relacionado
- `agent_error` - Error en cierre previo

#### **Estados Involucrados**

- **Desde**: `closed`
- **Hacia**: `active` o `waiting`

---

## 5. Gestión de Agentes

### 5.1 Pausar Conversación

**🎯 Propósito**: Pausar temporalmente una conversación activa.

**📡 Endpoint**: `PUT /conversations/:id/pause`
**🔗 Frontend**: `conversationsService.pauseConversation()`

#### **Implementación**

```typescript
const pauseConversation = async (
  conversationId: string,
  pauseData: PauseConversationData
) => {
  try {
    await conversationsService.pauseConversation(conversationId, {
      reason: pauseData.reason,
      customMessage: pauseData.customMessage,
      expectedResumeTime: pauseData.expectedResumeTime,
      autoResume: pauseData.autoResume,
    });

    // Efectos:
    // - Estado cambia a 'paused'
    // - Se envía mensaje automático al cliente
    // - Se programa reanudación automática (opcional)
    // - Se libera parcialmente la capacidad del agente

    toast.success("Conversación pausada");
  } catch (error) {
    toast.error("Error al pausar conversación");
  }
};
```

#### **Motivos de Pausa**

- `agent_break` - Descanso del agente
- `investigation` - Investigación requerida
- `escalation_pending` - Esperando escalación
- `customer_request` - Cliente solicita tiempo
- `technical_issue` - Problema técnico temporal

#### **Mensaje Automático al Cliente**

```
⏸️ Hemos pausado temporalmente esta conversación.
Motivo: [Razón personalizada]
Tiempo estimado de reanudación: [Tiempo]
Gracias por tu paciencia.
```

---

### 5.2 Reanudar Conversación

**🎯 Propósito**: Reactivar una conversación pausada.

**📡 Endpoint**: `PUT /conversations/:id/resume`
**🔗 Frontend**: `conversationsService.resumeConversation()`

#### **Implementación**

```typescript
const resumeConversation = async (
  conversationId: string,
  resumeMessage?: string
) => {
  try {
    await conversationsService.resumeConversation(conversationId, {
      resumeReason: "manual_resume",
      followUpMessage: resumeMessage || "Continuamos con tu consulta",
    });

    // Efectos:
    // - Estado cambia a 'active'
    // - Se envía mensaje de reanudación
    // - Se restaura capacidad del agente
    // - Se reinicia timer de inactividad

    toast.success("Conversación reanudada");
  } catch (error) {
    toast.error("Error al reanudar conversación");
  }
};

// Reanudación automática programada
useEffect(() => {
  const checkAutoResume = () => {
    pausedConversations.forEach((conv) => {
      if (conv.autoResume && conv.expectedResumeTime <= Date.now()) {
        resumeConversation(conv.id, "Reanudando conversación automáticamente");
      }
    });
  };

  const interval = setInterval(checkAutoResume, 60000); // Check every minute
  return () => clearInterval(interval);
}, [pausedConversations]);
```

#### **Estados Involucrados**

- **Desde**: `paused`
- **Hacia**: `active`

---

## 6. Análisis y Métricas

### 6.1 Análisis de Sentimiento

**🎯 Propósito**: Obtener análisis de sentimiento de la conversación.

**📡 Endpoint**: `GET /conversations/:id/sentiment`
**🔗 Frontend**: `conversationsService.getConversationSentiment()`

#### **Implementación**

```typescript
const getConversationSentiment = async (conversationId: string) => {
  try {
    const sentimentData = await conversationsService.getConversationSentiment(
      conversationId
    );

    // Datos recibidos:
    // - Sentimiento general (positive, negative, neutral)
    // - Score numérico (-1 a 1)
    // - Análisis por mensaje
    // - Tendencia temporal
    // - Palabras clave detectadas

    setSentimentData(sentimentData);

    // Mostrar alertas si es necesario
    if (sentimentData.overall === "negative" && sentimentData.score < -0.7) {
      showSentimentAlert("Cliente muy insatisfecho - considerar escalación");
    }
  } catch (error) {
    console.error("Error al obtener análisis de sentimiento:", error);
  }
};

// Análisis automático en tiempo real
useEffect(() => {
  if (messages.length > 0) {
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.sender === "customer") {
      // Analizar solo mensajes del cliente
      getConversationSentiment(conversationId);
    }
  }
}, [messages]);
```

#### **Datos de Respuesta**

```typescript
interface SentimentAnalysis {
  overall: "positive" | "negative" | "neutral";
  score: number; // -1 a 1
  confidence: number; // 0 a 1
  messages: Array<{
    messageId: string;
    sentiment: string;
    score: number;
    keywords: string[];
  }>;
  trend: "improving" | "declining" | "stable";
  alerts: string[];
}
```

---

### 6.2 Historial de Transferencias

**🎯 Propósito**: Obtener historial completo de transferencias de una conversación.

**📡 Endpoint**: `GET /conversations/:id/transfers`
**🔗 Frontend**: `conversationsService.getConversationTransfers()`

#### **Implementación**

```typescript
const getTransferHistory = async (conversationId: string) => {
  try {
    const transfers = await conversationsService.getConversationTransfers(
      conversationId
    );

    // Datos incluyen:
    // - Agente origen y destino
    // - Timestamp de transferencia
    // - Motivo de transferencia
    // - Duración en cada agente
    // - Efectividad de la transferencia

    setTransferHistory(transfers);
  } catch (error) {
    console.error("Error al obtener historial de transferencias:", error);
  }
};
```

#### **Estructura de Datos**

```typescript
interface TransferHistory {
  transfers: Array<{
    id: string;
    fromAgent: AgentInfo;
    toAgent: AgentInfo;
    timestamp: number;
    reason: string;
    duration: number; // tiempo con agente anterior
    effectiveness: number; // 0-1
  }>;
  totalTransfers: number;
  averageDuration: number;
  currentAgent: AgentInfo;
}
```

---

## 7. Administración Avanzada

### 7.1 Exportar Conversación

**🎯 Propósito**: Generar exportación de conversación en diferentes formatos.

**📡 Endpoint**: `POST /conversations/:id/export`
**🔗 Frontend**: `conversationsService.exportConversation()`

#### **Implementación**

```typescript
const exportConversation = async (
  conversationId: string,
  exportOptions: ExportOptions
) => {
  try {
    const exportData = await conversationsService.exportConversation(
      conversationId,
      {
        format: exportOptions.format, // 'pdf', 'json', 'csv', 'html'
        includeInternalNotes: exportOptions.includeInternalNotes,
        includeAgentMetrics: exportOptions.includeAgentMetrics,
        includeSystemMessages: exportOptions.includeSystemMessages,
        dateRange: exportOptions.dateRange,
        language: exportOptions.language || "es",
      }
    );

    // Descargar archivo
    const blob = new Blob([exportData.content], {
      type: exportData.mimeType,
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = exportData.filename;
    link.click();

    toast.success("Conversación exportada exitosamente");
  } catch (error) {
    toast.error("Error al exportar conversación");
  }
};
```

#### **Formatos Disponibles**

- `pdf` - Documento PDF formateado
- `json` - Datos estructurados JSON
- `csv` - Tabla CSV para análisis
- `html` - Página web con estilos
- `txt` - Texto plano simple

---

### 7.2 Gestión de Notas

**🎯 Propósito**: Agregar, editar y gestionar notas internas de conversación.

**📡 Endpoints**:

- `GET /conversations/:id/notes`
- `POST /conversations/:id/notes`
- `PUT /conversations/:id/notes/:noteId`
- `DELETE /conversations/:id/notes/:noteId`

**🔗 Frontend**: `conversationsService.getNotes()`, `conversationsService.createNote()`

#### **Implementación**

```typescript
const manageConversationNotes = () => {
  const [notes, setNotes] = useState<ConversationNote[]>([]);

  const loadNotes = async (conversationId: string) => {
    try {
      const notesData = await conversationsService.getNotes(conversationId);
      setNotes(notesData);
    } catch (error) {
      console.error("Error al cargar notas:", error);
    }
  };

  const createNote = async (noteData: CreateNoteData) => {
    try {
      const newNote = await conversationsService.createNote(
        noteData.conversationId,
        noteData.content,
        noteData.category, // 'general', 'follow_up', 'escalation', 'resolution'
        noteData.priority, // 'low', 'medium', 'high'
        noteData.visibility // 'private', 'team', 'public'
      );

      setNotes((prev) => [...prev, newNote]);
      toast.success("Nota agregada exitosamente");
    } catch (error) {
      toast.error("Error al crear nota");
    }
  };

  const deleteNote = async (conversationId: string, noteId: string) => {
    try {
      await conversationsService.deleteNote(conversationId, noteId);
      setNotes((prev) => prev.filter((note) => note.id !== noteId));
      toast.success("Nota eliminada");
    } catch (error) {
      toast.error("Error al eliminar nota");
    }
  };

  return { notes, loadNotes, createNote, deleteNote };
};
```

#### **Categorías de Notas**

- `general` - Notas generales
- `follow_up` - Seguimiento requerido
- `escalation` - Información de escalación
- `resolution` - Detalles de resolución
- `customer_info` - Información del cliente
- `technical` - Detalles técnicos

---

### 7.3 Gestión de Etiquetas

**🎯 Propósito**: Etiquetar conversaciones para organización y búsqueda.

**📡 Endpoint**: `PUT /conversations/:id/tags`
**🔗 Frontend**: `conversationsService.updateTags()`

#### **Implementación**

```typescript
const manageConversationTags = () => {
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [conversationTags, setConversationTags] = useState<string[]>([]);

  const updateTags = async (conversationId: string, tags: string[]) => {
    try {
      await conversationsService.updateTags(conversationId, tags);
      setConversationTags(tags);

      // Efectos:
      // - Mejora búsqueda y filtrado
      // - Permite agrupación de conversaciones
      // - Facilita generación de reportes
      // - Ayuda en análisis de tendencias

      toast.success("Etiquetas actualizadas");
    } catch (error) {
      toast.error("Error al actualizar etiquetas");
    }
  };

  const addTag = (tag: string) => {
    if (!conversationTags.includes(tag)) {
      const newTags = [...conversationTags, tag];
      updateTags(conversationId, newTags);
    }
  };

  const removeTag = (tag: string) => {
    const newTags = conversationTags.filter((t) => t !== tag);
    updateTags(conversationId, newTags);
  };

  return { conversationTags, addTag, removeTag, updateTags };
};
```

#### **Etiquetas Predefinidas**

- `urgent` - Urgente
- `billing` - Facturación
- `technical` - Técnico
- `complaint` - Queja
- `vip` - Cliente VIP
- `follow_up` - Seguimiento
- `resolved` - Resuelto
- `escalated` - Escalado

---

## 🔄 Sincronización en Tiempo Real

Todas las acciones documentadas mantienen sincronización automática via Firebase:

```typescript
// Hook global para sincronización
const useConversationSync = (conversationId: string) => {
  const { items: conversation, loading } = useFirebaseListener(
    `/conversations/${conversationId}`,
    {
      realTimeUpdates: true,
      throttleMs: 1000,
    }
  );

  // Los cambios se reflejan automáticamente:
  // ✅ Estados de conversación
  // ✅ Nuevos mensajes
  // ✅ Transferencias
  // ✅ Asignaciones
  // ✅ Indicadores de escritura
  // ✅ Notas y etiquetas
  // ✅ Métricas actualizadas

  return { conversation, loading };
};
```

---

## 📊 Resumen de Acciones

| Categoría          | Acciones   | Endpoints   | Efectos en Tiempo Real            |
| ------------------ | ---------- | ----------- | --------------------------------- |
| **Ciclo de Vida**  | 4 acciones | 4 endpoints | ✅ Estados, asignaciones          |
| **Transferencias** | 3 acciones | 3 endpoints | ✅ Reasignaciones, notificaciones |
| **Mensajería**     | 3 acciones | 3 endpoints | ✅ Mensajes, indicadores          |
| **Cierre**         | 2 acciones | 2 endpoints | ✅ Estados, métricas              |
| **Gestión**        | 2 acciones | 2 endpoints | ✅ Estados temporales             |
| **Análisis**       | 2 acciones | 2 endpoints | ✅ Métricas, reportes             |
| **Administración** | 3 acciones | 5 endpoints | ✅ Metadatos, organización        |

**Total**: **19 acciones principales** que afectan conversaciones

---

_Última actualización: Enero 2025 (Post-migración Smart API Router → API Directa)_
_Documento técnico: Acciones detalladas del sistema de conversaciones_
