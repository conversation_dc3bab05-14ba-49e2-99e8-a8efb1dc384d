-- CX Gateway Database Schema
-- Using PostgreSQL with modern practices

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Organizations (Multi-tenancy support)
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Channels (WhatsApp, Web Chat, SMS, etc.)
CREATE TABLE channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL, -- 'whatsapp', 'webchat', 'sms', 'email'
    display_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    configuration JSONB DEFAULT '{}', -- Channel-specific config (API keys, etc.)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Agents
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(255),
    avatar_url VARCHAR(500),
    role VARCHAR(50) DEFAULT 'agent', -- 'agent', 'supervisor', 'admin'
    status VARCHAR(50) DEFAULT 'offline', -- 'online', 'offline', 'away', 'busy'
    max_concurrent_chats INTEGER DEFAULT 3,
    skills TEXT[], -- Array of skills for routing
    settings JSONB DEFAULT '{}',
    last_activity_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Customers
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    external_id VARCHAR(255), -- ID from CRM or other system
    email VARCHAR(255),
    phone VARCHAR(50),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    display_name VARCHAR(255),
    avatar_url VARCHAR(500),
    metadata JSONB DEFAULT '{}', -- Additional customer data from CRM
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customer channel identities (one customer can have multiple channel identities)
CREATE TABLE customer_identities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID NOT NULL REFERENCES customers(id),
    channel_id UUID NOT NULL REFERENCES channels(id),
    channel_user_id VARCHAR(255) NOT NULL, -- Phone number for WhatsApp, email for email, etc.
    display_name VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(channel_id, channel_user_id)
);

-- Conversations
CREATE TABLE conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    customer_id UUID NOT NULL REFERENCES customers(id),
    channel_id UUID NOT NULL REFERENCES channels(id),
    customer_identity_id UUID NOT NULL REFERENCES customer_identities(id),
    assigned_agent_id UUID REFERENCES agents(id),
    
    -- Conversation metadata
    subject VARCHAR(500),
    status VARCHAR(50) DEFAULT 'open', -- 'open', 'assigned', 'closed', 'transferred'
    priority VARCHAR(50) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    tags TEXT[],
    
    -- Timing
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    first_response_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    closed_at TIMESTAMP WITH TIME ZONE,
    
    -- Bot interaction
    bot_handled BOOLEAN DEFAULT false,
    bot_handoff_reason VARCHAR(255),
    
    -- Source tracking
    source_type VARCHAR(50), -- 'incoming', 'deflected_call', 'proactive', 'follow_up'
    source_metadata JSONB DEFAULT '{}',
    
    -- Customer satisfaction
    csat_score INTEGER, -- 1-5 rating
    csat_comment TEXT,
    csat_submitted_at TIMESTAMP WITH TIME ZONE,
    nps_score INTEGER, -- 0-10 rating
    nps_submitted_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional data
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Messages
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id),
    
    -- Message metadata
    message_type VARCHAR(50) DEFAULT 'text', -- 'text', 'image', 'file', 'system', 'template'
    sender_type VARCHAR(50) NOT NULL, -- 'customer', 'agent', 'bot', 'system'
    sender_id UUID, -- Can be agent_id or customer_id
    
    -- Content
    content TEXT,
    content_metadata JSONB DEFAULT '{}', -- File URLs, image metadata, etc.
    
    -- Channel-specific data
    channel_message_id VARCHAR(255), -- External message ID from channel
    channel_metadata JSONB DEFAULT '{}',
    
    -- Delivery status
    status VARCHAR(50) DEFAULT 'sent', -- 'sent', 'delivered', 'read', 'failed'
    delivery_attempts INTEGER DEFAULT 0,
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    
    -- Bot data
    bot_intent VARCHAR(255),
    bot_confidence DECIMAL(3,2),
    bot_response_time_ms INTEGER,
    
    -- Threading
    reply_to_message_id UUID REFERENCES messages(id),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversation assignments (for tracking agent assignments over time)
CREATE TABLE conversation_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES conversations(id),
    agent_id UUID NOT NULL REFERENCES agents(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    assigned_by_id UUID REFERENCES agents(id), -- Who made the assignment
    unassigned_at TIMESTAMP WITH TIME ZONE,
    unassignment_reason VARCHAR(255)
);

-- Agent sessions (for tracking when agents are online/offline)
CREATE TABLE agent_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES agents(id),
    status VARCHAR(50) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE,
    session_metadata JSONB DEFAULT '{}'
);

-- Bot configurations
CREATE TABLE bot_configurations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    provider VARCHAR(100) NOT NULL, -- 'dialogflow', 'custom', etc.
    configuration JSONB NOT NULL, -- Provider-specific config
    fallback_settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Knowledge base articles
CREATE TABLE knowledge_articles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    category VARCHAR(255),
    tags TEXT[],
    is_published BOOLEAN DEFAULT false,
    view_count INTEGER DEFAULT 0,
    helpful_count INTEGER DEFAULT 0,
    not_helpful_count INTEGER DEFAULT 0,
    vector_embedding vector(1536), -- For semantic search (if using pgvector)
    created_by_id UUID REFERENCES agents(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Canned responses/templates
CREATE TABLE response_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(255),
    content TEXT NOT NULL,
    shortcut VARCHAR(50), -- Quick access shortcut like '/greeting'
    is_shared BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    created_by_id UUID REFERENCES agents(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Integration configurations
CREATE TABLE integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    type VARCHAR(100) NOT NULL, -- 'zoho_crm', 'jira', 'oracle_fusion', etc.
    name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    configuration JSONB NOT NULL, -- Encrypted configuration
    last_sync_at TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit logs
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    entity_type VARCHAR(100) NOT NULL, -- 'conversation', 'message', 'agent', etc.
    entity_id UUID NOT NULL,
    action VARCHAR(100) NOT NULL, -- 'created', 'updated', 'deleted', 'assigned', etc.
    actor_type VARCHAR(50) NOT NULL, -- 'agent', 'system', 'customer'
    actor_id UUID,
    changes JSONB, -- What changed
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analytics events (for real-time analytics)
CREATE TABLE analytics_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    conversation_id UUID REFERENCES conversations(id),
    agent_id UUID REFERENCES agents(id),
    customer_id UUID REFERENCES customers(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    date DATE GENERATED ALWAYS AS (timestamp::date) STORED -- For partitioning
) PARTITION BY RANGE (date);

-- Create indexes for performance
CREATE INDEX idx_conversations_organization_status ON conversations(organization_id, status);
CREATE INDEX idx_conversations_agent_status ON conversations(assigned_agent_id, status) WHERE assigned_agent_id IS NOT NULL;
CREATE INDEX idx_conversations_customer ON conversations(customer_id);
CREATE INDEX idx_conversations_started_at ON conversations(started_at);

CREATE INDEX idx_messages_conversation_created ON messages(conversation_id, created_at);
CREATE INDEX idx_messages_sender ON messages(sender_type, sender_id);
CREATE INDEX idx_messages_channel_message_id ON messages(channel_message_id) WHERE channel_message_id IS NOT NULL;

CREATE INDEX idx_customer_identities_channel_user ON customer_identities(channel_id, channel_user_id);
CREATE INDEX idx_customer_identities_customer ON customer_identities(customer_id);

CREATE INDEX idx_agents_organization_status ON agents(organization_id, status);
CREATE INDEX idx_agents_email ON agents(email);

CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_organization_created ON audit_logs(organization_id, created_at);

CREATE INDEX idx_analytics_events_org_type_timestamp ON analytics_events(organization_id, event_type, timestamp);

-- Full-text search indexes
CREATE INDEX idx_messages_content_fts ON messages USING gin(to_tsvector('english', content));
CREATE INDEX idx_knowledge_articles_content_fts ON knowledge_articles USING gin(to_tsvector('english', title || ' ' || content));

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON conversations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Sample data for development
INSERT INTO organizations (name, slug) VALUES ('Demo FinTech', 'demo-fintech');

-- Get the organization ID for sample data
DO $$
DECLARE
    org_id UUID;
BEGIN
    SELECT id INTO org_id FROM organizations WHERE slug = 'demo-fintech';
    
    -- Insert sample channels
    INSERT INTO channels (organization_id, name, display_name) VALUES
    (org_id, 'whatsapp', 'WhatsApp'),
    (org_id, 'webchat', 'Web Chat'),
    (org_id, 'sms', 'SMS'),
    (org_id, 'email', 'Email');
    
    -- Insert sample agents
    INSERT INTO agents (organization_id, email, first_name, last_name, role, status) VALUES
    (org_id, '<EMAIL>', 'Sophia', 'Chen', 'agent', 'online'),
    (org_id, '<EMAIL>', 'Marcus', 'Johnson', 'agent', 'online'),
    (org_id, '<EMAIL>', 'Emma', 'Rodriguez', 'supervisor', 'online');
END $$;