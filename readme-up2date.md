# 🚀 CX System - Production-Ready Customer Experience Platform

**Version**: 2.0 Production-Ready  
**Status**: ✅ Active Development  
**Last Updated**: 2025-11-12  
**Architecture**: Hybrid Microservices + Real-time + Analytics

---

## 🎯 What is the CX System?

The **CX System** is a **production-ready Customer Experience platform** built for modern businesses that need to handle high-volume customer conversations across multiple channels (primarily WhatsApp) with intelligent automation and human agent support.

### Core Capabilities
- **🔄 Seamless omnichannel communication** (WhatsApp, with extensibility for other channels)
- **🤖 Intelligent bot-to-human handoffs** using AI decision engines (n8n workflows)
- **⚡ Real-time agent collaboration** with optimistic UI for instant responses
- **👁️ Supervisor oversight and analytics** for operational excellence
- **☁️ Scalable microservices architecture** ready for Google Cloud Run deployment

---

## 🏗️ System Architecture

### Message Flow (Production Architecture)
```
WhatsApp → Twilio → Channel Router → Session Manager (Redis)
                         ↓              ↓
                   PubSub (inbound/outbound)
                         ↓
                Bot Human Router → Decision Engine:
                         ↓         ├── Bot: n8n webhook  
                         ↓         └── Human: Load Balancing
                         ↓
                Chat Realtime ← → Firebase Realtime Database
                         ↓
                   Chat UI (Next.js Hybrid Architecture)
                         ↓
                 Analytics → Supabase PostgreSQL
```

### 7 Core Microservices

| Service | Port | Status | Purpose |
|---------|------|--------|---------|
| **twilio** | 3000 | ✅ Production-Ready | WhatsApp integration via webhooks |
| **channel-router** | 3002 | ✅ Production-Ready | Message routing + conversation lifecycle |
| **session-manager** | 3001 | ✅ Production-Ready | Session state management (Redis) |
| **bot-human-router** | 3004 | ✅ Production-Ready | AI/Human routing via n8n webhooks |
| **chat-realtime** | 3003 | ✅ Production-Ready | Firebase Realtime Database interface |
| **chat-ui** | 3007 | ✅ Production-Ready | Next.js frontend with hybrid architecture |
| **analytics** | 3006 | 📋 Planned | Metrics collection using Supabase |

---

## 💻 Technology Stack

### Backend Technologies
- **Runtime**: Node.js 18+ with TypeScript (100% typed)
- **Databases**: 
  - **Firebase Realtime Database** (real-time operations, <100ms latency)
  - **Supabase PostgreSQL** (configuration, analytics, complex queries)
  - **Redis** (session management, shared state)
- **Communication**: Google Cloud PubSub + REST APIs
- **Automation**: n8n workflows for AI decision engine
- **External APIs**: Twilio WhatsApp Business API

### Frontend Technologies
- **Framework**: Next.js 15 with TypeScript
- **UI Library**: shadcn/ui + Tailwind CSS
- **State Management**: 
  - **TanStack Query** (supervisor mode - memory-safe polling)
  - **Custom Optimistic UI** (agent mode - <100ms real-time performance)
- **Real-time**: Firebase WebSocket connections with intelligent fallback

### Infrastructure
- **Deployment**: Google Cloud Run (containerized microservices)
- **Development**: Docker containers + Firebase emulators
- **Monitoring**: Built-in health checks + structured logging (Winston)

---

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- Docker (for Redis)
- Firebase CLI
- Google Cloud SDK

### Development Setup
```bash
# Clone and setup
git clone [repository-url]
cd cx-system

# Install dependencies for all services
npm run install:all

# Start infrastructure
./scripts/start-all-emulators-improved.sh

# Start core services (in separate terminals)
cd services/chat-realtime && npm run dev     # Port 3003
cd services/chat-ui && npm run dev -- --port 3007  # Port 3007
cd services/channel-router && npm run dev    # Port 3002
cd services/bot-human-router && npm run dev  # Port 3004
cd services/session-manager && npm run dev   # Port 3001

# Create test data
cd services/chat-realtime
node conversation_manager.js --create juan 5
```

### Access Points
- **Agent Interface**: http://localhost:3007/agent
- **Supervisor Dashboard**: http://localhost:3007/supervisor
- **Firebase Console**: http://127.0.0.1:4000
- **Chat Realtime API**: http://localhost:3003/api/health

---

## 🎯 Core Features

### 1. **Intelligent Message Routing**
- **Multi-channel support** starting with WhatsApp
- **AI-powered department assignment** via n8n workflows
- **Smart bot-to-human handoffs** based on conversation complexity
- **Load balancing** with utilization-based agent assignment

### 2. **Real-time Agent Interface**
- **Hybrid Architecture**: TanStack Query (supervisor) + Optimistic UI (agent)
- **<100ms response times** for agent chat interactions
- **Conversation management**: Transfer, close, escalate, add notes
- **Status management**: Available/busy/away with supervisor authorization
- **Real-time typing indicators** and message delivery

### 3. **Supervisor Oversight**
- **Memory-safe dashboard** with 30+ minute stable sessions
- **Real-time monitoring** of agent activity and conversation queues
- **Intervention capabilities** with observation/participation modes
- **Authorization workflow** for agent status changes
- **Analytics and reporting** (basic implementation)

### 4. **Production-Ready Infrastructure**
- **Zero memory leaks** after major TanStack Query migration
- **Automatic failover** from WebSocket to polling
- **Comprehensive error handling** and recovery mechanisms
- **Structured logging** with correlation IDs
- **Health checks** and monitoring endpoints

---

## 🔧 Key Architectural Decisions

### Hybrid Database Strategy
- **Firebase Realtime Database**: Real-time operations, messaging, live updates
- **Supabase PostgreSQL**: Configuration, analytics, complex queries, user management
- **Redis**: Session state, conversation queues, temporary data

### Hybrid Frontend Architecture
- **Supervisor Mode**: TanStack Query for reliable, memory-safe polling
- **Agent Mode**: Custom Optimistic UI for ultra-fast chat performance
- **Rationale**: Different use cases require different optimization strategies

### Microservices Communication
- **Synchronous**: Direct HTTP for immediate responses
- **Asynchronous**: Google Cloud PubSub for reliable message delivery
- **Real-time**: Firebase WebSocket connections with automatic fallback

---

## 📊 System Status & Metrics

### Performance Achievements
- **Agent Chat Latency**: <100ms maintained
- **Supervisor Dashboard Uptime**: >30 minutes stable (vs <5 minutes before refactor)
- **Memory Usage**: Stable with proper garbage collection
- **Code Quality**: 800+ lines of problematic hooks eliminated

### Production Readiness
- **6/7 Services**: Production-ready with comprehensive testing
- **TypeScript Coverage**: 100% typed codebase
- **Error Handling**: Robust error recovery and user feedback
- **Testing**: E2E testing with realistic conversation scenarios

---

## 📚 Documentation

### Essential Reading
- **`CLAUDE.md`**: Complete development context and rules
- **`docs-locales/SYSTEM_EVOLUTION_NARRATIVE.md`**: Chronological system evolution story
- **`docs-locales/QUICK_REFERENCE_GUIDE.md`**: Fast navigation to relevant docs
- **`docs-locales/sesiones/`**: Daily development session logs

### Technical Documentation
- **`docs-locales/arquitectura/`**: Architecture decisions and diagrams
- **`docs-locales/servicios/`**: Service-specific documentation
- **`docs-locales/N8N_WEBHOOKS_CONTRACTS.md`**: AI integration contracts

---

## 🧪 Testing & Development

### Running Tests
```bash
# All services
npm run test:all

# Specific service
cd services/chat-realtime && npm test
cd services/chat-ui && npm test
```

### Development Tools
```bash
# Conversation management tool
cd services/chat-realtime
node conversation_manager.js --help

# Debug tools (browser console)
window.cxDebug.clearCache()
window.cxDebug.showCacheState()
```

### Testing Strategy
- **Unit Tests**: Service-level functionality
- **Integration Tests**: Cross-service communication
- **E2E Tests**: Complete user workflows
- **Real-time Testing**: Firebase connection scenarios

---

## 🔮 Roadmap

### Immediate (Next Sprint)
- **Analytics Service**: Complete implementation
- **Advanced Reporting**: Supervisor dashboard enhancements
- **Multi-channel Expansion**: Email and SMS integration

### Medium Term
- **Advanced AI**: Enhanced n8n decision engine capabilities
- **Enterprise Features**: Advanced compliance and security
- **Performance Optimization**: Further latency improvements

### Long Term
- **Voice Integration**: Phone call support
- **Advanced Analytics**: ML-powered insights
- **Global Deployment**: Multi-region support

---

## 🏆 What Makes This System Special

### Technical Excellence
- **Hybrid Architecture**: Right tool for each use case
- **Real-time First**: Built for instant communication
- **Production Stability**: Proven reliability under load
- **Developer Experience**: Superior debugging and development tools

### Business Value
- **Operational Efficiency**: Intelligent automation reduces agent workload
- **Scalable Growth**: Microservices architecture supports business expansion
- **Quality Assurance**: Supervisor oversight ensures service excellence
- **Data-Driven Decisions**: Comprehensive analytics and reporting

---

## 🤝 Contributing

### Development Principles
- **MVP Focus**: Implement only requested functionality
- **100% TypeScript**: Type safety throughout
- **Testing Required**: All code must be testable
- **Documentation First**: Document decisions and changes

### Workflow
- Feature branches for development
- Comprehensive testing before merge
- Documentation updates with each change
- Structured commit messages

---

## 🔒 Security & Compliance

### Authentication & Authorization
- **JWT-based authentication** with Supabase Auth
- **Role-based access control** (Agent, Supervisor, Admin)
- **Row-level security** policies in Supabase
- **API endpoint protection** with middleware validation

### Data Protection
- **Encryption at rest** for sensitive conversation data
- **Secure WebSocket connections** with authentication
- **PII handling** following privacy regulations
- **Audit logging** for compliance requirements

---

## 🚨 Troubleshooting

### Common Issues

#### Chat UI Cache Problems
```bash
# Hard refresh browser
Cmd+Shift+R → "Empty Cache and Hard Reload"

# Clear storage in browser console
localStorage.clear(); sessionStorage.clear(); location.reload(true);

# Reset Next.js cache
rm -rf .next && npm run build && npm run dev -- --port 3007
```

#### Firebase Connection Issues
```bash
# Restart emulators
./scripts/start-all-emulators-improved.sh

# Recreate test data
cd services/chat-realtime
node conversation_manager.js --delete-all
node conversation_manager.js --create juan 5
```

#### Service Health Checks
```bash
# Check all services
curl http://localhost:3003/api/health  # Chat Realtime
curl http://localhost:3002/health      # Channel Router
curl http://localhost:3004/health      # Bot Human Router
curl http://localhost:3001/health      # Session Manager
```

---

## 📈 Monitoring & Observability

### Logging Strategy
- **Structured JSON logs** with Winston
- **Correlation IDs** for request tracing
- **Log levels**: debug (dev) → info (prod)
- **Automatic log rotation** and retention

### Health Monitoring
- **Service health endpoints** for all microservices
- **Database connection monitoring** (Firebase, Supabase, Redis)
- **Real-time connection status** in Chat UI
- **Performance metrics** collection

### Debug Tools
```javascript
// Browser console debugging (Chat UI)
window.cxDebug.clearCache()        // Clear all cached data
window.cxDebug.showCacheState()    // Inspect current cache
window.cxDebug.forceSync()         // Force data synchronization
window.cxDebug.connectionStatus()  // Check real-time connection
```

---

## 🌟 Success Stories

### Performance Improvements
- **Memory Leak Elimination**: From <5 minute crashes to >30 minute stable sessions
- **Response Time Optimization**: Maintained <100ms agent chat latency
- **Code Quality**: Eliminated 800+ lines of problematic React hooks
- **Architecture Stability**: Zero breaking changes during major refactor

### Feature Completeness
- **Complete Conversation Lifecycle**: Create, assign, transfer, close with full audit trail
- **Real-time Collaboration**: Multiple agents can observe and participate in conversations
- **Intelligent Routing**: AI-powered decision engine for optimal resource allocation
- **Supervisor Oversight**: Comprehensive monitoring and intervention capabilities

---

## 📞 Support & Resources

### Getting Help
- **Documentation**: Start with `docs-locales/QUICK_REFERENCE_GUIDE.md`
- **System Evolution**: Read `docs-locales/SYSTEM_EVOLUTION_NARRATIVE.md`
- **Development Context**: Review `CLAUDE.md` for development guidelines
- **Session Logs**: Check `docs-locales/sesiones/` for recent changes

### Key Commands Reference
```bash
# Development environment
./scripts/start-all-emulators-improved.sh  # Start infrastructure
npm run dev:all                            # Start all services

# Data management
node conversation_manager.js --help         # Conversation management
node conversation_manager.js --create juan 5  # Create test data

# Testing
npm run test:all                           # Run all tests
npm run test:e2e                          # End-to-end tests
```

---

**Organization**: n1co
**Architecture**: Hybrid Microservices (Firebase + Supabase + Redis)
**Status**: Production-Ready Platform with Proven Stability and Performance
**Evolution**: 8+ months of thoughtful development and architectural refinement
