# Firebase CX Chat System - Product Requirements Document

**Version:** 2.0  
**Date:** April 19, 2025  
**Status:** Draft  
**Document for Download and Context Reference**  

## 1. Introduction

### 1.1 Purpose
This document outlines the product requirements for a real-time Customer Experience (CX) Chat System built on Firebase. The system enables customer service agents to handle conversations across multiple channels while providing automation features to improve efficiency and customer satisfaction.

### 1.2 Scope
The CX Chat System will provide:
- A real-time chat platform for human agents to interact with customers
- Message routing and queue management
- Agent tools and information access
- Chat transfer capabilities (manual and automatic)
- Customer and agent automation features
- Conversation history and context preservation
- Analytics and reporting capabilities
- Bot-to-human handoff capabilities
- Conversation session management

### 1.3 Definitions and Acronyms
- **CX**: Customer Experience
- **Firebase RTDB**: Firebase Real-time Database
- **SLA**: Service Level Agreement
- **CSAT**: Customer Satisfaction
- **RAG**: Retrieval Augmented Generation

## 2. System Architecture

### 2.1 Technology Stack
- **Frontend**: React.js
- **Backend**: Firebase (Realtime Database, Cloud Functions, Authentication)
- **Messaging**: Firebase Realtime Database, Cloud Messaging
- **Integrations**: n8n workflow engine
- **Long-term Storage**: Firestore (for chat transcripts and archives)

### 2.2 High-Level Architecture

```
                  +------------------+
                  |  Channel Router  |
                  | (WhatsApp, IG,   |
                  |  Email, Web, etc)|
                  +--------+---------+
                           |
                           | Pub/Sub Events
                           v
          +---------------------------------+
          |      Bot/Human Router Logic     |
          | (Determines message destination)|
          +---------------------------------+
                 |                  |
     Bot Messages|                  |Human Agent Messages
                 v                  v
+------------------+     +---------------------------+
| Bot Processing   |     | Firebase Realtime Database|
+--------+---------+     +---------------------------+
         |                           |
         |                           |
         v                           v
+------------------+     +------------------------+
| Bot-to-Human     |     | Agent Web Console      |
| Handoff System   |     | (React + Firebase)     |
+--------+---------+     +------------------------+
         |
         v
+------------------+
| Agent Queue      |
| Management       |
+------------------+
```

### 2.3 Data Model

Key collections in Firebase Realtime Database:

```
/conversations/
  /{conversationId}/
    - metadata
      - customerId: "customer123"
      - customerName: "Jane Doe"
      - channel: "whatsapp"
      - status: "active" | "waiting" | "closed" | "transferring"
      - startTime: timestamp
      - lastUpdateTime: timestamp
      - lastCustomerMessageTime: timestamp
      - sessionTimeout: ******** // 24 hours in milliseconds
    - assignment
      - assignedAgentId: "agent456"
      - assignmentTime: timestamp
      - previousAgentId: null
    - messages
    - indicators
    - transfer
    - botHandoff
    - automationState
    - customerContext
    - routingInfo
      - initialQueue: "sales"
      - currentQueue: "technical_support"
      - queueEntryTime: timestamp
      - routingHistory: []

/agents/
  /{agentId}/
    - profile
    - status
    - activeConversations
    - notifications
    - metrics

/teams/
  /{teamId}/
    - name
    - agents
    - automationSettings

/queues/
  /{queueId}/
    - waitingConversations
      - {conversationId}: {entryTime: timestamp, priority: 2}

/systemConfig/
  /sessionManagement/
    - defaultSessionTimeout: ******** // 24 hours
    - channelTimeouts: {
        "whatsapp": ********,
        "webchat": 3600000,
        "email": *********
      }
  /automations/
    /inactivity/
    /businessHours/
    /sla/
  /botHandoff/
    /triggers/
      - explicitRequest: true
      - lowConfidence: true
      - confidenceThreshold: 0.4
      - maxConsecutiveLowConfidence: 2
      - escalationKeywords: []
      - complexityThreshold: 0.7
      - maxTurnsBeforeHandoff: 10
      - restrictedTopics: []

/tools/
  /{toolId}/
    - name
    - endpoint
    - requiredParams
    - agentAccess
    - botAccess
```

## 3. Core Functionality

### 3.1 Conversation Management
- Multi-channel message processing (WhatsApp, Web, SMS, etc.)
- Real-time message delivery
- Typing indicators
- Read receipts
- File/media sharing
- Conversation history
- Conversation tagging and categorization
- Session management and timeout handling

### 3.2 Agent Interface
- Login and authentication
- Conversation list view
- Active conversation panel
- Multi-conversation handling
- Agent status management (online, away, busy)
- Canned responses
- Access to customer information
- External system integrations

### 3.3 Routing and Queue Management
- New conversation assignment
- Skill-based routing
- Queue prioritization
- Overflow handling
- Business hours routing

### 3.4 Tools Framework
- Centralized tools registry in Firebase
- Access control (agents vs. bots)
- Standardized interfaces
- Dynamic discovery
- Tool usage tracking

### 3.5 Transfer System
- Agent-to-agent transfers
- Team/department transfers
- Automated transfers
- Transfer history tracking
- Context preservation during transfers

### 3.6 Automation Framework
- Customer inactivity handling
- Business hours management
- SLA monitoring
- Agent automations
- Custom automation rules

### 3.7 Bot-to-Human Handoff
- Bot handoff decision points
- Handoff context capture
- Intelligent routing for handoffs
- Handoff queue management
- Handoff analytics and metrics

### 3.8 Session Management
- Conversation creation and continuity rules
- Message association with active conversations
- Session timeout configuration
- On-demand conversation creation

## 4. Detailed Requirements

### 4.1 Chat Transcription

#### 4.1.1 Functionality
- Archive completed conversations from Realtime Database to Firestore
- Provide transcript retrieval for agents and customers
- Enable searching historical conversations by various parameters
- Make historical data available for context in future conversations

#### 4.1.2 Technical Specifications
- Use Cloud Functions to trigger archiving on conversation close
- Implement transcript formatting for different output formats (text, HTML)
- Create indexed fields in Firestore for efficient searching
- Build API endpoints for transcript retrieval

#### 4.1.3 Acceptance Criteria
- Transcripts are successfully archived to Firestore when conversations are closed
- Agents can retrieve and search past conversations by customer, date, topic
- Transcripts can be shared with customers on request
- Historical data can be used for context in new conversations
- Transcripts preserve all metadata and conversation flow

### 4.2 Transfer System

#### 4.2.1 Functionality
- Enable manual transfers between agents
- Support team-based transfers
- Implement automatic transfers based on conditions
- Preserve context during transfers
- Notify customers about transfers

#### 4.2.2 Technical Specifications
- Store transfer metadata in conversation object
- Track transfer history within conversation
- Implement transfer acceptance workflow
- Create transfer queue for teams/departments
- Build automatic transfer monitoring service

#### 4.2.3 Acceptance Criteria
- Agents can initiate transfers to other agents or teams
- Receiving agents see transfer context and history
- Customer receives appropriate notifications during transfer
- Automatic transfers trigger correctly based on defined conditions
- Transfer analytics track frequency and success rates

#### 4.2.4 Transfer Reasons
- Skill-based requirements (technical expertise, product specialist)
- Agent availability (shift ending, workload balancing)
- Customer requests (language preference, dissatisfaction)
- Escalation needs (supervisor attention, complaints)
- Process requirements (department handoff, authorization levels)

### 4.3 Customer Inactivity Automation

#### 4.3.1 Functionality
- Detect inactive customer conversations
- Send configurable reminder messages
- Automatically close conversations after extended inactivity
- Reset automation when customer responds
- Support team/department-specific settings

#### 4.3.2 Technical Specifications
- Configure inactivity thresholds and messages in system config
- Track automation state in conversation object
- Use Cloud Functions to monitor and process inactive conversations
- Implement hierarchical settings (global > department > team)
- Enable channel-specific exclusions

#### 4.3.3 Acceptance Criteria
- System correctly identifies inactive customer conversations
- Reminder messages are sent according to configured thresholds
- Conversations are auto-closed after final threshold is reached
- Customer activity appropriately resets the automation
- Team-specific settings override global defaults
- Excluded channels are not affected by automation

#### 4.3.4 Configuration Parameters
- First reminder delay (default: 30 minutes)
- Second reminder delay (default: 60 minutes after first reminder)
- Auto-close delay (default: 30 minutes after second reminder)
- Customizable reminder messages
- Channel exclusion list

### 4.4 Bot-to-Human Handoff System

#### 4.4.1 Functionality
- Define handoff decision points for bots
- Capture context and reason for handoffs
- Route handoffs to appropriate agents
- Monitor handoff queue and performance
- Provide rich context to agents receiving handoffs

#### 4.4.2 Technical Specifications
- Implement dedicated `/botHandoff` structure in conversations
- Configure handoff triggers in system settings
- Create specialized routing for handoffs
- Build monitoring system for handoff queue
- Develop analytics for bot containment and handoff rates

#### 4.4.3 Acceptance Criteria
- Bot correctly identifies situations requiring human intervention
- Handoffs capture context that helps human agents
- Customers receive appropriate messaging during handoff
- Agents receive clear indication of bot handoffs with context
- Handoffs are routed to agents with appropriate skills
- Metrics track bot containment rate and handoff performance

#### 4.4.4 Handoff Decision Points
- Explicit user requests for human agent
- Low confidence in bot responses
- Multiple consecutive low-confidence responses
- Conversation complexity exceeding thresholds
- Conversation length exceeding maximum turns
- Restricted topics requiring human judgment

#### 4.4.5 Handoff Metadata
- Handoff status tracking
- Reason for handoff
- Bot confidence levels
- Suggested agent skills
- Conversation summary
- Detected entities and context
- Customer sentiment
- Time-to-handoff metrics

### 4.5 Session Management and Conversation Creation

#### 4.5.1 Functionality
- Determine when to create new conversations vs. continue existing ones
- Associate incoming messages with the correct conversations
- Support programmatic creation of new conversations
- Configure session timeout rules by channel

#### 4.5.2 Technical Specifications
- Implement message processor to handle incoming messages
- Create session manager to track conversation continuity
- Build API for on-demand conversation creation
- Configure timeout rules in system settings
- Develop routing logic for new conversations

#### 4.5.3 Acceptance Criteria
- System correctly associates messages with existing conversations
- New conversations are created when appropriate
- Session timeouts correctly applied by channel
- On-demand conversations are created successfully
- New conversations are routed to appropriate queues
- Customer context is preserved across related conversations

#### 4.5.4 Message Association Logic
- Match incoming messages to customer and channel
- Check for active conversations within timeout window
- Apply channel-specific timeout rules
- Create new conversation when no active match is found
- Link related conversations for context

#### 4.5.5 On-Demand Conversation API
- Support creation with specified routing
- Allow setting initial message content
- Enable priority specification
- Support custom metadata fields
- Provide targeting to specific teams or agents

### 4.6 Agent Automation

#### 4.6.1 Functionality
- Monitor agent response times
- Detect overloaded agents
- Identify inactive/away agents with open conversations
- Automatically transfer conversations when needed
- Provide notification system for agents

#### 4.6.2 Technical Specifications
- Track agent activity and response times
- Monitor agent workload against configured thresholds
- Implement agent inactivity detection
- Create transfer workflow for affected conversations
- Build notification system for agents

#### 4.6.3 Acceptance Criteria
- System identifies agents not responding within threshold time
- Overloaded agents have excess conversations redistributed
- Inactive agents have conversations transferred
- Agents receive notifications about potential issues
- Supervisors receive alerts about systematic problems

### 4.7 Additional Automations (Future Phases)

#### 4.7.1 Business Hours Management
- Configure working hours by team/department
- Set up holiday calendar
- Automated responses outside business hours
- Queue management for after-hours messages

#### 4.7.2 SLA Monitoring
- Define SLA targets by conversation type
- Monitor response and resolution times
- Trigger notifications for approaching breaches
- Automatic escalation for critical breaches

#### 4.7.3 Conversation Categorization
- Automatically categorize conversations based on content
- Route to appropriate teams based on category
- Set initial priority based on content analysis
- Track category distribution for reporting

#### 4.7.4 Proactive Notifications
- Trigger outbound messages based on system events
- Manage templates for different notification types
- Support multiple channels for notifications
- Track delivery and response rates

#### 4.7.5 Sentiment Analysis
- Analyze customer message sentiment
- Escalate negative interactions
- Prioritize potentially problematic conversations
- Track sentiment trends over time

#### 4.7.6 Customer Recognition
- Identify returning customers
- Surface relevant previous interactions
- Customize greeting for VIP customers
- Apply special routing rules for priority customers

#### 4.7.7 Response Suggestions
- Analyze customer queries for intent
- Suggest relevant canned responses to agents
- Enable one-click response insertion
- Improve suggestions based on usage

#### 4.7.8 Knowledge Base Integration
- Suggest relevant knowledge articles to agents
- Allow easy sharing of articles with customers
- Track which articles resolve which issues
- Identify knowledge gaps based on unresolved queries

#### 4.7.9 Follow-up Automation
- Schedule follow-up messages after resolution
- Re-engage customers after abandoned conversations
- Gather resolution satisfaction data
- Create tickets for unresolved issues

#### 4.7.10 Conversation Summarization
- Generate summaries of completed conversations
- Extract key points and action items
- Tag conversations with relevant categories
- Integrate summaries with ticketing systems

## 5. Implementation Priorities

### 5.1 Phase 1: Core Platform
1. Firebase Realtime Database setup
2. Basic conversation flow
3. Agent interface for messaging
4. Simple queue management
5. Essential tools integration
6. Session management implementation

### 5.2 Phase 2: Transfer System & Bot Handoff
1. Manual transfer between agents
2. Team-based transfers
3. Bot-to-human handoff system
4. Transfer acceptance workflow
5. Transfer history tracking
6. Basic automatic transfers

### 5.3 Phase 3: Automation Framework
1. Customer inactivity automation
2. Agent inactivity monitoring
3. Configuration management
4. Business hours handling
5. SLA monitoring foundation

### 5.4 Phase 4: Advanced Features
1. Sentiment analysis
2. Response suggestions
3. Knowledge base integration
4. Proactive notifications
5. Conversation summarization

## 6. Success Metrics

### 6.1 Performance Metrics
- Average response time (target: < 2 minutes)
- Average resolution time (target: < 30 minutes)
- Concurrent conversations per agent (target: 3-5)
- System latency for message delivery (target: < 500ms)
- Bot containment rate (target: > 70%)
- Time-to-handoff (target: < 30 seconds)

### 6.2 Efficiency Metrics
- Percentage of conversations handled by automation (target: 25%)
- Average handling time reduction due to tools (target: 30%)
- Transfer success rate (target: > 95%)
- Automation rule effectiveness (measured by success/failure rates)
- Bot handoff success rate (target: > 98%)

### 6.3 Customer Experience Metrics
- Customer satisfaction score (target: > 4.5/5)
- First contact resolution rate (target: > 80%)
- Abandoned conversation rate (target: < 5%)
- Channel switching frequency (target: < 10%)
- Post-handoff satisfaction (target: > 4.3/5)

## 7. Technical Considerations

### 7.1 Security
- Implement proper Firebase security rules
- Ensure PII handling follows regulations
- Encrypt sensitive data at rest and in transit
- Implement proper authentication and authorization

### 7.2 Performance
- Optimize Firebase queries with proper indexing
- Implement caching strategies for frequently accessed data
- Consider sharding for high-volume systems
- Monitor RTDB size and transaction volume

### 7.3 Scalability
- Design for horizontal scaling with Firebase
- Plan for conversation volume growth
- Monitor Firebase quotas and limits
- Implement load testing for peak scenarios

### 7.4 Maintenance
- Design monitoring and alerting for system health
- Create backup and restore procedures
- Implement logging for debugging and auditing
- Plan for version updates and migrations

## 8. Appendix

### 8.1 Firebase Data Structure Examples

#### Conversation Object
```javascript
{
  "metadata": {
    "customerId": "customer123",
    "customerName": "Jane Doe",
    "channel": "whatsapp",
    "status": "active",
    "startTime": *************,
    "lastUpdateTime": *************,
    "lastCustomerMessageTime": *************,
    "category": "billing",
    "sessionTimeout": ******** // 24 hours
  },
  "assignment": {
    "assignedAgentId": "agent456",
    "assignmentTime": 1678901234590,
    "previousAgentId": null
  },
  "messages": {
    "msg1": {
      "text": "Hello, I have a question about my bill",
      "senderId": "customer123",
      "senderType": "customer",
      "timestamp": *************,
      "status": "read"
    },
    "msg2": {
      "text": "Hi Jane, I'd be happy to help with your billing question.",
      "senderId": "agent456",
      "senderType": "agent",
      "timestamp": *************,
      "status": "read"
    }
  },
  "transfer": {
    "status": "none",
    "requestedAt": null,
    "requestedBy": null,
    "requestedTo": null,
    "reason": null,
    "previousAgents": []
  },
  "botHandoff": {
    "status": "none",
    "requestedAt": null,
    "reason": null,
    "botConfidence": null,
    "suggestedSkills": [],
    "conversationSummary": null,
    "customerSentiment": null
  },
  "automationState": {
    "inactivity": {
      "lastCustomerMessageTime": *************,
      "lastAgentMessageTime": *************,
      "stage": "none"
    }
  },
  "routingInfo": {
    "initialQueue": "billing",
    "currentQueue": "billing",
    "queueEntryTime": *************,
    "routingHistory": [
      {
        "queue": "general",
        "enteredAt": *************,
        "exitedAt": *************
      },
      {
        "queue": "billing",
        "enteredAt": *************
      }
    ]
  }
}
```

#### Bot Handoff Configuration Example
```javascript
{
  "triggers": {
    "explicitRequest": true,
    "lowConfidence": true,
    "confidenceThreshold": 0.4,
    "maxConsecutiveLowConfidence": 2,
    "escalationKeywords": ["supervisor", "human", "agent", "person", "manager"],
    "complexityThreshold": 0.7,
    "maxTurnsBeforeHandoff": 10,
    "restrictedTopics": ["refunds", "cancellations", "compliance", "complaints"]
  },
  "messages": {
    "handoffInitiated": "I'll connect you with a human agent who can better assist with your question. Please wait a moment.",
    "longWait": "Thank you for your patience. Our agents are still assisting other customers. You'll be connected as soon as possible.",
    "handoffCompleted": "You're now connected with {agentName} who will continue to assist you."
  },
  "queueing": {
    "defaultPriority": 2,
    "priorityBoost": 1,
    "defaultQueue": "general",
    "specializedQueues": {
      "billing": "billing_specialists",
      "technical": "tech_support",
      "complaints": "customer_retention"
    }
  },
  "escalation": {
    "warningThreshold": 5,
    "escalationThreshold": 10,
    "criticalThreshold": 15,
    "notifyEmail": "<EMAIL>"
  }
}
```

#### Session Management Configuration Example
```javascript
{
  "defaultSessionTimeout": ********, // 24 hours
  "channelTimeouts": {
    "whatsapp": ********, // 24 hours
    "webchat": 3600000, // 1 hour
    "email": *********, // 7 days
    "sms": 43200000 // 12 hours
  },
  "preserveContextAcrossSessions": true,
  "maxSessionsToLink": 5,
  "sessionLinkingWindow": 2592000000 // 30 days
}
```

#### Tools Registry
```javascript
{
  "customer-lookup": {
    "name": "Customer Lookup",
    "description": "Retrieve customer information from CRM",
    "endpoint": "https://us-central1-your-project.cloudfunctions.net/customerLookup",
    "requiredParams": ["customerId", "fields"],
    "agentAccess": true,
    "botAccess": true,
    "category": "customer",
    "uiComponent": "customer-lookup-form"
  },
  "payment-processing": {
    "name": "Process Payment",
    "description": "Process a payment transaction",
    "endpoint": "https://us-central1-your-project.cloudfunctions.net/processPayment",
    "requiredParams": ["customerId", "amount", "method"],
    "agentAccess": true,
    "botAccess": false,
    "category": "billing",
    "uiComponent": "payment-form"
  }
}
```

### 8.2 API Endpoints

#### Session and Conversation Management API
- `POST /api/conversations` - Create a new conversation
- `GET /api/conversations/{customerId}/active` - Get active conversations for a customer
- `POST /api/conversations/{conversationId}/message` - Send a message to a conversation

#### Chat Transcript API
- `GET /api/transcripts/{conversationId}` - Retrieve conversation transcript
- `POST /api/transcripts/{conversationId}/share` - Share transcript with customer

#### Transfer API
- `POST /api/conversations/{conversationId}/transfer` - Initiate transfer
- `POST /api/transfers/{transferId}/accept` - Accept transfer
- `POST /api/transfers/{transferId}/reject` - Reject transfer

#### Bot Handoff API
- `POST /api/conversations/{conversationId}/handoff` - Initiate bot-to-human handoff
- `GET /api/handoffs/pending` - Get pending handoffs
- `POST /api/handoffs/{handoffId}/accept` - Accept handoff
- `GET /api/analytics/handoffs` - Get handoff analytics

#### Automation API
- `GET /api/config/automations` - Get automation configurations
- `PUT /api/config/automations/{automationType}` - Update automation settings
- `POST /api/automations/{automationType}/run` - Manually trigger automation
- `POST /api/automations/{automationType}/disable` - Temporarily disable automation

### 8.3 UI Mockups

UI mockups will be provided as separate attachments, focusing on:

1. Agent Chat Interface
2. Transfer Dialog
3. Bot Handoff Interface
4. Automation Configuration Panel
5. Tools Integration Panel
6. Transcript Viewer
7. Session Management Dashboard

---

## Document History

| Version | Date | Author | Description |
|---------|------|--------|-------------|
| 0.1 | 2025-04-10 | Initial Team | Initial draft |
| 1.0 | 2025-04-19 | Product Team | First complete version |
| 2.0 | 2025-04-19 | Product Team | Added Session Management and Bot Handoff features |

---

*End of Document*