# SOLUCIÓN: Preservar Datos Originales de Webhook Twilio

## 🔍 Problema Identificado

Los datos originales del webhook de Twilio se pierden en el flujo:

```
WhatsApp → Twilio → Twilio Service → Channel Router → ... → Chat Realtime
                      ❌ AQUÍ SE PIERDE    (solo mensaje procesado)
```

## ✅ Solución Propuesta

### 1. Modificar Twilio Service
Debe enviar **TANTO** el mensaje procesado **COMO** los datos originales del webhook:

```typescript
// Twilio Service → Channel Router
{
  message: IncomingMessage,           // Mensaje procesado (actual)
  originalWebhookData: {              // ✅ NUEVO: Datos originales
    // Datos directos de Twilio webhook
    MessageSid: "SMxxxxxxx",
    WaId: "5215551234567", 
    From: "whatsapp:+5215551234567",
    To: "whatsapp:+12345678901",
    Body: "mi tarjeta no funciona",
    // O datos de Conversations API
    ConversationSid: "CHxxxxxxx",
    ParticipantSid: "MBxxxxxxx"
  }
}
```

### 2. Modificar Channel Router Types
Actualizar `RouteMessageRequest` para incluir webhook data:

```typescript
export interface RouteMessageRequest {
  message: IncomingMessage;
  originalWebhookData?: WebhookData;  // ✅ NUEVO
  conversationId?: string;
}
```

### 3. Flujo a través de PubSub
Los datos deben fluir por todo el sistema:

```
Channel Router → PubSub → Bot Human Router → Chat Realtime
```

Cada servicio debe preservar `originalWebhookData` en el mensaje PubSub.

### 4. Chat Realtime Process Message
Usar `originalWebhookData` para generar conversation ID:

```typescript
async processMessage(messageData: any) {
  const webhookData = messageData.originalWebhookData || {};
  const conversationId = this.generateConversationId(webhookData);
  // ... resto del procesamiento
}
```

## 🚀 Implementación

### Paso 1: Actualizar tipos universales
- Crear interface `WebhookData` compartida
- Actualizar todos los tipos de mensaje

### Paso 2: Modificar Twilio Service
- Preservar datos originales del webhook
- Enviar ambos: mensaje procesado + webhook original

### Paso 3: Actualizar Channel Router
- Recibir y preservar `originalWebhookData`
- Enviar a PubSub con datos completos

### Paso 4: Actualizar Bot Human Router  
- Preservar `originalWebhookData` en llamadas a Chat Realtime

### Paso 5: Testing
- Verificar que datos fluyan end-to-end
- Testear generación correcta de conversation IDs

## 📊 Beneficios

1. **Conversation IDs correctos** basados en fuente original
2. **Compatibilidad futura** con múltiples proveedores (Twilio, WhatsApp directo)
3. **Debugging mejorado** con datos completos del webhook
4. **Flexibilidad** para cambios de integración sin afectar ID generation

## 🔧 Scripts de Migración

Una vez implementado:
1. Usar `migration_conversation_ids.js` para conversaciones existentes
2. Las nuevas conversaciones usarán automáticamente el nuevo sistema