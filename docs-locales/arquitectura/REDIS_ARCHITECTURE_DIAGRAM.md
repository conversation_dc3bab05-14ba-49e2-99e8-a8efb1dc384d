# 🔧 ARQUITECTURA REDIS - Estado de Conversaciones Compartido

## 🎯 **PROBLEMA RESUELTO:**
- **<PERSON><PERSON><PERSON>les instancias** de servicios necesitaban estado compartido
- **Cambios dinámicos** en asignación (N8N → Human) perdían sincronización
- **Cache in-memory** causaba inconsistencias críticas en customer service

## 🏗️ **NUEVA ARQUITECTURA CON REDIS:**

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              ESTADO COMPARTIDO EN REDIS                            │
└─────────────────────────────────────────────────────────────────────────────────────┘

                    Redis Container (Port 6379)
                   ┌─────────────────────────────┐
                   │  Key-Value Store            │
                   │  ─────────────────────      │
                   │  conversation:CHxxx123      │
                   │  {                          │
                   │    conversationId: "CH..."  │
                   │    assignedTo: "n8n"        │
                   │    assignedAt: 1693234567   │
                   │    department: "support"    │
                   │    lastUpdated: 1693234890  │
                   │    updatedBy: "chat-real."  │
                   │  }                          │
                   │                             │
                   │  TTL: 2 hours               │
                   └─────────────────────────────┘
                              ↑     ↓
                       ┌──────┴──┐ ┌─┴──────────┐
                       │  READ   │ │   WRITE    │
                       └──────┬──┘ └─┬──────────┘
                              │      │
    ┌─────────────────────────┼──────┼─────────────────────────┐
    │                         │      │                         │
    ▼                         ▼      ▼                         ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Bot Human Router│    │  Chat Realtime  │    │ Session Manager │
│                 │    │                 │    │                 │
│ READS state     │    │ WRITES state    │    │ Uses separate   │
│ for routing     │    │ on transfer     │    │ Redis DB        │
│ decisions       │    │ events          │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
    Port 3004               Port 3003              Port 3001
```

---

## 🔄 **FLUJO COMPLETO: N8N → Transfer → Human Routing**

### **PASO 1: Conversación Inicial → N8N**
```
1. Customer message → WhatsApp → Channel Router → Bot Human Router

2. Bot Human Router checks Redis:
   Key: conversation:CHxxx123
   Value: NOT FOUND (first message)

3. Decision Engine: Try N8N first
   N8N.canHandle(message) → YES

4. Bot Human Router WRITES to Redis:
   conversation:CHxxx123 = {
     assignedTo: "n8n",
     updatedBy: "bot-human-router"
   }

5. Route message to N8N ✅
```

### **PASO 2: Próximos Mensajes → N8N Passthrough**
```
6. Next customer messages → Bot Human Router

7. Bot Human Router READS from Redis:
   conversation:CHxxx123 → assignedTo: "n8n"

8. Fast passthrough to N8N (no decision needed) ⚡
```

### **PASO 3: N8N Ejecuta Transfer**
```
9. N8N Workflow Logic: "This needs human attention"

10. N8N → HTTP Request → Chat Realtime:
    POST /api/conversations/CHxxx123/transfer
    { targetAgentId: "agent-456", reason: "complex_query" }

11. Chat Realtime:
    - Updates Firebase (assignedAgent, transferInfo)
    - 🆕 WRITES to Redis:
      conversation:CHxxx123 = {
        assignedTo: "human",
        updatedBy: "chat-realtime"
      }

12. Transfer completed ✅
```

### **PASO 4: Post-Transfer Messages → Human Routing**
```
13. Next customer message → Bot Human Router

14. Bot Human Router READS from Redis:
    conversation:CHxxx123 → assignedTo: "human" ✅

15. Route message to Chat Realtime → Human Agent ✅
```

---

## 🔧 **COMPONENTES TÉCNICOS:**

### **RedisService (Shared)**
```typescript
export class RedisService {
  // Singleton pattern - shared instance
  static getInstance(): RedisService

  // Core operations
  async setObject(key: string, obj: any, ttl?: number): Promise<void>
  async getObject<T>(key: string): Promise<T | null>
  
  // Health monitoring
  async healthCheck(): Promise<{ connected: boolean; ping: boolean }>
}
```

### **ConversationStateService (Bot Human Router)**
```typescript
export class ConversationStateService {
  private redisService: RedisService;
  private readonly KEY_PREFIX = 'conversation:';
  
  // Always fresh from Redis
  async getConversationState(conversationId: string): Promise<ConversationState>
  
  // Persist to Redis with TTL
  async setConversationState(
    conversationId: string, 
    assignedTo: 'n8n' | 'human',
    department?: string,
    updatedBy: string = 'bot-human-router'
  ): Promise<void>
}
```

### **Chat Realtime Integration**
```typescript
// In transferConversation()
private async updateConversationStateInRedis(
  conversationId: string, 
  assignedTo: 'n8n' | 'human',
  department?: string
): Promise<void> {
  // Update Redis immediately after Firebase transfer
  const state = {
    conversationId,
    assignedTo,
    updatedBy: 'chat-realtime',
    lastUpdated: Date.now()
  };
  
  await this.redisService.setObject(`conversation:${conversationId}`, state, TTL);
}
```

---

## 📊 **REDIS MANAGEMENT SCRIPT:**

```bash
# Start Redis Docker container
./scripts/manage-redis.sh start

# Check Redis status & data
./scripts/manage-redis.sh status
./scripts/manage-redis.sh data

# Clean all data (development)
./scripts/manage-redis.sh clean

# Connect to Redis CLI
./scripts/manage-redis.sh cli

# Stop Redis
./scripts/manage-redis.sh stop
```

---

## ✅ **BENEFICIOS DE ESTA ARQUITECTURA:**

### **1. Consistencia Garantizada** 🎯
- ✅ Single source of truth para estados
- ✅ No más cache inconsistencies
- ✅ Cambios dinámicos reflejados inmediatamente

### **2. Performance Óptimo** ⚡
- ✅ Redis sub-millisecond reads
- ✅ No queries a Firebase por cada mensaje
- ✅ TTL automático (cleanup)

### **3. Multi-Instance Ready** 🔄
- ✅ Horizontal scaling preparado  
- ✅ Estado compartido entre replicas
- ✅ Load balancer compatible

### **4. Debuggeable** 🔍
- ✅ CLI access para inspección
- ✅ Logs de quien actualizó qué
- ✅ Health checks integrados

### **5. Resiliente** 🛡️
- ✅ Redis failover no afecta routing
- ✅ Fallback a decisión por defecto
- ✅ Auto-recovery al reconectar

---

## 🚀 **PRÓXIMOS PASOS:**

1. ✅ **Redis Shared State** - Implementado
2. 🔄 **Testing E2E** - Validar flujo N8N → Transfer → Human  
3. 📈 **Production Redis** - Configurar Redis managed service
4. 🔧 **Monitoring** - Métricas de estado por conversación
5. 🔒 **Security** - Redis AUTH en producción

---

**🎯 RESULTADO:** Bot Human Router ahora mantiene consistencia perfecta con cambios dinámicos de estado sin sacrificar performance.