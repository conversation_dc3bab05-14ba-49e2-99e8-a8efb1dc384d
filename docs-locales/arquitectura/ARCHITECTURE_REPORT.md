# Sistema CX - Reporte de Arquitectura Completa

## 📋 Resumen Ejecutivo

Sistema de Customer Experience (CX) basado en microservicios para Cloud Run, enfocado en gestión de conversaciones multicanal con énfasis en WhatsApp, automatización inteligente y analytics avanzados.

## 🏗️ Arquitectura General

### Servicios Core (7 servicios)

```
services/
├── twilio/              # WhatsApp integration via Twilio
├── channel-router/      # Message routing & conversation lifecycle  
├── session-manager/     # Session state management (Redis)
├── bot-human-router/    # AI/Human routing with n8n integration
├── chat-realtime/       # Firebase Realtime Database interface
├── chat-ui/            # Next.js frontend with offline resilience
└── analytics/          # Metrics collection & KPI reporting
```

### Flujo de Mensajes

```
WhatsApp Message
    ↓
Twilio Webhook → Channel Router → AI Department Analysis (n8n)
    ↓                ↓                    ↓
Session Manager → Department Assignment → PubSub Inbound
    ↓ (Redis)           ↓                    ↓
    ↓                   ↓               Bot Human Router
    ↓                   ↓                    ↓
    ↓                   ↓            Decision Engine:
    ↓                   ↓            ├── Bot Path: n8n Webhook  
    ↓                   ↓            └── Human Path: Continue
    ↓                   ↓                    ↓
    └───────────────────┴──────→ Chat Realtime ↔ Firebase Realtime Database
                                       ↓
                               Chat UI (Next.js) + Analytics (Supabase)
```

## 🔧 Stack Tecnológico

### Core Technologies
- **TypeScript**: 100% del código backend y frontend
- **Next.js**: Chat UI framework
- **Node.js**: Runtime para todos los servicios backend
- **Docker**: Containerización para Cloud Run

### Servicios Externos

#### Desarrollo Local
- **Firebase Realtime Database**: Emulador (datos operacionales en tiempo real)
- **Redis**: Docker container (gestión de sesiones)
- **Supabase**: Instancia dev compartida (PostgreSQL para analytics y configuración)
- **Google Cloud PubSub**: Emulador (comunicación entre servicios)
- **n8n**: Docker container (workflows de bots)
- **Twilio**: Cuenta dev + ngrok para webhooks

#### Producción
- **Firebase Realtime Database**: Instancia productiva (datos operacionales)
- **Redis**: Managed service o Cloud Memorystore (sesiones)
- **Supabase**: Instancia productiva (PostgreSQL analytics/config)
- **Google Cloud PubSub**: Push Subscriptions (comunicación)
- **n8n**: Instancia cloud (bot workflows)
- **Twilio**: Cuenta productiva con WhatsApp Business API

## 📊 Funcionalidades por Bloque

### Bloque 1: Gestión de Conversaciones (Core Platform)
- Aceptar/Rechazar conversaciones
- Transferencias entre agentes y departamentos
- Escalación a supervisores
- Pausa/Reanudación de conversaciones
- Finalización con métricas
- Sistema de notas internas
- Gestión de transferencias

### Bloque 5: Analytics y Reportes
- **Métricas en Tiempo Real**:
  - Conversaciones activas
  - Agentes disponibles/ocupados
  - Cola de espera actual
- **Reportes y KPIs**:
  - Performance de agentes
  - Métricas de conversaciones
  - Reportes de transferencias
- **Storage**: Eventos en Supabase con Edge Functions

### Bloque 6: Administración y Configuración
- Gestión de usuarios y roles
- Configuración del sistema
- Parámetros operacionales

## 🗄️ Arquitectura de Base de Datos

### Estrategia Híbrida de Datos

El sistema utiliza una **arquitectura híbrida** que separa datos operacionales en tiempo real de analytics y configuración:

#### Firebase Realtime Database (Operaciones en Tiempo Real)
```javascript
// Estructura operacional
/conversations/{conversationId}/
├── metadata (customer, channel, status, timestamps)
├── messages/{messageId} (content, sender, timestamp, status)
├── assignment (agentId, assignmentTime, previousAgents)
├── transfer (status, reason, targetAgent, history)
├── indicators (typing, readStatus, lastActivity)
├── botHandoff (status, reason, confidence, context)
├── departmentRouting (
│   ├── assignedDepartment: "technical_support"
│   ├── aiAnalysisAttempts: 2
│   ├── aiAnalysisHistory: [...attempts]
│   └── departmentAssignedAt: timestamp
│   )
└── routingInfo (queue, priority, escalation)

/agents/{agentId}/
├── status (online, away, busy, offline)
├── activeConversations/{conversationId} (startTime, priority)
├── capacity (current, maximum, available)
└── session (loginTime, lastActivity, location)

/queues/{queueId}/
├── waitingConversations/{conversationId} (entryTime, priority)
├── agents/{agentId} (availability, skills, workload)
└── metrics (waitTime, queueLength, throughput)
```

**Responsabilidades Firebase**:
- Estados de conversación activos
- Mensajes en tiempo real
- Estados de agentes (online/offline)
- Gestión de colas de conversaciones
- Transferencias en progreso
- Indicadores de escritura/lectura
- Sesiones activas

#### Supabase PostgreSQL (Analytics y Configuración)
```sql
-- Tablas principales de configuración
organizations (id, name, settings, created_at)
agents (id, email, role, skills, max_concurrent_chats)
customers (id, external_id, metadata, tags)
channels (id, name, configuration, is_active)
customer_identities (customer_id, channel_id, channel_user_id)
departments (id, name, description, is_active, routing_keywords)
agent_departments (agent_id, department_id, is_primary, skill_level)

-- Knowledge management
knowledge_articles (id, title, content, tags, vector_embedding)
response_templates (id, name, content, category, shortcut)
bot_configurations (id, provider, configuration, fallback_settings)

-- Analytics y auditoría  
analytics_events (id, event_type, event_data, timestamp) -- Partitioned by date
audit_logs (id, entity_type, action, actor_id, changes, created_at)
integrations (id, type, configuration, sync_status)

-- Índices críticos para performance
CREATE INDEX idx_analytics_events_org_type_timestamp 
  ON analytics_events(organization_id, event_type, timestamp);
CREATE INDEX idx_messages_content_fts 
  ON messages USING gin(to_tsvector('english', content));
```

**Responsabilidades Supabase**:
- Configuración de agentes, canales y departamentos
- Relaciones agente-departamento (multi-departamento support)
- Perfiles de clientes y metadata
- Base de conocimiento y respuestas predefinidas
- Eventos de analytics (particionados por fecha)  
- Logs de auditoría (incluyendo asignaciones de departamento)
- Configuraciones de integraciones
- Datos históricos para reportes

### Flujo de Datos por Caso de Uso

#### Mensaje Entrante con Asignación de Departamento (WhatsApp → Sistema)
```
1. Twilio webhook → channel-router
2. channel-router → detecta nueva conversación
3. channel-router → n8n webhook (AI department analysis)
4. AI analysis loop (máximo 3 intentos):
   ├── Mensaje claro → departamento asignado
   ├── Mensaje unclear → solicita clarificación  
   └── 3 intentos fallidos → departamento "general"
5. channel-router → session-manager (Redis + department info)
6. session-manager → Firebase (conversation state + department)
7. message → Firebase (/conversations/{id}/messages)
8. analytics event → Supabase (department_assignment_event)
```

#### Transferencia Entre Agentes
```
1. Agent action → chat-ui → chat-realtime
2. chat-realtime → Firebase (transfer state)
3. PubSub event → bot-human-router (assignment logic)
4. Firebase update → all connected UIs (real-time)
5. Analytics event → Supabase (transfer metrics)
```

#### Dashboard de Analytics  
```
1. analytics service → Supabase (query historical events)
2. Real-time metrics → Firebase (active conversations)
3. Combined data → analytics UI
```

## 🔄 Patrones de Comunicación

### PubSub Architecture (Cloud Run Compatible)
```typescript
// Push Subscription Pattern
@app.post("/pubsub-endpoint")
async function handleMessage(request: Request) {
  const envelope = request.json();
  const message = base64.decode(envelope.message.data);
  
  // Process message
  await processInboundMessage(message);
  
  return new Response("", { status: 204 }); // ACK
}
```

### Session Management (Redis)
```typescript
// Session lifecycle
SessionManager.createSession(userId, conversationId);
SessionManager.updateSession(sessionId, data);
SessionManager.recycleSession(sessionId); // On conversation close
```

### Firebase Integration
```typescript
// Chat UI - Direct listeners for real-time
useFirebaseListener("/conversations/:id/messages");

// Chat UI - API calls for CRUD
chatRealtimeService.getConversations();
chatRealtimeService.createConversation(data);
```

## 🛡️ Resilencia y Offline Support

### Chat UI Offline Strategy
```typescript
// Cache local inteligente
LocalStorage:
├── conversations_cache     // Conversaciones activas
├── messages_cache_{convId} // Mensajes por conversación  
├── agents_status_cache     // Estados de agentes
├── connection_state        // Estado de conexión Firebase
└── pending_actions_queue   // Acciones pendientes de envío

// Debug tools (CRÍTICO - implementar desde día 1)
window.cxDebug = {
  clearCache(),           // Limpiar cache
  showCacheState(),       // Ver estado del cache
  forceSync(),           // Forzar sincronización
  simulateOffline(),     // Simular desconexión
  showPendingQueue(),    // Ver acciones pendientes
  resetConnection()      // Reset conexión Firebase
}
```

### Estados de Conexión
- **Verde**: Conectado + sincronizado
- **Amarillo**: Modo cache (desconectado)
- **Rojo**: Error crítico
- **Spinner**: Sincronizando

## 📈 Analytics & Eventos

### Eventos Críticos a Capturar
```typescript
// Eventos del sistema
ConversationEvents = {
  STARTED: "conversation.started",
  TRANSFERRED: "conversation.transferred",
  ESCALATED: "conversation.escalated", 
  CLOSED: "conversation.closed",
  AGENT_RESPONSE: "agent.response",
  CUSTOMER_MESSAGE: "customer.message"
}

// Flow de eventos
Service → PubSub → Analytics → Supabase Edge Functions → Storage
```

### KPIs Principales
- Tiempo promedio de respuesta
- Tasa de resolución primera llamada
- Satisfacción del cliente (CSAT/NPS)
- Efectividad de transferencias
- Volumen por canal/período
- Performance por agente

## 🚀 Deployment Strategy

### Cloud Run Configuration
```yaml
# Configuración base para cada servicio
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/execution-environment: "gen2"
    spec:
      containerConcurrency: 1000
      timeoutSeconds: 600
      # Service Account por servicio
      serviceAccountName: {service-name}@project.iam.gserviceaccount.com
  scaling:
    minInstances: 1    # Para evitar cold starts
    maxInstances: 100
```

### PubSub Push Subscriptions
```bash
# Configuración para cada servicio
gcloud pubsub subscriptions create {service-name}-subscription \
    --topic=inbound-messages \
    --push-endpoint=https://{service-name}.run.app/pubsub \
    --push-auth-service-account={service-name}-<EMAIL>
```

## 🔒 Seguridad

### Autenticación
- **Local**: Sin autenticación entre servicios
- **Cloud Run**: Service Accounts con roles específicos
- **Firebase**: Rules para acceso por rol de usuario
- **Supabase**: RLS policies por usuario/rol

### Datos Sensibles
- Encriptación en tránsito (HTTPS/TLS)
- Encriptación en reposo (Firebase/Supabase)
- No almacenar tokens en código
- Uso de Secret Manager para credenciales

## ⚠️ Restricciones y Decisiones

### NO Implementar (Explícitamente excluido)
- ❌ Sistema de auxiliares (breaks, lunch, etc.)
- ❌ Exportación de transcripts (para después)
- ❌ Autenticación local entre servicios
- ❌ Funcionalidades post-MVP hasta completar core

### Principios MVP
- **Simple y legible**: No código complejo
- **100% Testable**: Diseño para testing fácil
- **Enfoque quirúrgico**: Solo lo solicitado
- **TypeScript obligatorio**: Type safety en todo
- **Do what has been asked; nothing more, nothing less**

## 🎯 Next Steps

### Implementación Sugerida (Orden)
1. **session-manager** (Redis + básico)
2. **chat-realtime** (Firebase interface)
3. **channel-router** (PubSub + session integration + AI department routing)
4. **twilio** (Webhook integration)
5. **bot-human-router** (n8n routing + department-aware assignment)
6. **analytics** (Event collection + department metrics)
7. **chat-ui** (Next.js + offline support + department visibility)

### Criterios de Éxito MVP
- ✅ WhatsApp → Sistema → Agente flow funcional
- ✅ AI department assignment funcionando (con mock n8n endpoint)
- ✅ Asignación de agentes por departamento
- ✅ Sesiones persistentes en Redis
- ✅ Mensajes en tiempo real con Firebase
- ✅ Configuración de agentes/canales/departamentos en Supabase
- ✅ Analytics capturando eventos de department assignment
- ✅ Cache offline funcional con debug tools
- ✅ Deploy exitoso en Cloud Run con base de datos híbrida

### Validación de Arquitectura de Datos
- ✅ Firebase maneja < 100ms latencia para mensajes
- ✅ Supabase analytics procesa > 10K events/día  
- ✅ Configuración de agentes disponible con < 200ms
- ✅ Métricas históricas consultables en < 2s
- ✅ Resiliencia: Firebase offline no afecta analytics
- ✅ Escalabilidad: Particionamiento analytics_events funcional

---

**Fecha**: Enero 2025  
**Versión**: 1.0 - Arquitectura MVP  
**Estado**: Documentación completa - Listo para implementación