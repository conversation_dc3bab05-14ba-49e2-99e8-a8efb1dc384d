<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CX Supervisor Mobile Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }
        
        /* Mobile Layout */
        .mobile-container {
            width: 375px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        /* Mobile Header */
        .mobile-header {
            background: #1e40af;
            color: white;
            padding: 12px 16px;
            display: flex;
            justify-content: between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .mobile-header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .hamburger {
            width: 24px;
            height: 24px;
            cursor: pointer;
        }
        
        .header-title {
            font-weight: 600;
            font-size: 16px;
        }
        
        .mobile-header-right {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .notification-dot {
            width: 24px;
            height: 24px;
            background: #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        /* Quick Stats Bar */
        .quick-stats {
            display: flex;
            background: white;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .quick-stat {
            flex: 1;
            text-align: center;
            padding: 16px 8px;
            border-right: 1px solid #e2e8f0;
        }
        
        .quick-stat:last-child {
            border-right: none;
        }
        
        .quick-stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #1e293b;
        }
        
        .quick-stat-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 4px;
        }
        
        .quick-stat.urgent .quick-stat-number {
            color: #ef4444;
        }
        
        .quick-stat.success .quick-stat-number {
            color: #10b981;
        }
        
        .quick-stat.warning .quick-stat-number {
            color: #f59e0b;
        }
        
        /* Action Buttons */
        .action-buttons {
            padding: 16px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            background: white;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .action-btn {
            padding: 16px;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }
        
        .action-btn-primary {
            background: #ef4444;
            color: white;
        }
        
        .action-btn-secondary {
            background: #f1f5f9;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
        
        .action-btn:active {
            transform: scale(0.98);
        }
        
        .action-icon {
            font-size: 24px;
        }
        
        .action-label {
            font-size: 14px;
        }
        
        /* Tabs */
        .tabs {
            display: flex;
            background: white;
            border-bottom: 1px solid #e2e8f0;
            overflow-x: auto;
        }
        
        .tab {
            padding: 16px 20px;
            font-weight: 500;
            color: #64748b;
            cursor: pointer;
            white-space: nowrap;
            border-bottom: 2px solid transparent;
            transition: all 0.2s;
        }
        
        .tab.active {
            color: #1e40af;
            border-bottom-color: #1e40af;
        }
        
        .tab-badge {
            background: #ef4444;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: 6px;
        }
        
        /* Content Area */
        .content-area {
            padding: 16px;
            background: #f8fafc;
            min-height: 60vh;
        }
        
        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            margin-bottom: 16px;
            overflow: hidden;
        }
        
        .card-header {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-weight: 600;
            font-size: 16px;
            color: #1e293b;
        }
        
        .card-action {
            font-size: 12px;
            color: #3b82f6;
            font-weight: 500;
        }
        
        /* Agent List */
        .agent-item {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .agent-item:last-child {
            border-bottom: none;
        }
        
        .agent-avatar {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
        }
        
        .agent-info {
            flex: 1;
        }
        
        .agent-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .agent-status {
            font-size: 14px;
            color: #64748b;
        }
        
        .agent-actions {
            display: flex;
            flex-direction: column;
            gap: 4px;
            align-items: flex-end;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 4px;
        }
        
        .status-available {
            background: #10b981;
        }
        
        .status-busy {
            background: #f59e0b;
        }
        
        .status-away {
            background: #ef4444;
        }
        
        .workload-text {
            font-size: 12px;
            color: #64748b;
        }
        
        .quick-action {
            background: #f1f5f9;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            margin: 2px 0;
        }
        
        .quick-action.urgent {
            background: #fecaca;
            color: #991b1b;
            border-color: #f87171;
        }
        
        .quick-action.primary {
            background: #dbeafe;
            color: #1e40af;
            border-color: #60a5fa;
        }
        
        /* Escalation Items */
        .escalation-item {
            padding: 16px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .escalation-item:last-child {
            border-bottom: none;
        }
        
        .escalation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .escalation-customer {
            font-weight: 600;
            color: #1e293b;
        }
        
        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-urgent {
            background: #fecaca;
            color: #991b1b;
        }
        
        .priority-critical {
            background: #fee2e2;
            color: #7f1d1d;
        }
        
        .escalation-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .escalation-reason {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .escalation-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn-small {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            flex: 1;
            text-align: center;
        }
        
        .btn-resolve {
            background: #10b981;
            color: white;
        }
        
        .btn-view {
            background: #3b82f6;
            color: white;
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
        
        /* Bottom Tab Bar */
        .bottom-tabs {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 375px;
            background: white;
            border-top: 1px solid #e2e8f0;
            display: flex;
            padding: 8px 0;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .bottom-tab {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px;
            color: #64748b;
            cursor: pointer;
            position: relative;
        }
        
        .bottom-tab.active {
            color: #1e40af;
        }
        
        .bottom-tab-icon {
            font-size: 20px;
        }
        
        .bottom-tab-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        .bottom-tab-badge {
            position: absolute;
            top: 2px;
            right: 8px;
            background: #ef4444;
            color: white;
            font-size: 9px;
            padding: 2px 4px;
            border-radius: 8px;
            min-width: 16px;
            text-align: center;
        }
        
        /* Content padding for bottom tabs */
        body {
            padding-bottom: 70px;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Mobile Header -->
        <header class="mobile-header">
            <div class="mobile-header-left">
                <div class="hamburger">☰</div>
                <div class="header-title">Supervisor</div>
            </div>
            <div class="mobile-header-right">
                <div class="notification-dot">3</div>
            </div>
        </header>

        <!-- Quick Stats -->
        <div class="quick-stats">
            <div class="quick-stat urgent">
                <div class="quick-stat-number">2</div>
                <div class="quick-stat-label">Urgentes</div>
            </div>
            <div class="quick-stat success">
                <div class="quick-stat-number">12</div>
                <div class="quick-stat-label">Agentes</div>
            </div>
            <div class="quick-stat warning">
                <div class="quick-stat-number">1</div>
                <div class="quick-stat-label">Pausas</div>
            </div>
            <div class="quick-stat">
                <div class="quick-stat-number">85%</div>
                <div class="quick-stat-label">Disponib.</div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="action-btn action-btn-primary">
                <div class="action-icon">🚨</div>
                <div class="action-label">Escalaciones</div>
            </button>
            <button class="action-btn action-btn-secondary">
                <div class="action-icon">👁️</div>
                <div class="action-label">Intervenir Chat</div>
            </button>
        </div>

        <!-- Tabs -->
        <div class="tabs">
            <div class="tab active">
                Agentes
            </div>
            <div class="tab">
                Escalaciones
                <span class="tab-badge">2</span>
            </div>
            <div class="tab">
                Cola
                <span class="tab-badge">4</span>
            </div>
            <div class="tab">
                Supervisión
                <span class="tab-badge">3</span>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <!-- Agents List -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">Estado de Agentes</div>
                    <div class="card-action">Ver todos</div>
                </div>
                
                <div class="agent-item">
                    <div class="agent-avatar">JP</div>
                    <div class="agent-info">
                        <div class="agent-name">Juan Pérez</div>
                        <div class="agent-status">
                            <span class="status-indicator status-available"></span>
                            Disponible • 3/5 chats
                        </div>
                    </div>
                    <div class="agent-actions">
                        <button class="quick-action primary">👁️ Ver</button>
                        <button class="quick-action">💬 Chat</button>
                    </div>
                </div>

                <div class="agent-item">
                    <div class="agent-avatar" style="background: #f59e0b;">AM</div>
                    <div class="agent-info">
                        <div class="agent-name">Ana Martínez</div>
                        <div class="agent-status">
                            <span class="status-indicator status-busy"></span>
                            Ocupado • 4/5 chats
                        </div>
                    </div>
                    <div class="agent-actions">
                        <button class="quick-action">👁️ Ver</button>
                        <button class="quick-action">💬 Chat</button>
                    </div>
                </div>

                <div class="agent-item">
                    <div class="agent-avatar" style="background: #ef4444;">LG</div>
                    <div class="agent-info">
                        <div class="agent-name">Luis García</div>
                        <div class="agent-status">
                            <span class="status-indicator status-away"></span>
                            Solicita pausa • 5/5 chats
                        </div>
                    </div>
                    <div class="agent-actions">
                        <button class="quick-action urgent">✓ Autorizar</button>
                        <button class="quick-action">💬 Chat</button>
                    </div>
                </div>
            </div>

            <!-- Escalations Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">Escalaciones Urgentes</div>
                    <div class="card-action">Gestionar</div>
                </div>
                
                <div class="escalation-item">
                    <div class="escalation-header">
                        <div class="escalation-customer">María González</div>
                        <div class="priority-badge priority-urgent">URGENTE</div>
                    </div>
                    <div class="escalation-meta">
                        <span>Agente: Juan Pérez</span>
                        <span>Hace 15 min</span>
                    </div>
                    <div class="escalation-reason">
                        Cliente insatisfecho con tiempo de resolución. Solicita hablar con supervisor inmediatamente.
                    </div>
                    <div class="escalation-actions">
                        <button class="btn-small btn-resolve">✓ Resolver</button>
                        <button class="btn-small btn-view">👁️ Ver Chat</button>
                    </div>
                </div>

                <div class="escalation-item">
                    <div class="escalation-header">
                        <div class="escalation-customer">Carlos Ruiz</div>
                        <div class="priority-badge priority-critical">CRÍTICO</div>
                    </div>
                    <div class="escalation-meta">
                        <span>Agente: Ana Martínez</span>
                        <span>Hace 5 min</span>
                    </div>
                    <div class="escalation-reason">
                        Problema técnico severo afectando sistema de pagos del cliente.
                    </div>
                    <div class="escalation-actions">
                        <button class="btn-small btn-resolve">✓ Resolver</button>
                        <button class="btn-small btn-view">👁️ Ver Chat</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Tab Bar -->
        <div class="bottom-tabs">
            <div class="bottom-tab active">
                <div class="bottom-tab-icon">📊</div>
                <div class="bottom-tab-label">Dashboard</div>
            </div>
            <div class="bottom-tab">
                <div class="bottom-tab-icon">🚨</div>
                <div class="bottom-tab-label">Urgentes</div>
                <div class="bottom-tab-badge">2</div>
            </div>
            <div class="bottom-tab">
                <div class="bottom-tab-icon">👥</div>
                <div class="bottom-tab-label">Agentes</div>
            </div>
            <div class="bottom-tab">
                <div class="bottom-tab-icon">👁️</div>
                <div class="bottom-tab-label">Supervisión</div>
                <div class="bottom-tab-badge">3</div>
            </div>
            <div class="bottom-tab">
                <div class="bottom-tab-icon">📈</div>
                <div class="bottom-tab-label">Analytics</div>
            </div>
        </div>
    </div>
</body>
</html>