<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CX Supervisor Dashboard - Wireframe</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
        }
        
        /* Layout Principal */
        .dashboard-container {
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: 60px 1fr;
            height: 100vh;
        }
        
        /* Header */
        .header {
            grid-column: 1 / -1;
            background: white;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 24px;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .logo {
            font-weight: 700;
            font-size: 18px;
            color: #1e40af;
        }
        
        .header-title {
            font-weight: 600;
            color: #64748b;
            font-size: 14px;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .notification-bell {
            position: relative;
            width: 24px;
            height: 24px;
            background: #ef4444;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }
        
        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }
        
        /* Sidebar Navigation */
        .sidebar {
            background: white;
            border-right: 1px solid #e2e8f0;
            padding: 24px 0;
        }
        
        .nav-section {
            margin-bottom: 32px;
        }
        
        .nav-title {
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            color: #94a3b8;
            margin-bottom: 12px;
            padding: 0 24px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 24px;
            color: #64748b;
            text-decoration: none;
            transition: all 0.2s;
            position: relative;
        }
        
        .nav-item:hover {
            background: #f1f5f9;
            color: #1e40af;
        }
        
        .nav-item.active {
            background: #dbeafe;
            color: #1e40af;
            font-weight: 600;
        }
        
        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: #1e40af;
        }
        
        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .badge {
            background: #ef4444;
            color: white;
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: auto;
        }
        
        .badge.warning {
            background: #f59e0b;
        }
        
        .badge.success {
            background: #10b981;
        }
        
        /* Main Content */
        .main-content {
            padding: 24px;
            overflow-y: auto;
        }
        
        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 700;
            color: #1e293b;
        }
        
        .quick-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #1e40af;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
        }
        
        .btn-secondary {
            background: white;
            color: #64748b;
            border: 1px solid #e2e8f0;
        }
        
        .btn-secondary:hover {
            background: #f8fafc;
        }
        
        /* Stats Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .stat-title {
            font-size: 14px;
            font-weight: 500;
            color: #64748b;
        }
        
        .stat-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .stat-icon.red { background: #ef4444; }
        .stat-icon.yellow { background: #f59e0b; }
        .stat-icon.green { background: #10b981; }
        .stat-icon.blue { background: #3b82f6; }
        
        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 8px;
        }
        
        .stat-change {
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .stat-change.positive {
            color: #10b981;
        }
        
        .stat-change.negative {
            color: #ef4444;
        }
        
        /* Content Grid */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 320px;
            gap: 32px;
            margin-bottom: 32px;
        }
        
        /* Tables */
        .data-table {
            background: white;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .table-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 16px;
            font-weight: 600;
            color: #1e293b;
        }
        
        .table-actions {
            display: flex;
            gap: 8px;
        }
        
        .table-content {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .table-row {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
            align-items: center;
            gap: 16px;
        }
        
        .table-row:hover {
            background: #f8fafc;
        }
        
        .agent-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #3b82f6;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 16px;
        }
        
        .agent-details h4 {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
        }
        
        .agent-details p {
            font-size: 14px;
            color: #64748b;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .status-available {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-busy {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-away {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .workload-bar {
            width: 80px;
            height: 8px;
            background: #f1f5f9;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .workload-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 4px;
        }
        
        .workload-fill.high {
            background: #ef4444;
        }
        
        .workload-fill.medium {
            background: #f59e0b;
        }
        
        /* Right Panel */
        .right-panel .data-table {
            margin-bottom: 24px;
        }
        
        .escalation-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
            padding: 16px 24px;
            border-bottom: 1px solid #f1f5f9;
        }
        
        .escalation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .escalation-priority {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .priority-urgent {
            background: #fecaca;
            color: #991b1b;
        }
        
        .priority-critical {
            background: #fee2e2;
            color: #7f1d1d;
        }
        
        .escalation-customer {
            font-weight: 600;
            color: #1e293b;
        }
        
        .escalation-time {
            font-size: 12px;
            color: #64748b;
        }
        
        .escalation-reason {
            font-size: 14px;
            color: #64748b;
        }
        
        .escalation-actions {
            display: flex;
            gap: 8px;
            margin-top: 8px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }
        
        .btn-resolve {
            background: #10b981;
            color: white;
        }
        
        .btn-view {
            background: #3b82f6;
            color: white;
        }
        
        /* Responsive */
        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                display: none;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .table-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <header class="header">
            <div class="header-left">
                <div class="logo">CX System</div>
                <div class="header-title">Panel de Supervisor</div>
            </div>
            <div class="header-right">
                <div class="notification-bell">3</div>
                <div class="user-profile">
                    <div class="avatar">MS</div>
                    <span>María Supervisor</span>
                </div>
            </div>
        </header>

        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="nav-section">
                <div class="nav-title">Dashboard</div>
                <a href="#" class="nav-item active">
                    <div class="nav-icon">📊</div>
                    <span>Vista General</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">👥</div>
                    <span>Agentes</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">💬</div>
                    <span>Conversaciones</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-title">Gestión</div>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🚨</div>
                    <span>Escalaciones</span>
                    <div class="badge">2</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">⏸️</div>
                    <span>Autorizaciones</span>
                    <div class="badge warning">1</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">📋</div>
                    <span>Cola de Supervisión</span>
                    <div class="badge">4</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">🔄</div>
                    <span>Transferencias</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-title">Supervisión</div>
                <a href="#" class="nav-item">
                    <div class="nav-icon">👁️</div>
                    <span>Intervenciones Activas</span>
                    <div class="badge success">3</div>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">💬</div>
                    <span>Chat con Agentes</span>
                </a>
            </div>

            <div class="nav-section">
                <div class="nav-title">Analytics</div>
                <a href="#" class="nav-item">
                    <div class="nav-icon">📈</div>
                    <span>Métricas</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">📊</div>
                    <span>Reportes</span>
                </a>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <div class="content-header">
                <h1 class="page-title">Dashboard de Supervisión</h1>
                <div class="quick-actions">
                    <button class="btn btn-secondary">
                        ⚙️ Configuración
                    </button>
                    <button class="btn btn-primary">
                        🚨 Intervenir Chat
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Escalaciones Urgentes</div>
                        <div class="stat-icon red">🚨</div>
                    </div>
                    <div class="stat-value">2</div>
                    <div class="stat-change negative">
                        ↗️ +1 desde ayer
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Agentes Activos</div>
                        <div class="stat-icon green">👥</div>
                    </div>
                    <div class="stat-value">12</div>
                    <div class="stat-change positive">
                        ↗️ 85% disponibles
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Supervisión Activa</div>
                        <div class="stat-icon blue">👁️</div>
                    </div>
                    <div class="stat-value">3</div>
                    <div class="stat-change">
                        📊 2 observación, 1 participando
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Tiempo Respuesta Promedio</div>
                        <div class="stat-icon yellow">⏱️</div>
                    </div>
                    <div class="stat-value">1.2m</div>
                    <div class="stat-change positive">
                        ↘️ -15s vs ayer
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Main Table - Agents Status -->
                <div class="data-table">
                    <div class="table-header">
                        <div class="table-title">Estado de Agentes</div>
                        <div class="table-actions">
                            <button class="btn btn-secondary">🔄 Actualizar</button>
                            <button class="btn btn-secondary">⚙️ Configurar</button>
                        </div>
                    </div>
                    <div class="table-content">
                        <div class="table-row">
                            <div class="agent-info">
                                <div class="agent-avatar">JP</div>
                                <div class="agent-details">
                                    <h4>Juan Pérez</h4>
                                    <p>Soporte Técnico • Turno Mañana</p>
                                </div>
                            </div>
                            <div class="status-badge status-available">Disponible</div>
                            <div>
                                <div class="workload-bar">
                                    <div class="workload-fill" style="width: 60%;"></div>
                                </div>
                                <small>3/5 chats</small>
                            </div>
                            <div class="table-actions">
                                <button class="btn-small btn-view">👁️ Ver</button>
                                <button class="btn-small btn-secondary">💬 Chat</button>
                            </div>
                        </div>

                        <div class="table-row">
                            <div class="agent-info">
                                <div class="agent-avatar" style="background: #f59e0b;">AM</div>
                                <div class="agent-details">
                                    <h4>Ana Martínez</h4>
                                    <p>Ventas • Turno Tarde</p>
                                </div>
                            </div>
                            <div class="status-badge status-busy">Ocupado</div>
                            <div>
                                <div class="workload-bar">
                                    <div class="workload-fill medium" style="width: 80%;"></div>
                                </div>
                                <small>4/5 chats</small>
                            </div>
                            <div class="table-actions">
                                <button class="btn-small btn-view">👁️ Ver</button>
                                <button class="btn-small btn-secondary">💬 Chat</button>
                            </div>
                        </div>

                        <div class="table-row">
                            <div class="agent-info">
                                <div class="agent-avatar" style="background: #ef4444;">LG</div>
                                <div class="agent-details">
                                    <h4>Luis García</h4>
                                    <p>Soporte Premium • Turno Noche</p>
                                </div>
                            </div>
                            <div class="status-badge status-away">Pausa</div>
                            <div>
                                <div class="workload-bar">
                                    <div class="workload-fill high" style="width: 100%;"></div>
                                </div>
                                <small>5/5 chats</small>
                            </div>
                            <div class="table-actions">
                                <button class="btn-small btn-resolve">✓ Autorizar</button>
                                <button class="btn-small btn-secondary">💬 Chat</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel -->
                <div class="right-panel">
                    <!-- Escalaciones Urgentes -->
                    <div class="data-table">
                        <div class="table-header">
                            <div class="table-title">Escalaciones Urgentes</div>
                            <button class="btn-small btn-secondary">Ver Todas</button>
                        </div>
                        <div class="table-content">
                            <div class="escalation-item">
                                <div class="escalation-header">
                                    <div class="escalation-customer">María González</div>
                                    <div class="escalation-priority priority-urgent">URGENTE</div>
                                </div>
                                <div class="escalation-time">Hace 15 minutos</div>
                                <div class="escalation-reason">Cliente insatisfecho con tiempo de resolución</div>
                                <div class="escalation-actions">
                                    <button class="btn-small btn-resolve">✓ Resolver</button>
                                    <button class="btn-small btn-view">👁️ Ver Chat</button>
                                </div>
                            </div>

                            <div class="escalation-item">
                                <div class="escalation-header">
                                    <div class="escalation-customer">Carlos Ruiz</div>
                                    <div class="escalation-priority priority-critical">CRÍTICO</div>
                                </div>
                                <div class="escalation-time">Hace 5 minutos</div>
                                <div class="escalation-reason">Problema técnico severo sin resolver</div>
                                <div class="escalation-actions">
                                    <button class="btn-small btn-resolve">✓ Resolver</button>
                                    <button class="btn-small btn-view">👁️ Ver Chat</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Cola de Supervisión -->
                    <div class="data-table">
                        <div class="table-header">
                            <div class="table-title">Cola de Supervisión</div>
                            <button class="btn-small btn-secondary">Gestionar</button>
                        </div>
                        <div class="table-content">
                            <div class="escalation-item">
                                <div class="escalation-header">
                                    <div class="escalation-customer">Ana M. - Solicita pausa</div>
                                    <div class="escalation-time">2m</div>
                                </div>
                                <div class="escalation-reason">Descanso programado - 15 min</div>
                                <div class="escalation-actions">
                                    <button class="btn-small btn-resolve">✓ Aprobar</button>
                                    <button class="btn-small btn-secondary">✗ Rechazar</button>
                                </div>
                            </div>

                            <div class="escalation-item">
                                <div class="escalation-header">
                                    <div class="escalation-customer">Juan P. - Transferencia</div>
                                    <div class="escalation-time">5m</div>
                                </div>
                                <div class="escalation-reason">Cliente requiere especialista</div>
                                <div class="escalation-actions">
                                    <button class="btn-small btn-view">👁️ Revisar</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>