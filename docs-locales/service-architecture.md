# Service Architecture Reference

This document provides quick reference for Claude Code sessions about each service's core purpose and communication patterns.

## Implemented Services

### 1. Twilio Service
- **Purpose**: WhatsApp message receiver via Twilio webhooks
- **Key Endpoint**: `POST /webhook` - receives Twilio webhook data
- **Core Function**: Converts Twilio format → internal WhatsApp message format
- **Port**: Default (not configured yet)
- **Dependencies**: None (entry point)
- **Status**: ✅ Basic implementation (receives, logs, responds OK)
- **Next Integration**: Should forward to Channel Router

### 2. Channel Router  
- **Purpose**: Intelligent message routing with department assignment
- **Key Endpoint**: `POST /api/route` - main routing decision maker
- **Core Function**: 
  - Receives messages → analyzes with AI (mock N8N) → assigns department → forwards to Session Manager
  - Handles vague messages by requesting clarification
- **Port**: 3002
- **Dependencies**: Session Manager (3001), Mock N8N webhook
- **Status**: ✅ Fully functional (24 tests passing)
- **Integration**: Ready to receive from <PERSON><PERSON><PERSON>, sends to Session Manager

### 3. Session Manager
- **Purpose**: Conversation session lifecycle management using Redis
- **Key Endpoint**: `POST /api/sessions` - conversation session creation/updates
- **Core Function**: 
  - Creates conversation sessions when chat starts
  - Maintains session while conversation is active
  - Manages agent assignments to conversations
  - Destroys session when conversation ends
  - Tracks conversation state in Redis (1hr TTL)
- **Port**: 3001
- **Dependencies**: Redis (Docker container)
- **Status**: ✅ Functional with Redis integration
- **Integration**: Receives from Channel Router, coordinates with Bot Human Router

### 4. Bot Human Router
- **Purpose**: Bot vs Human routing decision engine
- **Key Endpoint**: `POST /api/route` - routing decision (bot/human)
- **Core Function**:
  - Analyzes conversation context
  - Applies configurable routing rules
  - Routes to N8N (bot) or human agent queue
- **Port**: 3003
- **Dependencies**: None (uses internal rule engine)
- **Status**: ✅ Functional with rule-based routing
- **Integration**: Called by Session Manager for routing decisions

## Not Implemented Services

### 5. Chat Realtime
- **Purpose**: CORE conversational orchestrator - manages complete conversation lifecycle
- **Key Function**: 
  - Conversation management (create, assign, transfer, pause, close)
  - Agent assignment and capacity control (3-5 concurrent chats)
  - Transfer workflows (agent↔agent, departmental, supervisor escalation)
  - Supervisor interventions and monitoring
  - Conversation automations (inactivity, SLA alerts, priorities)
  - Internal notes system and conversation context
  - Real-time synchronization via Firebase Realtime Database
- **Planned Port**: 3004
- **Dependencies**: Firebase Realtime Database
- **Integration**: Central hub called by Chat UI, receives from Channel Router, emits Pub/Sub events for analytics/transcripts

### 6. Chat UI  
- **Purpose**: Next.js frontend for agent interface
- **Key Function**: Agent chat interface with offline resilience
- **Planned Port**: 3000 (Next.js default)
- **Dependencies**: Chat Realtime service, Firebase direct connection
- **Integration**: Primary user interface for agents

### 7. Analytics
- **Purpose**: Metrics collection and KPI reporting
- **Key Function**: Event tracking, performance analysis via Supabase
- **Planned Port**: 3005
- **Dependencies**: Supabase (PostgreSQL)
- **Integration**: Receives events from all services

## Message Flow (Current Implementation)

```
WhatsApp → Twilio Service → (NOT CONNECTED YET)
                ↓
Channel Router (3002) → analyzes → assigns department → Session Manager (3001)
                ↓                                              ↓
Mock N8N AI Analysis                               creates session → Bot Human Router (3003)
                                                            ↓
                                                   routing decision (bot/human)
```

## Service Communication Patterns

1. **HTTP REST APIs**: Primary communication method
2. **Redis**: Session state storage (Session Manager)
3. **Mock N8N**: Department analysis (Channel Router)
4. **Environment Variables**: Service URLs and configuration

## Ports Summary
- **3001**: Session Manager
- **3002**: Channel Router  
- **3003**: Bot Human Router
- **3004**: Chat Realtime (planned)
- **3005**: Analytics (planned)
- **3000**: Chat UI (planned, Next.js)

## Development Status per Service

### Completed & Tested ✅
- Channel Router: Full department assignment with AI analysis
- Session Manager: Redis-based session management
- Bot Human Router: Rule-based routing engine

### Basic Implementation ⚠️
- Twilio Service: Receives webhooks but doesn't forward yet

### Not Started ❌
- Chat Realtime: Firebase interface
- Chat UI: Agent frontend
- Analytics: Metrics collection

## Key Integration Points

1. **Twilio → Channel Router**: Missing connection
2. **Channel Router → Session Manager**: ✅ Working
3. **Session Manager → Bot Human Router**: ✅ Working
4. **All Services → Analytics**: Not implemented
5. **Chat UI → Chat Realtime → Firebase**: Not implemented

This reference should help maintain context across development sessions about what each service does and how they connect.

---

## NEXT SESSION: Chat Realtime Implementation

### 🎯 Ready to Start Implementation

**Branch**: Create new branch `chat-realtime-core`
**Port**: 3004
**Priority**: Core conversation management MVP

### Implementation Roadmap

#### Phase 1: Base Infrastructure ✅ NEXT SESSION
1. **Project Setup**:
   - TypeScript/Node.js service structure
   - Firebase Realtime Database connection
   - Express.js API with real-time capabilities
   - Basic health check and configuration

2. **Core Data Models**:
   - Conversation entity with states (waiting, active, paused, transferring, closed)
   - Agent assignment and capacity tracking
   - Message structure for real-time sync

3. **Essential API Endpoints**:
   - `POST /api/conversations` - Create new conversation
   - `GET /api/conversations` - List conversations with filters
   - `PUT /api/conversations/:id/assign` - Assign to agent
   - `PUT /api/conversations/:id/status` - Change status (pause, resume, close)

#### Phase 2: Transfer System
- Manual agent-to-agent transfers
- Departmental transfers  
- Transfer acceptance/rejection workflow

#### Phase 3: Supervisor Features
- Intervention system
- Monitoring capabilities
- Escalation handling

### Firebase Structure to Implement

```javascript
/conversations/{conversationId}/
├── metadata (customer, channel, status, timestamps)
├── assignment (agentId, assignmentTime, capacity)
├── messages/{messageId} (real-time messaging)
├── transfer (status, history, workflow)
├── notes/{noteId} (internal agent notes)
└── automation (inactivity, SLA tracking)

/agents/{agentId}/
├── status (online, away, busy)
├── activeConversations (count, list)
├── capacity (current, maximum)
└── availability
```

### Integration Points to Consider
- **Incoming**: Channel Router → create conversations
- **Outgoing**: Pub/Sub events → Analytics, Transcript services  
- **Real-time**: Chat UI ← → Firebase listeners
- **Coordination**: Session Manager integration

### Key References for Implementation
- `/context/CONVERSATION_FLOWS.md` - Complete workflow details
- `/context/firebase-cx-system-prd-v2.md` - Data models and features
- `/context/acciones-servicios-sistema.md` - Feature requirements (Section 1)

**This service is the CORE of the CX system - all conversation logic lives here.**