# Estados de Conversación - Sistema CX

## 📋 Resumen Ejecutivo

El sistema CX maneja 7 estados diferentes de conversación que definen el flujo operativo desde la creación hasta el cierre. Este documento detalla cada estado, sus transiciones y comportamientos específicos.

## 🔄 Estados Disponibles

### 1. `pending` - Conversación Sin Asignar
- **Descripción**: Conversación creada pero sin agente asignado
- **Agente puede conversar**: ❌ No
- **Uso**: Estado inicial tras creación automática
- **Transiciones típicas**: `pending` → `active`

### 2. `active` - Conversación Activa
- **Descripción**: Conversación con agente trabajando normalmente
- **Agente puede conversar**: ✅ Sí 
- **Uso**: Estado principal de trabajo operativo
- **Transiciones típicas**: 
  - `active` → `transferring`
  - `active` → `escalated`
  - `active` → `supervised`
  - `active` → `closed`

### 3. `transferring` - Procesando Transferencia
- **Descripción**: Transfer iniciado, procesándose internamente
- **Agente puede conversar**: ❌ No
- **Duración**: Milisegundos (raramente visible en UI)
- **Uso real**:
  - Prevenir acciones concurrentes
  - Logging/auditoría
  - Error handling si falla la transferencia
  - UI optimista feedback
- **Transiciones típicas**: `transferring` → `pending_acceptance`

### 4. `pending_acceptance` - Esperando Aceptación
- **Descripción**: Transfer completado, esperando que nuevo agente acepte
- **Agente puede conversar**: ❌ No (agente original ya no asignado)
- **Uso**: Estado intermedio visible en UI del nuevo agente
- **Transiciones típicas**: `pending_acceptance` → `active`

### 5. `supervised` - Bajo Supervisión
- **Descripción**: Supervisor observando o participando activamente
- **Agente puede conversar**: ✅ Sí (con supervisor presente)
- **Uso**: Coaching, apoyo, escalación resuelta
- **Transiciones típicas**: 
  - `supervised` → `active` (problema resuelto)
  - `supervised` → `escalated` (requiere más atención)
  - `supervised` → `closed`

### 6. `escalated` - Escalada a Supervisor
- **Descripción**: Requiere atención/intervención del supervisor
- **Agente puede conversar**: ✅ Sí (pero necesita ayuda)
- **Uso**: Problemas complejos, casos especiales
- **Transiciones típicas**: `escalated` → `supervised`

### 7. `closed` - Conversación Cerrada
- **Descripción**: Conversación finalizada completamente
- **Agente puede conversar**: ❌ No
- **Uso**: Estado final, lista para analytics
- **Transiciones típicas**: ❌ Estado final

## 🎯 Estados Activos vs Inactivos

### ✅ Estados donde el agente puede conversar (3/7):
1. **`active`** - Trabajo normal
2. **`supervised`** - Con supervisor presente  
3. **`escalated`** - Requiere atención especial pero activa

### ❌ Estados donde el agente NO puede conversar (4/7):
1. **`pending`** - Sin agente asignado
2. **`transferring`** - En procesamiento 
3. **`pending_acceptance`** - Esperando nuevo agente
4. **`closed`** - Finalizada

## 🔄 Flujos de Estado Principales

### Flujo Normal:
```
pending → active → closed
```

### Flujo con Transferencia:
```
active → transferring → pending_acceptance → active
```

### Flujo con Supervisión:
```
active → escalated → supervised → active
                                 ↓
                               closed
```

### Flujo Complejo:
```
pending → active → escalated → supervised → active → transferring → pending_acceptance → active → closed
```

## 🎨 Representación Visual en UI

| Estado | Color UI | Icono | Texto Mostrado |
|--------|----------|-------|----------------|
| `pending` | Gris | ⏳ | "Pendiente" |
| `active` | Verde | ✅ | "Activa" |
| `transferring` | Azul | ↔️ | "Transfiriendo" |
| `pending_acceptance` | Naranja | ⏰ | "Pendiente Aceptación" |
| `supervised` | Púrpura | 👁️ | "Supervisada" |
| `escalated` | Rojo | ⚠️ | "Escalada" |
| `closed` | Gris oscuro | ✅ | "Cerrada" |

## 🔍 Análisis del Estado `transferring`

### Problema Identificado:
El estado `transferring` dura milisegundos y rara vez es visible para el agente.

### Valor Real:
1. **Control de concurrencia**: Previene múltiples transfers simultáneos
2. **Auditoría**: Registra inicio de transferencia para logs
3. **Error handling**: Manejo de fallos durante el proceso
4. **UX optimista**: Feedback inmediato "Transfiriendo..."

### Opciones de Mejora:
1. **Mantener**: Útil para logging y prevenir race conditions
2. **Eliminar**: Simplificar flujo si nunca se ve
3. **Hacer visible**: Agregar delay o procesamiento asíncrono

## 📊 Impacto en Workload y Métricas

### Estados que cuentan para Workload del Agente:
- `active` ✅
- `supervised` ✅  
- `escalated` ✅
- `pending_acceptance` ✅
- `transferring` ✅

### Estados que NO cuentan para Workload:
- `pending` ❌
- `closed` ❌

## 🔧 Implementación Técnica

### Ubicación de Tipos:
- **Frontend**: `/services/chat-ui/src/types/api.ts:39-46`
- **Backend**: `/services/chat-realtime/src/types.ts:215-218`

### Validación de Estados:
Todos los estados están implementados tanto en UI como en backend con consistencia completa.

---

**Última actualización**: 2025-08-24  
**Autor**: Sistema de documentación automática  
**Estado**: Análisis completo validado contra código actual