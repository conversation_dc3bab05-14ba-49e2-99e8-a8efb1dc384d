# 🚀 Quick Reference Guide - CX System Documentation

**Purpose**: Fast navigation to the right documentation for your needs  
**Last Updated**: 2025-10-01

---

## 📖 "I Need To..." Quick Links

### Understand the Current System
→ **Start here**: [`CLAUDE.md`](../CLAUDE.md) (lines 1-150)  
→ **Latest changes**: [`docs-locales/sesiones/session-2025-08-27.md`](./sesiones/session-2025-08-27.md)  
→ **Quick overview**: [`README.md`](../README.md)

### Understand the Architecture
→ **High-level**: [`docs-locales/arquitectura/ARCHITECTURE_REPORT.md`](./arquitectura/ARCHITECTURE_REPORT.md)  
→ **Frontend architecture**: [`docs-locales/sesiones/session-2025-08-25-tanstack-refactor.md`](./sesiones/session-2025-08-25-tanstack-refactor.md)  
→ **Visual flows**: [`docs-locales/CONVERSATION_FLOWS.md`](./CONVERSATION_FLOWS.md)

### Start Development Work
→ **Development rules**: [`CLAUDE.md`](../CLAUDE.md) (lines 28-46)  
→ **Workflow protocols**: [`SESSION_GUIDE.md`](../SESSION_GUIDE.md)  
→ **Service-specific**: [`docs-locales/servicios/{service}/README.md`](./servicios/)

### Understand a Specific Service
→ **Chat UI**: [`docs-locales/servicios/chat-ui/README.md`](./servicios/chat-ui/README.md)  
→ **Chat Realtime**: [`docs-locales/servicios/chat-realtime/README.md`](./servicios/chat-realtime/README.md)  
→ **Bot Human Router**: [`docs-locales/servicios/bot-human-router/README.md`](./servicios/bot-human-router/README.md)  
→ **Channel Router**: [`docs-locales/servicios/channel-router/README.md`](./servicios/channel-router/README.md)

### Understand N8N Integration
→ **Webhook contracts**: [`docs-locales/N8N_WEBHOOKS_CONTRACTS.md`](./N8N_WEBHOOKS_CONTRACTS.md)  
→ **Decision engine**: [`docs-locales/ARQUITECTURA_DECISION_ENGINE_CLARIFICADA.md`](./ARQUITECTURA_DECISION_ENGINE_CLARIFICADA.md)

### Understand the Evolution
→ **Timeline**: [`docs-locales/DOCUMENTATION_ACCURACY_REPORT.md`](./DOCUMENTATION_ACCURACY_REPORT.md) (Evolution section)  
→ **Session history**: [`docs-locales/sesiones/`](./sesiones/) (read in chronological order)  
→ **Git history**: `git log --oneline --graph --all --decorate -30`

### Debug or Troubleshoot
→ **Troubleshooting**: [`CLAUDE.md`](../CLAUDE.md) (lines 200-250)  
→ **Common issues**: [`docs-locales/servicios/chat-ui/README.md`](./servicios/chat-ui/README.md) (Cache Issues section)  
→ **Debug tools**: [`CLAUDE.md`](../CLAUDE.md) (Debug Tools section)

---

## 🎯 Documentation by Use Case

### Use Case: New Developer Onboarding

**Day 1: Understanding the System**
1. Read [`README.md`](../README.md) (15 min)
2. Read [`CLAUDE.md`](../CLAUDE.md) sections:
   - Project Overview
   - Architecture
   - Development Rules (CRITICAL)
   - Current System Status
3. Skim [`docs-locales/INDEX.md`](./INDEX.md) to understand documentation structure

**Day 2: Deep Dive**
1. Read [`ARCHITECTURE_REPORT.md`](./arquitectura/ARCHITECTURE_REPORT.md)
2. Read latest session doc: [`session-2025-08-27.md`](./sesiones/session-2025-08-27.md)
3. Read TanStack refactor doc: [`session-2025-08-25-tanstack-refactor.md`](./sesiones/session-2025-08-25-tanstack-refactor.md)

**Day 3: Hands-On**
1. Read [`SESSION_GUIDE.md`](../SESSION_GUIDE.md) (development protocols)
2. Follow Quick Start in [`README.md`](../README.md)
3. Read service-specific README for your area

---

### Use Case: Frontend Development (Chat UI)

**Essential Reading**:
1. [`CLAUDE.md`](../CLAUDE.md) - Frontend Architecture Rules (lines 35-40)
2. [`session-2025-08-25-tanstack-refactor.md`](./sesiones/session-2025-08-25-tanstack-refactor.md) - Hybrid architecture
3. [`docs-locales/servicios/chat-ui/README.md`](./servicios/chat-ui/README.md)

**Key Concepts**:
- **Supervisor Mode**: Use TanStack Query hooks (`useQuery`, `useMutation`)
- **Agent Mode**: Use optimistic UI custom hooks
- **NO Mixed Patterns**: Never mix useEffect/useCallback polling with TanStack Query

**Reference Files**:
- TypeScript types: `services/chat-ui/src/types/api.ts`
- TanStack hooks: `services/chat-ui/src/hooks/supervisor/useSupervisorQueries.tsx`
- Optimistic hooks: `services/chat-ui/src/hooks/useOptimisticConversationAnimations.ts`

---

### Use Case: Backend Development (APIs)

**Essential Reading**:
1. [`CLAUDE.md`](../CLAUDE.md) - API Contracts & Data Consistency (lines 42-46)
2. Service-specific README for your service
3. [`docs-locales/N8N_WEBHOOKS_CONTRACTS.md`](./N8N_WEBHOOKS_CONTRACTS.md) (if working with N8N)

**Key Concepts**:
- **Check TypeScript types** in `src/types/api.ts` before implementing
- **Transform database responses** to match UI contracts exactly
- **Firebase dot notation fix**: Use `metadata: { field: value }` NOT `'metadata.field': value`

**Reference Files**:
- Chat Realtime API: `services/chat-realtime/src/routes.ts`
- Conversation Service: `services/chat-realtime/src/conversationService.ts`
- TypeScript types: `services/chat-realtime/src/types.ts`

---

### Use Case: Understanding a Bug

**Investigation Steps**:
1. **Check latest session docs** - Bug may have been recently introduced
2. **Read [`CLAUDE.md`](../CLAUDE.md) Troubleshooting** section
3. **Check service README** for known issues
4. **Review git history**: `git log --oneline --grep="keyword"`
5. **Check TypeScript types** for contract violations

**Common Bug Patterns**:
- **Cache issues**: See Chat UI README, Cache Issues section
- **Firebase dot notation**: See CLAUDE.md, line 45
- **Memory leaks**: See session-2025-08-25-tanstack-refactor.md
- **Authorization issues**: See session-2025-08-27.md

---

### Use Case: Adding a New Feature

**Planning Phase**:
1. Read [`SESSION_GUIDE.md`](../SESSION_GUIDE.md) - Rule #2: Always give options
2. Check [`CLAUDE.md`](../CLAUDE.md) - Development Rules
3. Review similar features in session docs

**Implementation Phase**:
1. **Check API contracts**: `src/types/api.ts`
2. **Follow architecture patterns**:
   - Frontend: TanStack Query (supervisor) or Optimistic UI (agent)
   - Backend: Clean Architecture, TypeScript strict
3. **Update documentation**: Service README, session doc

**Testing Phase**:
1. Write tests (see service-specific testing docs)
2. Run E2E tests
3. Update documentation with results

---

## 📊 Documentation Accuracy Ratings

| Document | Accuracy | Update Frequency | Best For |
|----------|----------|------------------|----------|
| **CLAUDE.md** | ⭐⭐⭐⭐⭐ | Every session | Current state, rules |
| **SESSION_GUIDE.md** | ⭐⭐⭐⭐⭐ | When workflow changes | Development protocols |
| **Latest Session Doc** | ⭐⭐⭐⭐⭐ | Per session | Latest changes |
| **TanStack Refactor Doc** | ⭐⭐⭐⭐⭐ | One-time (critical) | Frontend architecture |
| **README.md** | ⭐⭐⭐⭐ | Major milestones | Quick overview |
| **Service READMEs** | ⭐⭐⭐⭐ | Major changes | Service specifics |
| **ARCHITECTURE_REPORT** | ⭐⭐⭐ | Quarterly | High-level design |
| **Flow Diagrams** | ⭐⭐⭐ | As needed | Visual understanding |

---

## 🔍 How to Verify Documentation Accuracy

Before trusting any documentation:

1. **Check the date**: Recent = more accurate
2. **Look for commit hashes**: Verifiable against git history
3. **Cross-reference with code**: Does the code match the docs?
4. **Check session docs**: Was this recently changed?
5. **Validate TypeScript types**: Do types match documentation?

**Golden Rule**: When in doubt, trust:
```
CLAUDE.md > Latest Session Doc > Service README > Architecture Doc
```

---

## 🛠️ Essential Commands

### Check System Status
```bash
./scripts/manage-services.sh status
./scripts/manage-services.sh build-status
```

### Start Development Environment
```bash
./scripts/start-all-emulators-improved.sh
cd services/chat-realtime && npm run dev &
cd services/chat-ui && npm run dev -- --port 3007 &
```

### Recreate Test Data
```bash
cd services/chat-realtime
node conversation_manager.js --create juan 5
```

### View Logs
```bash
./scripts/manage-services.sh logs chat-realtime
./scripts/manage-services.sh logs chat-ui
```

---

## 📚 Complete Documentation Index

For a complete list of all documentation files, see:
→ [`docs-locales/INDEX.md`](./INDEX.md)

For detailed accuracy analysis, see:
→ [`docs-locales/DOCUMENTATION_ACCURACY_REPORT.md`](./DOCUMENTATION_ACCURACY_REPORT.md)

---

## 🎓 Key Architectural Decisions

### Hybrid Database Strategy
- **Firebase Realtime Database**: Real-time operations (conversations, messages, agent status)
- **Supabase (PostgreSQL)**: Configuration & analytics (organizations, agents, customers)

**Why**: Firebase for real-time, Supabase for complex queries and analytics

### Hybrid Frontend Architecture (Aug 25, 2025)
- **Supervisor Mode**: TanStack Query (memory-safe polling, 30s intervals)
- **Agent Mode**: Custom Optimistic UI (ultra-fast chat, <100ms latency)

**Why**: Best-of-both-worlds - reliability for supervisors, speed for agents

### Unified Bot/Human Architecture (Aug 26, 2025)
- **Single conversation flow**: Bot and human agents use same conversation structure
- **N8N continuity**: Conversations can transfer between bot and human seamlessly
- **Agent type field**: Distinguishes bot vs human without separate flows

**Why**: Simplifies architecture, enables seamless handoffs, reduces code duplication

---

## 🚨 Critical Rules (From SESSION_GUIDE.md)

1. **NEVER restart or kill services** - Always ask user to restart
2. **ALWAYS give 2-3 options** before implementing
3. **Check API contracts** in `src/types/api.ts` before modifying
4. **Use real Supabase IDs** - never generate fake IDs
5. **NO mock data** in real implementations
6. **Ask for auth tokens** for protected endpoints

---

## 📞 Need Help?

- **Can't find documentation?** → Check [`INDEX.md`](./INDEX.md)
- **Documentation seems outdated?** → Check latest session doc
- **Conflicting information?** → Trust CLAUDE.md > Session Docs > Service READMEs
- **Need to understand a change?** → Search session docs for commit hash

---

**Last Updated**: 2025-10-01  
**Maintained By**: Updated with each major documentation change  
**Related**: [DOCUMENTATION_ACCURACY_REPORT.md](./DOCUMENTATION_ACCURACY_REPORT.md), [INDEX.md](./INDEX.md)

