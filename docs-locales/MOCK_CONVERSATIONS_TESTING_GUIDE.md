# 🧪 Mock Conversations & Testing Guide - CX System

**Purpose**: Complete guide to all scripts and tools for creating mock conversations, test data, and development testing  
**Last Updated**: 2025-10-01

---

## 📋 Overview

The CX System includes a comprehensive suite of tools for creating realistic mock conversations, test data, and development scenarios. These tools are essential for development, testing, and debugging the system.

---

## 🎯 Primary Mock Conversation Tools

### 1. **Conversation Manager CLI** ⭐ **MAIN TOOL**
**Location**: `services/chat-realtime/conversation_manager.js`  
**Purpose**: Complete conversation lifecycle management with realistic data

#### Key Features:
- **Create N conversations** for specific agents with realistic customer data
- **Transfer conversations** between agents with proper workflow
- **Close conversations** with system messages
- **Delete operations** (agent-specific, conversation-specific, or all)
- **List operations** (agents, conversations with filtering)
- **Agent management** (recreate from Supabase, cleanup)

#### Usage Examples:
```bash
# Create 5 conversations for <PERSON>
cd services/chat-realtime
node conversation_manager.js --create juan 5

# Transfer specific conversation to another agent
node conversation_manager.js --transfer CHaaa...123 maria "Load balancing"

# List all available agents
node conversation_manager.js --list-agents

# Delete all conversations for specific agent
node conversation_manager.js --delete-agent juan

# ⚠️ DANGEROUS: Delete all conversations
node conversation_manager.js --delete-all
```

#### Realistic Data Generated:
- **Customer Pool**: 8 realistic customers (Roberto Silva, Carmen López, etc.)
- **Conversation Topics**: 8 realistic scenarios (billing issues, technical support, etc.)
- **System Messages**: welcome, agent_assigned, agent_transferred, conversation_ended
- **Twilio Format IDs**: CHaaaa... format for WhatsApp compatibility
- **Proper Timestamps**: Staggered creation times for realistic distribution

---

### 2. **Master Data Creation Script**
**Location**: `scripts/create_complete_data.js`  
**Purpose**: Create comprehensive test environment with all data types

#### Features:
- **15 complete conversations** with realistic message flows
- **Multiple departments** (technical_support, billing, sales)
- **Agent assignments** using real Supabase agent IDs
- **Queue management** with department-based routing
- **TypeScript contract compliance** (100% type-safe)
- **Selective data insertion** with command-line flags

#### Usage:
```bash
cd scripts
node create_complete_data.js --all
node create_complete_data.js --conversations --agents
node create_complete_data.js --help  # See all options
```

#### Data Created:
- Conversations with full message history
- Agent status and availability
- Department queues
- Transfer history
- Supervision records
- Metadata and routing information

---

## 🔧 Development Helper Scripts

### 3. **Client Simulator Service**
**Location**: `services/client-simulator/`  
**Port**: 3008  
**Purpose**: Simulate customer interactions for end-to-end testing

#### Features:
- **Web interface** for simulating customer messages
- **Real-time message sending** to Channel Router
- **Conversation management** with message history
- **Integration testing** with full system flow

#### Usage:
```bash
cd services/client-simulator
npm run dev
# Open: http://localhost:3008
```

#### API Endpoints:
- `POST /api/conversations` - Create new simulated conversation
- `POST /api/conversations/:id/send` - Send customer message
- `GET /api/conversations/:id/messages` - Get message history

---

### 4. **Specific Scenario Scripts**

#### **Add Messages to Pending Conversation**
**Location**: `services/chat-realtime/add_messages_to_pending.js`  
**Purpose**: Add realistic message flow to existing pending conversations

```bash
node add_messages_to_pending.js
```

#### **Create Second Pending Conversation**
**Location**: `services/chat-realtime/create_second_pending.js`  
**Purpose**: Create additional pending_acceptance conversation for testing transfers

```bash
node create_second_pending.js
```

#### **Fix Agent Assignment**
**Location**: `services/chat-realtime/fix_agent_assignment.js`  
**Purpose**: Correct agent assignments for specific conversations

```bash
node fix_agent_assignment.js
```

---

## 🔍 Debug & Verification Tools

### 5. **Firebase Structure Debugger**
**Location**: `scripts/debug_firebase_structure.js`  
**Purpose**: Inspect Firebase database structure and verify data

```bash
cd scripts
node debug_firebase_structure.js
```

**Output**:
- Root database structure
- Conversation counts and IDs
- Agent data verification
- Sample conversation structure

### 6. **Firebase Data Verifier**
**Location**: `scripts/verify_firebase_data.js`  
**Purpose**: Verify conversations exist and are properly assigned

```bash
cd scripts
node verify_firebase_data.js
```

**Checks**:
- Conversation existence
- Agent assignments
- API-like queries
- Data consistency

### 7. **Transfer Debug Script**
**Location**: `services/chat-realtime/debug_transfer.js`  
**Purpose**: Debug conversation transfer functionality

```bash
cd services/chat-realtime
node debug_transfer.js
```

**Features**:
- Conversation schema validation
- Transfer logic testing
- API call testing
- State verification

### 8. **Agent Check Script**
**Location**: `services/chat-realtime/test_agent_check.js`  
**Purpose**: Test agent existence and API functionality

```bash
cd services/chat-realtime
node test_agent_check.js
```

---

## 🧪 Testing Infrastructure

### 9. **E2E Testing Orchestrator**
**Location**: `services/chat-realtime/testing/e2e-orchestrator.ts`  
**Purpose**: Comprehensive end-to-end testing with real authentication

#### Test Suites:
- **Health Tests**: Service availability
- **Authentication Tests**: Real Supabase auth
- **Agent Journey Tests**: Complete agent workflow
- **Supervisor Journey Tests**: Supervisor functionality

#### Usage:
```bash
cd services/chat-realtime/testing
npm run test
```

### 10. **Unit Test Mocks**
**Location**: `services/chat-realtime/tests/unit/`  
**Purpose**: Unit testing with mocked services

#### Mock Types:
- **Conversation Service Mocks**: `conversationService.simple.test.ts`
- **Edge Case Testing**: `edgeCases.test.ts`
- **API Integration Mocks**: `api.test.ts`

### 11. **Browser Debug Tools**
**Location**: `services/chat-ui/src/utils/cxDebug.ts`  
**Purpose**: Frontend debugging and testing utilities

#### Available in Browser Console:
```javascript
// Cache management
cxDebug.clearCache()
cxDebug.showCacheState()
cxDebug.exportCache()

// Offline testing
cxDebug.simulateOffline()
cxDebug.simulateOnline()

// Test data
cxDebug.seedTestData()
cxDebug.resetToDefaults()

// Connection testing
cxDebug.forceSync()
cxDebug.resetConnection()
```

---

## 📊 Mock Data Specifications

### Customer Pool (8 Customers)
```javascript
const CUSTOMER_POOL = [
  { id: 'customer-001', name: 'Roberto Silva', phone: '+1234567001' },
  { id: 'customer-002', name: 'Carmen López', phone: '+1234567002' },
  { id: 'customer-003', name: 'Diego Martínez', phone: '+1234567003' },
  { id: 'customer-004', name: 'Lucía Fernández', phone: '+1234567004' },
  { id: 'customer-005', name: 'Patricia Morales', phone: '+1234567005' },
  { id: 'customer-006', name: 'Fernando Castro', phone: '+1234567006' },
  { id: 'customer-007', name: 'Isabel Jiménez', phone: '+1234567007' },
  { id: 'customer-008', name: 'Miguel Vargas', phone: '+1234567008' }
];
```

### Conversation Topics (8 Scenarios)
```javascript
const CONVERSATION_TOPICS = [
  'Mi tarjeta no está funcionando correctamente',
  'Tengo una duda sobre mi facturación',
  'Quiero información sobre sus productos',
  'Necesito ayuda con mi cuenta online',
  'Hay un cargo que no reconozco',
  'Mi aplicación móvil no carga',
  'Quiero cambiar mis datos personales',
  'Necesito un reporte de mis transacciones'
];
```

### System Message Templates
- **welcome**: Customer greeting
- **agent_assigned**: Agent assignment notification
- **agent_transferred**: Transfer notification
- **conversation_ended**: Conversation closure
- **session_ended**: Session termination

---

## 🚀 Quick Start Workflows

### Development Environment Setup
```bash
# 1. Start emulators
./scripts/start-all-emulators-improved.sh

# 2. Start services
cd services/chat-realtime && npm run dev &
cd services/chat-ui && npm run dev -- --port 3007 &

# 3. Create test data
cd services/chat-realtime
node conversation_manager.js --create juan 5

# 4. Verify data
cd scripts
node verify_firebase_data.js
```

### Testing Specific Scenarios
```bash
# Test transfers
cd services/chat-realtime
node debug_transfer.js

# Test client simulation
cd services/client-simulator
npm run dev
# Open: http://localhost:3008

# Test E2E flows
cd services/chat-realtime/testing
npm run test
```

### Debug Issues
```bash
# Check Firebase structure
cd scripts
node debug_firebase_structure.js

# Verify agent assignments
cd services/chat-realtime
node test_agent_check.js

# Browser debugging
# Open: http://localhost:3007/agent
# F12 → Console → cxDebug.help()
```

---

## 🎯 Best Practices

### 1. **Always Use Real Agent IDs**
- Never generate fake UUIDs
- Use `--list-agents` to see available agents
- Scripts automatically resolve agent names to IDs

### 2. **Clean Environment Between Tests**
```bash
# Clean slate approach
node conversation_manager.js --delete-all
node conversation_manager.js --create juan 5
```

### 3. **Verify Data After Creation**
```bash
# Always verify after creating data
node verify_firebase_data.js
```

### 4. **Use Appropriate Tools for Each Task**
- **Development**: `conversation_manager.js`
- **Comprehensive testing**: `create_complete_data.js`
- **E2E testing**: Client Simulator + E2E Orchestrator
- **Debugging**: Debug scripts + Browser tools

### 5. **TypeScript Contract Compliance**
- All mock data follows TypeScript interfaces
- Check `src/types/api.ts` for contracts
- Scripts validate data structure

---

## 🔗 Integration Points

### With Development Workflow
- **CLAUDE.md**: References conversation_manager.js as primary tool
- **SESSION_GUIDE.md**: Prohibits mock data in real implementations
- **Quick Start**: Uses conversation_manager.js for initial setup

### With Testing Strategy
- **Unit Tests**: Use mocked services
- **Integration Tests**: Use real Firebase emulator
- **E2E Tests**: Use Client Simulator + real auth

### With Services
- **Chat Realtime**: Primary data creation target
- **Chat UI**: Consumes created conversations
- **Client Simulator**: Generates realistic customer interactions
- **Channel Router**: Receives simulated messages

---

## 📞 Troubleshooting

### Common Issues
1. **"Agent not found"** → Use `--list-agents` to see available agents
2. **"Firebase connection failed"** → Ensure emulators are running
3. **"No conversations created"** → Check Supabase connection
4. **"Transfer failed"** → Use debug_transfer.js to investigate

### Debug Commands
```bash
# Check system status
./scripts/manage-services.sh status

# Check Firebase emulator
curl http://127.0.0.1:9000/.json

# Check Chat Realtime API
curl http://localhost:3003/api/health
```

---

**Related Documentation**:
- [CLAUDE.md](../CLAUDE.md) - Development rules and current status
- [SESSION_GUIDE.md](../SESSION_GUIDE.md) - Development protocols
- [PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md) - System overview
- [QUICK_REFERENCE_GUIDE.md](./QUICK_REFERENCE_GUIDE.md) - Navigation guide
