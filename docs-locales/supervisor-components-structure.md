# 🏗️ Estructura de Componentes - Supervisor Dashboard

## 📁 **Arquitectura de Carpetas**

```
services/chat-ui/src/
├── app/
│   └── supervisor/
│       ├── page.tsx                    # ✅ Main supervisor dashboard
│       ├── agents/
│       │   └── page.tsx               # Agent management view
│       ├── escalations/
│       │   └── page.tsx               # Escalations management
│       ├── queue/
│       │   └── page.tsx               # Supervisor queue
│       └── analytics/
│           └── page.tsx               # Analytics dashboard
├── components/
│   └── supervisor/
│       ├── layout/
│       │   ├── SupervisorLayout.tsx   # Main layout wrapper
│       │   ├── SupervisorSidebar.tsx  # Navigation sidebar
│       │   ├── SupervisorHeader.tsx   # Top header with actions
│       │   └── SupervisorMobileNav.tsx # Mobile navigation
│       ├── dashboard/
│       │   ├── StatsCards.tsx         # Quick stats overview
│       │   ├── AgentsTable.tsx        # Agents status table
│       │   ├── EscalationsPanel.tsx   # Urgent escalations
│       │   ├── SupervisorQueue.tsx    # Pending requests
│       │   └── QuickActions.tsx       # Action buttons
│       ├── agents/
│       │   ├── AgentCard.tsx          # Individual agent card
│       │   ├── AgentStatusBadge.tsx   # Status indicator
│       │   ├── AgentWorkload.tsx      # Workload visualization
│       │   ├── AgentActions.tsx       # Agent action buttons
│       │   └── AgentHistory.tsx       # Status history
│       ├── escalations/
│       │   ├── EscalationCard.tsx     # Individual escalation
│       │   ├── PriorityBadge.tsx      # Priority indicator
│       │   ├── EscalationActions.tsx  # Resolve/view actions
│       │   └── EscalationFilters.tsx  # Filter controls
│       ├── supervision/
│       │   ├── ActiveSupervisions.tsx # Currently supervised chats
│       │   ├── SupervisionModal.tsx   # Supervision interface
│       │   ├── CoachingPanel.tsx      # Agent coaching
│       │   └── InterventionControls.tsx # Observe/participate
│       ├── queue/
│       │   ├── QueueItem.tsx          # Individual queue item
│       │   ├── AuthorizationCard.tsx  # Authorization requests
│       │   ├── TransferRequest.tsx    # Transfer approvals
│       │   └── QueueActions.tsx       # Bulk actions
│       ├── analytics/
│       │   ├── PerformanceCharts.tsx  # KPI visualizations
│       │   ├── AgentMetrics.tsx       # Individual metrics
│       │   ├── EscalationTrends.tsx   # Escalation analytics
│       │   └── RealtimeStats.tsx      # Live statistics
│       └── shared/
│           ├── SupervisorCard.tsx     # Base card component
│           ├── ActionButton.tsx       # Consistent buttons
│           ├── StatusIndicator.tsx    # Universal status
│           ├── TimeAgo.tsx            # Time formatting
│           └── LoadingSpinner.tsx     # Loading states
└── hooks/
    └── supervisor/
        ├── useSupervisorDashboard.tsx # Dashboard data
        ├── useAgentStatus.tsx         # Agent monitoring
        ├── useEscalations.tsx         # Escalation management
        ├── useSupervisionActions.tsx  # Supervision controls
        ├── useAuthorizations.tsx      # Authorization workflow
        └── useRealTimeUpdates.tsx     # Firebase subscriptions
```

## 🎯 **Componentes Principales**

### 1. **SupervisorLayout.tsx**
```typescript
interface SupervisorLayoutProps {
  children: React.ReactNode;
  activeSection?: 'dashboard' | 'agents' | 'escalations' | 'queue' | 'analytics';
}

// Features:
// - Responsive sidebar/mobile nav
// - Real-time notifications
// - Global search
// - User context menu
```

### 2. **StatsCards.tsx**
```typescript
interface SupervisorStats {
  urgentEscalations: number;
  activeAgents: number;
  pendingAuthorizations: number;
  averageResponseTime: string;
  activeSuperVisions: number;
}

// Features:
// - Real-time data updates
// - Trend indicators (+/- changes)
// - Click-through navigation
// - Mobile-optimized layout
```

### 3. **AgentsTable.tsx**
```typescript
interface AgentTableProps {
  agents: AgentSummary[];
  onAgentAction: (agentId: string, action: AgentAction) => void;
  onSupervise: (agentId: string) => void;
  onDirectChat: (agentId: string) => void;
}

// Features:
// - Real-time status updates
// - Workload visualization
// - Quick actions (chat, supervise, authorize)
// - Sorting and filtering
// - Mobile-responsive cards
```

### 4. **EscalationsPanel.tsx**
```typescript
interface EscalationsPanelProps {
  escalations: EscalatedConversation[];
  onResolve: (escalationId: string) => void;
  onView: (conversationId: string) => void;
  maxItems?: number;
}

// Features:
// - Priority-based sorting
// - Auto-refresh every 30s
// - One-click resolution
// - Time-since-escalated
// - Customer context preview
```

## 🔄 **Hooks de Estado**

### 1. **useSupervisorDashboard.tsx**
```typescript
export function useSupervisorDashboard() {
  return {
    // Data
    stats: SupervisorStats,
    agents: AgentSummary[],
    escalations: EscalatedConversation[],
    queue: QueueItem[],
    
    // Actions
    refreshDashboard: () => Promise<void>,
    
    // State
    loading: boolean,
    error: string | null,
    lastUpdated: Date,
  };
}
```

### 2. **useEscalations.tsx**
```typescript
export function useEscalations() {
  return {
    // Data
    escalations: EscalatedConversation[],
    filters: EscalationFilters,
    
    // Actions
    resolveEscalation: (id: string, reason?: string) => Promise<void>,
    getEscalationDetails: (id: string) => Promise<EscalationDetail>,
    
    // State
    resolving: Record<string, boolean>,
    loading: boolean,
  };
}
```

### 3. **useSupervisionActions.tsx**
```typescript
export function useSupervisionActions() {
  return {
    // Data
    activeSupervisIons: ActiveSupervision[],
    
    // Actions
    startSupervision: (conversationId: string, mode: 'observe' | 'participate') => Promise<void>,
    endSupervision: (conversationId: string) => Promise<void>,
    switchMode: (conversationId: string, mode: 'observe' | 'participate') => Promise<void>,
    sendCoachingMessage: (conversationId: string, message: string) => Promise<void>,
    
    // State
    supervising: Record<string, SupervisionMode>,
    loading: boolean,
  };
}
```

## 📱 **Diseño Responsivo**

### **Breakpoints**
```css
/* Mobile First */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr; /* Mobile: 1 column */
  gap: 16px;
}

@media (min-width: 640px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr); /* Tablet: 2 columns */
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr); /* Desktop: 4 columns */
  }
}

/* Layout Adaptativo */
@media (max-width: 768px) {
  .supervisor-layout {
    grid-template-columns: 1fr; /* Hide sidebar */
  }
  
  .mobile-bottom-nav {
    display: flex; /* Show bottom navigation */
  }
}
```

### **Componentes Adaptativos**
- **Desktop**: Sidebar + main content
- **Tablet**: Collapsible sidebar + optimized tables
- **Mobile**: Bottom navigation + stacked cards

## 🎨 **Sistema de Diseño**

### **Colores de Estado**
```css
:root {
  /* Status Colors */
  --status-available: #10b981;
  --status-busy: #f59e0b; 
  --status-away: #ef4444;
  
  /* Priority Colors */
  --priority-urgent: #ef4444;
  --priority-critical: #dc2626;
  --priority-high: #f59e0b;
  --priority-medium: #3b82f6;
  --priority-low: #6b7280;
  
  /* Action Colors */
  --action-primary: #1e40af;
  --action-success: #10b981;
  --action-warning: #f59e0b;
  --action-danger: #ef4444;
}
```

### **Componentes Reutilizables**
- **SupervisorCard**: Container base con header/content/actions
- **StatusBadge**: Indicador universal de estados
- **ActionButton**: Botones consistentes con iconografía
- **MetricDisplay**: Visualización de números con tendencias
- **TimeDisplay**: Formateo consistente de tiempo

## 🔄 **Flujos de Usuario Críticos**

### **1. Resolver Escalación (30s)**
```
Dashboard → Ver Escalación → Leer Contexto → Resolver → Confirmar
```

### **2. Autorizar Estado Agente (15s)**  
```
Dashboard → Ver Solicitud → Revisar Razón → Aprobar/Rechazar
```

### **3. Iniciar Supervisión (20s)**
```
Agentes → Seleccionar Agente → Ver Conversación → Iniciar Supervisión → Elegir Modo
```

### **4. Intervenir en Chat (10s)**
```
Notificación → Ver Chat → Evaluar Situación → Intervenir/Coaching
```

## 📊 **Métricas UX**

### **Indicadores Críticos**
- ⏱️ **Tiempo de resolución de escalación**: < 60 segundos
- 🎯 **Clicks para acción crítica**: ≤ 3 clicks  
- 📱 **Usabilidad móvil**: Touch targets ≥ 44px
- 🔄 **Tiempo de carga inicial**: < 2 segundos
- ⚡ **Actualizaciones en tiempo real**: < 1 segundo latency

### **Accesibilidad**
- **Contraste**: AAA compliance (7:1)
- **Teclado**: Full navigation support
- **Screen readers**: ARIA labels completos
- **Focus management**: Clear focus indicators

## 🚀 **Implementación Progresiva**

### **Fase 1: MVP (Semana 1)**
1. ✅ Layout básico con sidebar
2. ✅ Stats cards con datos mock
3. ✅ Lista de agentes básica
4. ✅ Panel de escalaciones
5. ✅ Acciones de resolución

### **Fase 2: Interacciones (Semana 2)**
1. 🔄 Real-time updates con Firebase
2. 🔄 Modal de supervisión
3. 🔄 Sistema de autorización  
4. 🔄 Filtros y búsqueda
5. 🔄 Mobile responsiveness

### **Fase 3: Analytics (Semana 3)**
1. 📊 Dashboard de métricas
2. 📊 Charts interactivos
3. 📊 Reportes exportables
4. 📊 Trends históricos
5. 📊 Alertas automatizadas

Esta estructura proporciona una base sólida y escalable para el dashboard de supervisor, priorizando la usabilidad, performance y mantenibilidad del código.