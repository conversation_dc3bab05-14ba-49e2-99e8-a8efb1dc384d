# Chat Realtime Service - Especificación de Endpoints

## 📋 Índice

- [Endpoint Inteligente](#endpoint-inteligente)
- [Gestión del Ciclo de Vida](#gestión-del-ciclo-de-vida)
- [Mensajería y Comunicación](#mensajería-y-comunicación)
- [Transferencias y Escalación](#transferencias-y-escalación)
- [Gestión de Agentes](#gestión-de-agentes)
- [Supervisión](#supervisión)
- [Notas y Contexto](#notas-y-contexto)
- [Exportación y Limpieza](#exportación-y-limpieza)
- [Estados y Métricas](#estados-y-métricas)

---

## 🧠 Endpoint Inteligente

### `POST /api/process-message`
**Usado por**: Bot Human Router (desde PubSub)
**Propósito**: Procesar mensajes entrantes y deducir automáticamente las acciones necesarias

**Responsabilidad de Chat Realtime**:
- ✅ Determinar si es conversación nueva o existente
- ✅ Crear conversación nueva si es necesario
- ✅ Buscar y asignar agente disponible (lógica de balance, skills, capacity)
- ✅ Enviar mensaje a conversación existente
- ✅ Actualizar estados en Firebase
- ✅ Detectar intents de escalación/cierre automático

**Se delega a Tools**:
- 🔧 **Agent Matcher Tool**: Análisis avanzado de skills matching
- 🔧 **Department Analyzer Tool**: Análisis de contenido para asignación departamental
- 🔧 **Intent Detection Tool**: NLP para detectar intents complejos
- 🔧 **Analytics Tool**: Registrar métricas de asignación

---

## 🔄 Gestión del Ciclo de Vida

### `POST /api/conversations`
**Usado por**: Chat UI (supervisores), APIs externas
**Propósito**: Crear conversación manualmente

**Responsabilidad de Chat Realtime**:
- ✅ Crear conversación en Firebase
- ✅ Asignar ID único
- ✅ Establecer estado inicial
- ✅ Notificar en tiempo real

**Se delega a Tools**:
- 🔧 **Notification Tool**: Notificaciones push a supervisores
- 🔧 **Analytics Tool**: Registrar creación manual

### `PUT /api/conversations/:id/status`
**Usado por**: Chat UI (agentes/supervisores), procesos internos
**Propósito**: Cambiar estado de conversación

**Responsabilidad de Chat Realtime**:
- ✅ Actualizar estado en Firebase
- ✅ Validar transiciones permitidas
- ✅ Actualizar timestamps relevantes
- ✅ Liberar/asignar capacidad de agente

**Se delega a Tools**:
- 🔧 **State Validator Tool**: Validaciones complejas de negocio
- 🔧 **Metrics Tool**: Tracking de cambios de estado

---

## 💬 Mensajería y Comunicación

### `POST /api/conversations/:id/messages`
**Usado por**: Chat UI (agentes), Bot Human Router
**Propósito**: Enviar mensaje en conversación

**Responsabilidad de Chat Realtime**:
- ✅ Validar que conversación existe y está activa
- ✅ Crear mensaje en Firebase
- ✅ Actualizar timestamp de última actividad
- ✅ Actualizar contador de mensajes
- ✅ Reiniciar timer de inactividad

**Se delega a Tools**:
- 🔧 **Content Filter Tool**: Filtrado de contenido inapropiado
- 🔧 **Translation Tool**: Traducción automática si es necesario
- 🔧 **Sentiment Analysis Tool**: Análisis de sentimiento del mensaje

### `PUT /api/conversations/:id/messages/:messageId/read`
**Usado por**: Chat UI (agentes)
**Propósito**: Marcar mensaje como leído

**Responsabilidad de Chat Realtime**:
- ✅ Actualizar estado de lectura en Firebase
- ✅ Actualizar timestamp de lectura
- ✅ Notificar al cliente si es necesario

### `POST /api/conversations/:id/typing`
**Usado por**: Chat UI (agentes)
**Propósito**: Indicadores de escritura

**Responsabilidad de Chat Realtime**:
- ✅ Actualizar indicador en Firebase
- ✅ Auto-limpiar después de timeout

---

## 🔄 Transferencias y Escalación

### `POST /api/conversations/:id/transfer`
**Usado por**: Chat UI (agentes/supervisores)
**Propósito**: Transferir conversación a otro agente

**Responsabilidad de Chat Realtime**:
- ✅ Validar que agente destino está disponible
- ✅ Actualizar asignación en Firebase
- ✅ Crear registro en historial de transferencias
- ✅ Enviar mensaje de sistema automático
- ✅ Actualizar capacidad de ambos agentes
- ✅ Cambiar estado a 'transferring' → 'pending_acceptance'

**Se delega a Tools**:
- 🔧 **Agent Availability Tool**: Validación avanzada de disponibilidad
- 🔧 **Notification Tool**: Notificar al agente destino
- 🔧 **Analytics Tool**: Métricas de transferencias

### `POST /api/conversations/:id/accept-transfer`
**Usado por**: Chat UI (agentes), Session Manager
**Propósito**: Aceptar transferencia pendiente

**Responsabilidad de Chat Realtime**:
- ✅ Cambiar estado a 'active'
- ✅ Confirmar asignación del agente
- ✅ Actualizar timestamp de aceptación
- ✅ Limpiar estado de transferencia pendiente

### `POST /api/conversations/:id/reject-transfer`
**Usado por**: Chat UI (agentes)
**Propósito**: Rechazar transferencia

**Responsabilidad de Chat Realtime**:
- ✅ Registrar rechazo en historial
- ✅ Reasignar automáticamente a otro agente disponible
- ✅ Si no hay agentes, regresar a cola

**Se delega a Tools**:
- 🔧 **Agent Matcher Tool**: Buscar siguiente mejor agente
- 🔧 **Analytics Tool**: Registrar razones de rechazo

### `POST /api/conversations/:id/escalate`
**Usado por**: Chat UI (agentes), procesos automáticos
**Propósito**: Escalar conversación a supervisor

**Responsabilidad de Chat Realtime**:
- ✅ Aumentar nivel de escalación
- ✅ Cambiar prioridad automáticamente
- ✅ Buscar y asignar supervisor disponible
- ✅ Crear mensaje de sistema
- ✅ Actualizar metadata de escalación

**Se delega a Tools**:
- 🔧 **Supervisor Matcher Tool**: Encontrar supervisor adecuado
- 🔧 **Escalation Analytics Tool**: Tracking de escalaciones
- 🔧 **Urgent Notification Tool**: Alertas urgentes a supervisores

### `POST /api/conversations/:id/transfer-department`
**Usado por**: Chat UI (agentes/supervisores)
**Propósito**: Transferir a otro departamento

**Responsabilidad de Chat Realtime**:
- ✅ Actualizar departamento asignado
- ✅ Buscar agente disponible en departamento destino
- ✅ Preservar contexto e historial
- ✅ Actualizar routing info

**Se delega a Tools**:
- 🔧 **Department Routing Tool**: Lógica avanzada de departamentos
- 🔧 **Context Preservation Tool**: Preservar contexto relevante

---

## 👥 Gestión de Agentes

### `POST /api/conversations/:id/pause`
**Usado por**: Chat UI (agentes)
**Propósito**: Pausar conversación temporalmente

**Responsabilidad de Chat Realtime**:
- ✅ Cambiar estado a 'paused'
- ✅ Enviar mensaje automático al cliente
- ✅ Liberar parcialmente capacidad del agente
- ✅ Programar reanudación automática si se especifica

**Se delega a Tools**:
- 🔧 **Auto Message Tool**: Generar mensaje de pausa personalizado
- 🔧 **Schedule Tool**: Programar reanudación automática

### `POST /api/conversations/:id/resume`
**Usado por**: Chat UI (agentes), procesos automáticos
**Propósito**: Reanudar conversación pausada

**Responsabilidad de Chat Realtime**:
- ✅ Cambiar estado a 'active'
- ✅ Restaurar capacidad del agente
- ✅ Enviar mensaje de reanudación opcional
- ✅ Reiniciar timer de inactividad

### `PUT /api/agents/:id/status`
**Usado por**: Session Manager, Chat UI
**Propósito**: Actualizar estado de agente

**Responsabilidad de Chat Realtime**:
- ✅ Actualizar disponibilidad en Firebase
- ✅ Actualizar lista de conversaciones activas
- ✅ Manejar reasignaciones si agente se desconecta
- ✅ Actualizar timestamp de última actividad

**Se delega a Tools**:
- 🔧 **Reassignment Tool**: Reasignar chats de agente desconectado
- 🔧 **Workforce Analytics Tool**: Métricas de disponibilidad

### `PUT /api/agents/:id/capacity`
**Usado por**: Chat UI (supervisores)
**Propósito**: Establecer límites de conversaciones

**Responsabilidad de Chat Realtime**:
- ✅ Actualizar límite de chats concurrentes
- ✅ Validar que no excede límite actual
- ✅ Reasignar conversaciones si es necesario

---

## 👁️ Supervisión

### `POST /api/conversations/:id/supervise`
**Usado por**: Chat UI (supervisores)
**Propósito**: Iniciar supervisión de conversación

**Responsabilidad de Chat Realtime**:
- ✅ Agregar supervisor a conversación
- ✅ Marcar como supervisada
- ✅ Crear registro de intervención
- ✅ Notificar al agente

**Se delega a Tools**:
- 🔧 **Coaching Analytics Tool**: Registrar patrones de supervisión

### `DELETE /api/conversations/:id/supervise`
**Usado por**: Chat UI (supervisores)
**Propósito**: Terminar supervisión

**Responsabilidad de Chat Realtime**:
- ✅ Remover supervisor de conversación
- ✅ Registrar duración de supervisión
- ✅ Crear nota de intervención automática

### `POST /api/conversations/:id/supervisor-request`
**Usado por**: Chat UI (agentes)
**Propósito**: Solicitar ayuda de supervisor

**Responsabilidad de Chat Realtime**:
- ✅ Crear solicitud de supervisor
- ✅ Encontrar supervisor disponible
- ✅ Notificar urgencia
- ✅ Cambiar estado a 'supervisor_requested'

**Se delega a Tools**:
- 🔧 **Supervisor Alert Tool**: Notificaciones urgentes
- 🔧 **Queue Management Tool**: Priorizar solicitudes

---

## 📝 Notas y Contexto

### `POST /api/conversations/:id/notes`
**Usado por**: Chat UI (agentes/supervisores)
**Propósito**: Agregar nota interna

**Responsabilidad de Chat Realtime**:
- ✅ Crear nota en Firebase
- ✅ Asignar categoría y prioridad
- ✅ Establecer visibilidad
- ✅ Actualizar timestamp

### `PUT /api/conversations/:id/notes/:noteId`
**Usado por**: Chat UI (agentes - solo propias notas)
**Propósito**: Editar nota existente

**Responsabilidad de Chat Realtime**:
- ✅ Validar permisos de edición
- ✅ Actualizar contenido
- ✅ Mantener historial de cambios

### `PUT /api/conversations/:id/tags`
**Usado por**: Chat UI (agentes/supervisores)
**Propósito**: Gestionar etiquetas

**Responsabilidad de Chat Realtime**:
- ✅ Actualizar tags en Firebase
- ✅ Validar tags permitidos

**Se delega a Tools**:
- 🔧 **Tag Analytics Tool**: Análisis de patterns de etiquetado

---

## 📊 Exportación y Limpieza

### `POST /api/conversations/:id/close`
**Usado por**: Chat UI (agentes), procesos automáticos
**Propósito**: Cerrar conversación

**Responsabilidad de Chat Realtime**:
- ✅ Cambiar estado a 'closed'
- ✅ Liberar capacidad del agente
- ✅ Calcular métricas básicas (duración, mensajes)
- ✅ Marcar para exportación
- ✅ Crear mensaje de cierre

**Se delega a Tools**:
- 🔧 **Transcript Export Tool**: Generar PDF del transcript
- 🔧 **Email Tool**: Enviar transcript al cliente
- 🔧 **Analytics Tool**: Procesar métricas detalladas
- 🔧 **Archive Tool**: Archivar conversación
- 🔧 **Satisfaction Survey Tool**: Enviar encuesta de satisfacción

### `POST /api/conversations/:id/reopen`
**Usado por**: Chat UI (supervisores), APIs externas
**Propósito**: Reabrir conversación cerrada

**Responsabilidad de Chat Realtime**:
- ✅ Cambiar estado a 'active'
- ✅ Reasignar al agente original o buscar disponible
- ✅ Crear mensaje de reapertura
- ✅ Restaurar contexto

### `POST /api/conversations/:id/export`
**Usado por**: Chat UI (agentes/supervisores)
**Propósito**: Exportar transcript bajo demanda

**Responsabilidad de Chat Realtime**:
- ✅ Extraer datos básicos de conversación
- ✅ Preparar estructura para exportación
- ✅ Marcar solicitud de exportación

**Se delega a Tools**:
- 🔧 **Export Generator Tool**: Crear PDF/HTML/JSON según formato
- 🔧 **Email Delivery Tool**: Enviar por correo
- 🔧 **File Storage Tool**: Guardar en cloud storage

---

## 📈 Estados y Métricas

### `GET /api/conversations/:id/history`
**Usado por**: Chat UI
**Propósito**: Obtener historial completo

**Responsabilidad de Chat Realtime**:
- ✅ Recuperar todos los mensajes
- ✅ Obtener historial de transferencias
- ✅ Incluir notas visibles
- ✅ Formatear cronológicamente

### `GET /api/conversations/:id/metrics`
**Usado por**: Chat UI (supervisores)
**Propósito**: Métricas de conversación específica

**Responsabilidad de Chat Realtime**:
- ✅ Calcular métricas básicas en tiempo real
- ✅ Tiempo de primera respuesta
- ✅ Tiempo promedio de respuesta
- ✅ Número de transferencias

**Se delega a Tools**:
- 🔧 **Advanced Metrics Tool**: Análisis detallado de performance
- 🔧 **Sentiment Analytics Tool**: Análisis de satisfacción
- 🔧 **Efficiency Analytics Tool**: Métricas de eficiencia

### `GET /api/health`
**Usado por**: Load balancers, monitoring
**Propósito**: Health check del servicio

**Responsabilidad de Chat Realtime**:
- ✅ Verificar conexión a Firebase
- ✅ Verificar estado del servicio
- ✅ Retornar métricas básicas

---

## 🎯 Resumen de Arquitectura

### **Chat Realtime SE ENCARGA DE:**
- ✅ **Estado y Persistencia**: Todo lo relacionado con Firebase
- ✅ **Lógica de Negocio Core**: Asignaciones, transferencias, estados
- ✅ **Coordinación**: Orquestar procesos y disparar tools
- ✅ **Tiempo Real**: Mantener sincronización en Firebase
- ✅ **Validaciones Básicas**: Permisos, estados válidos

### **Tools EXTERNOS SE ENCARGAN DE:**
- 🔧 **Procesamiento Pesado**: PDFs, emails, análisis complejos
- 🔧 **Integraciones**: Sistemas externos, APIs terceros
- 🔧 **Analytics Avanzados**: Métricas complejas, ML, IA
- 🔧 **Notificaciones**: Push, SMS, webhooks
- 🔧 **Archivado**: Limpieza, compresión, backup

**Total de Endpoints**: ~35 endpoints específicos + 1 endpoint inteligente
**Patrón**: Chat Realtime = Coordinador inteligente, Tools = Ejecutores especializados