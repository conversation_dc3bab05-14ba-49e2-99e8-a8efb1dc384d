#!/bin/bash

# Fix remaining dot notation patterns one by one

# 1. Replace 'transferInfo.currentTransfer': null, patterns
sed -i '' "s/'transferInfo\.currentTransfer': null,//" src/conversationService.ts
sed -i '' "s/'transferInfo\.history': transferHistory/transferHistory: transferHistory/" src/conversationService.ts

# 2. Replace 'metadata.escalationLevel' pattern  
sed -i '' "s/'metadata\.escalationLevel': currentEscalationLevel,//" src/conversationService.ts

# 3. Replace 'escalation.*' patterns
sed -i '' "s/'escalation\.escalationId': escalationId,//" src/conversationService.ts
sed -i '' "s/'escalation\.reason': reason,//" src/conversationService.ts
sed -i '' "s/'escalation\.escalatedBy': escalatedBy,//" src/conversationService.ts
sed -i '' "s/'escalation\.escalatedAt': now,//" src/conversationService.ts
sed -i '' "s/'escalation\.priority': priority,//" src/conversationService.ts
sed -i '' "s/'escalation\.notes': notes//" src/conversationService.ts

# 4. Replace 'supervision.*' patterns
sed -i '' "s/'supervision\.isSupervised': true,//" src/conversationService.ts
sed -i '' "s/'supervision\.supervisorId': supervisorId,//" src/conversationService.ts
sed -i '' "s/'supervision\.supervisionId': supervisionId,//" src/conversationService.ts
sed -i '' "s/'supervision\.mode': mode,//" src/conversationService.ts
sed -i '' "s/'supervision\.startedAt': now,//" src/conversationService.ts
sed -i '' "s/'supervision\.reason': reason || 'Quality assurance',//" src/conversationService.ts
sed -i '' "s/'supervision\.status': 'active'//" src/conversationService.ts

sed -i '' "s/'supervision\.isSupervised': false,//" src/conversationService.ts
sed -i '' "s/'supervision\.endedAt': now,//" src/conversationService.ts
sed -i '' "s/'supervision\.duration': supervisionDuration,//" src/conversationService.ts
sed -i '' "s/'supervision\.status': 'completed',//" src/conversationService.ts
sed -i '' "s/'supervision\.endNotes': notes,//" src/conversationService.ts
sed -i '' "s/'supervision\.rating': rating//" src/conversationService.ts

sed -i '' "s/'supervision\.mode': newMode,//" src/conversationService.ts
sed -i '' "s/'supervision\.modeChangedAt': now,//" src/conversationService.ts

echo "✅ Simple dot notation fixes applied"
