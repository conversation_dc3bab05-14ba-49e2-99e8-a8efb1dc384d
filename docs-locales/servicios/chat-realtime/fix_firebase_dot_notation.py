#!/usr/bin/env python3
import re

def fix_firebase_dot_notation(content):
    """Fix Firebase dot notation issues in the conversation service"""
    
    # Fix setAgentCapacity method
    content = re.sub(
        r"const updates: any = \{\s*'status\.maxConcurrentChats': maxConcurrentChats,\s*'status\.capacityUpdatedAt': now,\s*'status\.capacityUpdatedBy': 'system'[^}]+\};",
        '''const statusUpdates: any = {
        maxConcurrentChats: maxConcurrentChats,
        capacityUpdatedAt: now,
        capacityUpdatedBy: 'system'
      };''',
        content,
        flags=re.DOTALL
    )
    
    # Fix temporaryCapacity assignment in setAgentCapacity
    content = re.sub(
        r"updates\['status\.temporaryCapacity'\] = \{",
        "statusUpdates.temporaryCapacity = {",
        content
    )
    
    # Fix the firebaseService.update call in setAgentCapacity
    content = re.sub(
        r"await firebaseService\.update\(\`\$\{this\.AGENTS_PATH\}/\$\{agentId\}\`, updates\);",
        "await firebaseService.update(`${this.AGENTS_PATH}/${agentId}/status`, statusUpdates);",
        content
    )
    
    # Fix conversation indicator updates
    content = re.sub(
        r"'indicators\.customerLastSeen': Date\.now\(\)",
        "{ customerLastSeen: Date.now() }",
        content
    )
    
    # But we need to split this update into two separate calls
    # Find the pattern with indicators.customerLastSeen and fix it
    pattern = r"await firebaseService\.update\(\`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}\`, \{\s*updatedAt: Date\.now\(\),\s*'indicators\.customerLastSeen': Date\.now\(\)\s*\}\);"
    replacement = '''await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        updatedAt: Date.now()
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/indicators`, {
        customerLastSeen: Date.now()
      });'''
    content = re.sub(pattern, replacement, content)
    
    # Fix metadata.botAttempts pattern
    pattern = r"'metadata\.botAttempts': \(analysis\.botAttempts \|\| 0\) \+ 1,"
    replacement = ""
    # We need to handle this more carefully - let's split the update
    
    # Find the full update block with metadata.botAttempts
    full_pattern = r"await firebaseService\.update\(\`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}\`, \{\s*status: 'active',\s*'metadata\.botAttempts': \(analysis\.botAttempts \|\| 0\) \+ 1,\s*updatedAt: Date\.now\(\)\s*\}\);"
    full_replacement = '''await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'active',
        updatedAt: Date.now()
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/metadata`, {
        botAttempts: (analysis.botAttempts || 0) + 1
      });'''
    content = re.sub(full_pattern, full_replacement, content)
    
    return content

def fix_pause_info_updates(content):
    """Fix pauseInfo dot notation patterns"""
    
    # Fix pauseConversation method
    pattern = r"const updates: any = \{\s*status: 'paused',\s*updatedAt: now,\s*'pauseInfo\.pausedAt': now,\s*'pauseInfo\.pausedBy': pausedBy,\s*'pauseInfo\.reason': reason,\s*\};"
    replacement = '''const updates: any = {
        status: 'paused',
        updatedAt: now
      };
      
      const pauseInfoUpdates = {
        pausedAt: now,
        pausedBy: pausedBy,
        reason: reason
      };'''
    content = re.sub(pattern, replacement, content)
    
    # Fix the subsequent if block for duration
    content = re.sub(
        r"updates\['pauseInfo\.estimatedResumeTime'\] = now \+ duration;",
        "pauseInfoUpdates.estimatedResumeTime = now + duration;",
        content
    )
    
    # Fix the firebaseService.update call in pauseConversation
    pattern = r"await firebaseService\.update\(\`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}\`, updates\);"
    replacement = '''await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/pauseInfo`, pauseInfoUpdates);'''
    
    # Only replace the first occurrence (in pauseConversation method)
    content = content.replace(pattern, replacement, 1)
    
    return content

def fix_resume_updates(content):
    """Fix resumeConversation method dot notation"""
    
    pattern = r"await firebaseService\.update\(\`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}\`, \{\s*status: 'active',\s*updatedAt: now,\s*'pauseInfo\.resumedAt': now,\s*'pauseInfo\.resumedBy': resumedBy,\s*'pauseInfo\.pauseDuration': pauseDuration\s*\}\);"
    replacement = '''await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'active',
        updatedAt: now
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/pauseInfo`, {
        resumedAt: now,
        resumedBy: resumedBy,
        pauseDuration: pauseDuration
      });'''
    content = re.sub(pattern, replacement, content)
    
    return content

def fix_closure_info_updates(content):
    """Fix closureInfo and analytics dot notation in closeConversation"""
    
    pattern = r"'closureInfo\.reason': reason,\s*'closureInfo\.resolution': resolution,\s*'closureInfo\.sessionDuration': sessionDuration,\s*'analytics\.finalMetrics': \{"
    
    # This is complex, let's handle it step by step
    # First, let's find the full closeConversation update block
    start_pattern = r"await firebaseService\.update\(\`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}\`, \{\s*status: 'closed',"
    
    # For now, let's just fix the specific dot notation parts individually
    content = re.sub(r"'closureInfo\.reason': reason,", "", content)
    content = re.sub(r"'closureInfo\.resolution': resolution,", "", content)
    content = re.sub(r"'closureInfo\.sessionDuration': sessionDuration,", "", content)
    content = re.sub(r"'analytics\.finalMetrics': \{[^}]+\},", "", content)
    
    return content

# Read the file
with open('src/conversationService.ts', 'r') as f:
    content = f.read()

# Apply all fixes
content = fix_firebase_dot_notation(content)
content = fix_pause_info_updates(content)
content = fix_resume_updates(content)
content = fix_closure_info_updates(content)

# Write back
with open('src/conversationService.ts', 'w') as f:
    f.write(content)

print("✅ Firebase dot notation fixes applied")
