#!/usr/bin/env python3
import re

def fix_transfer_accept(content):
    """Fix acceptTransfer method"""
    pattern = r"""await firebaseService\.update\(`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}`, \{
        status: 'active',
        assignedTo: acceptedBy,
        assignedAt: now,
        updatedAt: now,
        'transferInfo\.currentTransfer\.status': 'completed',
        'transferInfo\.currentTransfer\.acceptedAt': now,
        'transferInfo\.currentTransfer\.acceptedBy': acceptedBy,
        'transferInfo\.currentTransfer\.transferDuration': transferDuration
      \}\);"""
    
    replacement = """await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        status: 'active',
        assignedTo: acceptedBy,
        assignedAt: now,
        updatedAt: now
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo/currentTransfer`, {
        status: 'completed',
        acceptedAt: now,
        acceptedBy: acceptedBy,
        transferDuration: transferDuration
      });"""
    
    content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    return content

def fix_transfer_timeout(content):
    """Fix transfer timeout updates"""
    # Fix first occurrence (reassignment)
    pattern1 = r"""'transferInfo\.currentTransfer': null,
          'transferInfo\.history': transferHistory"""
    replacement1 = """transferHistory: transferHistory"""
    
    content = re.sub(pattern1, replacement1, content)
    
    # Add separate transferInfo updates after main updates
    pattern2 = r"""await firebaseService\.update\(`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}`, \{
          assignedTo: alternativeAgent\.id,
          assignedAt: now,
          updatedAt: now,
          transferHistory: transferHistory
        \}\);"""
    
    replacement2 = """await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
          assignedTo: alternativeAgent.id,
          assignedAt: now,
          updatedAt: now
        });
        
        await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, {
          currentTransfer: null,
          history: transferHistory
        });"""
    
    content = re.sub(pattern2, replacement2, content, flags=re.DOTALL)
    return content

def fix_queue_return(content):
    """Fix return to queue updates"""
    pattern1 = r"""'transferInfo\.currentTransfer': null,
          'transferInfo\.history': transferHistory"""
    replacement1 = ""
    
    # Find the second occurrence for queue return
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if "'transferInfo.currentTransfer': null," in line and i > 1100:  # Second occurrence
            # Replace the block
            if i+1 < len(lines) and "'transferInfo.history': transferHistory" in lines[i+1]:
                lines[i] = "          transferHistory: transferHistory"
                lines[i+1] = ""
    
    content = '\n'.join(lines)
    
    # Fix the update call
    pattern2 = r"""await firebaseService\.update\(`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}`, \{
          assignedTo: null,
          assignedAt: null,
          updatedAt: now,
          departmentId,
          transferHistory: transferHistory
        \}\);"""
    
    replacement2 = """await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
          assignedTo: null,
          assignedAt: null,
          updatedAt: now,
          departmentId
        });
        
        await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, {
          currentTransfer: null,
          history: transferHistory
        });"""
    
    content = re.sub(pattern2, replacement2, content, flags=re.DOTALL)
    return content

def fix_escalation_updates(content):
    """Fix escalateConversation method"""
    # Find the escalation updates block
    pattern = r"""const updates: any = \{
        priority: newPriorityValue,
        updatedAt: now,
        'metadata\.escalationLevel': currentEscalationLevel,
        'escalation\.escalationId': escalationId,
        'escalation\.reason': reason,
        'escalation\.escalatedBy': escalatedBy,
        'escalation\.escalatedAt': now,
        'escalation\.priority': priority,
        'escalation\.notes': notes
      \};"""
    
    replacement = """const updates: any = {
        priority: newPriorityValue,
        updatedAt: now
      };
      
      const metadataUpdates = {
        escalationLevel: currentEscalationLevel
      };
      
      const escalationUpdates = {
        escalationId: escalationId,
        reason: reason,
        escalatedBy: escalatedBy,
        escalatedAt: now,
        priority: priority,
        notes: notes
      };"""
    
    content = re.sub(pattern, replacement, content, flags=re.DOTALL)
    
    # Fix the assignedSupervisor handling
    content = re.sub(
        r"updates\['escalation\.assignedSupervisor'\] = assignedSupervisor;",
        "escalationUpdates.assignedSupervisor = assignedSupervisor;",
        content
    )
    
    # Fix the firebaseService.update call
    pattern2 = r"await firebaseService\.update\(`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}`, updates\);"
    replacement2 = """await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/metadata`, metadataUpdates);
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/escalation`, escalationUpdates);"""
    
    content = content.replace(pattern2, replacement2, 1)  # Only first occurrence
    return content

def fix_supervision_updates(content):
    """Fix supervision-related updates"""
    # Fix startSupervision
    pattern1 = r"""const updates: any = \{
        updatedAt: now,
        'supervision\.isSupervised': true,
        'supervision\.supervisorId': supervisorId,
        'supervision\.supervisionId': supervisionId,
        'supervision\.mode': mode, // 'observe' or 'participate'
        'supervision\.startedAt': now,
        'supervision\.reason': reason \|\| 'Quality assurance',
        'supervision\.status': 'active'
      \};"""
    
    replacement1 = """const updates: any = {
        updatedAt: now
      };
      
      const supervisionUpdates = {
        isSupervised: true,
        supervisorId: supervisorId,
        supervisionId: supervisionId,
        mode: mode, // 'observe' or 'participate'
        startedAt: now,
        reason: reason || 'Quality assurance',
        status: 'active'
      };"""
    
    content = re.sub(pattern1, replacement1, content, flags=re.DOTALL)
    
    # Fix endSupervision
    pattern2 = r"""const updates: any = \{
        updatedAt: now,
        'supervision\.isSupervised': false,
        'supervision\.endedAt': now,
        'supervision\.duration': supervisionDuration,
        'supervision\.status': 'completed',
        'supervision\.endNotes': notes,
        'supervision\.rating': rating
      \};"""
    
    replacement2 = """const updates: any = {
        updatedAt: now
      };
      
      const supervisionUpdates = {
        isSupervised: false,
        endedAt: now,
        duration: supervisionDuration,
        status: 'completed',
        endNotes: notes,
        rating: rating
      };"""
    
    content = re.sub(pattern2, replacement2, content, flags=re.DOTALL)
    
    # Fix changeSupervisionMode
    pattern3 = r"""await firebaseService\.update\(`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}`, \{
        'supervision\.mode': newMode,
        'supervision\.modeChangedAt': now,
        updatedAt: now
      \}\);"""
    
    replacement3 = """await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        updatedAt: now
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/supervision`, {
        mode: newMode,
        modeChangedAt: now
      });"""
    
    content = re.sub(pattern3, replacement3, content, flags=re.DOTALL)
    
    # Fix the firebaseService.update calls for supervision
    content = re.sub(
        r"await firebaseService\.update\(`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}`, updates\);",
        """await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, updates);
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/supervision`, supervisionUpdates);""",
        content
    )
    
    return content

def fix_simple_transfer_null(content):
    """Fix simple transferInfo.currentTransfer: null patterns"""
    pattern = r"'transferInfo\.currentTransfer': null,"
    replacement = ""
    content = re.sub(pattern, replacement, content)
    
    # Add separate updates where needed
    # This is for acceptTransferRequest method
    pattern2 = r"""await firebaseService\.update\(`\$\{this\.CONVERSATIONS_PATH\}/\$\{conversationId\}`, \{
        assignedTo: agentId,
        assignedAt: now,
        updatedAt: now,
      \}\);"""
    
    replacement2 = """await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}`, {
        assignedTo: agentId,
        assignedAt: now,
        updatedAt: now
      });
      
      await firebaseService.update(`${this.CONVERSATIONS_PATH}/${conversationId}/transferInfo`, {
        currentTransfer: null
      });"""
    
    content = re.sub(pattern2, replacement2, content, flags=re.DOTALL)
    return content

# Read the file
with open('src/conversationService.ts', 'r') as f:
    content = f.read()

# Apply all fixes
print("Fixing transfer accept...")
content = fix_transfer_accept(content)

print("Fixing transfer timeout...")  
content = fix_transfer_timeout(content)

print("Fixing queue return...")
content = fix_queue_return(content)

print("Fixing escalation updates...")
content = fix_escalation_updates(content)

print("Fixing supervision updates...")
content = fix_supervision_updates(content)

print("Fixing simple transfer null...")
content = fix_simple_transfer_null(content)

# Write back
with open('src/conversationService.ts', 'w') as f:
    f.write(content)

print("✅ All remaining Firebase dot notation fixes applied")
