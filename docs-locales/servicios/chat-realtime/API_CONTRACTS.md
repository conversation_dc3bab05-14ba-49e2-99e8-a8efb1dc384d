# Chat Realtime API Contracts

Este documento define los contratos exactos (request/response) para todos los endpoints del servicio Chat Realtime. Estos contratos servirán como especificación para crear tipos TypeScript y validaciones.

## Convenciones Generales

### Response Format Estándar
```typescript
{
  success: boolean;
  data?: any;           // Solo si success = true
  error?: {             // Solo si success = false
    code: string;
    message: string;
    details?: any;
  }
  timestamp: string;    // ISO 8601
}
```

### Error Codes Estándar
- `VALIDATION_ERROR` - Request inválido
- `CONVERSATION_NOT_FOUND` - Conversación no existe
- `AGENT_NOT_FOUND` - Agente no existe
- `PERMISSION_DENIED` - Sin permisos
- `INTERNAL_ERROR` - Error del servidor
- `NOT_IMPLEMENTED` - Endpoint no implementado aún

---

## 1. Core Message Processing

### POST /api/process-message
**Descripción:** Endpoint principal que procesa mensajes entrantes y toma decisiones inteligentes.

#### Request
```typescript
{
  // Message data from PubSub
  id: string;                    // Unique message ID
  from: string;                  // Customer phone/identifier
  body: string;                  // Message content
  channel: string;               // 'whatsapp' | 'web' | 'sms'
  type?: string;                 // 'text' (default)
  timestamp: number;             // Unix timestamp
  
  // Session context
  sessionId: string;             // From Session Manager
  
  // Optional message metadata
  metadata?: {
    messageId?: string;          // External message ID (WhatsApp, etc)
    replyTo?: string;           // If replying to specific message
    [key: string]: any;         // Additional metadata
  }
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    processed: boolean;
    conversationId: string;
    action: 'bot_response' | 'agent_assigned' | 'queued' | 'transferred' | 'escalated';
    
    // Optional response to send back to customer
    response?: {
      to: string;                // Customer identifier
      body: string;              // Response message
      channel: string;           // Channel to send via
      sessionId: string;         // Session identifier
      messageType: 'bot' | 'agent' | 'system';
    };
    
    // Conversation state
    conversation: {
      id: string;
      status: 'new' | 'queued' | 'active' | 'paused' | 'transferred' | 'closed';
      departmentId: string;
      agentId?: string;          // If assigned
      queuePosition?: number;    // If queued
    };
    
    // Processing details
    processing: {
      aiAnalysis?: {
        department: string;
        confidence: number;
        intent?: string;
        needsHuman: boolean;
      };
      botAttempts: number;
      escalationReason?: string;
    };
  };
  timestamp: string;
}
```

---

## 2. Conversation Management

### GET /api/conversations/:id
**Descripción:** Obtener detalles completos de una conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Query Parameters (optional)
query: {
  includeMessages?: boolean;     // Default: true
  messageLimit?: number;         // Default: 50
  includeAnalytics?: boolean;    // Default: false
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversation: {
      id: string;
      metadata: {
        customerId: string;
        customerName?: string;
        channel: string;
        status: string;
        priority: 'low' | 'normal' | 'high' | 'urgent';
        createdAt: string;
        updatedAt: string;
        lastActivityAt: string;
      };
      
      routingInfo: {
        assignedDepartment: string;
        aiAnalysisAttempts: number;
        departmentAssignedAt: string;
        aiAnalysisHistory?: Array<{
          attempt: number;
          input: string;
          result: string;
          confidence?: number;
          timestamp: string;
        }>;
      };
      
      assignment: {
        agentId?: string;
        assignedAt?: string;
        assignedBy: 'system' | 'supervisor' | 'transfer';
        previousAgents: Array<{
          agentId: string;
          assignedAt: string;
          unassignedAt: string;
          reason: string;
        }>;
      };
      
      transfer?: {
        status: 'pending' | 'completed' | 'failed';
        reason: string;
        fromDepartment: string;
        toDepartment: string;
        fromAgent?: string;
        toAgent?: string;
        requestedAt: string;
        completedAt?: string;
      };
      
      indicators: {
        customerTyping: boolean;
        agentTyping: boolean;
        customerLastSeen: string;
        agentLastSeen?: string;
      };
    };
    
    messages?: Array<{
      id: string;
      type: 'text' | 'system';
      content: {
        body: string;
        timestamp: string;
      };
      sender: {
        type: 'customer' | 'agent' | 'bot' | 'system';
        id: string;
        name: string;
        department?: string;
      };
      metadata?: {
        [key: string]: any;
      };
    }>;
    
    analytics?: {
      messageCount: {
        total: number;
        customer: number;
        agent: number;
        bot: number;
      };
      responseTime: {
        firstResponse?: number;    // milliseconds
        averageResponse?: number;
        longestWait?: number;
      };
      sessionDuration?: number;    // milliseconds
    };
  };
  timestamp: string;
}
```

### POST /api/conversations
**Descripción:** Crear nueva conversación manualmente.

#### Request
```typescript
{
  customerId: string;            // Customer identifier
  customerName?: string;         // Customer display name
  channel: string;               // 'whatsapp' | 'web' | 'sms'
  departmentId?: string;         // Force specific department
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  metadata?: {
    [key: string]: any;
  };
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    status: 'new';
    departmentId: string;
    queuePosition?: number;
    createdAt: string;
  };
  timestamp: string;
}
```

### PUT /api/conversations/:id/status
**Descripción:** Cambiar estado de conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  status: 'active' | 'paused' | 'closed';
  reason?: string;               // Reason for status change
  agentId?: string;              // Agent making the change
  metadata?: {
    [key: string]: any;
  };
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    previousStatus: string;
    newStatus: string;
    updatedAt: string;
    updatedBy: string;
  };
  timestamp: string;
}
```

---

## 3. Message Operations

### POST /api/conversations/:id/messages
**Descripción:** Enviar mensaje en una conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  type: 'text' | 'system';
  content: {
    body: string;
  };
  sender: {
    type: 'agent' | 'bot' | 'system';
    id: string;                  // Agent ID, bot ID, or 'system'
    name: string;
    department?: string;
  };
  metadata?: {
    isTemplateResponse?: boolean;
    templateId?: string;
    responseTime?: number;       // milliseconds
    [key: string]: any;
  };
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    messageId: string;
    conversationId: string;
    timestamp: string;
    deliveryStatus: 'sent' | 'queued' | 'failed';
  };
  timestamp: string;
}
```

### GET /api/conversations/:id/messages
**Descripción:** Obtener mensajes de una conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Query Parameters
query: {
  limit?: number;                // Default: 50
  offset?: number;               // Default: 0
  since?: string;                // ISO timestamp
  messageTypes?: string;         // Comma separated: 'text,system'
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    messages: Array<{
      id: string;
      type: 'text' | 'system';
      content: {
        body: string;
        timestamp: string;
      };
      sender: {
        type: 'customer' | 'agent' | 'bot' | 'system';
        id: string;
        name: string;
        department?: string;
      };
      metadata?: {
        [key: string]: any;
      };
    }>;
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
}
```

---

## 4. Agent Assignment

### POST /api/conversations/:id/assign
**Descripción:** Asignar agente a conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  agentId?: string;              // Specific agent, or auto-assign if omitted
  departmentId?: string;         // Prefer agents from this department
  priority?: 'normal' | 'urgent';
  assignedBy: string;            // Agent or supervisor ID making assignment
  reason?: string;               // Assignment reason
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    agentId: string;
    assignedAt: string;
    assignedBy: string;
    previousStatus: string;
    newStatus: 'active';
    estimatedResponseTime?: number; // seconds
  };
  timestamp: string;
}
```

### DELETE /api/conversations/:id/assign
**Descripción:** Desasignar agente de conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  reason: string;                // Required: reason for unassignment
  transferToDepartment?: string; // Transfer to different department
  unassignedBy: string;          // Agent or supervisor ID
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    previousAgentId: string;
    unassignedAt: string;
    unassignedBy: string;
    newStatus: 'queued';
    queuePosition?: number;
  };
  timestamp: string;
}
```

---

## 5. Transfer Operations

### POST /api/conversations/:id/transfer
**Descripción:** Transferir conversación a otro agente o departamento.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  transferType: 'agent' | 'department';
  targetAgentId?: string;        // Required if transferType = 'agent'
  targetDepartmentId?: string;   // Required if transferType = 'department'
  reason: string;                // Required: transfer reason
  notes?: string;                // Additional context for receiving agent
  transferredBy: string;         // Current agent ID
  priority?: 'normal' | 'urgent';
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    transferId: string;
    conversationId: string;
    transferType: 'agent' | 'department';
    fromAgentId?: string;
    toAgentId?: string;
    fromDepartmentId: string;
    toDepartmentId: string;
    status: 'pending' | 'completed';
    queuePosition?: number;       // If transferred to department queue
    estimatedWaitTime?: number;   // seconds
    transferredAt: string;
  };
  timestamp: string;
}
```

### GET /api/conversations/:id/transfer/status
**Descripción:** Verificar estado de transferencia.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    transfer?: {
      transferId: string;
      status: 'pending' | 'completed' | 'failed';
      transferType: 'agent' | 'department';
      fromAgentId?: string;
      toAgentId?: string;
      fromDepartmentId: string;
      toDepartmentId: string;
      reason: string;
      requestedAt: string;
      completedAt?: string;
      failureReason?: string;
    };
  };
  timestamp: string;
}
```

---

## 6. Queue Management

### GET /api/queues
**Descripción:** Obtener estado de todas las colas.

#### Request
```typescript
// Query Parameters (optional)
query: {
  departmentId?: string;         // Filter by specific department
  includeConversations?: boolean; // Default: false
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    queues: Array<{
      departmentId: string;
      departmentName: string;
      statistics: {
        current: {
          waitingCount: number;
          averageWaitTime: number;   // seconds
          longestWait: number;       // seconds
          activeAgents: number;
          availableAgents: number;
        };
        today: {
          totalProcessed: number;
          averageWaitTime: number;
          longestWait: number;
          escalations: number;
          abandoned: number;
        };
      };
      agents: {
        available: string[];       // Agent IDs
        busy: string[];
        away: string[];
        offline: string[];
      };
      conversations?: Array<{
        conversationId: string;
        customerId: string;
        addedAt: string;
        waitTime: number;          // seconds
        priority: string;
      }>;
    }>;
    globalStatistics: {
      totalWaiting: number;
      totalActiveAgents: number;
      averageWaitTime: number;
    };
  };
  timestamp: string;
}
```

### GET /api/queues/:departmentId
**Descripción:** Obtener estado específico de una cola.

#### Request
```typescript
// URL Parameters
params: {
  departmentId: string;
}

// Query Parameters
query: {
  includeConversations?: boolean; // Default: true
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    queue: {
      departmentId: string;
      departmentName: string;
      meta: {
        priority: 'low' | 'normal' | 'high';
        maxWaitTime: number;       // seconds
        escalationThreshold: number; // seconds
        lastUpdated: string;
      };
      statistics: {
        current: {
          waitingCount: number;
          averageWaitTime: number;
          longestWait: number;
          activeAgents: number;
          availableAgents: number;
        };
        today: {
          totalProcessed: number;
          averageWaitTime: number;
          longestWait: number;
          escalations: number;
          abandoned: number;
        };
      };
      agents: {
        available: Array<{
          agentId: string;
          name: string;
          currentConversations: number;
          maxConversations: number;
        }>;
        busy: Array<{
          agentId: string;
          name: string;
          currentConversations: number;
          maxConversations: number;
        }>;
        away: string[];
        offline: string[];
      };
      conversations?: Array<{
        conversationId: string;
        customerId: string;
        customerName?: string;
        addedAt: string;
        waitTime: number;
        priority: 'low' | 'normal' | 'high' | 'urgent';
        lastMessage?: string;
      }>;
    };
  };
  timestamp: string;
}
```

---

## 7. Agent Management

### GET /api/agents
**Descripción:** Obtener lista de agentes con su estado.

#### Request
```typescript
// Query Parameters (optional)
query: {
  departmentId?: string;         // Filter by department
  status?: string;               // Filter by availability status
  includeStatistics?: boolean;   // Default: false
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    agents: Array<{
      id: string;
      profile: {
        name: string;
        email: string;
        departments: string[];
        primaryDepartment: string;
        role: 'agent' | 'supervisor' | 'admin';
        skillLevel: 'junior' | 'mid' | 'senior' | 'expert';
      };
      status: {
        availability: 'available' | 'busy' | 'away' | 'offline';
        currentConversations: string[];  // Conversation IDs
        maxConversations: number;
        lastActivity: string;
        connectedAt?: string;
      };
      statistics?: {
        today: {
          conversationsHandled: number;
          averageResponseTime: number;  // seconds
          customerSatisfactionAvg?: number;
          onlineTime: number;           // seconds
        };
      };
    }>;
    summary: {
      total: number;
      available: number;
      busy: number;
      away: number;
      offline: number;
    };
  };
  timestamp: string;
}
```

### GET /api/agents/:agentId
**Descripción:** Obtener detalles específicos de un agente.

#### Request
```typescript
// URL Parameters
params: {
  agentId: string;
}

// Query Parameters
query: {
  includeConversations?: boolean; // Default: false
  includeStatistics?: boolean;    // Default: true
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    agent: {
      id: string;
      profile: {
        name: string;
        email: string;
        avatar?: string;
        departments: string[];
        primaryDepartment: string;
        role: 'agent' | 'supervisor' | 'admin';
        skillLevel: 'junior' | 'mid' | 'senior' | 'expert';
        languages: string[];
        createdAt: string;
      };
      status: {
        availability: 'available' | 'busy' | 'away' | 'offline';
        currentConversations: string[];
        maxConversations: number;
        lastActivity: string;
        connectedAt?: string;
        workingHours?: {
          timezone: string;
          schedule: {
            [day: string]: { start: string; end: string; } | null;
          };
        };
      };
      statistics: {
        today: {
          conversationsHandled: number;
          averageResponseTime: number;
          customerSatisfactionAvg?: number;
          onlineTime: number;
          lastUpdated: string;
        };
        thisWeek: {
          conversationsHandled: number;
          averageResponseTime: number;
          customerSatisfactionAvg?: number;
          totalOnlineTime: number;
        };
        thisMonth: {
          conversationsHandled: number;
          averageResponseTime: number;
          customerSatisfactionAvg?: number;
          totalOnlineTime: number;
        };
      };
      preferences?: {
        notifications: {
          newConversation: boolean;
          transfer: boolean;
          mention: boolean;
          escalation: boolean;
        };
        workload: 'light' | 'normal' | 'heavy';
        autoAcceptTransfers: boolean;
      };
      conversations?: Array<{
        conversationId: string;
        customerId: string;
        customerName?: string;
        status: string;
        assignedAt: string;
        lastActivity: string;
      }>;
    };
  };
  timestamp: string;
}
```

---

## 8. Analytics & Reporting

### GET /api/analytics/conversations
**Descripción:** Obtener métricas de conversaciones.

#### Request
```typescript
// Query Parameters
query: {
  period: 'today' | 'week' | 'month' | 'custom';
  startDate?: string;            // Required if period = 'custom'
  endDate?: string;              // Required if period = 'custom'
  departmentId?: string;         // Filter by department
  agentId?: string;              // Filter by agent
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    metrics: {
      totalConversations: number;
      activeConversations: number;
      closedConversations: number;
      averageSessionDuration: number;    // minutes
      averageResponseTime: number;       // seconds
      averageWaitTime: number;          // seconds
      customerSatisfactionAvg?: number;  // 1-5
      escalationRate: number;           // percentage
      resolutionRate: number;           // percentage
    };
    breakdown: {
      byStatus: {
        [status: string]: number;
      };
      byDepartment: {
        [departmentId: string]: {
          name: string;
          count: number;
          averageWaitTime: number;
          resolutionRate: number;
        };
      };
      byChannel: {
        [channel: string]: number;
      };
      byHour: Array<{
        hour: number;               // 0-23
        conversations: number;
        averageWaitTime: number;
      }>;
    };
    trends: {
      conversationsOverTime: Array<{
        date: string;               // ISO date
        count: number;
      }>;
      responseTimeOverTime: Array<{
        date: string;
        averageResponseTime: number;
      }>;
    };
  };
  timestamp: string;
}
```

---

## 9. Health & Status

### GET /api/health
**Descripción:** Health check del servicio.

#### Request
```typescript
// No parameters
```

#### Response
```typescript
{
  success: boolean;
  data: {
    service: 'chat-realtime';
    status: 'healthy' | 'degraded' | 'unhealthy';
    version: string;
    uptime: number;                // seconds
    checks: {
      firebase: 'healthy' | 'unhealthy';
      pubsub: 'healthy' | 'unhealthy';
      memory: {
        status: 'healthy' | 'warning' | 'critical';
        usage: number;             // percentage
      };
    };
    features: {
      realTimeMessaging: boolean;
      aiAnalysis: boolean;
      agentAssignment: boolean;
      analytics: boolean;
    };
  };
  timestamp: string;
}
```

### GET /api/status
**Descripción:** Estado detallado del sistema.

#### Request
```typescript
// Query Parameters (optional)
query: {
  includeQueues?: boolean;       // Default: true
  includeAgents?: boolean;       // Default: true
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    system: {
      version: string;
      environment: 'development' | 'production';
      startedAt: string;
      uptime: number;
      currentTime: string;
    };
    counters: {
      totalConversations: number;
      activeConversations: number;
      totalAgents: number;
      availableAgents: number;
      totalMessages: number;
    };
    queues?: Array<{
      departmentId: string;
      departmentName: string;
      waitingCount: number;
      averageWaitTime: number;
      availableAgents: number;
    }>;
    agents?: {
      total: number;
      available: number;
      busy: number;
      away: number;
      offline: number;
    };
    performance: {
      averageResponseTime: number;   // milliseconds
      uptime: number;               // percentage
      errorRate: number;            // percentage
    };
  };
  timestamp: string;
}
```

---

## 10. Missing MVP Endpoints - Críticos

### POST /api/conversations/:id/pause
**Descripción:** Pausar conversación temporalmente.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  reason: string;                // Required: pause reason
  duration?: number;             // Optional: auto-resume in seconds
  pausedBy: string;              // Agent ID
  message?: string;              // Optional message to customer
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    previousStatus: string;
    newStatus: 'paused';
    pausedAt: string;
    pausedBy: string;
    autoResumeAt?: string;       // If duration specified
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/resume
**Descripción:** Reanudar conversación pausada.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  resumedBy: string;             // Agent ID
  message?: string;              // Optional message to customer
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    previousStatus: 'paused';
    newStatus: 'active';
    resumedAt: string;
    resumedBy: string;
    pauseDuration: number;       // seconds
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/escalate
**Descripción:** Escalar conversación a supervisor.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  reason: string;                // Required: escalation reason
  priority: 'normal' | 'urgent' | 'critical';
  escalatedBy: string;           // Agent ID
  notes?: string;                // Additional context
  requestSupervisor?: string;    // Specific supervisor ID
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    escalationId: string;
    previousPriority: string;
    newPriority: string;
    escalatedAt: string;
    escalatedBy: string;
    assignedSupervisor?: string;
    estimatedResponseTime?: number; // seconds
  };
  timestamp: string;
}
```

### PUT /api/agents/:agentId/status
**Descripción:** Actualizar estado de disponibilidad del agente.

#### Request
```typescript
// URL Parameters
params: {
  agentId: string;
}

// Request Body
{
  availability: 'available' | 'busy' | 'away' | 'offline';
  updatedBy: string;             // Agent ID or supervisor ID
  reason?: string;               // Reason for status change
  autoReturn?: number;           // Auto-return to available in seconds
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    agentId: string;
    previousStatus: string;
    newStatus: string;
    updatedAt: string;
    updatedBy: string;
    affectedConversations: Array<{
      conversationId: string;
      action: 'transferred' | 'queued' | 'maintained';
      newAgentId?: string;
    }>;
    autoReturnAt?: string;
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/notes
**Descripción:** Agregar nota interna a conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  content: string;               // Note content
  type: 'general' | 'issue' | 'resolution' | 'transfer';
  visibility: 'private' | 'team' | 'department' | 'all';
  priority?: 'low' | 'normal' | 'high';
  createdBy: string;             // Agent ID
  tags?: string[];               // Optional tags
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    noteId: string;
    conversationId: string;
    content: string;
    type: string;
    visibility: string;
    createdAt: string;
    createdBy: string;
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/typing
**Descripción:** Actualizar indicador de escritura.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  isTyping: boolean;
  agentId: string;
  timeout?: number;              // Auto-clear typing in seconds (default: 10)
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    agentId: string;
    typingStatus: boolean;
    updatedAt: string;
    autoTimeoutAt?: string;
  };
  timestamp: string;
}
```

### GET /api/conversations/:id/messages
**Descripción:** Obtener mensajes con paginación mejorada.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Query Parameters
query: {
  limit?: number;                // Default: 50, Max: 200
  offset?: number;               // Default: 0
  since?: string;                // ISO timestamp
  until?: string;                // ISO timestamp
  messageTypes?: string;         // Comma separated: 'text,system'
  includeMeta?: boolean;         // Include message metadata
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    messages: Array<{
      id: string;
      type: 'text' | 'system';
      content: {
        body: string;
        timestamp: string;
      };
      sender: {
        type: 'customer' | 'agent' | 'bot' | 'system';
        id: string;
        name: string;
        department?: string;
      };
      metadata?: {
        responseTime?: number;
        isTemplateResponse?: boolean;
        confidence?: number;
        [key: string]: any;
      };
      readBy?: Array<{
        agentId: string;
        readAt: string;
      }>;
    }>;
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    conversationMeta: {
      status: string;
      lastActivity: string;
      participantCount: number;
    };
  };
  timestamp: string;
}
```

---

## 11. Endpoints Críticos Adicionales - MVP

### POST /api/conversations/:id/close
**Descripción:** Cerrar conversación completamente.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  reason?: string;               // Closure reason
  closedBy: string;              // Agent ID
  sendSurvey?: boolean;          // Send satisfaction survey
  resolution?: string;           // Resolution description
  tags?: string[];               // Final tags
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    previousStatus: string;
    newStatus: 'closed';
    closedAt: string;
    closedBy: string;
    sessionDuration: number;       // Total duration in seconds
    finalMetrics: {
      messageCount: {
        total: number;
        customer: number;
        agent: number;
        bot: number;
      };
      responseTime: {
        firstResponse: number;     // seconds
        averageResponse: number;
      };
    };
    surveyScheduled?: boolean;
  };
  timestamp: string;
}
```

### GET /api/conversations
**Descripción:** Listar conversaciones del agente o supervisar todas.

#### Request
```typescript
// Query Parameters
query: {
  agentId?: string;              // Filter by agent (supervisors can omit)
  status?: string;               // Filter by status
  department?: string;           // Filter by department
  limit?: number;                // Default: 20, Max: 100
  offset?: number;               // Default: 0
  sortBy?: 'lastActivity' | 'createdAt' | 'priority';
  sortOrder?: 'asc' | 'desc';
  includeMetrics?: boolean;      // Include basic metrics
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversations: Array<{
      id: string;
      metadata: {
        customerId: string;
        customerName?: string;
        channel: string;
        status: string;
        priority: string;
        lastActivityAt: string;
        unreadCount?: number;      // Unread messages for agent
      };
      assignment: {
        agentId?: string;
        agentName?: string;
        department: string;
      };
      preview: {
        lastMessage: string;
        lastMessageType: 'customer' | 'agent' | 'bot';
        lastMessageAt: string;
      };
      metrics?: {
        messageCount: number;
        responseTime?: number;
        waitTime?: number;
      };
    }>;
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
    summary: {
      totalActive: number;
      totalQueued: number;
      totalPaused: number;
    };
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/supervise
**Descripción:** Iniciar supervisión de conversación (supervisor).

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  supervisorId: string;          // Supervisor ID
  mode: 'observe' | 'participate'; // Supervision mode
  reason?: string;               // Supervision reason
  notifyAgent?: boolean;         // Default: true
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    supervisionId: string;
    supervisorId: string;
    mode: string;
    startedAt: string;
    agentNotified: boolean;
    supervisionStatus: 'active';
  };
  timestamp: string;
}
```

### DELETE /api/conversations/:id/supervise
**Descripción:** Terminar supervisión de conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  supervisorId: string;          // Supervisor ID
  notes?: string;                // Supervision notes
  feedback?: string;             // Feedback for agent
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    supervisionId: string;
    supervisorId: string;
    endedAt: string;
    supervisionDuration: number;   // seconds
    noteCreated: boolean;
    feedbackProvided: boolean;
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/supervisor-request
**Descripción:** Solicitar ayuda de supervisor (agente).

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  requestedBy: string;           // Agent ID
  urgency: 'normal' | 'high' | 'critical';
  reason: string;                // Reason for supervisor request
  requestSupervisor?: string;    // Specific supervisor ID
  context?: string;              // Additional context
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    requestId: string;
    conversationId: string;
    requestedBy: string;
    urgency: string;
    requestedAt: string;
    status: 'pending' | 'assigned' | 'responded';
    assignedSupervisor?: string;
    estimatedResponseTime?: number; // seconds
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/accept-transfer
**Descripción:** Aceptar transferencia pendiente.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  acceptedBy: string;            // Agent ID accepting transfer
  message?: string;              // Optional message to customer
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    transferId: string;
    acceptedBy: string;
    acceptedAt: string;
    previousStatus: 'pending_transfer';
    newStatus: 'active';
    transferDuration: number;      // Time in transfer state (seconds)
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/reject-transfer
**Descripción:** Rechazar transferencia con motivo.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  rejectedBy: string;            // Agent ID rejecting transfer
  reason: string;                // Required: rejection reason
  message?: string;              // Optional internal message
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    transferId: string;
    rejectedBy: string;
    rejectedAt: string;
    reason: string;
    nextAction: 'reassigning' | 'returning_to_queue';
    newAssignedAgent?: string;     // If automatically reassigned
    queuePosition?: number;        // If returned to queue
  };
  timestamp: string;
}
```

### PUT /api/agents/:agentId/capacity
**Descripción:** Establecer límites de conversaciones por agente (supervisor).

#### Request
```typescript
// URL Parameters
params: {
  agentId: string;
}

// Request Body
{
  maxConversations: number;      // New conversation limit
  updatedBy: string;             // Supervisor ID
  reason?: string;               // Reason for change
  temporary?: boolean;           // If temporary change
  expiresAt?: string;            // When to revert (if temporary)
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    agentId: string;
    previousCapacity: number;
    newCapacity: number;
    updatedBy: string;
    updatedAt: string;
    temporary: boolean;
    expiresAt?: string;
    affectedConversations: Array<{
      conversationId: string;
      action: 'maintained' | 'transferred' | 'queued';
      newAgentId?: string;
    }>;
  };
  timestamp: string;
}
```

---

## 12. Supervisión Avanzada - Crítico MVP

### POST /api/conversations/:id/supervise/message
**Descripción:** Supervisor envía mensaje en conversación supervisada (modo participate).

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  supervisorId: string;          // Supervisor ID
  type: 'supervisor_message' | 'supervisor_note';
  content: {
    body: string;                // Message content
  };
  visibility: 'all' | 'agent_only'; // Who can see this message
  metadata?: {
    supervisorName: string;      // Display name
    messageToAgent?: string;     // Private note to agent
    [key: string]: any;
  };
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    messageId: string;
    conversationId: string;
    supervisorId: string;
    timestamp: string;
    visibility: string;
    agentNotified: boolean;       // If agent was notified of supervisor participation
    customerNotified: boolean;    // If customer sees supervisor message
  };
  timestamp: string;
}
```

### GET /api/conversations/:id/supervision/status
**Descripción:** Verificar estado actual de supervisión de conversación.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    isBeingSupervised: boolean;
    supervision?: {
      supervisorId: string;
      supervisorName: string;
      mode: 'observe' | 'participate';
      startedAt: string;
      duration: number;          // seconds since started
      messagesFromSupervisor: number;
      lastActivity: string;
    };
    agentNotifications: {
      supervisionStarted: boolean;
      supervisorParticipating: boolean;
      hasUnreadSupervisorNotes: boolean;
    };
  };
  timestamp: string;
}
```

### PUT /api/conversations/:id/supervise/mode
**Descripción:** Cambiar modo de supervisión (observe ↔ participate).

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  supervisorId: string;          // Supervisor ID
  newMode: 'observe' | 'participate';
  reason?: string;               // Reason for mode change
  notifyAgent?: boolean;         // Default: true
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    conversationId: string;
    supervisorId: string;
    previousMode: string;
    newMode: string;
    changedAt: string;
    agentNotified: boolean;
    systemMessage: {
      messageId: string;         // Auto-generated system message ID
      content: string;           // "Supervisor is now participating in conversation"
    };
  };
  timestamp: string;
}
```

### POST /api/conversations/:id/supervise/coach
**Descripción:** Supervisor envía mensaje privado de coaching al agente.

#### Request
```typescript
// URL Parameters
params: {
  id: string;                    // Conversation ID
}

// Request Body
{
  supervisorId: string;          // Supervisor ID
  message: string;               // Coaching message
  type: 'tip' | 'correction' | 'encouragement' | 'instruction';
  priority: 'low' | 'normal' | 'high';
  requiresResponse?: boolean;    // Agent must acknowledge
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    coachingId: string;
    conversationId: string;
    supervisorId: string;
    message: string;
    type: string;
    priority: string;
    sentAt: string;
    requiresResponse: boolean;
    agentNotified: boolean;
    deliveryStatus: 'sent' | 'delivered' | 'read';
  };
  timestamp: string;
}
```

### GET /api/conversations/supervised
**Descripción:** Listar conversaciones actualmente bajo supervisión (dashboard supervisor).

#### Request
```typescript
// Query Parameters
query: {
  supervisorId?: string;         // Filter by supervisor
  mode?: 'observe' | 'participate';
  department?: string;
  status?: 'active' | 'paused';
  limit?: number;                // Default: 20
  offset?: number;               // Default: 0
}
```

#### Response
```typescript
{
  success: boolean;
  data: {
    supervisedConversations: Array<{
      conversationId: string;
      customer: {
        id: string;
        name: string;
      };
      agent: {
        id: string;
        name: string;
        department: string;
      };
      supervision: {
        supervisorId: string;
        supervisorName: string;
        mode: 'observe' | 'participate';
        startedAt: string;
        duration: number;
        messagesFromSupervisor: number;
        coachingMessages: number;
      };
      conversationStatus: string;
      lastActivity: string;
      priority: string;
      unreadMessages: number;
    }>;
    summary: {
      totalSupervised: number;
      byMode: {
        observe: number;
        participate: number;
      };
      byDepartment: {
        [departmentId: string]: number;
      };
    };
    pagination: {
      total: number;
      limit: number;
      offset: number;
      hasMore: boolean;
    };
  };
  timestamp: string;
}
```

---

## Notas de Implementación

### Validación
- Todos los requests deben ser validados contra estos contratos
- Usar TypeScript types generados desde estos contratos
- Implementar validación en runtime con libraries como Joi o Zod

### Manejo de Errores
- Siempre retornar la estructura estándar de error
- Incluir códigos de error específicos y mensajes descriptivos
- Log detallado para debugging interno

### Performance
- Implementar paginación donde sea apropiado
- Cachear responses frecuentes
- Usar índices apropiados en Firebase

### Security
- Validar permisos de agente para operaciones
- Sanitizar inputs para prevenir injection
- Rate limiting en endpoints críticos