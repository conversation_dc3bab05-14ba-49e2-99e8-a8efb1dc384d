# Bot Human Router Service

## 📋 Resumen

Servicio que decide si un mensaje debe ser procesado por un bot (n8n) o por un agente humano, y enruta accordingly.

- **Puerto**: 3004  
- **Estado**: ✅ COMPLETO - Decision engine + ConversationStateService implementado
- **Responsabilidad**: Decision engine con estado + passthrough inteligente

## 🎯 Responsabilidad Específica

### ✅ Lo que SÍ debe hacer Bot Human Router:
- Recibir mensajes vía PubSub INBOUND
- **Consultar estado de conversación SIEMPRE** (ConversationStateService)
- **Decision Engine**: Primera vez → intentar N8N → fallback humano
- **Passthrough inteligente**: N8N asignado → N8N, humano asignado → Chat Realtime
- **Estado persistente**: Guardar assignedTo (n8n/human/unassigned)

### ❌ Lo que NO hace:
- ❌ Asignación de agentes (eso es Chat Realtime)
- ❌ Gestión de Firebase (eso es Chat Realtime)
- ❌ Lógica de conversaciones (eso es Chat Realtime)

## 📡 Arquitectura Actual

### ✅ Implementado (2025-08-22)
- **ConversationStateService**: Estado persistente de conversaciones ✅ 
- **Decision Engine refactorado**: processInboundMessage + makeInitialDecision + passthrough ✅
- **PubSub INBOUND Listener**: `/pubsub/inbound` ✅
- **Passthrough inteligente**: N8N y humano según estado ✅
- **Error handling**: Robusto ✅

### 📋 Pendiente (no crítico)
- **N8N real workflows**: Actualmente mock N8N (ya funcional)
- **Persistent storage**: ConversationStateService usa in-memory (MVP)

## 🔄 Flujo Implementado (2025-08-22)

### Flujo Completo con Estado
```
PubSub INBOUND → Bot Human Router → ConversationStateService.getState()
                                   ↓
                      ┌─ unassigned: makeInitialDecision()
                      │   ├── Try N8N → status 200 → save 'n8n' → N8N control
                      │   └── N8N fail → save 'human' → Chat Realtime assign
                      │
                      ├─ n8n: passthrough directo → N8N webhook
                      │
                      └─ human: passthrough directo → Chat Realtime
```

## 🏗️ Arquitectura Implementada (2025-08-22)

### 1. ConversationStateService ✅
```typescript
interface ConversationState {
  conversationId: string;
  assignedTo: 'n8n' | 'human' | 'unassigned';
  assignedAt: Date;
  department: string;
}
```

### 2. Decision Engine ✅  
```typescript
// decisionEngine.ts - REFACTORED
async processInboundMessage(message: InboundMessage): Promise<ProcessingResult>
async makeInitialDecision(message: InboundMessage): Promise<DecisionResult>
// Passthrough methods for assigned conversations
```

### 3. N8N Integration ✅
Mock funcional, endpoints listos para N8N real workflows.

## 🔧 REFACTOR COMPLETO (2025-08-22)
- **ConversationStateService**: Interface + in-memory storage MVP implementado ✅
- **Decision Engine**: Refactorado con estado persistente + passthrough ✅
- **processInboundMessage**: Consulta estado SIEMPRE antes de decidir ✅
- **Passthrough inteligente**: N8N vs humano según estado guardado ✅
- **TypeScript**: Compilación limpia en todo el servicio ✅

## 📊 Estado Actual (COMPLETO)

- **ConversationStateService**: ✅ Estado persistente funcional
- **Decision Engine**: ✅ Lógica completa con passthrough inteligente
- **PubSub INBOUND**: ✅ Listener funcional
- **N8N Integration**: ✅ Mock funcional, listo para workflows reales
- **Chat Realtime Integration**: ✅ Passthrough humano funcional

## 📁 Documentación Disponible

- **TEST_SUMMARY.md**: Resumen de testing del servicio

## 🎯 Próximos Pasos (No críticos)

1. **N8N real workflows**: Reemplazar mock con workflows reales
2. **Persistent storage**: Redis/Database para ConversationStateService
3. **Monitoring**: Health checks y debugging tools
4. **Performance testing**: Validar passthrough bajo carga

---

**Última actualización**: 2025-08-22  
**Estado**: ✅ COMPLETO - Decision engine + ConversationStateService + passthrough inteligente implementado