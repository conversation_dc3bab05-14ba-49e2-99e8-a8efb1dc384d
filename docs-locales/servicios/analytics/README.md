# Analytics Service

## 📋 Resumen

Servicio para colección de métricas, análisis de performance y generación de KPIs usando Supabase como storage.

- **Puerto**: 3006 (propuesto)
- **Estado**: 📋 Pendiente de implementación
- **Responsabilidad**: Event collection + KPI reporting + metrics dashboard

## 🎯 Responsabilidad Específica

### ✅ Lo que SÍ debe hacer Analytics Service:
- **Event Collection**: Capturar eventos de todos los servicios
- **Metrics Processing**: Procesar y agregar métricas en tiempo real
- **KPI Reporting**: Generar reportes y dashboards
- **Historical Data**: Almacenar y consultar datos históricos

## 🗄️ Arquitectura de Datos

### Supabase Tables (PostgreSQL)
```sql
-- Eventos del sistema (particionado por fecha)
analytics_events (
  id, organization_id, event_type, event_data, 
  timestamp, service_source, session_id
)

-- Métricas agregadas por período
metrics_summary (
  id, organization_id, metric_type, value,
  period_type, period_start, period_end
)

-- KPIs calculados
kpi_snapshots (
  id, organization_id, kpi_name, value,
  calculation_timestamp, metadata
)
```

### Event Types a Capturar
```typescript
ConversationEvents = {
  STARTED: "conversation.started",
  ASSIGNED: "conversation.assigned", 
  TRANSFERRED: "conversation.transferred",
  ESCALATED: "conversation.escalated",
  CLOSED: "conversation.closed",
  DEPARTMENT_ASSIGNED: "department.assigned"
}

AgentEvents = {
  LOGIN: "agent.login",
  LOGOUT: "agent.logout", 
  STATUS_CHANGE: "agent.status_change",
  MESSAGE_SENT: "agent.message_sent"
}

SystemEvents = {
  BOT_HANDOFF: "bot.handoff_to_human",
  ERROR_OCCURRED: "system.error"
}
```

## 📊 KPIs Principales

### Response Metrics
- **First Response Time**: Tiempo hasta primera respuesta del agente
- **Average Response Time**: Tiempo promedio de respuesta
- **Resolution Time**: Tiempo total de resolución

### Volume Metrics
- **Conversations per Hour/Day**: Volumen de conversaciones
- **Messages per Conversation**: Promedio de mensajes
- **Active Agents**: Agentes activos por período

### Quality Metrics  
- **Transfer Rate**: Porcentaje de conversaciones transferidas
- **Escalation Rate**: Porcentaje de escalaciones
- **Agent Utilization**: Uso de capacidad por agente

### Department Metrics
- **Assignment Accuracy**: Precisión del AI department assignment
- **Department Load**: Carga por departamento
- **Cross-Department Transfers**: Transferencias entre departamentos

## 🔄 Flujo de Eventos

### Event Collection
```
Service → PubSub Analytics Topic → Analytics Service → Supabase
```

### Real-time Processing
```
Analytics Service → Process Events → Update Aggregates → Broadcast Metrics
```

### Reporting
```
Dashboard Request → Analytics API → Query Supabase → Format Response
```

## 📡 API Endpoints Esperados

### Event Collection
```typescript
POST /api/events        // Receive events from services
POST /api/events/batch  // Batch event processing
```

### Metrics & KPIs
```typescript
GET /api/metrics/realtime    // Real-time metrics
GET /api/metrics/historical  // Historical data
GET /api/kpis               // Current KPIs
GET /api/reports/:type      // Specific reports
```

### Dashboard Data
```typescript
GET /api/dashboard/agent/:id     // Agent-specific metrics
GET /api/dashboard/department    // Department performance  
GET /api/dashboard/system       // System-wide metrics
```

## 🛠️ Tecnologías Requeridas

### Data Processing
```typescript
// Event processing
interface EventProcessor {
  processEvent(event: AnalyticsEvent): Promise<void>;
  aggregateMetrics(events: AnalyticsEvent[]): MetricsSummary;
  calculateKPIs(timeframe: TimeFrame): KPISnapshot[];
}
```

### Storage & Querying
- **Supabase**: PostgreSQL con partitioning por fecha
- **Edge Functions**: Processing pesado en Supabase
- **Real-time subscriptions**: Para métricas live

### Visualization (Future)
- Dashboard embebido en Chat UI
- Export capabilities (PDF, Excel)
- Real-time metric widgets

## 🔧 Configuración Requerida

### Variables de Entorno
```bash
SUPABASE_URL=<supabase_instance>
SUPABASE_SERVICE_KEY=<service_role_key>  # Para writes
PUBSUB_ANALYTICS_TOPIC=analytics-events
PUBSUB_SUBSCRIPTION=analytics-processor
```

### Supabase Setup
```sql
-- Partitioning para performance
CREATE TABLE analytics_events_y2025m01 PARTITION OF analytics_events 
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- Índices para queries frecuentes  
CREATE INDEX idx_analytics_events_org_type_timestamp 
ON analytics_events(organization_id, event_type, timestamp);
```

## 📈 Métricas de Performance

### Event Processing
- **Target**: 1000+ events/minute
- **Latency**: < 100ms event processing
- **Storage**: Efficient partitioning por fecha

### Query Performance
- **Dashboard Load**: < 2s para métricas básicas
- **Historical Reports**: < 10s para reports complejos
- **Real-time Updates**: < 1s refresh rate

## 🚧 Dependencias

### Upstream Event Sources
- **Chat Realtime**: Conversation lifecycle events
- **Bot Human Router**: Routing decisions
- **Channel Router**: Department assignments  
- **Session Manager**: Session metrics
- **Chat UI**: User interaction events

### Downstream Consumers
- **Chat UI**: Real-time metrics dashboard
- **Supervisor Dashboard**: Management reports
- **External BI**: Data exports

## 🎯 Plan de Implementación

### Phase 1: Event Collection
1. Setup PubSub subscription para events
2. Basic event storage en Supabase
3. Event validation y processing

### Phase 2: Metrics Processing
1. Real-time metrics calculation
2. Historical aggregation
3. KPI computation engine

### Phase 3: Reporting & Dashboard
1. API endpoints para metrics  
2. Dashboard integration con Chat UI
3. Export capabilities

## 🎯 Próximos Pasos

1. **Setup Supabase tables** con partitioning
2. **Implementar event collection** via PubSub
3. **Basic metrics processing** 
4. **Integration con otros servicios** para event emission
5. **Dashboard API** para Chat UI integration

---

**Última actualización**: 2025-08-19  
**Estado**: 📋 Diseño completo - Listo para implementación (priority tras core system completion)