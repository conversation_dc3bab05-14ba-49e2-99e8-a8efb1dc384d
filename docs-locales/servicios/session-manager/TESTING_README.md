# Session Manager Tests

This directory contains comprehensive test suites for the Session Manager service.

## Test Structure

```
tests/
├── setup/
│   └── testSetup.ts          # Global test configuration
├── integration/
│   └── redis.integration.test.ts  # Redis integration tests
├── sessionService.test.ts    # Core business logic tests
├── routes.test.ts           # API endpoint tests
└── README.md               # This file
```

## Test Categories

### Unit Tests
- **sessionService.test.ts**: Tests core CRUD operations, Redis interactions, error handling
- **routes.test.ts**: Tests API endpoints, HTTP status codes, request/response handling

### Integration Tests
- **redis.integration.test.ts**: Tests with real Redis instance (optional)

## Running Tests

### All Tests
```bash
npm test
```

### Unit Tests Only
```bash
npm test -- --selectProjects unit
```

### Integration Tests Only
```bash
npm test -- --selectProjects integration
```

### Watch Mode
```bash
npm run test:watch
```

### Coverage Report
```bash
npm run test:coverage
```

## Test Coverage

The test suite aims for:
- **80%** line coverage
- **80%** function coverage
- **80%** branch coverage
- **80%** statement coverage

## Critical Test Cases Covered

### SessionService
- ✅ Session creation with valid data
- ✅ Session retrieval (exists/not exists)
- ✅ Session updates (partial/full)
- ✅ Session deletion
- ✅ Redis error handling
- ✅ Data validation and transformation
- ✅ TTL management

### API Routes
- ✅ POST /sessions (201, 500)
- ✅ GET /sessions/:id (200, 404, 500)
- ✅ PUT /sessions/:id (200, 404, 500)
- ✅ DELETE /sessions/:id (204, 404, 500)
- ✅ Error handling and validation
- ✅ Request/response format validation

### Integration
- ✅ Redis connection and operations
- ✅ Concurrent session handling
- ✅ TTL verification
- ✅ Performance under load

## Environment Setup

### For Unit Tests
No external dependencies required - uses mocks.

### For Integration Tests
Requires Redis server:

```bash
# Docker
docker run --name redis-test -p 6379:6379 -d redis:alpine

# Enable integration tests
export REDIS_TEST_ENABLED=true
npm test -- --selectProjects integration
```

## Mock Strategy

- **Redis**: Mocked for unit tests, real for integration tests
- **Express**: Uses supertest for request/response testing
- **Console**: Mocked to reduce test noise
- **Date**: Mocked where timestamp consistency is needed

## Key Testing Patterns

### Mocking Redis
```typescript
const mockRedisClient = {
  setEx: jest.fn(),
  get: jest.fn(),
  del: jest.fn(),
};
```

### API Testing
```typescript
const response = await request(app)
  .post('/sessions')
  .send(createRequest);
```

### Error Scenarios
```typescript
mockRedisClient.get.mockRejectedValue(new Error('Redis error'));
await expect(service.getSession('id')).rejects.toThrow('Redis error');
```

## Maintenance

- Update tests when adding new features
- Maintain high coverage for critical paths
- Keep mocks in sync with real implementations
- Review integration test performance regularly