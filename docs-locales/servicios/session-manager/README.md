# Session Manager Service

## 📋 Resumen

Servicio **COMPLETO** responsable únicamente de la gestión de sesiones usuario en Redis.

- **Puerto**: 3001
- **Estado**: ✅ Completamente implementado, funcional y compilación limpia
- **Responsabilidad**: Solo phoneNumber ↔ sessionId en Redis (TTL 2 horas)

## 🎯 Responsabilidad Específica

### ✅ Lo que SÍ hace Session Manager:
- Gestión de sesiones: `phoneNumber` → `sessionId` en Redis
- TTL automático de 2 horas
- Limpieza de números de teléfono (formato consistente)
- Health check de Redis
- Endpoints para Channel Router

### ❌ Lo que NO hace (común confusión):
- ❌ Asignación de agentes (eso es Chat Realtime)
- ❌ Gestión de estados de agentes (eso es Chat Realtime)
- ❌ Workload balancing (eso es Chat Realtime)
- ❌ Queue management (eso es Chat Realtime)

## 📡 API Endpoints

### `POST /api/session`
**Usado por**: Channel Router
**Propósito**: Obtener o crear session ID para un número de teléfono

### `GET /api/sessions/:id`
**Usado por**: Debugging/monitoring
**Propósito**: Obtener sesión por ID

### `POST /api/sessions/:id/activity`
**Usado por**: Servicios que mantienen sesiones activas
**Propósito**: Actualizar timestamp de actividad

### `GET /api/health`
**Usado por**: Load balancers
**Propósito**: Health check y estado de Redis

## 🔧 Configuración

### Variables de Entorno
```bash
REDIS_HOST=localhost
REDIS_PORT=6379
SESSION_TTL=7200  # 2 horas
```

### Redis Keys Pattern
```
session:phone:{phoneNumber} → sessionData
session:id:{sessionId} → sessionData (mismo datos)
```

## 📊 Métricas

- **4 endpoints únicamente**
- **149 líneas de código**
- **Completamente testado**
- **Redis connection health monitoring**

## 📁 Documentación Disponible

- **TEST_SUMMARY.md**: Resumen de testing del servicio
- **TESTING_README.md**: Documentación de tests (del directorio tests/)

## ✅ Estado: COMPLETO

Este servicio está **100% completo** según su responsabilidad en la arquitectura. No requiere cambios adicionales.

### Flujo de Uso
```
1. Channel Router → Session Manager (/api/session)
2. Session Manager → Redis (get/create session)
3. Session Manager → Channel Router (sessionId)
4. Channel Router usa sessionId para otros servicios
```

---

**Última actualización**: 2025-08-19  
**Estado**: ✅ COMPLETO - Dependencies limpias y compilación funcional