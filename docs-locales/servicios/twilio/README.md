# Twilio Service

## 📋 Resumen

Servicio para integración con Twilio WhatsApp Business API - manejo de webhooks entrantes y envío de mensajes.

- **Puerto**: 3000
- **Estado**: 🔄 Estructura básica implementada
- **Responsabilidad**: Integración WhatsApp via Twilio webhooks

## 🎯 Responsabilidad Específica

### ✅ Lo que SÍ debe hacer Twilio Service:
- Recibir webhooks de Twilio (WhatsApp messages)
- Enviar mensajes a Channel Router
- Manejar mensajes salientes hacia WhatsApp
- Gestión de status callbacks de Twilio

## 📡 Arquitectura Esperada

### Webhooks Entrantes
```
WhatsApp → Twilio → Twilio Service → Channel Router
```

### Mensajes Salientes  
```
System → PubSub OUTBOUND → Twilio Service → Twilio → WhatsApp
```

## 📊 Estado Actual

- **Estructura básica**: Implementada
- **Testing framework**: Disponible (ver TESTING_README.md)
- **Webhooks**: Pendiente de implementación completa
- **Twilio SDK**: Pendiente de configuración

## 📁 Documentación Disponible

- **TESTING_README.md**: Documentación de testing framework

## 🛠️ Pendiente de Implementación

### 1. Webhook Endpoints
```typescript
POST /webhook/incoming  // Mensajes entrantes de WhatsApp
POST /webhook/status    // Status callbacks de Twilio
```

### 2. Twilio SDK Integration
```typescript
const twilio = require('twilio');
const client = twilio(accountSid, authToken);
```

### 3. Message Sending
```typescript
interface TwilioService {
  sendWhatsAppMessage(to: string, body: string): Promise<void>;
  handleIncomingMessage(twilioPayload: any): Promise<void>;
}
```

### 4. PubSub Integration
- Subscriber para PubSub OUTBOUND
- Envío de mensajes recibidos a Channel Router

## 🔧 Configuración Requerida

### Variables de Entorno
```bash
TWILIO_ACCOUNT_SID=<account_sid>
TWILIO_AUTH_TOKEN=<auth_token>  
TWILIO_WHATSAPP_NUMBER=<whatsapp_business_number>
CHANNEL_ROUTER_URL=http://localhost:3002
NGROK_URL=<ngrok_tunnel_for_webhooks>  # Para desarrollo
```

### Webhook Configuration (Twilio Console)
```
Incoming Messages: {NGROK_URL}/webhook/incoming
Status Callbacks: {NGROK_URL}/webhook/status  
```

## 🚧 Bloqueadores

1. **Twilio Account Setup**: Necesita cuenta dev con WhatsApp Business API
2. **ngrok Setup**: Para webhooks locales durante desarrollo
3. **Channel Router Integration**: Envío de mensajes procesados

## 🎯 Próximos Pasos

1. **Configurar Twilio account** (dev/sandbox)
2. **Implementar webhook handlers** para mensajes entrantes
3. **Integrar con Channel Router** 
4. **Setup ngrok** para webhooks locales
5. **Testing end-to-end** WhatsApp → Twilio → System

## 📱 WhatsApp Integration Notes

### Desarrollo
- Usar Twilio Sandbox para WhatsApp
- ngrok para webhook tunneling
- Test messages desde número personal

### Producción
- WhatsApp Business API aprobado
- Webhook endpoints en Cloud Run
- Phone number verification

---

**Última actualización**: 2025-08-19  
**Estado**: 📋 Estructura básica - Pendiente implementación (priority tras core system completion)