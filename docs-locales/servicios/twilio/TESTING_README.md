# Twilio Service Tests

This directory contains comprehensive test suites for the Twilio webhook service.

## Test Structure

```
tests/
├── setup/
│   └── testSetup.ts          # Global test configuration
├── webhookHandler.test.ts    # Core webhook processing logic
├── server.test.ts           # Server integration and middleware tests
└── README.md               # This file
```

## Test Categories

### Unit Tests
- **webhookHandler.test.ts**: Tests message processing, transformation, error handling
- **server.test.ts**: Tests Express server, middleware, routing, health checks

## Running Tests

### All Tests
```bash
npm test
```

### Watch Mode
```bash
npm run test:watch
```

### Coverage Report
```bash
npm run test:coverage
```

## Test Coverage

The test suite aims for:
- **75%** line coverage
- **75%** function coverage
- **75%** branch coverage
- **75%** statement coverage

## Critical Test Cases Covered

### WebhookHandler
- ✅ Valid WhatsApp message processing
- ✅ Media message handling
- ✅ Phone number cleaning (whatsapp: prefix removal)
- ✅ Message format transformation (Twilio → Internal)
- ✅ Empty/undefined body handling
- ✅ Error handling for malformed requests
- ✅ Timestamp generation
- ✅ Console logging verification

### Server Integration
- ✅ POST /webhook with form data
- ✅ POST /webhook with JSON data
- ✅ GET /health endpoint
- ✅ Middleware functionality (URL-encoded, JSON parsing)
- ✅ Error handling and status codes
- ✅ Large payload handling
- ✅ Invalid endpoint handling (404)
- ✅ Performance expectations

## Webhook Message Formats

### Twilio Input Format
```typescript
{
  MessageSid: 'SM**********',
  From: 'whatsapp:+**********',
  To: 'whatsapp:+**********',
  Body: 'Message content',
  NumMedia: '1',
  MediaUrl0: 'https://api.twilio.com/media/123.jpg',
  MediaContentType0: 'image/jpeg'
}
```

### Internal Format (Transformed)
```typescript
{
  id: 'SM**********',
  from: '+**********',
  to: '+**********',
  body: 'Message content',
  mediaUrl: 'https://api.twilio.com/media/123.jpg',
  mediaType: 'image/jpeg',
  timestamp: '2023-01-01T12:00:00.000Z'
}
```

## Key Testing Patterns

### Mocking Express Request/Response
```typescript
const mockReq = { body: twilioMessage };
const mockRes = {
  status: jest.fn().mockReturnThis(),
  send: jest.fn(),
};
```

### Supertest API Testing
```typescript
const response = await request(app)
  .post('/webhook')
  .type('form')
  .send(formData);
```

### Console Mocking
```typescript
jest.spyOn(console, 'log').mockImplementation();
expect(console.log).toHaveBeenCalledWith('Expected message');
```

## Error Scenarios Tested

### WebhookHandler
- ✅ Null/undefined request body
- ✅ Missing required fields
- ✅ Malformed JSON
- ✅ Exception during processing

### Server
- ✅ Handler throws exception
- ✅ Invalid HTTP methods
- ✅ Unknown endpoints
- ✅ Payload size limits
- ✅ Content-type handling

## Mock Strategy

- **Console**: Mocked to control test output and verify logging
- **Date**: Mocked for consistent timestamps
- **WebhookHandler**: Mocked for server integration tests
- **Express**: Real instance for comprehensive middleware testing

## Performance Expectations

- ✅ Health check responds < 100ms
- ✅ Webhook processing handles large payloads
- ✅ Concurrent request handling

## Integration Points

### Future Integrations
Tests are structured to easily accommodate:
- Channel Router integration
- PubSub message publishing
- Message queuing
- Database persistence

### Current MVP Scope
- Webhook receives Twilio messages
- Transforms message format
- Logs message details
- Returns HTTP 200 OK

## Maintenance

- Update tests when integrating with Channel Router
- Add performance benchmarks for high-volume scenarios
- Consider adding webhook signature validation tests
- Monitor test execution time for CI/CD pipeline