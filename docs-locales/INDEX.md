# 📚 Índice de Documentación - Sistema CX

## 🎯 Documentos Principales

### 📋 Estados y Flujos
- **[CONVERSATION_STATES.md](./CONVERSATION_STATES.md)** - Estados completos de conversación, flujos y transiciones
- **[CONVERSATION_FLOWS.md](./CONVERSATION_FLOWS.md)** - Flujos detallados del sistema
- **[CONVERSATION_ACTIONS.md](./CONVERSATION_ACTIONS.md)** - Acciones disponibles por conversación

### 🏗️ Arquitectura y Diseño
- **[README.md](./README.md)** - Estado general y servicios production-ready
- **[arquitectura/ARCHITECTURE_REPORT.md](./arquitectura/ARCHITECTURE_REPORT.md)** - Reporte completo de arquitectura
- **[arquitectura/REDIS_ARCHITECTURE_DIAGRAM.md](./arquitectura/REDIS_ARCHITECTURE_DIAGRAM.md)** - Arquitectura Redis para estado compartido

### 🔗 Integraciones y Contratos
- **[N8N_WEBHOOKS_CONTRACTS.md](./N8N_WEBHOOKS_CONTRACTS.md)** - Contratos de webhooks con n8n
- **[cloud-run-pubsub.md](./cloud-run-pubsub.md)** - Integración Cloud Run y PubSub
- **[google-cloud-run-microservices-analysis.md](./google-cloud-run-microservices-analysis.md)** - Análisis de microservicios

## 🛠️ Documentación por Servicio

### 🔥 Chat Realtime (Puerto 3003) - ✅ PRODUCTION-READY
- **[servicios/chat-realtime/README.md](./servicios/chat-realtime/README.md)** - Estado y funcionalidades
- **[servicios/chat-realtime/API_CONTRACTS.md](./servicios/chat-realtime/API_CONTRACTS.md)** - Contratos de API validados
- **[servicios/chat-realtime/ENDPOINTS_SPECIFICATION.md](./servicios/chat-realtime/ENDPOINTS_SPECIFICATION.md)** - Especificación completa de endpoints
- **[servicios/chat-realtime/FINAL_E2E_TESTING_REPORT.md](./servicios/chat-realtime/FINAL_E2E_TESTING_REPORT.md)** - Reporte testing E2E
- **[servicios/chat-realtime/TESTING_REPORT.md](./servicios/chat-realtime/TESTING_REPORT.md)** - Reporte de testing unitario
- **[servicios/chat-realtime/TEST_COVERAGE_REPORT.md](./servicios/chat-realtime/TEST_COVERAGE_REPORT.md)** - Cobertura de tests

### 💬 Chat UI (Puerto 3007) - ✅ PRODUCTION-READY  
- **[servicios/chat-ui/README.md](./servicios/chat-ui/README.md)** - Interface híbrida Firebase + offline resilience

### 🔄 Channel Router (Puerto 3002) - ✅ FUNCIONAL
- **[servicios/channel-router/README.md](./servicios/channel-router/README.md)** - Ruteo de mensajes + AI analysis

### 🤖 Bot Human Router (Puerto 3004) - ✅ COMPLETO
- **[servicios/bot-human-router/README.md](./servicios/bot-human-router/README.md)** - Decision engine + ConversationStateService
- **[servicios/bot-human-router/TEST_SUMMARY.md](./servicios/bot-human-router/TEST_SUMMARY.md)** - Resumen de testing

### 🔑 Session Manager (Puerto 3001) - ✅ COMPLETO
- **[servicios/session-manager/README.md](./servicios/session-manager/README.md)** - Gestión sesiones Redis
- **[servicios/session-manager/TESTING_README.md](./servicios/session-manager/TESTING_README.md)** - Guía de testing
- **[servicios/session-manager/TEST_SUMMARY.md](./servicios/session-manager/TEST_SUMMARY.md)** - Resumen de tests

### 📱 Twilio (Puerto 3000) - 📋 PENDIENTE
- **[servicios/twilio/README.md](./servicios/twilio/README.md)** - Integración WhatsApp (estructura básica)
- **[servicios/twilio/TESTING_README.md](./servicios/twilio/TESTING_README.md)** - Guía de testing

### 📊 Analytics (Puerto 3006) - 📋 PENDIENTE
- **[servicios/analytics/README.md](./servicios/analytics/README.md)** - Métricas y KPIs (diseño completo)

## 📝 Sesiones de Desarrollo

### 🗓️ Registros Históricos
- **[sesiones/session-2025-08-20.md](./sesiones/session-2025-08-20.md)** - Sesión de desarrollo histórica
- **[sesiones/session-2025-08-21.md](./sesiones/session-2025-08-21.md)** - Sesión de desarrollo histórica  
- **[sesiones/session-2025-08-22.md](./sesiones/session-2025-08-22.md)** - Implementación Redis completa

### 📋 Sesiones Individuales (Legacy)
- **[SESSION-2025-08-19.md](./SESSION-2025-08-19.md)** - Agent assignment + load balancing
- **[SESSION-2025-08-19-DECISION_ENGINE_IMPLEMENTATION.md](./SESSION-2025-08-19-DECISION_ENGINE_IMPLEMENTATION.md)** - Decision engine
- **[SESSION-2025-08-20.md](./SESSION-2025-08-20.md)** - Desarrollo histórico
- **[SESSION-2025-08-21.md](./SESSION-2025-08-21.md)** - Desarrollo histórico
- **[SESSION-2025-08-22.md](./SESSION-2025-08-22.md)** - Redis implementation completa

## 🧪 Testing y QA

### 📊 Reportes Generales
- **[testing/TEST_SUMMARY.md](./testing/TEST_SUMMARY.md)** - Resumen general de testing del sistema

### 🔍 Análisis Específicos  
- **[AGENT_ASSIGNMENT_REPORT.md](./AGENT_ASSIGNMENT_REPORT.md)** - Reporte de asignación de agentes
- **[ARQUITECTURA_DECISION_ENGINE_CLARIFICADA.md](./ARQUITECTURA_DECISION_ENGINE_CLARIFICADA.md)** - Arquitectura del decision engine

## 🎨 UI/UX y Wireframes

### 📱 Diseños de Interfaz
- **[wireframes/supervisor-dashboard-wireframe.html](./wireframes/supervisor-dashboard-wireframe.html)** - Wireframe dashboard supervisor
- **[wireframes/supervisor-mobile-wireframe.html](./wireframes/supervisor-mobile-wireframe.html)** - Wireframe móvil supervisor
- **[supervisor-components-structure.md](./supervisor-components-structure.md)** - Estructura de componentes supervisor

## 📋 Planificación y Gestión

### 🎯 Backlog y Prioridades
- **[BACKLOG.md](./BACKLOG.md)** - Backlog general del sistema
- **[acciones-servicios-sistema.md](./acciones-servicios-sistema.md)** - Acciones y servicios del sistema

### 🔧 Configuración y Setup
- **[service-architecture.md](./service-architecture.md)** - Arquitectura de servicios
- **[firebase-cx-system-prd-v2.md](./firebase-cx-system-prd-v2.md)** - Configuración Firebase
- **[research.md](./research.md)** - Research y decisiones técnicas
- **[webhook_data_flow_fix.md](./webhook_data_flow_fix.md)** - Fix de flujo de webhooks

## 📈 Estado Actual del Sistema

### ✅ Production-Ready (6/7 servicios)
- **Chat Realtime**: API completa + Load Balancing + Session Management
- **Chat UI**: Interface híbrida Firebase + conversaciones completas  
- **Channel Router**: Message routing operacional
- **Bot Human Router**: Decision engine + Redis state
- **Session Manager**: Gestión sesiones completa
- **Twilio**: Estructura básica implementada

### 🔄 Redis Implementation (COMPLETADO)
- Estado compartido entre servicios
- TTL 2 horas para sesiones
- ConversationStateService funcional
- Scripts de gestión completos

### 🎯 Próximos Pasos
1. Twilio Integration completa
2. Analytics Service implementación  
3. Production deployment (Cloud Run)

---

**📌 Nota**: Este índice se actualiza automáticamente con cada sesión de desarrollo.  
**📅 Última actualización**: 2025-08-24  
**📊 Estado general**: SISTEMA PRODUCTION-READY con Redis implementation completa