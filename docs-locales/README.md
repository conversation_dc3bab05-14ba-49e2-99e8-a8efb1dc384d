# Documentación Local - Sistema CX

## 📁 Estructura de Documentación

```
docs-locales/
├── arquitectura/
│   └── ARCHITECTURE_REPORT.md        # Reporte completo de arquitectura
├── servicios/
│   ├── twilio/                       # Integración WhatsApp
│   ├── channel-router/               # Enrutamiento de mensajes
│   ├── session-manager/              # Gestión de sesiones (Redis)
│   ├── bot-human-router/             # Ruteo bot/humano + n8n
│   ├── chat-realtime/               # Interface Firebase + lógica de asignación
│   │   ├── API_CONTRACTS.md         # Contratos de API validados
│   │   ├── ENDPOINTS_SPECIFICATION.md # Especificación completa de endpoints
│   │   ├── FINAL_E2E_TESTING_REPORT.md # Reporte testing E2E
│   │   ├── TESTING_REPORT.md        # Reporte de testing
│   │   ├── TEST_COVERAGE_REPORT.md  # Cobertura de tests
│   │   └── (scripts de fixes)       # Scripts de desarrollo y fixes
│   ├── chat-ui/                     # Frontend Next.js + offline
│   └── analytics/                   # Métricas y KPIs
└── testing/                         # Estrategias y reportes de testing
```

## 🎯 Estados de Servicios (ACTUALIZADO 2025-08-26)

### ✅ PRODUCTION-READY
- **chat-realtime**: API completa + Agent Load Balancing + Session Management
- **channel-router**: Message routing operacional + dependencies clean
- **bot-human-router**: Integration funcional + dependencies clean  
- **chat-ui**: **HYBRID ARCHITECTURE - TanStack Query (Supervisor) + Optimistic UI (Agent)**
- **session-manager**: Completamente funcional + compilación limpia

### 🚀 MAJOR REFACTOR COMPLETADO (2025-08-25)
- **TanStack Query Migration**: Supervisor hooks completamente migrados, eliminados 800+ líneas problemáticas
- **Memory Leak Resolution**: Dashboard supervisor estable sin infinite re-renders
- **Hybrid Architecture**: Best-of-both-worlds approach implementado
- **Enhanced Optimistic UI**: Agent mode con animations, priority updates, instant filtering
- **Performance Gains**: 30s polling memory-safe, intelligent caching, <100ms chat response

### 🔄 Pendiente de Desarrollo
- **twilio**: Integración WhatsApp completa (priority tras core completion)
- **analytics**: Métricas y KPIs avanzados (priority tras core completion)
- **supervisor-interface**: Dashboard de monitoreo (future)

## 📖 Documentación por Servicio

### Chat Realtime (Puerto 3003) - UPDATED
- **Estado**: ✅ PRODUCTION-READY con Agent Management
- **Nuevas APIs**: `/conversations/assign` - Load balancing automático
- **Load Balancing**: Algoritmo por utilización validado end-to-end
- **Session Tracking**: Updates automáticos en Supabase + Firebase
- **Testing**: Sistema completo validado con 2 agentes reales

### Channel Router (Puerto 3002) - UPDATED
- **Estado**: ✅ Operacional con dependencies limpias + startup resuelto
- **Fix aplicado**: Clean reinstall resolvió startup issues (tsx dependencies)
- **Integración**: Mock n8n habilitado con `ENABLE_MOCK_N8N=true`

### Session Manager (Puerto 3001)
- **Estado**: ✅ Completo
- **Responsabilidad**: Solo gestión phoneNumber ↔ sessionId en Redis
- **TTL**: 2 horas de sesión

## 🔧 Herramientas de Desarrollo

### Emuladores Locales
- **Firebase**: `127.0.0.1:9000` (chat-realtime integrado)
- **PubSub**: `localhost:8085` (comunicación servicios)
- **Redis**: Docker container (session-manager)

### Scripts Útiles
- `scripts/start-all-emulators.sh` - Iniciar todos los emuladores
- `services/chat-realtime/testing/` - Tests E2E con autenticación real

## 🎯 Próximos Pasos de Desarrollo

1. **Implementar `/api/process-message`** en chat-realtime
2. **Completar bot-human-router** con decision engine
3. **Desarrollar chat-ui** con confianza total en backend
4. **Integrar twilio** para flujo completo WhatsApp

## 📝 Notas de Desarrollo

- **Filosofía MVP**: Simple, testable, solo lo solicitado
- **TypeScript**: Obligatorio en todos los servicios
- **Testing**: Cada servicio debe ser 100% testeable
- **Commit strategy**: Direct merges, branches por sesión de desarrollo

---

**Última actualización**: 2025-08-26  
**Estado general**: HYBRID ARCHITECTURE IMPLEMENTED - TanStack Query (Supervisor) + Optimistic UI (Agent) - Memory-Safe & Performance Optimized