# N8N Webhooks API Contracts

Esta documentación define los contratos completos para la integración entre el Bot Human Router y N8N workflows.

## Arquitectura General

```
Bot Human Router → N8N Webhooks → Decision → Response
                              ↓
                        Internal N8N routing to:
                        ├── Department-specific bots
                        ├── Escalation workflows  
                        └── Emergency handlers
```

## 1. Department Analysis Webhook

**Propósito**: <PERSON><PERSON><PERSON> el mensaje inicial para determinar el departamento apropiado.
**Consumidor**: **Channel Router** (NO Bot Human Router)
**Flujo**: Channel Router → n8n → Channel Router (loop hasta department != "more_info")

### Endpoint
```
POST https://n8n-auto.n1co.io/webhook/e84e61ef-8fe5-43b1-9037-d9b9c63efbac
```

### Request Payload (Channel Router → n8n)
```typescript
interface DepartmentAnalysisRequest {
  conversationId: string;          // "CHaaaa..." (Twilio format)
  message: string;                 // Message content to analyze
}
```

### Response Contract (n8n → Channel Router)
```typescript
interface DepartmentAnalysisResponse {
  convId: string;                  // "CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6"
  department: string;              // "billing", "sales", "technical_support", "general", "more_info"
  reply: string;                   // Reply to send to customer OR empty string ""
}
```

### Flow Logic
```typescript
// Loop continues while department === "more_info"
if (response.department === "more_info" && response.reply !== "") {
  // Channel Router sends reply to customer via Twilio
  // Wait for customer response and repeat analysis
}

if (response.department !== "more_info" && response.reply === "") {
  // Department determined - message goes to PubSub → Bot Human Router
  // BHR processes with known department
}
```

## 2. Bot Analysis Webhook

**Endpoint**: `POST /bot-analysis`  
**Consumer**: Bot Human Router (BHR)  
**Purpose**: Determine if conversation should be handled by bot or human

### Request Payload (BHR → n8n)
```typescript
interface BotAnalysisRequest {
  conversationId: string;          // "CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6" - REQUIRED, never empty
  message: string;                 // Customer message content - REQUIRED, never empty
  channel: string;                 // "whatsapp", "web", "sms" - REQUIRED, never empty
  customerPhone: string;           // "+1234567890" - Twilio format - REQUIRED, never empty
  department: string;              // Department assigned by Channel Router - REQUIRED
  
  // Configuration for N8N to make Chat Realtime API calls
  chatRealtimeConfig: {
    baseUrl: string;               // "http://localhost:3003/api" 
    endpoints: {
      getConversation: string;     // "/conversations/{id}"
      getMessages: string;         // "/conversations/{id}/messages"
      sendMessage: string;         // "/conversations/{id}/messages"  
      updateStatus: string;        // "/conversations/{id}/status"
      assignAgent: string;         // "/conversations/{id}/assign"
      escalate: string;           // "/conversations/{id}/escalate"
      transfer: string;           // "/conversations/{id}/transfer"
    }
  };
  
  // NEW: Twilio Conversations API metadata (optional but preserved through chain)
  metadata?: {
    twilioMessageSid?: string;      // Original Twilio message ID
    twilioConversationSid?: string; // Original Twilio conversation ID
    messageIndex?: string;          // Message sequence in conversation
    participantSid?: string;        // Twilio participant ID
    source?: string;                // "SMS", "API", "Chat"
    eventType?: string;             // "onMessageAdded", etc.
    retryCount?: string;            // Webhook retry count
    attributes?: string;            // Custom Twilio attributes (JSON string)
  };
}
```

### Response Contract (n8n → BHR)
```typescript
// N8N responds with standard HTTP status codes only
// 201: Successfully processed and handled by bot
// ANY OTHER CODE: N8N unavailable, BHR should fallback to human assignment
// Response body is typically empty or contains minimal status message
```

### Input Validation (BHR Side)
Before sending payload to N8N, BHR validates all required fields:

```typescript
// All required fields must be non-empty strings
if (!conversationId || conversationId.trim() === '') {
  return { success: false, handled: false, error: 'conversationId is required and cannot be empty' };
}

if (!message || message.trim() === '') {
  return { success: false, handled: false, error: 'message is required and cannot be empty' };
}

if (!channel || channel.trim() === '') {
  return { success: false, handled: false, error: 'channel is required and cannot be empty' };
}

if (!customerPhone || customerPhone.trim() === '') {
  return { success: false, handled: false, error: 'customerPhone is required and cannot be empty' };
}
```

### Flow Logic
1. **BHR** validates all required fields (fails early if invalid)
2. **BHR** sends payload with conversation context + Chat Realtime API config
3. **N8N** processes the message and handles it internally (calls Chat Realtime APIs as needed)
4. **N8N** returns HTTP status code (201 = bot handled, ≠201 = human needed)
5. **BHR** uses status code to determine next action based on conversation state:

#### Initial Assignment (First Message)
- **201**: N8N takes control → State: `n8n`
- **≠201**: N8N cannot handle → **Initial assignment** to human → State: `human`

#### Passthrough Mode (Subsequent Messages)  
- **State: `n8n`** → 201: N8N continues handling
- **State: `n8n`** → ≠201: N8N **transfers control** to human → State: `human`
- **State: `human`** → Messages bypass N8N completely

### Status Code Logic
- **201 Created**: Bot successfully handled the conversation
- **≠201 (400, 401, 404, 500, timeout, etc.)**: N8N unavailable/error → Assign to human
- **Current assumption**: N8N is always available (no special error handling yet)

### Key Design Principles
- **N8N as Admin Service**: Uses Supabase service token for admin-level access
- **Direct API Access**: N8N calls Chat Realtime endpoints directly without individual agent auth
- **State-Aware Processing**: BHR distinguishes between initial assignment vs mid-conversation transfer
- **Smart Transfers**: N8N can proactively transfer via Chat Realtime APIs while returning 201
- **Simple Response**: Binary status code logic - 201 success, anything else = human needed
- **Rich Context**: Includes all data N8N needs for Chat Realtime API interactions
- **Strict Validation**: All required fields validated before webhook call (fail fast)
- **Metadata Preservation**: Twilio metadata flows through entire chain for workflow flexibility

## 3. Emergency Escalation Webhook

**Propósito**: Detectar situaciones de emergencia que requieren escalación inmediata.

### Endpoint
```
POST https://n8n-auto.n1co.io/webhook/9717e676-3981-484e-b3fa-59a481725af2
```

### Request Payload
```typescript
interface EmergencyEscalationRequest {
  // === Alert Context ===
  alertType: "customer_escalation" | "system_error" | "security_issue" | "service_outage";
  severity: "low" | "medium" | "high" | "critical";
  
  // === Conversation Context ===
  conversationId: string;
  sessionId: string;
  customer: CustomerInfo;
  
  // === Emergency Details ===
  emergencyData: {
    trigger: string;               // What triggered the emergency
    description: string;           // Detailed description
    detectedAt: string;           // ISO timestamp
    
    // Customer escalation specific
    escalationKeywords?: string[]; // ["supervisor", "complaint", "lawsuit"]
    customerSentiment?: "very_negative" | "threatening" | "urgent";
    
    // System error specific
    errorCode?: string;            // System error code
    affectedServices?: string[];   // ["chat-realtime", "session-manager"]
    
    // Security specific
    securityLevel?: "suspicious" | "threat" | "breach";
    ipAddress?: string;
    userAgent?: string;
  };
  
  // === Current State ===
  currentAssignment: {
    assignedTo: "bot" | "human";
    assignedId?: string;           // Bot or agent ID
    assignedAt: string;
    transferAttempts: number;
  };
  
  // === System Context ===
  systemState: {
    availableSupervisors: number;
    onCallPersonnel: string[];     // IDs of on-call staff
    currentSystemLoad: "normal" | "high" | "critical";
  };
}
```

### Response Contract
```typescript
interface EmergencyEscalationResponse {
  // === Escalation Decision ===
  success: boolean;
  
  // === Emergency Response ===
  escalation: {
    shouldEscalate: boolean;
    escalationType: "supervisor" | "on_call" | "emergency_team";
    priority: "urgent" | "critical" | "immediate";
    reason: string;                // "Customer threatening legal action"
  };
  
  // === Assignment Instructions ===
  assignment?: {
    assignTo: "human";             // Always human for emergencies
    specificPersonnel?: string[];  // Specific people to notify
    departmentOverride?: string;   // Override normal department routing
    maxResponseTime: number;       // Max response time in seconds
  };
  
  // === Notification Instructions ===
  notifications: {
    sendSMS: boolean;              // Send SMS to managers
    sendEmail: boolean;            // Send email alerts  
    sendSlack: boolean;            // Post to emergency Slack
    recipients: {
      sms?: string[];              // Phone numbers
      email?: string[];            // Email addresses
      slack?: string[];            // Slack user IDs
    };
  };
  
  // === Follow-up Actions ===
  followUp?: {
    scheduleFollowUp: boolean;
    followUpInterval: number;      // Minutes
    escalationChain: string[];     // Chain of escalation
    documentationRequired: boolean;
  };
  
  // === Processing Metadata ===
  analysis: {
    processingTime: number;
    riskScore: number;             // 0.0-1.0 risk assessment
    recommendedActions: string[];
  };
  
  // === Error Handling ===
  error?: {
    code: string;
    message: string;
    retryable: boolean;
  };
}
```

## Common Error Codes

```typescript
// Department Analysis Errors
"DEPT_ANALYSIS_TIMEOUT"     // Analysis took too long
"DEPT_INVALID_MESSAGE"      // Message content invalid  
"DEPT_LANGUAGE_UNSUPPORTED" // Language not supported
"DEPT_ANALYSIS_FAILED"      // General analysis failure

// Bot Analysis Errors  
"BOT_ANALYSIS_TIMEOUT"      // Analysis took too long
"BOT_CAPABILITY_ERROR"      // Can't assess bot capabilities
"BOT_KNOWLEDGE_OUTDATED"    // Knowledge base too old
"BOT_ANALYSIS_FAILED"       // General analysis failure

// Emergency Escalation Errors
"EMERGENCY_INVALID_ALERT"   // Invalid alert type
"EMERGENCY_NO_PERSONNEL"    // No personnel available
"EMERGENCY_NOTIFICATION_FAILED" // Failed to send notifications
"EMERGENCY_ESCALATION_FAILED"   // General escalation failure
```

## Fallback Behavior

### Department Analysis Fallback
```typescript
// When webhook fails or times out
{
  success: true,
  department: {
    id: "general",
    name: "General Support", 
    confidence: 0.5,
    reason: "Fallback - webhook unavailable"
  }
}
```

### Bot Analysis Fallback
```typescript
// When webhook fails - default to human
{
  success: true,
  decision: {
    canHandle: false,
    confidence: 0.0,
    reason: "Fallback to human - webhook unavailable",
    escalationReason: "System unavailable"
  }
}
```

### Emergency Escalation Fallback
```typescript
// When webhook fails - assume emergency
{
  success: true,
  escalation: {
    shouldEscalate: true,
    escalationType: "supervisor",
    priority: "urgent", 
    reason: "Fallback emergency escalation - webhook unavailable"
  }
}
```

## Implementation Notes

### Authentication
- All webhooks use `Bearer ${N8N_AUTH_TOKEN}` authentication
- Token should be configured in environment variables
- Webhook URLs are environment-specific (dev/staging/prod)

### Timeout Configuration  
- Default timeout: 5000ms (5 seconds)
- Configurable via `N8N_WEBHOOK_TIMEOUT` environment variable
- All webhooks should respond within timeout or fallback is triggered

### Retry Logic
- No automatic retries on webhook failures
- Fallback behavior ensures system continues operating
- Failed webhook calls should be logged for N8N team analysis

### Monitoring
- All webhook calls should be logged with timing metrics
- Track success/failure rates per webhook
- Monitor fallback trigger frequency
- Alert on high failure rates (>10%)

## Testing Contracts

During development, mock responses should match these exact contracts to ensure compatibility when real N8N workflows are deployed.

Current mock implementations in Bot Human Router match these contracts for:
- ✅ Department Analysis (basic)  
- ❌ Bot Analysis (needs expansion)
- ❌ Emergency Escalation (needs expansion)

**Next Steps**: Update mock implementations to match full contracts.