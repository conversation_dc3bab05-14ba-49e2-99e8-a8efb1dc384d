# Estrategia de Filtrado para Analytics de Supervisor

## 🎯 Objetivo
Asegurar que todos los reportes y métricas del supervisor puedan separar correctamente el rendimiento de agentes humanos vs agentes bot (N8N), evitando distorsión de métricas de performance.

---

## 🏗️ Arquitectura de Filtrado

### **1. Campo Discriminador Principal**
```sql
-- En tabla agents (Supabase)
agent_type: 'human' | 'bot' | 'supervisor'
```

### **2. Campos de Contexto Adicionales**
```typescript
interface AgentContext {
  agent_type: 'human' | 'bot' | 'supervisor';
  is_automated: boolean;        // true para bots
  response_capability: 'unlimited' | 'limited'; // unlimited para bots
  availability_24_7: boolean;   // true para bots
  department_scope: 'single' | 'multi' | 'all'; // 'all' para bots universales
}
```

---

## 📊 Métricas Críticas que Requieren Separación

### **A. Tiempos de Respuesta**
**Problema**: Bots responden en ~1-3 segundos, humanos 30-300 segundos
```typescript
interface ResponseTimeMetrics {
  first_response_time: number;      // ⚠️ CRÍTICO - separar bot/human
  average_response_time: number;    // ⚠️ CRÍTICO - separar bot/human  
  max_response_time: number;        // ⚠️ CRÍTICO - separar bot/human
}

// Filtros necesarios:
// - Solo humanos
// - Solo bots
// - Comparativo lado a lado
```

### **B. Disponibilidad y Carga de Trabajo**
**Problema**: Bots disponibles 24/7 con capacidad ilimitada
```typescript
interface AvailabilityMetrics {
  uptime_percentage: number;         // ⚠️ Bot siempre 100%
  concurrent_conversations: number;  // ⚠️ Bot sin límites
  max_capacity_reached: boolean;     // ⚠️ Bot nunca
  offline_periods: number;           // ⚠️ Bot siempre 0
}

// Filtros necesarios:
// - Excluir bots de métricas de disponibilidad humana
// - Métricas separadas para bots (operational status)
```

### **C. Resolución y Escalación**
**Problema**: Patrones de resolución diferentes
```typescript
interface ResolutionMetrics {
  resolution_rate: number;           // ⚠️ Bots pueden tener mayor/menor rate
  escalation_rate: number;          // ⚠️ Bots escalan más frecuentemente
  transfer_rate: number;            // ⚠️ Bots transfieren por diseño
  closure_rate: number;             // ⚠️ Patrones diferentes
}

// Filtros necesarios:
// - Resolución solo humanos
// - Resolución solo bots  
// - Escalaciones por tipo de agente
```

### **D. Satisfacción del Cliente**
**Problema**: Clientes pueden evaluar diferente bot vs humano
```typescript
interface SatisfactionMetrics {
  csat_score: number;               // ⚠️ Puede variar por tipo agente
  nps_score: number;                // ⚠️ Puede variar por tipo agente
  complaint_rate: number;           // ⚠️ Patrones diferentes
}

// Filtros necesarios:
// - CSAT por tipo de agente
// - Comparativo satisfacción bot vs human
```

---

## 🛠️ Implementación Técnica

### **1. Base de Datos - Campos Requeridos**

#### **Agents Table (Supabase)**
```sql
ALTER TABLE agents ADD COLUMN agent_type VARCHAR(20) DEFAULT 'human';
ALTER TABLE agents ADD COLUMN is_automated BOOLEAN DEFAULT false;
ALTER TABLE agents ADD COLUMN max_conversations INTEGER DEFAULT 5;
ALTER TABLE agents ADD COLUMN availability_24_7 BOOLEAN DEFAULT false;

-- N8N Agent Record
INSERT INTO agents VALUES (
  'n8n-agent-universal',
  'AI Assistant',
  '<EMAIL>', 
  'bot',           -- agent_type
  true,            -- is_automated
  999999,          -- max_conversations
  true,            -- availability_24_7
  '["all"]',       -- departments (JSON)
  'available'
);
```

#### **Conversations Table (Firebase)**
```javascript
// En cada conversación
{
  assignment: {
    agentId: "n8n-agent-universal",
    agentType: "bot",        // ⭐ CRÍTICO - duplicar para queries rápidas
    assignedAt: "timestamp",
    assignedBy: "system"
  },
  metadata: {
    handledByBot: true,      // ⭐ Flag rápido para filtros
    botSessions: 1,          // Cuántas veces manejó bot
    humanSessions: 0,        // Cuántas veces manejó humano
    transferredFromBot: false,
    transferredToBot: false
  }
}
```

### **2. API Endpoints con Filtros**

#### **Analytics Endpoints Actualizados**
```typescript
// GET /api/analytics/conversations
interface AnalyticsRequest {
  period: 'today' | 'week' | 'month';
  agentType?: 'human' | 'bot' | 'all';        // ⭐ NUEVO filtro
  agentIds?: string[];                         // Agentes específicos
  departmentId?: string;
  includeTransfers?: boolean;                  // Incluir conversaciones transferidas
  excludeAutomated?: boolean;                  // Excluir completamente bots
}

// Response con métricas separadas
interface AnalyticsResponse {
  overall: GlobalMetrics;
  byAgentType: {
    human: HumanAgentMetrics;
    bot: BotAgentMetrics;
    comparison: ComparisonMetrics;
  };
  individual: AgentMetrics[];
}
```

#### **Supervisor Dashboard Endpoints**
```typescript
// GET /api/supervisor/performance
interface PerformanceRequest {
  agentType: 'human' | 'bot' | 'both';
  showComparison: boolean;
  period: TimeRange;
}

// GET /api/supervisor/availability  
interface AvailabilityRequest {
  excludeAlwaysAvailable: boolean;  // Excluir bots de métricas de disponibilidad
  humanOnly: boolean;
}
```

---

## 📊 Reportes Específicos por Implementar

### **1. Tiempos de Respuesta Comparativo**
```typescript
interface ResponseTimeReport {
  human: {
    firstResponse: { avg: 45, median: 30, p95: 120 };
    avgResponse: { avg: 25, median: 20, p95: 60 };
  };
  bot: {
    firstResponse: { avg: 2, median: 2, p95: 3 };
    avgResponse: { avg: 1.5, median: 1, p95: 2 };
  };
  comparison: {
    botSpeedAdvantage: "22.5x faster first response";
    humanVsBotPreference: "68% customers prefer human followup";
  };
}
```

### **2. Reporte de Carga de Trabajo**
```typescript
interface WorkloadReport {
  human: {
    totalAgents: 12;
    averageConcurrent: 3.2;
    capacityUtilization: 0.64;
    peakHours: ["9-11 AM", "2-4 PM"];
  };
  bot: {
    totalBots: 1;
    concurrentHandled: 47;
    capacityUtilization: "unlimited";
    operationalUptime: 0.997;
  };
}
```

### **3. Reporte de Escalación/Transferencia**
```typescript
interface EscalationReport {
  bot: {
    escalationRate: 0.23;     // 23% de casos escalados
    transferToHumanRate: 0.31; // 31% transferidos a humano
    topEscalationReasons: ["complex_billing", "emotional_customer", "policy_exception"];
  };
  human: {
    escalationRate: 0.08;     // 8% escalados a supervisor
    transferRate: 0.05;       // 5% transferidos entre humanos
    topEscalationReasons: ["authorization_needed", "technical_expertise", "complaint"];
  };
}
```

---

## ⚙️ Configuración de Filtros UI

### **Supervisor Dashboard - Filter Controls**
```typescript
interface FilterControls {
  agentType: {
    options: ['All Agents', 'Human Only', 'Bot Only', 'Compare Side by Side'];
    default: 'Human Only';                    // ⭐ Por defecto excluir bots
  };
  
  metrics: {
    responseTime: {
      showComparison: boolean;
      normalizeForComparison: boolean;        // Ajustar escalas para comparar
    };
    
    availability: {
      exclude247Agents: boolean;              // Excluir bots de métricas availability
    };
    
    performance: {
      separateByType: boolean;                // Mostrar métricas separadas
      showTransferPatterns: boolean;          // Bot→Human, Human→Human patterns
    };
  };
}
```

---

## 🚨 Alertas y Configuraciones Especiales

### **1. Alertas de Performance**
```typescript
interface PerformanceAlerts {
  human: {
    responseTimeThreshold: 300;               // 5 minutos para humanos
    availabilityThreshold: 0.85;             // 85% uptime mínimo
  };
  
  bot: {
    responseTimeThreshold: 10;                // 10 segundos para bots (algo mal)
    operationalThreshold: 0.95;              // 95% operational uptime
    escalationRateThreshold: 0.40;           // >40% escalation = revisar training
  };
}
```

### **2. Configuraciones de Reporting**
```typescript
interface ReportingConfig {
  defaultFilters: {
    performanceReports: 'human_only';        // Por defecto solo humanos
    availabilityReports: 'exclude_bots';     // Excluir bots de availability
    satisfactionReports: 'show_separate';    // Mostrar ambos separados
  };
  
  comparisonReports: {
    enabled: boolean;                         // Permitir comparaciones bot vs human
    normalizeMetrics: boolean;                // Ajustar para comparación justa
    showContextualNotes: boolean;             // Notas explicando diferencias
  };
}
```

---

## 🔄 Migración de Reportes Existentes

### **Pasos de Actualización**
1. **Agregar agent_type a agents table**
2. **Actualizar Firebase conversations con agentType**
3. **Modificar todos los endpoints de analytics** para incluir filtros
4. **Actualizar Supervisor Dashboard UI** con controles de filtro
5. **Crear reportes comparativos** específicos
6. **Configurar alertas diferenciadas** por tipo de agente

### **Backward Compatibility**
```typescript
// Para reportes existentes sin filtro
interface LegacySupport {
  defaultBehavior: 'human_only';             // Por defecto excluir bots
  migrationWarning: boolean;                 // Mostrar aviso de cambio
  allowLegacyEndpoints: boolean;             // Mantener endpoints viejos temporalmente
}
```

---

## 📋 Checklist para Futuros Reportes

**Para cada nuevo reporte de supervisor, verificar:**
- [ ] ¿Incluye métricas de tiempo? → Agregar filtro agent_type
- [ ] ¿Incluye métricas de disponibilidad? → Excluir bots por defecto  
- [ ] ¿Incluye métricas de carga? → Separar capacidad unlimited vs limited
- [ ] ¿Incluye satisfacción del cliente? → Permitir filtro por tipo de agente
- [ ] ¿Incluye transferencias/escalaciones? → Mostrar patrones por tipo
- [ ] ¿Es comparativo? → Incluir contexto explicativo
- [ ] ¿Afecta alertas? → Configurar thresholds diferentes por tipo

---

## 🎯 Beneficios de esta Estrategia

1. **Métricas precisas** - Humanos vs bots comparables correctamente
2. **Alertas apropiadas** - Thresholds diferentes por tipo de agente  
3. **Insights útiles** - Entender fortalezas de cada tipo de agente
4. **Decisiones informadas** - Cuándo usar bot vs humano
5. **Escalabilidad** - Framework para futuros tipos de agentes

---

**Próximos pasos**: Implementar campos base y actualizar endpoints existentes con filtros agent_type.