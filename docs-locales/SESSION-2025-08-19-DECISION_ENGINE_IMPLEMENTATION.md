# SESIÓN 2025-08-19 - DECISION ENGINE IMPLEMENTATION
**Estado**: A punto de implementar - Todo planificado y validado

## 🎯 OBJETIVO PRINCIPAL
Implementar **Decision Engine completo** en Bot Human Router con n8n webhooks reales + sistema de escalación de emergencia.

## ✅ ANÁLISIS COMPLETADO

### **PROBLEMA CRÍTICO CONFIRMADO**
- ❌ **Decision Engine NO EXISTE** en Bot Human Router
- ❌ **TODO va a humanos** sin evaluación bot vs human  
- 📍 **Ubicación**: `services/bot-human-router/src/routes.ts:44`
- 🚨 **Impacto**: Sistema ineficiente, sobrecarga a agentes humanos

### **ARQUITECTURA VALIDADA**
**Flujo Actual (PROBLEMÁTICO):**
```
WhatsApp → Twilio → Channel Router → Bot Human Router → Chat Realtime (SIEMPRE humanos)
```

**F<PERSON><PERSON> Objet<PERSON> (A IMPLEMENTAR):**
```
WhatsApp → Twi<PERSON> → Channel Router → Bot Human Router → Decision Engine:
                                                       ├── Bot: n8n webhook  
                                                       └── Human: Chat Realtime + Load Balancing
```

### **ESTRUCTURA DE CONVERSACIÓN DEFINIDA**
```typescript
{
  id: "CHxxxx",
  assignedTo: "bot" | "human",           // ⭐ TIPO de asignación
  assignedBotId: "n8n-webhook",          // ⭐ ID del bot único
  assignedAgentId: "uuid-agent-123",     // ⭐ ID del agente específico
  departmentId: "technical_support",
  status: "active",
  // ... resto de campos existentes
}
```

## 🔧 CONFIGURACIÓN N8N - VALIDADA

### **URLs CONFIRMADAS Y TESTEADAS**
- ✅ **Department Assignment**: `https://n8n-auto.n1co.io/webhook/e84e61ef-8fe5-43b1-9037-d9b9c63efbac`
- ✅ **Bot Analysis**: `https://n8n-auto.n1co.io/webhook/ec3bcd57-036e-492a-943f-31b06f509a46`  
- ✅ **Emergency Escalation**: `https://n8n-auto.n1co.io/webhook/9717e676-3981-484e-b3fa-59a481725af2`

### **AUTENTICACIÓN**
```
Headers: Authorization: Bearer Lswhr7kUiuaOqd1g1ppfopX5OefsXpH1i7aImAO4zClwRLisHU3XGfXjIXwv1Ibi
```

### **PRUEBAS REALIZADAS**
- ✅ **HTTP 404** confirmado (comportamiento esperado)
- ✅ **Autenticación funciona** 
- ✅ **Response**: `{"message":"Workflow was started"}`

### **CONTRATOS API DEFINIDOS**

#### **1. Department Assignment**
```typescript
// REQUEST
POST /webhook/e84e61ef-8fe5-43b1-9037-d9b9c63efbac
{
  "conversationId": "CHaaaa12345",
  "message": "Mi tarjeta no funciona"
}
// RESPONSE (esperada pero no llegará por 404)
{ "departmentName": "technical_support" }
```

#### **2. Bot Analysis**  
```typescript
// REQUEST
POST /webhook/ec3bcd57-036e-492a-943f-31b06f509a46
{
  "conversationId": "CHaaaa12345", 
  "message": "Mi tarjeta no funciona"
}
// RESPONSE: HTTP 404 (forzar fallback a human)
```

#### **3. Emergency Escalation**
```typescript
// REQUEST  
POST /webhook/9717e676-3981-484e-b3fa-59a481725af2
{
  "conversationId": "CHaaaa12345"
}
// RESPONSE (esperada)
{ "escalated": true, "message": "Emergency escalation triggered" }
```

## 📋 PLAN DE IMPLEMENTACIÓN

### **TODO LIST ACTUAL**
```
✅ 1. Analizar flujo actual - COMPLETADO
✅ 2. Verificar compatibilidad transferencias - COMPLETADO  
🔄 3. Configurar variables de entorno - EN PROGRESO
⏳ 4. Modificar sistema transferencias (assignedTo/assignedBotId/assignedAgentId)
⏳ 5. Implementar Decision Engine completo en Bot Human Router
⏳ 6. Crear EmergencyNotificationService 
⏳ 7. Testing del flujo completo
```

### **VARIABLES DE ENTORNO A AGREGAR**
```env
# N8N Webhooks Configuration
N8N_AUTH_TOKEN=Lswhr7kUiuaOqd1g1ppfopX5OefsXpH1i7aImAO4zClwRLisHU3XGfXjIXwv1Ibi
N8N_DEPARTMENT_WEBHOOK_URL=https://n8n-auto.n1co.io/webhook/e84e61ef-8fe5-43b1-9037-d9b9c63efbac
N8N_BOT_WEBHOOK_URL=https://n8n-auto.n1co.io/webhook/ec3bcd57-036e-492a-943f-31b06f509a46
N8N_EMERGENCY_WEBHOOK_URL=https://n8n-auto.n1co.io/webhook/9717e676-3981-484e-b3fa-59a481725af2

# Bot Configuration
N8N_BOT_ID=n8n-webhook
N8N_WEBHOOK_TIMEOUT=5000

# Emergency Notifications  
EMERGENCY_NOTIFICATION_ENABLED=true
```

## 🛠️ IMPLEMENTACIÓN STEP-BY-STEP

### **PASO 1: Modificar Sistema de Transferencias**
**Archivos a modificar:**
- `services/chat-realtime/src/types.ts` - Agregar campos assignedTo/assignedBotId/assignedAgentId
- `services/chat-realtime/src/conversationService.ts` - Actualizar transferConversation() y acceptTransfer()

**Cambio crítico:**
```typescript
// ANTES
fromAgentId: conversation.assignedTo  // ❌ Asume agent ID

// DESPUÉS  
fromAgentId: conversation.assignedAgentId,  // ✅ Específico
fromBotId: conversation.assignedBotId,      // ✅ Para bot transfers
assignedTo: "bot" | "human"                 // ✅ Tipo
```

### **PASO 2: Implementar Decision Engine**
**Archivo principal:** `services/bot-human-router/src/decisionEngine.ts` (NUEVO)

**Lógica principal:**
```typescript
class DecisionEngine {
  async processInboundMessage(messageData: any) {
    // 1. ¿Conversación existe?
    const conversation = await this.findExistingConversation(messageData.sessionId);
    
    if (conversation) {
      // Routing directo - conversación ya asignada
      return this.routeToExisting(conversation, messageData);
    }
    
    // 2. Nueva conversación - Decision completo
    const departmentAnalysis = await this.callDepartmentWebhook(messageData);
    const botAnalysis = await this.callBotWebhook(messageData); 
    
    // 3. Bot analysis devuelve 404 → fallback a human
    const assignedTo = botAnalysis.success ? "bot" : "human";
    
    // 4. Crear conversación con asignación
    const newConversation = await this.createConversationWithAssignment({
      assignedTo,
      departmentId: departmentAnalysis.departmentName,
      assignedBotId: assignedTo === "bot" ? "n8n-webhook" : null,
      assignedAgentId: assignedTo === "human" ? await this.findBestAgent() : null
    });
    
    // 5. Si no hay agentes → Emergency escalation
    if (assignedTo === "human" && !newConversation.assignedAgentId) {
      await this.triggerEmergencyEscalation(newConversation.id);
    }
    
    return newConversation;
  }
}
```

### **PASO 3: Crear EmergencyNotificationService**
**Archivo:** `services/bot-human-router/src/emergencyNotificationService.ts` (NUEVO)

### **PASO 4: Integrar en Bot Human Router**
**Modificar:** `services/bot-human-router/src/routes.ts`
```typescript
// REEMPLAZAR lógica actual en POST /pubsub/inbound
const decisionEngine = new DecisionEngine();
const result = await decisionEngine.processInboundMessage(messageData);
```

## 🔍 ESTADO ACTUAL DEL SISTEMA

### **Servicios PRODUCTION-READY**
- ✅ **Chat Realtime** (3003): Load balancing + Agent management
- ✅ **Chat UI** (3007): Interface completa + real-time  
- ✅ **Channel Router** (3002): Message routing
- ✅ **Session Manager** (3001): Redis sessions
- ⚠️ **Bot Human Router** (3004): FALTA Decision Engine

### **Transferencias Existentes**
- ✅ **Firebase structure**: `transferInfo` con `currentTransfer` y `transferHistory`
- ✅ **APIs**: `/transfer`, `/accept-transfer`, `/reject-transfer`
- ⚠️ **Compatibility**: Requiere modificación para bot↔human

### **Load Balancing Validado**
- ✅ **Algorithm**: Utilization-based (sessions/max_sessions)
- ✅ **Department filtering**: Agents por departamentos 
- ✅ **Fallback**: 'general' department siempre existe

## 🚨 PRÓXIMO PASO INMEDIATO
**Continuar con la implementación desde:**
1. Agregar variables de entorno al `.env`
2. Modificar types.ts para nueva estructura de conversación
3. Implementar DecisionEngine class completa

## 📝 NOTAS IMPORTANTES
- **Bot único**: "n8n-webhook" maneja todas las conversaciones bot
- **Webhook behavior**: Todos devuelven 404 para forzar fallback a human
- **Emergency escalation**: Tool extensible para notificaciones
- **Testing**: Todas las URLs validadas y funcionando

---
**CONTEXTO PRESERVADO - LISTO PARA CONTINUAR IMPLEMENTACIÓN**