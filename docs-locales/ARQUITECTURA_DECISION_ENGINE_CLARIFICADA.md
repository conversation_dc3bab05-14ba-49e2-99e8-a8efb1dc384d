# 🧠 Arquitectura del Decision Engine - Clarificación Completa

## 📋 **Resumen Ejecutivo**

Documentación de la arquitectura real del sistema de decisión y enrutamiento de mensajes, incluyendo las responsabilidades correctas de cada servicio y los flujos de comunicación entre Channel Router, Bot Human Router, N8N y Chat Realtime.

---

## 🏗️ **Arquitectura Correcta por Servicio**

### **🔄 1. Channel Router - Department Manager**

#### **Responsabilidades:**
- **Análisis de departamento** vía N8N Department Webhook
- **Creación de conversación** en Firebase con departamento asignado
- **Gestión del loop** de preguntas de departamento
- **Publicación a PubSub** solo cuando departamento está definido

#### **Flujo Department Analysis:**
```mermaid
sequenceDiagram
    participant CR as Channel Router
    participant N8N_DEPT as N8N Department
    participant FB as Firebase
    participant PUB as PubSub

    CR->>N8N_DEPT: POST department analysis webhook
    
    alt Department aún analizando
        N8N_DEPT-->>CR: {convId: "123", department: "more_info", reply: "¿De qué necesita ayuda?"}
        CR->>Customer: Envía reply al customer vía Twilio
        Note over CR: Espera siguiente mensaje del customer
    else Department definido
        N8N_DEPT-->>CR: {convId: "123", department: "soporte", reply: ""}
        CR->>FB: Create conversation(convId="123", department="soporte")
        CR->>PUB: Publish message to inbound topic
    end
```

#### **Response Format del N8N Department Webhook:**
```json
{
  "convId": "conv_12345",
  "department": "soporte|ventas|general|more_info",
  "reply": "Respuesta para customer o vacío si department != more_info"
}
```

#### **Lógica Channel Router:**
```javascript
// Pseudocódigo
const response = await n8nDepartmentWebhook(message);

if (response.department === "more_info") {
  // Continuar análisis de departamento
  await sendToCustomer(response.reply);
  // Esperar próximo mensaje del customer
} else {
  // Departamento definido
  await chatRealtimeService.createConversation(response.convId, response.department);
  await publishToPubSubInbound(message);
  // Trabajo de Channel Router terminado para esta conversación
}
```

---

### **🧠 2. Bot Human Router - Decision Engine**

#### **Responsabilidades:**
- **Decision Engine interno** y configurable
- **SIEMPRE intentar N8N primero** (configurable)
- **Fallback a humano** si N8N no responde 200
- **Assignment de agente** óptimo (load balancing + department)
- **Passthrough mode** para conversaciones asignadas

#### **Flujo Decision Engine:**
```mermaid
sequenceDiagram
    participant PUB as PubSub Inbound
    participant BHR as Bot Human Router
    participant N8N_BOT as N8N Bot Workflow
    participant CHRT as Chat Realtime
    participant FB as Firebase

    PUB->>BHR: PUSH inbound message (Cloud Run endpoint)
    BHR->>BHR: Check conversation state
    
    alt Estado: unassigned (primera vez)
        BHR->>BHR: Decision Engine: Try N8N first
        BHR->>N8N_BOT: Send message to bot workflow
        
        alt N8N responde 200
            N8N_BOT->>CHRT: Process message + actions (N8N direct control)
            CHRT->>FB: Store bot response
            N8N_BOT-->>BHR: HTTP 200 - "Listo, gracias, sigo yo"
            BHR->>BHR: Save state: assignedTo = 'n8n'
            Note over BHR: ✅ Future messages → N8N passthrough
        else N8N no responde 200
            N8N_BOT-->>BHR: HTTP != 200 (error, timeout, etc.)
            BHR->>BHR: Save state: assignedTo = 'human'
            BHR->>CHRT: Assign conversation to human agent
            Note over BHR: ✅ Future messages → Human passthrough
        end
    else Estado: n8n (passthrough a N8N)
        BHR->>N8N_BOT: Forward message directly to N8N
        N8N_BOT->>CHRT: N8N handles message autonomously
        Note over BHR: N8N tiene control completo
    else Estado: human (passthrough a humano)
        BHR->>CHRT: Forward message directly to Chat Realtime
        Note over BHR: Humano tiene control completo
    end
```

#### **Decision Engine Logic:**
```javascript
// Pseudocódigo Bot Human Router - CON ESTADO DE CONVERSACIONES
async processInboundMessage(message) {
  // 1. SIEMPRE consultar estado de conversación primero
  const conversationState = await getConversationState(message.conversationId);
  
  if (conversationState.assignedTo === 'unassigned') {
    // 🆕 Primera vez - Decision Engine
    return await makeInitialDecision(message);
  } 
  else if (conversationState.assignedTo === 'n8n') {
    // 🤖 Passthrough a N8N - ya decidido previamente
    return await forwardToN8n(message);
  } 
  else if (conversationState.assignedTo === 'human') {
    // 👤 Passthrough a Chat Realtime - humano asignado
    return await forwardToChatRealtime(message);
  }
}

async makeInitialDecision(message) {
  try {
    // SIEMPRE intentar N8N primero (configurable)
    const n8nResponse = await callN8nBotWorkflow(message);
    
    if (n8nResponse.status === 200) {
      // ✅ N8N toma control - GUARDAR estado
      await setConversationState(message.conversationId, 'n8n');
      console.log("N8N dice: 'Listo, gracias, de aquí sigo yo'");
      return { success: true, handledBy: 'n8n', action: 'bot_took_control' };
    } else {
      // ❌ N8N falló - route to human - GUARDAR estado
      await setConversationState(message.conversationId, 'human');
      return await assignToHuman(message);
    }
  } catch (error) {
    // ❌ N8N no disponible - route to human - GUARDAR estado
    await setConversationState(message.conversationId, 'human');
    return await assignToHuman(message);
  }
}

async assignToHuman(conversationData) {
  const bestAgent = await findOptimalAgent({
    department: conversationData.department,
    loadBalancing: 'least_sessions',
    skillMatch: true,
    availability: 'available'
  });
  
  if (bestAgent) {
    await chatRealtimeService.assignConversation(conversationData.id, bestAgent.id);
    return { success: true, handledBy: 'human', agentId: bestAgent.id };
  } else {
    await chatRealtimeService.addToQueue(conversationData.id, conversationData.department);
    return { success: true, handledBy: 'queue', department: conversationData.department };
  }
}
```

#### **Estados de Conversación en Bot Human Router:**
- **`unassigned`**: Primera vez, aplicar Decision Engine
- **`n8n`**: N8N tomó control, passthrough directo a N8N
- **`human`**: Humano asignado, passthrough directo a Chat Realtime

#### **Estado de Conversación (Storage):**
```typescript
interface ConversationState {
  conversationId: string;
  assignedTo: 'n8n' | 'human' | 'unassigned';
  assignedAt: timestamp;
  department: string;
  // TODOs complejos (futuro):
  // assignedAgentId?: string;
  // lastN8nResponse?: timestamp; 
  // retryCount?: number;
}
```

---

### **🤖 3. N8N Bot - Agente Virtual Completo**

#### **Responsabilidades:**
- **Procesar mensaje** y generar respuesta inteligente
- **Llamar Chat Realtime** directamente para todas las acciones
- **Actuar como agente humano** (responder, transferir, escalar, cerrar)
- **Autonomía completa** una vez que toma control

#### **Endpoints Chat Realtime disponibles para N8N:**
```bash
# Respuestas del bot
POST /api/conversations/:id/messages          # Responder al customer
POST /api/conversations/:id/typing            # Typing indicators

# Acciones como agente humano  
POST /api/conversations/:id/transfer          # Transferir a humano
POST /api/conversations/:id/escalate          # Escalar a supervisor  
POST /api/conversations/:id/close             # Cerrar conversación
POST /api/conversations/:id/notes             # Agregar notas internas

# Información y estado
GET  /api/conversations/:id                   # Estado de conversación
GET  /api/conversations/:id/messages          # Historial de mensajes
PUT  /api/conversations/:id/status            # Cambiar estado
```

#### **Flujo N8N Bot Workflow:**
```mermaid
sequenceDiagram
    participant BHR as Bot Human Router
    participant N8N as N8N Bot Workflow
    participant AI as AI Service
    participant CHRT as Chat Realtime
    participant FB as Firebase

    BHR->>N8N: POST bot workflow webhook + message
    N8N->>AI: Analyze message + conversation context
    AI-->>N8N: Generated response + recommended actions
    
    alt Simple response
        N8N->>CHRT: POST /api/conversations/:id/messages
        CHRT->>FB: Store bot response
        N8N-->>BHR: HTTP 200 "Handled"
    else Complex action (transfer/escalate)
        N8N->>CHRT: POST /api/conversations/:id/messages (response)
        N8N->>CHRT: POST /api/conversations/:id/transfer (action)
        N8N-->>BHR: HTTP 200 "Handled"
    else Unable to handle
        N8N-->>BHR: HTTP 400/500 "Cannot handle"
        Note over BHR: Fallback to human assignment
    end
```

---

### **⚡ 4. Chat Realtime - API Gateway**

#### **Responsabilidades:**
- **Interface con Firebase** para todas las operaciones
- **Recibir calls de N8N** y procesar como requests de agente
- **Gestión de asignaciones** cuando Bot Human Router asigna humanos
- **Real-time updates** vía Firebase a Chat UI

#### **NO hace más:**
- ❌ **NO más IA mockada** en `/process-message`
- ❌ **NO más decision engine** - solo ejecuta decisiones
- ❌ **NO más análisis de departamento** - ya viene de Channel Router

---

## 🔄 **Flujo Completo End-to-End**

```mermaid
sequenceDiagram
    participant Customer as 👤 Customer
    participant Twilio as 📞 Twilio
    participant CR as 🔄 Channel Router
    participant N8N_DEPT as 🏢 N8N Department
    participant FB as 🔥 Firebase
    participant PUB as 📡 PubSub
    participant BHR as 🧠 Bot Human Router
    participant N8N_BOT as 🤖 N8N Bot
    participant CHRT as ⚡ Chat Realtime
    participant UI as 🖥️ Chat UI

    %% Phase 1: Department Analysis
    Customer->>Twilio: WhatsApp message
    Twilio->>CR: Webhook message
    
    loop Until department != "more_info"
        CR->>N8N_DEPT: Department analysis
        N8N_DEPT-->>CR: {convId, department, reply}
        
        alt department == "more_info"
            CR->>Twilio: Send reply to customer
            Twilio->>Customer: Department question
            Customer->>Twilio: Answer
            Twilio->>CR: Customer response
        end
    end
    
    CR->>CHRT: POST /api/conversations (convId, department)
    CHRT->>FB: Create conversation in Firebase
    CR->>PUB: Publish to inbound topic
    
    %% Phase 2: Bot vs Human Decision
    PUB->>BHR: PUSH inbound message (Cloud Run endpoint)
    BHR->>BHR: Check conversation state
    
    alt First time (unassigned)
        BHR->>N8N_BOT: Try bot workflow first
        
        alt N8N can handle (status 200)
            N8N_BOT->>CHRT: Process message + actions
            CHRT->>FB: Store bot response
            FB->>UI: Real-time update (if agent monitoring)
            N8N_BOT-->>BHR: "Listo, gracias, sigo yo"
            BHR->>BHR: Save state: assignedTo = 'n8n'
            Note over BHR: ✅ N8N assigned, future messages → passthrough
        else N8N cannot handle (status != 200)
            N8N_BOT-->>BHR: Error/Cannot handle
            BHR->>BHR: Save state: assignedTo = 'human'
            BHR->>BHR: Find optimal human agent
            BHR->>CHRT: Assign conversation to agent
            CHRT->>FB: Update assignment
            FB->>UI: Real-time update to agent
            Note over BHR: ✅ Human assigned, future messages → passthrough
        end
    else Already assigned
        alt assignedTo = 'n8n'
            BHR->>N8N_BOT: Passthrough to N8N (direct)
            N8N_BOT->>CHRT: N8N processes + responds autonomously  
            CHRT->>FB: Store N8N response
            FB->>UI: Real-time update (if agent monitoring)
            Note over N8N_BOT: N8N has complete control
        else assignedTo = 'human'  
            BHR->>CHRT: Passthrough to Chat Realtime (direct)
            Note over CHRT: Human agent handles via Chat UI
        end
    end
```

---

## 🎯 **Puntos Clave de la Arquitectura**

### **1. Responsabilidades Claras:**
- **Channel Router**: Department assignment + call Chat Realtime for conversation creation
- **Bot Human Router**: Decision engine + optimal human assignment + passthrough
- **N8N**: Bot intelligence + direct Chat Realtime integration
- **Chat Realtime**: API gateway + **EXCLUSIVE** Firebase interface

### **2. Flujo de Decisión:**
- **Siempre intentar bot primero** (configurable en Bot Human Router)
- **N8N toma control completo** si responde 200
- **Fallback inteligente a humano** si N8N falla
- **Modo passthrough** para conversaciones ya asignadas

### **3. Integración N8N:**
- **N8N actúa como agente completo** - puede hacer todo lo que un humano
- **Comunicación directa** N8N → Chat Realtime (sin Bot Human Router)
- **Autonomía completa** - transfers, escalations, closures, etc.

### **4. Estado de Implementación:**
- **Channel Router**: ✅ Mayormente implementado, needs conversation creation
- **Bot Human Router**: ⚠️ Needs refactor - remove department analysis, fix decision engine
- **N8N Workflows**: 📋 Pending implementation
- **Chat Realtime**: ✅ Ready, needs removal of IA mockada

---

## 📋 **TODOs Identificados**

### **Channel Router:**
- [ ] Implementar llamada a Chat Realtime POST /api/conversations
- [ ] Refinar loop logic para `department === "more_info"`

### **Bot Human Router:**
- [ ] Remover análisis de departamento (ya lo hace Channel Router)
- [ ] Implementar estado de conversaciones (storage)
- [ ] Implementar decision engine con consulta de estado
- [ ] Implementar passthrough inteligente (n8n/human según estado)
- [ ] Refactor `decisionEngine.ts` con nueva lógica
- [ ] TODOs complejos (futuro): N8N retry logic, health monitoring, reassignment

### **Chat Realtime:**
- [ ] Remover IA mockada de `/process-message`
- [ ] Asegurar que todos los endpoints estén disponibles para N8N

### **N8N Workflows:**
- [ ] Crear bot workflow principal
- [ ] Integrar AI service (OpenAI/Anthropic)
- [ ] Implementar llamadas directas a Chat Realtime
- [ ] Testing de respuestas complejas (transfer, escalate)

---

**Generado**: 2025-08-22  
**Arquitectura clarificada**: Basada en conversación con user sobre flujos reales  
**Estado de Conversaciones**: 2025-08-22 - Bot Human Router con estado y passthrough inteligente  
**Diagramas Corregidos**: 2025-08-22 - Passthrough puede ir a N8N O humano según estado  
**Próxima acción**: Implementar cambios según esta arquitectura definitiva