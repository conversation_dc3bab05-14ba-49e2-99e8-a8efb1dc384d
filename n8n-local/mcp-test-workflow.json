{"name": "MCP Test Workflow - AI Agent with Tools", "nodes": [{"parameters": {"httpMethod": "POST", "path": "mcp-test", "responseMode": "onReceived", "options": {}}, "id": "webhook-trigger", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300], "webhookId": "mcp-test-webhook"}, {"parameters": {"model": "gpt-4o-mini", "options": {"systemMessage": "You are a helpful AI assistant with access to search and analysis tools. When users ask questions, use the available MCP tools to search for information and provide comprehensive answers. Always explain what tools you used and summarize your findings clearly."}, "messages": {"values": [{"role": "user", "content": "={{ $json.query || 'Hello, what can you help me with today?' }}"}]}, "tools": {"values": [{"toolType": "n8nTool", "tool": "MCP Client <PERSON>l"}]}}, "id": "ai-agent", "name": "AI Agent with MCP Tools", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"sseEndpoint": "npx @modelcontextprotocol/server-brave-search", "authentication": "none", "toolsToInclude": "all"}, "id": "mcp-client-tool", "name": "MCP Client - Brave Search", "type": "@n8n/n8n-nodes-langchain.toolMcp", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"authentication": "bearer", "bearerToken": "test-token-123", "toolsToExpose": ["Search Knowledge Base", "Process Data"]}, "id": "mcp-server-trigger", "name": "MCP Server - Expose Tools", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "typeVersion": 1, "position": [900, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \n  \"success\": true,\n  \"query\": $('Webhook Trigger').first().json.query,\n  \"ai_response\": $('AI Agent with MCP Tools').first().json.output,\n  \"tools_used\": \"MCP Client (Brave Search) + MCP Server\",\n  \"timestamp\": new Date().toISOString()\n} }}", "options": {}}, "id": "response", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 300]}], "connections": {"Webhook Trigger": {"main": [[{"node": "AI Agent with MCP Tools", "type": "main", "index": 0}]]}, "AI Agent with MCP Tools": {"main": [[{"node": "MCP Client - Brave Search", "type": "main", "index": 0}]]}, "MCP Client - Brave Search": {"main": [[{"node": "MCP Server - Expose Tools", "type": "main", "index": 0}]]}, "MCP Server - Expose Tools": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-08-21T21:40:00.000Z", "updatedAt": "2025-08-21T21:40:00.000Z", "id": "mcp-test", "name": "MCP Test"}], "meta": {"templateCredsSetupCompleted": true}, "id": "mcp-test-workflow", "versionId": "1"}