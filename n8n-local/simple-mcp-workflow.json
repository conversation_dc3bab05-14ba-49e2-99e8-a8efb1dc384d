{"meta": {"templateCredsSetupCompleted": true}, "name": "MCP Test - Simple", "nodes": [{"parameters": {"httpMethod": "POST", "path": "mcp-test", "responseMode": "onReceived"}, "id": "webhook", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"message\": \"MCP Webhook funcionando!\",\n  \"received_data\": $json,\n  \"timestamp\": new Date().toISOString(),\n  \"next_step\": \"Agregamos nodos MCP después\"\n} }}"}, "id": "response", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [460, 300]}], "connections": {"Webhook": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}