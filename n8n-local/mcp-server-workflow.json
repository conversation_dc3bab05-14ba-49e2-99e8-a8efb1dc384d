{"name": "MCP Server - Expose N8N Tools", "nodes": [{"parameters": {"authentication": "bearer", "bearerToken": "test-mcp-token-123", "toolsToExpose": ["Process Data", "Format Response"]}, "id": "mcp-server-trigger", "name": "MCP Server Trigger", "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "// Procesamiento de datos desde cliente MCP\nconst inputData = $input.all();\nconst processedData = inputData.map(item => ({\n  ...item.json,\n  processed: true,\n  processedAt: new Date().toISOString(),\n  source: 'n8n-mcp-server'\n}));\n\nreturn processedData;"}, "id": "process-data", "name": "Process Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"jsCode": "// Formatear respuesta final\nconst processedItems = $input.all();\nconst response = {\n  success: true,\n  totalProcessed: processedItems.length,\n  results: processedItems.map(item => item.json),\n  timestamp: new Date().toISOString(),\n  serverInfo: {\n    name: 'N8N MCP Server',\n    version: '1.0',\n    tools: ['Process Data', 'Format Response']\n  }\n};\n\nreturn { json: response };"}, "id": "format-response", "name": "Format Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}], "connections": {"MCP Server Trigger": {"main": [[{"node": "Process Data", "type": "main", "index": 0}]]}, "Process Data": {"main": [[{"node": "Format Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}