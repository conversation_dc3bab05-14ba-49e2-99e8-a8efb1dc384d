#!/bin/bash

# Script para crear el workflow MCP directamente via API n8n
echo "🚀 Creando workflow MCP via API..."

# Crear el workflow via POST a la API de n8n
curl -X POST http://localhost:5678/api/v1/workflows \
  -H "Content-Type: application/json" \
  -d '{
    "name": "MCP Test Workflow - AI Agent with Tools",
    "nodes": [
      {
        "parameters": {
          "httpMethod": "POST",
          "path": "mcp-test",
          "responseMode": "onReceived",
          "options": {}
        },
        "id": "webhook-trigger",
        "name": "Webhook Trigger",
        "type": "n8n-nodes-base.webhook",
        "typeVersion": 2,
        "position": [240, 300],
        "webhookId": "mcp-test-webhook"
      },
      {
        "parameters": {
          "model": "gpt-4o-mini",
          "options": {
            "systemMessage": "You are a helpful AI assistant with access to search tools. Use MCP tools to search and analyze information."
          },
          "messages": {
            "values": [
              {
                "role": "user", 
                "content": "={{ $json.query || \"Hello, what can you help me with today?\" }}"
              }
            ]
          }
        },
        "id": "ai-agent",
        "name": "AI Agent with MCP Tools",
        "type": "@n8n/n8n-nodes-langchain.agent",
        "typeVersion": 1,
        "position": [460, 300]
      },
      {
        "parameters": {
          "sseEndpoint": "npx @modelcontextprotocol/server-brave-search",
          "authentication": "none",
          "toolsToInclude": "all"
        },
        "id": "mcp-client-tool",
        "name": "MCP Client - Brave Search",
        "type": "@n8n/n8n-nodes-langchain.toolMcp", 
        "typeVersion": 1,
        "position": [680, 300]
      },
      {
        "parameters": {
          "respondWith": "json",
          "responseBody": "={{ { \"success\": true, \"query\": $json.query, \"ai_response\": \"Processed with MCP tools\", \"timestamp\": new Date().toISOString() } }}",
          "options": {}
        },
        "id": "response",
        "name": "Response",
        "type": "n8n-nodes-base.respondToWebhook",
        "typeVersion": 1,
        "position": [900, 300]
      }
    ],
    "connections": {
      "Webhook Trigger": {
        "main": [
          [
            {
              "node": "AI Agent with MCP Tools",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "AI Agent with MCP Tools": {
        "main": [
          [
            {
              "node": "MCP Client - Brave Search",
              "type": "main", 
              "index": 0
            }
          ]
        ]
      },
      "MCP Client - Brave Search": {
        "main": [
          [
            {
              "node": "Response",
              "type": "main",
              "index": 0
            }
          ]
        ]
      }
    },
    "settings": {
      "executionOrder": "v1"
    },
    "active": false
  }' | jq '.'

echo ""
echo "✅ Workflow creado! Verifica en n8n UI"
echo "🌐 http://localhost:5678"