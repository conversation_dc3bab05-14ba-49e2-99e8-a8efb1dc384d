{"name": "MCP AI Agent - Correct Architecture", "nodes": [{"parameters": {"httpMethod": "POST", "path": "mcp-agent", "responseMode": "onReceived"}, "id": "webhook", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [240, 300]}, {"parameters": {"model": "gpt-4o-mini", "options": {"systemMessage": "You are a helpful AI assistant with access to web search tools via MCP. When users ask questions, search for current information using the MCP tools available to you. Always provide comprehensive answers based on your search results."}, "messages": {"values": [{"role": "user", "content": "={{ $json.query || 'Hello! I can help you search for information. What would you like to know?' }}"}]}, "tools": {"values": [{"toolType": "mcpTool", "tool": "brave_web_search"}]}}, "id": "ai-agent", "name": "AI Agent with MCP Tools", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"sseEndpoint": "npx @modelcontextprotocol/server-brave-search", "authentication": "none", "toolsToInclude": "all"}, "id": "mcp-client", "name": "MCP Client - Brave Search", "type": "@n8n/n8n-nodes-langchain.toolMcp", "typeVersion": 1, "position": [460, 480]}, {"parameters": {"respondWith": "json", "responseBody": "={{ {\n  \"success\": true,\n  \"query\": $json.query,\n  \"ai_response\": $('AI Agent with MCP Tools').first().json.output,\n  \"timestamp\": new Date().toISOString(),\n  \"tools_used\": \"MCP Brave Search\"\n} }}"}, "id": "response", "name": "Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 300]}], "connections": {"Webhook": {"main": [[{"node": "AI Agent with MCP Tools", "type": "main", "index": 0}]]}, "AI Agent with MCP Tools": {"main": [[{"node": "Response", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}