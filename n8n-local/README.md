# n8n Local con MCP Nativo

Setup local de n8n con nodos MCP nativos para testing de flujos AI.

## 🚀 Quick Start

```bash
# Desde la carpeta n8n-local
./scripts/setup-n8n.sh
```

## 📁 Estructura

```
n8n-local/
├── docker-compose.yml      # Configuración Docker
├── scripts/
│   └── setup-n8n.sh       # Script de inicio automático
├── config/                 # Configuraciones n8n
├── data/                   # Datos persistentes n8n
└── README.md              # Esta documentación
```

## 🔧 Nodos MCP Nativos Disponibles

### **MCP Client Tool Node**
- **Propósito**: Consumir tools de servidores MCP externos
- **Configuración**: SSE Endpoint + Authentication
- **Ubicación**: `n8n-nodes-langchain.toolmcp`

### **MCP Server Trigger Node** 
- **Propósito**: Exponer n8n como servidor MCP
- **Configuración**: Bearer/Header auth + URL exposure
- **Ubicación**: `n8n-nodes-langchain.mcptrigger`

## 🎯 Arquitectura de Flujo MCP

```
┌─────────────┐    ┌─────────────┐    ┌─────────────────┐    ┌─────────────────────┐
│   Webhook   │ -> │  AI Agent   │ -> │ MCP Client Tool │ -> │ MCP Server Trigger  │
│   Trigger   │    │    Node     │    │      Node       │    │       Node          │
└─────────────┘    └─────────────┘    └─────────────────┘    └─────────────────────┘
```

**Flujo Completo**:
1. **Webhook** recibe request externo
2. **AI Agent** procesa con instrucciones
3. **MCP Client** consume tools externos
4. **MCP Server** expone tools propios

## ⚙️ Configuración Docker

- **Puerto**: 5678 (http://localhost:5678)
- **Volúmenes**: `./data` y `./config` mapeados
- **Variables MCP**: Configuradas para development
- **Healthcheck**: Endpoint `/healthz`

## 🛠️ Comandos Útiles

```bash
# Iniciar n8n
docker-compose up -d

# Ver logs
docker-compose logs -f n8n-local

# Parar n8n
docker-compose down

# Reiniciar limpieza completa
docker-compose down -v && docker-compose up -d
```

## 🔑 Variables de Entorno MCP

En `docker-compose.yml`:
- `MCP_BRAVE_API_KEY` - Para Brave Search MCP server
- `MCP_OPENAI_API_KEY` - Para AI models
- `MCP_CUSTOM_SETTING` - Variable custom para testing

## 📝 Próximos Pasos

1. **Setup inicial**: Crear admin user en UI
2. **Crear workflow de prueba**:
   - Webhook trigger
   - AI Agent con instrucciones
   - MCP Client que consume tool externo
   - MCP Server que expone tool interno
3. **Testing end-to-end** con webhook calls

---

**Nota**: Los nodos MCP son nativos en n8n, no requieren instalación de community packages.