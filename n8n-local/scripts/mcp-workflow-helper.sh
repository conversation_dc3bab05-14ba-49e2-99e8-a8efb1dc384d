#!/bin/bash

# Helper script para trabajar con workflows MCP en n8n
# Usa la API key guardada en config/.env

cd "$(dirname "$0")/.."
API_KEY=$(cat config/.env | grep N8N_API_KEY | cut -d'=' -f2)

case "$1" in
  "list")
    echo "📋 Listando workflows..."
    curl -s -X GET http://localhost:5678/api/v1/workflows \
      -H "X-N8N-API-KEY: $API_KEY" | jq '.data[] | {id, name, active}'
    ;;
  "activate")
    echo "🚀 Activando workflow $2..."
    curl -s -X POST http://localhost:5678/api/v1/workflows/$2/activate \
      -H "X-N8N-API-KEY: $API_KEY" | jq '.name, .active'
    ;;
  "test")
    echo "🧪 Testeando webhook del workflow MCP..."
    curl -X POST http://localhost:5678/webhook/mcp-test \
      -H "Content-Type: application/json" \
      -d '{"query": "Test from helper script", "timestamp": "'$(date -Iseconds)'"}' | jq '.'
    ;;
  "status")
    echo "📊 Estado de workflows MCP..."
    echo "Main Workflow:"
    curl -s -X GET http://localhost:5678/api/v1/workflows/Q5sxFtqBwB9Ew1O9 \
      -H "X-N8N-API-KEY: $API_KEY" | jq '{id: "Q5sxFtqBwB9Ew1O9", name, active, nodes: (.nodes | length)}'
    echo "Sub-Workflow:"  
    curl -s -X GET http://localhost:5678/api/v1/workflows/Ko3qpJJXpffAGsMT \
      -H "X-N8N-API-KEY: $API_KEY" | jq '{id: "Ko3qpJJXpffAGsMT", name, active, nodes: (.nodes | length)}'
    ;;
  *)
    echo "🛠️  MCP Workflow Helper"
    echo ""
    echo "Comandos disponibles:"
    echo "  list     - Listar todos los workflows"
    echo "  activate - Activar workflow (requiere ID)"
    echo "  test     - Testear webhook MCP"
    echo "  status   - Ver estado del workflow MCP"
    echo ""
    echo "Ejemplo: ./mcp-workflow-helper.sh test"
    ;;
esac