#!/bin/bash

# Setup n8n with native MCP nodes
echo "🚀 Setting up n8n with native MCP support..."
cd "$(dirname "$0")/.." # Move to n8n-local directory

# Create necessary directories
mkdir -p ./data ./config

# Start n8n container
echo "📦 Starting n8n Docker container..."
docker-compose up -d

# Wait for n8n to be ready
echo "⏳ Waiting for n8n to be ready..."
sleep 30

# Check if n8n is running
if curl -f http://localhost:5678/healthz > /dev/null 2>&1; then
    echo "✅ n8n is running successfully!"
    echo "🌐 Access n8n at: http://localhost:5678"
    echo ""
    echo "📋 Next steps:"
    echo "1. Open http://localhost:5678 in your browser"
    echo "2. Complete initial setup (create admin user)"
    echo "3. Create MCP workflow using NATIVE nodes:"
    echo "   ✅ MCP Client Tool node - To consume external MCP servers"
    echo "   ✅ MCP Server Trigger node - To expose n8n as MCP server"
    echo "   ✅ AI Agent nodes - For AI processing"
    echo "4. No community nodes needed - MCP is built-in!"
    echo ""
    echo "🔧 Native MCP Architecture:"
    echo "   Webhook → AI Agent → MCP Client Tool → MCP Server Trigger"
else
    echo "❌ n8n failed to start. Check logs:"
    echo "docker-compose logs n8n-local"
fi