# 📋 GUÍA DE INICIO DE SESIÓN - CX SYSTEM

## 🚨 REGLAS CRÍTICAS PARA CADA SESIÓN

### 1. **GESTIÓN DE SERVICIOS MEDIANTE SCRIPT OFICIAL**
- ✅ **SIEMPRE** usar `./scripts/manage-services.sh` para toda gestión de servicios
- ❌ **NUNCA** ejecutar comandos directos como `npm start`, `pkill`, `killall`, etc.
- 🔧 **Para acciones que requieren autorización** (start/stop/restart/build):
  - ✅ **SIEMPRE** decir: "Por favor ejecuta: `./scripts/manage-services.sh [comando]`" 
  - ✅ **ESPERAR** confirmación del usuario antes de continuar
- 📊 **Para consultas de estado** (sin modificar sistema):
  - ✅ **PERMITIDO SIN AUTORIZACIÓN**: `./scripts/manage-services.sh status`
  - ✅ **PERMITIDO SIN AUTORIZACIÓN**: `./scripts/manage-services.sh build-status`
  - ✅ **PERMITIDO SIN AUTORIZACIÓN**: `./scripts/manage-services.sh logs [service]`

### 2. **SIEMPRE DAR OPCIONES - NUNCA DECIDIR**
- ❌ **NUNCA** implementar una solución sin dar opciones primero
- ❌ **NUNCA** asumir el "camino más fácil" sin consultar
- ✅ **SIEMPRE** presentar 2-3 opciones claras con pros/contras
- ✅ **ESPERAR** la selección del usuario antes de proceder
- 📝 **Formato**: "Opciones disponibles: 1) ... 2) ... 3) ... ¿Cuál prefieres?"

### 3. **PENSAMIENTO PROFUNDO Y SISTEMÁTICO**
- 🧠 **PAUSAR** y analizar profundamente antes de cualquier implementación
- 📝 Usar TodoWrite para planificar tareas complejas
- 🔍 Pensar paso a paso el flujo completo
- ⚡ Para bugs: reproducir → analizar → identificar causa raíz → proponer soluciones

### 4. **CONTRATOS API SON SAGRADOS**
- 📋 **SIEMPRE** revisar `/src/types/api.ts` antes de modificar estructuras
- 🔄 **VALIDAR** que Database → API → UI mantenga consistencia de contratos
- ❌ **NUNCA** crear campos de base de datos que no coincidan con TypeScript
- ✅ **TRANSFORMAR** datos si es necesario para cumplir contratos

### 5. **DOCUMENTACIÓN COMO PRIMERA FUENTE**
- 📖 **REVISAR** `/README.md` del proyecto principal
- 📁 **CONSULTAR** documentación local en `/docs/` de cada servicio  
- 📝 **RECORDAR** que el usuario actualiza docs al cerrar sesiones
- 🔍 Usar como referencia para arquitectura, patrones y decisiones

### 6. **SUPABASE IDs Y AUTENTICACIÓN**
- 🆔 **USAR IDs REALES** de Supabase - nunca generar IDs falsos o de ejemplo
- 👥 **AGENTES Y SUPERVISORES** tienen IDs específicos en la base de datos
- 📝 Al crear scripts o data de prueba, **PREGUNTAR** por IDs reales existentes
- 🔍 **VALIDAR** que los IDs existen antes de usarlos en queries o scripts
- ✅ Usar contratos TypeScript para estructura correcta de datos

### 7. **🚨 PROHIBIDO MOCK DATA EN IMPLEMENTACIÓN REAL**
- ❌ **NUNCA JAMÁS** usar mock data cuando implementas funcionalidad real
- ❌ **NUNCA** dejar hardcoded datos falsos en producción o desarrollo funcional
- 🔧 **IMPLEMENTAR SIEMPRE LA VERSIÓN REAL** cuando se pide una funcionalidad
- 📢 **SI ALGO DEBE QUEDAR MOCKEADO**: Solo por desviarse del foco actual Y AVISAR AL USUARIO EXPLÍCITAMENTE
- 📝 **FORMATO DE AVISO**: "⚠️ NOTA: [X funcionalidad] quedará mockeada temporalmente porque [razón específica]. ¿Quieres que la implemente real ahora o continuamos?"
- 🎯 **PRIORIDAD ABSOLUTA**: Funcionalidad real > Mock data - SIEMPRE

### 8. **TOKENS Y ENDPOINTS PROTEGIDOS**
- 🔒 **NUNCA ASUMIR** que tengo acceso a tokens de autenticación
- 📞 Para calls cURL a endpoints protegidos: **SIEMPRE PREGUNTAR** por el token
- 💬 **Formato**: "Necesito el token de autenticación para este endpoint, ¿puedes proporcionármelo?"
- 🔑 **ESPERAR** el token del usuario antes de ejecutar requests protegidos

## 🔄 PROTOCOLO DE INICIO DE SESIÓN

### Al comenzar cada sesión:
1. **Leer** este documento completo
2. **Revisar** el README principal del proyecto
3. **Verificar estado del sistema** usando comandos permitidos:
   ```bash
   ./scripts/manage-services.sh status        # Estado de servicios running
   ./scripts/manage-services.sh build-status  # Estado de builds
   ```
4. **Confirmar** entendimiento de las reglas
5. **Solicitar** descripción del objetivo de la sesión

### Durante el desarrollo:
- Seguir metodología MVP (solo lo solicitado)
- Usar TypeScript con tipado estricto
- Mantener código simple y testeable
- Aplicar principios de Clean Architecture

## ⚡ RECORDATORIOS IMPORTANTES

- **Base de datos híbrida**: Firebase Realtime (operaciones) + Supabase (analytics/config)
- **Servicios principales**: chat-ui, chat-realtime, channel-router, session-manager
- **Testing**: Usar agente especializado para crear tests
- **Code Review**: Usar agente TypeScript para validación de builds
- **Git workflow**: Feature branches con merge directo (no PRs)

## 🛠️ COMANDOS DE GESTIÓN DE SERVICIOS

### ✅ **Comandos Permitidos (Sin Autorización)**
```bash
# Verificar estado de servicios y emuladores
./scripts/manage-services.sh status

# Verificar estado de builds (nuevo comando)
./scripts/manage-services.sh build-status
./scripts/manage-services.sh build-status chat-realtime  # Servicio específico

# Ver logs de servicios
./scripts/manage-services.sh logs chat-realtime
./scripts/manage-services.sh logs logs-proxy
```

### 🔐 **Comandos que Requieren Autorización**
```bash
# SIEMPRE pedir permiso antes de ejecutar:
./scripts/manage-services.sh start chat-realtime
./scripts/manage-services.sh stop chat-realtime  
./scripts/manage-services.sh restart chat-realtime
./scripts/manage-services.sh build chat-realtime
./scripts/manage-services.sh clean chat-realtime
./scripts/manage-services.sh dev      # Entorno de desarrollo
./scripts/manage-services.sh full     # Sistema completo
```

### 📋 **Ejemplos de Uso Correcto**
- ✅ **Consulta**: "Verificando estado de builds..." → ejecuto `build-status` directamente
- ✅ **Acción**: "Para iniciar el servicio, por favor ejecuta: `./scripts/manage-services.sh start chat-realtime`"
- ✅ **Build**: "Para compilar, por favor ejecuta: `./scripts/manage-services.sh build chat-realtime`"

---

**📌 INSTRUCCIÓN PARA EL USUARIO:**
*Al inicio de cada sesión, envía: "Nueva sesión - lee la guía de inicio y confirma que la seguirás"*