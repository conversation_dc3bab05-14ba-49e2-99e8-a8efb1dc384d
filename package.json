{"name": "cx-system", "version": "1.0.0", "description": "Customer Experience System - Root scripts dependencies", "private": true, "scripts": {"setup": "npm install", "start-emulators": "./scripts/start-all-emulators.sh", "stop-emulators": "./scripts/stop-all-emulators.sh", "create-data": "node scripts/create_complete_data.js", "cleanup-temp": "./scripts/cleanup-firebase-temp.sh"}, "devDependencies": {"@google-cloud/pubsub": "^4.1.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/cx-system.git"}, "keywords": ["customer-experience", "microservices", "cloud-run", "pubsub", "firebase"], "author": "CX System Team", "license": "MIT", "dependencies": {"axios": "^1.11.0"}}