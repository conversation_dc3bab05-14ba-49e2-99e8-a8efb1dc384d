# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
*-debug.log
npm-debug.log*
firebase-debug.log
ui-debug.log
database-debug.log
browser-console.log
pids/
*.pid
*.seed
*.pid.lock

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Test files
test-results/
playwright-report/

# Temporary folders
tmp/
temp/
*.tmp
*.bak
*.backup

# Redis data
dump.rdb

# Firebase emulator data
firebase-data/

# Docker
.dockerignore
docker-compose.override.yml