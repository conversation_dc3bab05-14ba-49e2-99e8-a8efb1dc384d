{"rules": {".read": true, ".write": true, "conversations": {".indexOn": ["assignedTo", "agentId", "status", "customerId", "createdAt", "updatedAt"], "$conversationId": {".read": true, ".write": true}}, "agents": {".indexOn": ["status", "department", "isOnline"], "$agentId": {".read": true, ".write": true}}, "departments": {".read": true, ".write": true}, "analytics": {".indexOn": ["timestamp", "type", "agentId"], ".read": true, ".write": true}}}