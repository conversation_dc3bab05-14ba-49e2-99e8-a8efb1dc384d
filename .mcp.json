{"mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@0.4.4", "--project-ref=slafisavjpzzcysnajcf"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}, "github": {"type": "stdio", "command": "docker", "args": ["run", "-i", "--rm", "-e", "GITHUB_PERSONAL_ACCESS_TOKEN", "ghcr.io/github/github-mcp-server"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}}}}