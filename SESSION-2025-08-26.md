# SESSION-2025-08-26.md

## Inicio de Sesión - 2025-08-26 10:30

### Objetivos de la Sesión
- **Pendiente por definir** - Esperando objetivos específicos del usuario

### Puntos Críticos a Recordar

#### **Gestión de Servicios (CRÍTICO)**
- ✅ **SIEMPRE** usar `./scripts/manage-services.sh` para toda gestión de servicios
- ❌ **NUNCA** ejecutar comandos directos como `npm start`, `pkill`, `killall`
- 🔧 **Para start/stop/restart/build**: SIEMPRE pedir autorización primero
- 📊 **Para consultas** (status, build-status, logs): Permitido sin autorización

#### **Metodología de Desarrollo**
- ❌ **NUNCA** implementar solución sin dar opciones primero
- ✅ **SIEMPRE** presentar 2-3 opciones con pros/contras y esperar selección
- 🧠 **PAUSAR** y analizar profundamente antes de implementar
- 📝 Usar TodoWrite para planificar tareas complejas

#### **Contratos API y Data**
- 📋 **SIEMPRE** revisar `/src/types/api.ts` antes de modificar estructuras
- 🔄 **VALIDAR** que Database → API → UI mantenga consistencia
- 🆔 **USAR IDs REALES** de Supabase - nunca generar IDs falsos
- ❌ **NUNCA JAMÁS** usar mock data en implementación real

#### **Autenticación y Seguridad**
- 🔒 **NUNCA ASUMIR** que tengo acceso a tokens de autenticación
- 📞 **SIEMPRE PREGUNTAR** por tokens para endpoints protegidos

#### **Arquitectura del Sistema**
- **Base de datos híbrida**: Firebase Realtime (operaciones) + Supabase (analytics/config)
- **Servicios principales**: chat-ui (Hybrid TanStack + Optimistic), chat-realtime, channel-router, session-manager
- **Arquitectura post-refactor**: Supervisor mode usa TanStack Query, Agent mode usa Optimistic UI custom

### Preparación del Entorno

#### **Herramientas Disponibles**
- Git (repositorio principal)
- Docker (para Redis y emuladores)
- Firebase CLI y emuladores
- Scripts de gestión automatizados
- Supabase para configuración y analytics

#### **Estado Actual del Sistema**
- **Servicios**: 0/7 running (todos detenidos)
- **Builds**: 7/7 ready (todos compilados correctamente)
- **Emuladores**: Detenidos (Firebase, PubSub)
- **Arquitectura**: Hybrid TanStack + Optimistic implementada exitosamente

#### **Repositorios/Configuraciones**
- **Rama actual**: main (branch principal)
- **Última actualización**: 2025-08-26 (refactor híbrido documentado)
- **Variables de entorno**: Configuración centralizada en `.env`
- **Documentación**: CLAUDE.md, README.md y docs-locales actualizados

#### **Servicios Production-Ready**
- **chat-realtime** (Port 3003): Interface Firebase + conversation management
- **chat-ui** (Port 3007): Hybrid TanStack + Optimistic UI architecture
- **channel-router** (Port 3002): Message routing + AI department analysis
- **session-manager** (Port 3001): Redis session management
- **bot-human-router** (Port 3004): Decision engine + conversation state

### Estado de Preparación
- ✅ Documentación SESSION_GUIDE.md revisada y puntos clave extraídos
- ✅ README principal y docs-locales consultados
- ✅ Estado del sistema verificado (servicios detenidos, builds listos)
- ✅ Arquitectura híbrida documentada y comprendida
- ✅ Reglas críticas internalizadas

### Acción Siguiente
- **Esperando definición de objetivos** - Usuario debe especificar qué trabajar en esta sesión

---

## Log de Actividades

### 10:30 - Inicio de sesión
- SESSION_GUIDE.md leído completamente
- README.md y docs-locales revisados
- Estado de servicios verificado: 0/7 running, 7/7 builds ready
- Arquitectura híbrida TanStack + Optimistic comprendida
- Reglas críticas internalizadas y listas para aplicar

**Estado**: Entorno listo para desarrollo - Esperando objetivos específicos del usuario