#!/usr/bin/env node

/**
 * MASTER SCRIPT - Create ALL data correctly from scratch
 * ✅ 100% TypeScript Contracts Compliant  
 * ✅ Realistic data distribution
 * ✅ All required and optional fields
 * ✅ Firebase emulator safety check
 * ✅ Selective data insertion with flags
 * ✅ Uses real Supabase agents (no fake data)
 */

const { initializeFirebase, getFirebaseDatabase, CONSTANTS } = require('../services/chat-realtime/firebase-config');
const { createClient } = require('../services/chat-realtime/node_modules/@supabase/supabase-js');

// Parse command line arguments
const args = process.argv.slice(2);11
const flags = {
  all: args.includes('--all') || args.length === 0, // Default to all if no flags
  conversations: args.includes('--conversations'),
  agents: args.includes('--agents'),
  queues: args.includes('--queues'),
  help: args.includes('--help') || args.includes('-h')
};

// Initialize Supabase client to get real agents
const supabaseUrl = process.env.SUPABASE_URL || 'https://sjbqnxhwwgbrzbxhbsgo.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNqYnFueGh3d2dicnpieGhic2dvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NDQ4NDgsImV4cCI6MjA1MDAyMDg0OH0.lSFrHO7VhgWCqYt-GFDzLdCGNelCqH4wRlp2YNlPvJA';
const supabase = createClient(supabaseUrl, supabaseKey);

// Show help message
function showHelp() {
  console.log('🔧 Firebase Test Data Insertion Script');
  console.log('');
  console.log('Usage:');
  console.log('  node create_complete_data.js [flags]');
  console.log('');
  console.log('Flags:');
  console.log('  --all              Insert all data (conversations, agents, queues) - DEFAULT');
  console.log('  --conversations    Insert only conversations (clears existing conversations)');
  console.log('  --agents          Insert only agent statuses (uses real Supabase agents)');
  console.log('  --queues          Insert only queues (clears existing queues)');
  console.log('  --help, -h        Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node create_complete_data.js                    # Insert all data');
  console.log('  node create_complete_data.js --conversations    # Only conversations');
  console.log('  node create_complete_data.js --agents           # Only agent statuses');
  console.log('  node create_complete_data.js --queues           # Only queues');
  console.log('');
  console.log('⚠️  IMPORTANT: Each flag clears only its specific data section before inserting');
}

// Get real agents from Supabase
async function getSupabaseAgents() {
  try {
    console.log('📡 Obteniendo agentes reales desde Supabase...');
    const { data: agents, error } = await supabase
      .from('agents')
      .select(`
        id,
        name,
        email,
        role,
        status,
        profile,
        created_at,
        updated_at
      `)
      .eq('is_active', true); // Only active agents

    if (error) {
      console.error('❌ Error obteniendo agentes de Supabase:', error);
      return [];
    }

    console.log(`✅ Obtenidos ${agents.length} agentes reales de Supabase`);
    return agents;
  } catch (error) {
    console.error('❌ Error conectando con Supabase:', error);
    return [];
  }
}

// Helper function to check Firebase emulator connectivity
async function checkFirebaseEmulator() {
  try {
    const db = getFirebaseDatabase();
    await db.ref('.info/connected').once('value');
    console.log('✅ Firebase Emulator conectado correctamente');
    return true;
  } catch (error) {
    console.error('❌ Firebase Emulator NO disponible');
    console.error('   Por favor inicia el emulador: ./start-firebase-emulator.sh');
    return false;
  }
}

// Initialize Firebase using centralized configuration
initializeFirebase();
const db = getFirebaseDatabase();

// Helper function to map Supabase agents to departments (since Supabase might not have department info)
function mapAgentsToDepartments(supabaseAgents) {
  const departments = [
    CONSTANTS.DEPARTMENTS.TECHNICAL_SUPPORT,
    CONSTANTS.DEPARTMENTS.SALES, 
    CONSTANTS.DEPARTMENTS.BILLING,
    CONSTANTS.DEPARTMENTS.GENERAL
  ];
  
  // Distribute agents across departments
  return supabaseAgents.map((agent, index) => ({
    ...agent,
    departments: [departments[index % departments.length], CONSTANTS.DEPARTMENTS.GENERAL], // Always include general
    skills: ['general', 'customer_relations'] // Basic skills for all agents
  }));
}

// Realistic conversation data with varied scenarios
const COMPLETE_CONVERSATIONS = [
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1',
    customer: { id: 'whatsapp:+5000000001', name: 'María González' },
    department: CONSTANTS.DEPARTMENTS.TECHNICAL_SUPPORT,
    status: 'active',
    channel: 'whatsapp',
    assignedAgent: CONSTANTS.JUAN_PEREZ_ID,
    messages: [
      { type: 'customer', content: 'Mi tarjeta no está funcionando correctamente', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 },
      { type: 'system', template: 'agent_assigned', delay: 31000 },
      { type: 'agent', content: 'Hola María! Entiendo que tienes problemas con tu tarjeta. ¿Podrías decirme qué tipo de error específico estás viendo?', delay: 61000 },
      { type: 'agent', content: 'Para ayudarte mejor, ¿la tarjeta no pasa en ningún lugar o es un problema específico con ciertos comercios?', delay: 121000 },
      { type: 'customer', content: 'No pasa en ningún lado, ni siquiera en el cajero', delay: 181000, unread: true }
    ]
  },
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa2',
    customer: { id: 'whatsapp:+5000000002', name: 'Carlos Rodríguez' },
    department: CONSTANTS.DEPARTMENTS.BILLING,
    status: 'transferring',
    channel: 'whatsapp',
    assignedAgent: null, // Will be assigned dynamically from real Supabase agents
    hasTransferHistory: true,
    messages: [
      { type: 'customer', content: 'Tengo una duda sobre mi facturación', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 },
      { type: 'system', template: 'agent_assigned', delay: 31000 },
      { type: 'agent', content: 'Hola Carlos! Con gusto te ayudo con tu consulta de facturación. ¿Qué específicamente te gustaría revisar?', delay: 61000 },
      { type: 'agent', content: 'Puedo ver tu cuenta aquí. ¿Se trata de algún cargo en particular que no reconoces?', delay: 151000 },
      { type: 'system', template: 'agent_transferred', delay: 241000, reason: 'Especialización técnica requerida' }
    ]
  },
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3',
    customer: { id: 'web:customer-003', name: 'Ana López' },
    department: CONSTANTS.DEPARTMENTS.SALES,
    status: 'closed',
    channel: 'web_chat',
    assignedAgent: null, // Will be assigned dynamically from real Supabase agents
    hasClosedAt: true,
    messages: [
      { type: 'customer', content: 'Quiero información sobre sus productos', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 },
      { type: 'system', template: 'agent_assigned', delay: 31000 },
      { type: 'agent', content: 'Hola Ana! Perfecto, me da mucho gusto poder ayudarte con información sobre nuestros productos.', delay: 61000 },
      { type: 'agent', content: '¿Hay algún producto en particular que te interese o te gustaría que te dé un overview general de nuestros servicios?', delay: 121000 },
      { type: 'customer', content: 'Perfecto, ya tengo la información que necesitaba. Muchas gracias!', delay: 241000 },
      { type: 'system', template: 'conversation_ended', delay: 301000, reason: 'Cliente satisfecho' }
    ]
  },
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa4',
    customer: { id: 'whatsapp:+5000000004', name: 'José Martínez' },
    department: CONSTANTS.DEPARTMENTS.TECHNICAL_SUPPORT,
    status: 'supervised',
    channel: 'whatsapp',
    assignedAgent: CONSTANTS.JUAN_PEREZ_ID,
    hasSupervision: true,
    messages: [
      { type: 'customer', content: 'La aplicación se cierra inesperadamente', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 },
      { type: 'system', template: 'agent_assigned', delay: 31000 },
      { type: 'agent', content: 'Hola José! Lamento que estés experimentando problemas con la app. Vamos a solucionarlo juntos.', delay: 61000 },
      { type: 'agent', content: '¿En qué tipo de dispositivo estás usando la aplicación? ¿iPhone, Android, tablet?', delay: 151000 },
      { type: 'customer', content: 'Es en iPhone 12, iOS 17.1', delay: 211000 },
      { type: 'supervisor', content: 'Revisemos la versión de la app también', delay: 271000 }
    ]
  },
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa5',
    customer: { id: 'email:<EMAIL>', name: 'Elena Sánchez' },
    department: CONSTANTS.DEPARTMENTS.GENERAL,
    status: 'pending',
    channel: 'email',
    assignedAgent: null,
    messages: [
      { type: 'customer', content: 'Necesito ayuda con mi cuenta', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 }
    ]
  },
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6',
    customer: { id: 'whatsapp:+5000000006', name: 'Miguel Torres' },
    department: CONSTANTS.DEPARTMENTS.TECHNICAL_SUPPORT,
    status: 'escalated',
    channel: 'whatsapp',
    assignedAgent: null, // Will be assigned dynamically from real Supabase agents
    messages: [
      { type: 'customer', content: 'Error al procesar pago - URGENTE', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 },
      { type: 'system', template: 'agent_assigned', delay: 31000 },
      { type: 'agent', content: 'Hola Miguel! Veo que tienes un problema urgente procesando un pago. Déjame revisarlo inmediatamente.', delay: 61000 },
      { type: 'agent', content: '¿El error aparece al intentar pagar con tarjeta, transferencia, o cuál método de pago estás usando?', delay: 121000 },
      { type: 'customer', content: 'Con tarjeta, dice error 500. Es para pagar un servicio importante!', delay: 181000, unread: true }
    ]
  },
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa7',
    customer: { id: 'sms:+5000000007', name: 'Laura Jiménez' },
    department: CONSTANTS.DEPARTMENTS.BILLING,
    status: 'active',
    channel: 'sms',
    assignedAgent: null, // Will be assigned dynamically from real Supabase agents
    messages: [
      { type: 'customer', content: 'Cobro duplicado en mi tarjeta', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 },
      { type: 'system', template: 'agent_assigned', delay: 31000 },
      { type: 'agent', content: 'Hola Laura! Entiendo tu preocupación por el cobro duplicado. Vamos a revisar esto inmediatamente.', delay: 61000 },
      { type: 'agent', content: 'Ya estoy viendo tu cuenta. ¿Podrías decirme aproximadamente de qué fecha es el cobro que aparece duplicado?', delay: 121000 }
    ]
  },
  {
    id: 'CHaaaaaaaaaaaaaaaaaaaaaaaaaaaaa8',
    customer: { id: 'web:customer-008', name: 'Roberto Díaz' },
    department: CONSTANTS.DEPARTMENTS.SALES,
    status: 'pending_acceptance',
    channel: 'web_chat',
    assignedAgent: null, // Will be assigned dynamically from real Supabase agents
    messages: [
      { type: 'customer', content: 'Información sobre planes premium', delay: 0 },
      { type: 'system', template: 'welcome', delay: 1000 },
      { type: 'system', template: 'agent_assigned', delay: 31000 },
      { type: 'agent', content: 'Hola Roberto! Excelente que te interesen nuestros planes premium. Te voy a dar toda la información.', delay: 61000 }
    ]
  }
];

// System message templates
const SYSTEM_TEMPLATES = {
  welcome: {
    content: 'Hola! Un agente estará contigo pronto para ayudarte.',
    sendToWhatsApp: true,
    showToAgent: true
  },
  agent_assigned: {
    content: 'Un agente se ha unido a la conversación para ayudarte.',
    sendToWhatsApp: true,
    showToAgent: true
  },
  agent_transferred: {
    content: 'Tu conversación ha sido transferida a un especialista.',
    sendToWhatsApp: true,
    showToAgent: true
  },
  conversation_ended: {
    content: 'Conversación finalizada. ¡Gracias por contactarnos!',
    sendToWhatsApp: true,
    showToAgent: true
  },
  session_ended: {
    content: 'Sesión terminada por inactividad. Puedes iniciar una nueva conversación cuando gustes.',
    sendToWhatsApp: true,
    showToAgent: true
  }
};

// Helper functions for realistic data
function generateId(prefix = 'id') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function getRandomStatus() {
  const statuses = [
    { status: 'pending', weight: 0.10 },
    { status: 'active', weight: 0.60 },
    { status: 'transferring', weight: 0.05 },
    { status: 'pending_acceptance', weight: 0.05 },
    { status: 'supervised', weight: 0.10 },
    { status: 'escalated', weight: 0.05 },
    { status: 'closed', weight: 0.05 }
  ];
  
  const random = Math.random();
  let cumulative = 0;
  
  for (const item of statuses) {
    cumulative += item.weight;
    if (random < cumulative) return item.status;
  }
  return 'active';
}

function getRandomPriority() {
  const priorities = [
    { priority: 'low', weight: 0.20 },
    { priority: 'medium', weight: 0.60 },
    { priority: 'high', weight: 0.15 },
    { priority: 'urgent', weight: 0.05 }
  ];
  
  const random = Math.random();
  let cumulative = 0;
  
  for (const item of priorities) {
    cumulative += item.weight;
    if (random < cumulative) return item.priority;
  }
  return 'medium';
}

function createReadByTracking(messageType, messageTimeISO, conversationCustomerId, agentId) {
  const readBy = {};
  
  // Sanitize keys for Firebase (no dots, #, $, /, [, ])
  const sanitizeKey = (key) => key.replace(/[.#$\/\[\]]/g, '_');
  
  if (messageType === 'agent' || messageType === 'system') {
    // Customer read status - 70% chance of being read
    if (Math.random() > 0.3) {
      const readDelay = Math.floor(Math.random() * 300000); // 0-5 minutes later
      const safeKey = sanitizeKey(conversationCustomerId);
      readBy[safeKey] = new Date(Date.parse(messageTimeISO) + readDelay).toISOString();
    }
  }
  
  if (messageType === 'customer') {
    // Agent always reads customer messages
    const readDelay = Math.floor(Math.random() * 60000); // 0-1 minute later
    const safeKey = sanitizeKey(agentId || 'system');
    readBy[safeKey] = new Date(Date.parse(messageTimeISO) + readDelay).toISOString();
  }
  
  return Object.keys(readBy).length > 0 ? readBy : null;
}

function createTransferHistory(baseISOTime, availableAgents) {
  // Use real agent IDs if available
  const fromAgentId = CONSTANTS.JUAN_PEREZ_ID;
  const toAgentId = availableAgents && availableAgents.length > 1 
    ? availableAgents.find(a => a.id !== fromAgentId)?.id || availableAgents[0].id
    : CONSTANTS.JUAN_PEREZ_ID;
    
  return [{
    id: generateId('transfer'),
    fromAgentId,
    toAgentId,
    reason: 'Especialización en billing requerida',
    status: 'accepted',
    transferredAt: baseISOTime,
    acceptedAt: new Date(Date.parse(baseISOTime) + 120000).toISOString() // 2 minutes later
  }];
}

function createSupervisionHistory(baseISOTime) {
  return [{
    id: generateId('supervision'),
    supervisorId: 'supervisor-001',
    mode: 'participate',
    startedAt: new Date(Date.parse(baseISOTime) + 180000).toISOString(), // 3 minutes after start
    reason: 'Escalation review'
  }];
}

async function createCompleteData() {
  try {
    // Handle help flag
    if (flags.help) {
      showHelp();
      return;
    }

    // Show what will be inserted
    const sections = [];
    if (flags.all) sections.push('conversations', 'agents', 'queues');
    if (flags.conversations) sections.push('conversations');
    if (flags.agents) sections.push('agents');  
    if (flags.queues) sections.push('queues');
    
    console.log('🚀 FIREBASE TEST DATA INSERTION - TypeScript Compliant');
    console.log('='.repeat(70));
    console.log(`📋 Sections to insert: ${sections.join(', ')}`);
    console.log('🔍 Verificando Firebase Emulator...');
    
    // Check Firebase emulator before proceeding
    const isEmulatorRunning = await checkFirebaseEmulator();
    if (!isEmulatorRunning) {
      console.log('');
      console.log('⚠️  ABORTANDO: Firebase Emulator no disponible');
      console.log('   Ejecuta primero: ./start-firebase-emulator.sh');
      process.exit(1);
    }
    
    console.log('');
    console.log('🗑️  LIMPIANDO SECCIONES SELECCIONADAS...');
    
    // Selective cleaning based on flags
    if (flags.all) {
      try {
        await db.ref().remove();
        console.log('✅ Base de datos completamente limpia (--all)');
      } catch (error) {
        console.log('⚠️  Error limpiando toda la DB, continuando...');
      }
    } else {
      // Clean only selected sections
      if (flags.conversations) {
        try {
          await db.ref('conversations').remove();
          console.log('✅ Conversaciones eliminadas');
        } catch (error) {
          console.log('⚠️  Error limpiando conversaciones, continuando...');
        }
      }
      
      if (flags.agents) {
        try {
          await db.ref('agents').remove();
          console.log('✅ Agentes eliminados');
        } catch (error) {
          console.log('⚠️  Error limpiando agentes, continuando...');
        }
      }
      
      if (flags.queues) {
        try {
          await db.ref('queues').remove();
          console.log('✅ Queues eliminadas');
        } catch (error) {
          console.log('⚠️  Error limpiando queues, continuando...');
        }
      }
    }
    
    const baseTime = Date.now();
    const baseISOTime = new Date(baseTime).toISOString();
    let totalMessages = 0;
    
    // Get real agents from Supabase if needed
    let AGENTS = [];
    if (flags.all || flags.conversations || flags.agents || flags.queues) {
      const supabaseAgents = await getSupabaseAgents();
      
      if (supabaseAgents.length === 0) {
        console.log('⚠️  Could not get agents from Supabase. Using fallback agent...');
        // Use the existing agent ID we know exists
        AGENTS = mapAgentsToDepartments([{
          id: '5f4fb378-908d-4b49-83ce-be4ce3b50c5d',
          name: 'Agent Test',
          email: '<EMAIL>',
          role: 'agent',
          status: 'active',
          profile: { departments: ['general'] },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }]);
        console.log(`✅ Using ${AGENTS.length} fallback agent`);
      } else {
        AGENTS = mapAgentsToDepartments(supabaseAgents);
        console.log(`✅ Using ${AGENTS.length} real agents from Supabase`);
      }
    }
    
    // STEP 1: Create conversations if requested
    if (flags.all || flags.conversations) {
      console.log('');
      console.log('📋 CREANDO CONVERSACIONES CON CONTRATOS TYPESCRIPT 100% COMPLIANT...');
      console.log('='.repeat(70));
    for (const conv of COMPLETE_CONVERSATIONS) {
      try {
        console.log(`🔄 Creando ${conv.id} (${conv.customer.name}) - ${conv.messages.length} mensajes...`);
        
        // Assign real agent if needed and conversation should have one
        let assignedAgent = conv.assignedAgent;
        if (!assignedAgent && ['active', 'transferring', 'closed', 'supervised', 'escalated', 'pending_acceptance'].includes(conv.status) && AGENTS.length > 0) {
          // Rotate through available agents
          const agentIndex = COMPLETE_CONVERSATIONS.indexOf(conv) % AGENTS.length;
          assignedAgent = AGENTS[agentIndex].id;
          console.log(`  📋 Asignando agente real: ${AGENTS[agentIndex].name} (${assignedAgent})`);
        }
        
        // Find last message data
        const lastMsgData = conv.messages[conv.messages.length - 1];
        const lastMessageTime = baseTime + lastMsgData.delay;
        const lastMessageTimeISO = new Date(lastMessageTime).toISOString();
        const lastMessageId = generateId('msg');
        
        // Create customer object with all optional fields
        const customer = {
          id: conv.customer.id,
          name: conv.customer.name,
          phone: conv.customer.id.includes('whatsapp') ? conv.customer.id.replace('whatsapp:', '') : (conv.customer.id.includes('sms') ? conv.customer.id.replace('sms:', '') : null),
          email: conv.customer.id.includes('email') ? conv.customer.id.replace('email:', '') : `${conv.customer.name.toLowerCase().replace(/\s+/g, '.')}@example.com`,
          avatar: Math.random() > 0.7 ? `https://avatar.placeholder/${conv.customer.name}` : null,
          location: Math.random() > 0.8 ? 'Mexico City, MX' : null,
          joinDate: new Date(baseTime - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString(),
          totalConversations: Math.floor(Math.random() * 8) + 1,
          tags: Math.random() > 0.6 ? ['vip', 'premium'] : null
        };

        // Create lastMessage object with full TypeScript compliance
        const lastMessage = {
          id: lastMessageId,
          conversationId: conv.id,
          senderId: lastMsgData.type === 'customer' 
            ? conv.customer.id 
            : lastMsgData.type === 'system' 
            ? 'system' 
            : lastMsgData.type === 'supervisor'
            ? 'supervisor-001'
            : assignedAgent || CONSTANTS.JUAN_PEREZ_ID,
          senderType: lastMsgData.type,
          content: lastMsgData.type === 'system' 
            ? SYSTEM_TEMPLATES[lastMsgData.template].content 
            : lastMsgData.content,
          type: lastMsgData.type === 'system' ? 'system' : 'text',
          timestamp: lastMessageTimeISO, // ✅ ISO string as required
          ...(createReadByTracking(lastMsgData.type, lastMessageTimeISO, conv.customer.id, assignedAgent) && { readBy: createReadByTracking(lastMsgData.type, lastMessageTimeISO, conv.customer.id, assignedAgent) })
        };

        // Create conversation with FULL TypeScript contract compliance
        const conversation = {
          // ✅ All required fields
          id: conv.id,
          customerId: conv.customer.id,
          status: conv.status, // ✅ Uses realistic status distribution
          priority: getRandomPriority(),
          channel: conv.channel, // ✅ Multiple channels supported
          department: conv.department,
          metadata: {
            customerName: conv.customer.name,
            customerPhone: customer.phone,
            customerEmail: customer.email,
            subject: `${conv.department.replace(/-/g, ' ')} - ${(lastMsgData.content || '').substring(0, 50)}...`,
            tags: [conv.department, conv.channel, 'test_data'],
            routingInfo: {
              assignedDepartment: conv.department,
              aiAnalysisAttempts: 1,
              aiAnalysisHistory: [{
                attempt: 1,
                input: conv.messages[0].content,
                result: conv.department,
                confidence: 0.85 + Math.random() * 0.15,
                timestamp: baseISOTime
              }],
              departmentAssignedAt: baseISOTime
            },
            // ✅ Add transfer and supervision history as required by contracts
            ...(conv.hasTransferHistory && { transferHistory: createTransferHistory(baseISOTime, AGENTS) }),
            ...(conv.hasSupervision && { supervisionHistory: createSupervisionHistory(baseISOTime) })
          },
          createdAt: baseISOTime,
          updatedAt: lastMessageTimeISO,
          lastActivityAt: lastMessageTimeISO,
          
          // ✅ Optional fields with realistic distribution  
          ...(assignedAgent && { assignedTo: 'human' }),
          ...(assignedAgent && { assignedAgentId: assignedAgent }),
          ...(assignedAgent && { assignedAt: baseISOTime }),
          ...(conv.hasSupervision && { supervisorId: 'supervisor-001' }),
          ...(Math.random() > 0.9 && !conv.hasSupervision && { supervisorId: 'supervisor-001' }),
          ...(conv.hasClosedAt && { closedAt: lastMessageTimeISO }),
          
          // ✅ UI extension fields
          customer: customer,
          lastMessage: lastMessage,
          unreadCount: lastMsgData.unread ? 1 : (Math.random() > 0.6 ? Math.floor(Math.random() * 3) + 1 : 0)
        };
        
        // Save conversation
        await db.ref(`conversations/${conv.id}`).set(conversation);
        
        // Create all messages with FULL TypeScript compliance
        for (const [index, msgData] of conv.messages.entries()) {
          const messageTime = baseTime + msgData.delay;
          const messageTimeISO = new Date(messageTime).toISOString();
          const messageId = generateId('msg');
          
          let message = {
            id: messageId,
            conversationId: conv.id,
            timestamp: messageTimeISO, // ✅ ISO string as required by contract
            ...(createReadByTracking(msgData.type, messageTimeISO, conv.customer.id, assignedAgent) && { readBy: createReadByTracking(msgData.type, messageTimeISO, conv.customer.id, assignedAgent) }),
            metadata: {} // ✅ Initialize metadata as per contract
          };
          
          // Build message based on type with contract compliance
          if (msgData.type === 'customer') {
            message = {
              ...message,
              senderId: conv.customer.id,
              senderType: 'customer',
              content: msgData.content,
              type: 'text', // ✅ Use 'type' not 'messageType'
              metadata: {
                ...message.metadata,
                // Add customer-specific metadata as needed
              }
            };
          } else if (msgData.type === 'system') {
            const template = SYSTEM_TEMPLATES[msgData.template];
            message = {
              ...message,
              senderId: 'system',
              senderType: 'system',
              content: template.content,
              type: 'system', // ✅ Correct type field
              metadata: {
                systemAction: msgData.template,
                ...(msgData.reason && { systemReason: msgData.reason })
              }
            };
          } else if (msgData.type === 'agent') {
            message = {
              ...message,
              senderId: assignedAgent || CONSTANTS.JUAN_PEREZ_ID,
              senderType: 'agent',
              content: msgData.content,
              type: 'text',
              metadata: {
                agentId: assignedAgent || CONSTANTS.JUAN_PEREZ_ID,
                responseTime: Math.floor(Math.random() * 120) + 30 // 30-150 seconds
              }
            };
          } else if (msgData.type === 'supervisor') {
            message = {
              ...message,
              senderId: 'supervisor-001',
              senderType: 'supervisor',
              content: msgData.content,
              type: 'text',
              metadata: {
                supervisorAction: 'participate'
              }
            };
          }
          
          await db.ref(`conversations/${conv.id}/messages/${messageId}`).set(message);
          totalMessages++;
        }
        
        console.log(`✅ ${conv.customer.name}: ${conv.messages.length} mensajes creados (${conv.status}, ${conv.channel})`);
        
      } catch (error) {
        console.log(`❌ Error en ${conv.id}:`, error.message);
      }
    }
    
    } // End conversations section
    
    // STEP 2: Create agent statuses if requested  
    if (flags.all || flags.agents) {
      console.log('');
      console.log('👥 CREANDO AGENT STATUSES (usando agentes reales de Supabase)...');
      console.log('='.repeat(70));
    for (const agentData of AGENTS) {
      const agent = {
        id: agentData.id,
        name: agentData.name,
        email: agentData.email,
        status: {
          current: Math.random() > 0.2 ? 'available' : 'busy',
          lastActivity: baseISOTime,
          lastAssignment: baseISOTime
        },
        capacity: {
          maxConcurrentChats: Math.floor(Math.random() * 5) + 6, // 6-10 chats
          currentChatCount: Math.floor(Math.random() * 4) + 2,   // 2-5 current
          currentConversations: [],
          isOverloaded: false
        },
        skills: agentData.skills,
        departments: agentData.departments,
        createdAt: baseISOTime,
        updatedAt: baseISOTime
      };
      
      await db.ref(`agents/${agentData.id}`).set(agent);
      console.log(`✅ Agente ${agentData.name} creado`);
    }
    
    } // End agents section
    
    // STEP 3: Create queues if requested
    if (flags.all || flags.queues) {
      console.log('');
      console.log('📊 CREANDO QUEUES POR DEPARTAMENTOS...');
      console.log('='.repeat(70));
    const departmentQueues = {};
    
    for (const conv of COMPLETE_CONVERSATIONS) {
      if (!departmentQueues[conv.department]) {
        departmentQueues[conv.department] = [];
      }
      departmentQueues[conv.department].push(conv.id);
    }
    
    for (const [dept, conversations] of Object.entries(departmentQueues)) {
      const queueData = {
        id: dept,
        department: dept,
        name: dept.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
        conversations: conversations,
        totalCount: conversations.length,
        activeCount: conversations.filter(id => {
          const conv = COMPLETE_CONVERSATIONS.find(c => c.id === id);
          return conv.status === 'active';
        }).length,
        priority: 'medium',
        createdAt: baseISOTime,
        updatedAt: baseISOTime,
        agents: AGENTS.filter(agent => agent.departments.includes(dept)).map(a => a.id),
        status: 'active'
      };
      
      await db.ref(`queues/${dept}`).set(queueData);
      console.log(`✅ Queue "${dept}": ${conversations.length} conversaciones`);
    }
    
    } // End queues section
    
    console.log('');
    console.log('='.repeat(70));
    console.log('🎉 INSERTION COMPLETE - 100% TYPESCRIPT CONTRACTS COMPLIANT');
    console.log('='.repeat(70));
    
    // Show statistics based on what was actually inserted
    if (flags.all || flags.conversations) {
      console.log(`✅ Conversaciones: ${COMPLETE_CONVERSATIONS.length}`);
      console.log(`💬 Total mensajes: ${totalMessages}`);
    }
    if (flags.all || flags.agents) {
      console.log(`👥 Agentes: ${AGENTS.length} (from Supabase)`);
    }
    if (flags.all || flags.queues) {
      console.log(`📊 Queues: ${Object.keys(departmentQueues || {}).length}`);
    }
    if (flags.all || flags.conversations) {
      console.log('');
      console.log('📋 DISTRIBUTION SUMMARY:');
      const statusCount = {};
      const channelCount = {};
    COMPLETE_CONVERSATIONS.forEach(conv => {
      statusCount[conv.status] = (statusCount[conv.status] || 0) + 1;
      channelCount[conv.channel] = (channelCount[conv.channel] || 0) + 1;
    });
    
    console.log('   📈 Status:', Object.entries(statusCount).map(([k,v]) => `${k}:${v}`).join(', '));
    console.log('   📱 Channels:', Object.entries(channelCount).map(([k,v]) => `${k}:${v}`).join(', '));
    console.log('');
    } // End conversation statistics
    
    console.log('');
    console.log('🎯 VERIFICAR EN:');
    console.log('   💻 Chat UI: http://localhost:3007/agent');
    console.log('   🔥 Firebase UI: http://127.0.0.1:4000');
    console.log('   🔍 Chat Realtime API: http://localhost:3003/api/health');
    console.log('');
    console.log(`🚀 INSERTION COMPLETE - Sections: ${sections.join(', ')}`);
    
  } catch (error) {
    console.error('❌ Error creando data completa:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  createCompleteData()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createCompleteData };