#!/bin/bash

# 🚀 CX System - Unified Emulator Management Script
# Manages Firebase, PubSub, and Redis for development environment
# Combines functionality from multiple scripts into one comprehensive tool

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Firebase Configuration
FIREBASE_DB_PORT="9000"
FIREBASE_UI_PORT="4000"
FIREBASE_DATA_DIR="$PROJECT_ROOT/firebase-data"
FIREBASE_LOGS_DIR="$PROJECT_ROOT/logs/emulators"

# PubSub Configuration  
PUBSUB_PORT="8085"
PUBSUB_HOST="localhost:$PUBSUB_PORT"
export PUBSUB_EMULATOR_HOST="$PUBSUB_HOST"
export GOOGLE_CLOUD_PROJECT="cx-system-469120"

# Redis Configuration
REDIS_CONTAINER_NAME="cx-redis"
REDIS_PORT="6379"
REDIS_PASSWORD="cx-system-dev"
REDIS_VERSION="7-alpine"

# Ensure directories exist
mkdir -p "$FIREBASE_LOGS_DIR"
mkdir -p "$FIREBASE_DATA_DIR"

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

print_header() {
    echo -e "${PURPLE}════════════════════════════════════════${NC}"
    echo -e "${PURPLE}🚀 CX System Emulator Manager${NC}"
    echo -e "${PURPLE}════════════════════════════════════════${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_section() {
    echo
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '%.0s─' $(seq 1 ${#1}))${NC}"
}

# Check if port is in use
check_port() {
    nc -z "$1" "$2" 2>/dev/null
}

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Wait for service with timeout
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-15}
    
    echo -n "Waiting for $service_name"
    for i in $(seq 1 $timeout); do
        if check_port "$host" "$port"; then
            echo
            print_success "$service_name is ready"
            return 0
        fi
        echo -n "."
        sleep 1
    done
    
    echo
    print_error "$service_name failed to start within $timeout seconds"
    return 1
}

# =============================================================================
# FIREBASE FUNCTIONS
# =============================================================================

firebase_status() {
    print_section "🔥 Firebase Status"
    
    if check_port "127.0.0.1" "$FIREBASE_DB_PORT"; then
        print_success "Firebase Database emulator running on 127.0.0.1:$FIREBASE_DB_PORT"
    else
        print_error "Firebase Database emulator not running"
    fi
    
    if check_port "127.0.0.1" "$FIREBASE_UI_PORT"; then
        print_success "Firebase UI running on 127.0.0.1:$FIREBASE_UI_PORT"
    else
        print_error "Firebase UI not running"
    fi
    
    # Check data directory
    if [ -d "$FIREBASE_DATA_DIR" ] && [ "$(ls -A "$FIREBASE_DATA_DIR" 2>/dev/null)" ]; then
        print_info "Data persistence: Enabled ($FIREBASE_DATA_DIR)"
    else
        print_info "Data persistence: No existing data"
    fi
}

firebase_start() {
    print_section "🔥 Starting Firebase Emulators"
    
    if check_port "127.0.0.1" "$FIREBASE_DB_PORT"; then
        print_warning "Firebase emulators already running"
        return 0
    fi
    
    # Clean temporary export directories
    if [ -f "$SCRIPT_DIR/cleanup-firebase-temp.sh" ]; then
        "$SCRIPT_DIR/cleanup-firebase-temp.sh" >/dev/null 2>&1
    fi
    
    print_info "Starting Firebase Realtime Database + UI with data persistence..."
    
    cd "$PROJECT_ROOT"
    if [ -d "$FIREBASE_DATA_DIR" ] && [ "$(ls -A "$FIREBASE_DATA_DIR" 2>/dev/null)" ]; then
        firebase emulators:start --only database,ui --project cx-system-469120 \
            --import="$FIREBASE_DATA_DIR" --export-on-exit="$FIREBASE_DATA_DIR" \
            > "$FIREBASE_LOGS_DIR/firebase-emulator.log" 2>&1 &
        print_info "Importing existing data from $FIREBASE_DATA_DIR"
    else
        firebase emulators:start --only database,ui --project cx-system-469120 \
            --export-on-exit="$FIREBASE_DATA_DIR" \
            > "$FIREBASE_LOGS_DIR/firebase-emulator.log" 2>&1 &
        print_info "Starting with empty database"
    fi
    
    wait_for_service "127.0.0.1" "$FIREBASE_DB_PORT" "Firebase Database" 15
    wait_for_service "127.0.0.1" "$FIREBASE_UI_PORT" "Firebase UI" 10
}

firebase_stop() {
    print_section "🔥 Stopping Firebase Emulators"
    
    if ! check_port "127.0.0.1" "$FIREBASE_DB_PORT"; then
        print_warning "Firebase emulators not running"
        return 0
    fi
    
    # Export data before stopping
    print_info "Exporting Firebase data before stopping..."
    cd "$PROJECT_ROOT"
    firebase emulators:export "$FIREBASE_DATA_DIR" --project cx-system-469120 --force >/dev/null 2>&1 || print_warning "Export completed with warnings"
    
    # Stop Firebase processes
    print_info "Stopping Firebase processes..."
    pkill -f firebase >/dev/null 2>&1 || true
    
    # Force kill if needed
    sleep 2
    if pgrep -f firebase > /dev/null; then
        print_warning "Force killing Firebase processes..."
        pkill -9 -f firebase >/dev/null 2>&1 || true
    fi
    
    # Clean up temporary files
    rm -f database-debug.log ui-debug.log
    if [ -f "$SCRIPT_DIR/cleanup-firebase-temp.sh" ]; then
        "$SCRIPT_DIR/cleanup-firebase-temp.sh" >/dev/null 2>&1
    fi
    
    print_success "Firebase emulators stopped"
}

firebase_logs() {
    local lines=${1:-50}
    print_section "🔥 Firebase Logs (last $lines lines)"
    
    if [ -f "$FIREBASE_LOGS_DIR/firebase-emulator.log" ]; then
        tail -n "$lines" "$FIREBASE_LOGS_DIR/firebase-emulator.log"
    else
        print_error "No Firebase logs found"
    fi
}

firebase_data() {
    print_section "🔥 Firebase Data Management"
    
    if ! check_port "127.0.0.1" "$FIREBASE_DB_PORT"; then
        print_error "Firebase emulator not running"
        return 1
    fi
    
    print_info "Firebase emulator URLs:"
    echo "  • Database: http://127.0.0.1:$FIREBASE_DB_PORT"
    echo "  • UI: http://127.0.0.1:$FIREBASE_UI_PORT/database"
    echo
    
    print_info "Data management commands:"
    echo "  • Create test data: node scripts/create_complete_data.js --all"
    echo "  • Verify data: node scripts/verify_firebase_data.js"
    echo "  • Debug structure: node scripts/debug_firebase_structure.js"
    echo "  • Manual export: firebase emulators:export firebase-data --force"
    echo
    
    if [ -d "$FIREBASE_DATA_DIR" ]; then
        print_info "Data directory status:"
        du -sh "$FIREBASE_DATA_DIR" 2>/dev/null | sed 's/^/  • /'
        ls -la "$FIREBASE_DATA_DIR" 2>/dev/null | head -10 | sed 's/^/  /'
    fi
}

# =============================================================================
# PUBSUB FUNCTIONS  
# =============================================================================

pubsub_status() {
    print_section "📬 PubSub Status"
    
    if check_port "localhost" "$PUBSUB_PORT"; then
        print_success "PubSub emulator running on $PUBSUB_HOST"
        
        # Check topics
        if command -v gcloud >/dev/null 2>&1; then
            print_info "Checking topics..."
            PUBSUB_EMULATOR_HOST="$PUBSUB_HOST" gcloud pubsub topics list --project=cx-system-469120 2>/dev/null | grep -E "inbound-messages|outbound-messages" || print_warning "No topics found"
        fi
    else
        print_error "PubSub emulator not running"
    fi
}

pubsub_start() {
    print_section "📬 Starting PubSub Emulator"
    
    if check_port "localhost" "$PUBSUB_PORT"; then
        print_warning "PubSub emulator already running"
        return 0
    fi
    
    if ! command -v gcloud >/dev/null 2>&1; then
        print_error "gcloud CLI not found. Please install Google Cloud SDK."
        return 1
    fi
    
    print_info "Starting PubSub emulator on $PUBSUB_HOST..."
    gcloud beta emulators pubsub start --host-port="$PUBSUB_HOST" \
        > "$FIREBASE_LOGS_DIR/pubsub-emulator.log" 2>&1 &
    
    wait_for_service "localhost" "$PUBSUB_PORT" "PubSub emulator" 10
    
    # Setup topics and subscriptions
    pubsub_setup_topics
}

pubsub_stop() {
    print_section "📬 Stopping PubSub Emulator"
    
    if ! check_port "localhost" "$PUBSUB_PORT"; then
        print_warning "PubSub emulator not running"
        return 0
    fi
    
    print_info "Stopping PubSub processes..."
    pkill -f pubsub >/dev/null 2>&1 || true
    
    # Force kill if needed
    sleep 2
    if pgrep -f pubsub > /dev/null; then
        print_warning "Force killing PubSub processes..."
        pkill -9 -f pubsub >/dev/null 2>&1 || true
    fi
    
    # Clean up
    rm -f pubsub-debug.log
    
    print_success "PubSub emulator stopped"
}

pubsub_logs() {
    local lines=${1:-50}
    print_section "📬 PubSub Logs (last $lines lines)"
    
    if [ -f "$FIREBASE_LOGS_DIR/pubsub-emulator.log" ]; then
        tail -n "$lines" "$FIREBASE_LOGS_DIR/pubsub-emulator.log"
    else
        print_error "No PubSub logs found"
    fi
}

pubsub_setup_topics() {
    print_info "Setting up PubSub topics and subscriptions..."
    
    if [ -f "$SCRIPT_DIR/setup-pubsub-topics-improved.js" ]; then
        cd "$PROJECT_ROOT"
        PUBSUB_EMULATOR_HOST="$PUBSUB_HOST" node "$SCRIPT_DIR/setup-pubsub-topics-improved.js"
    else
        print_warning "Advanced PubSub setup script not found, using basic setup..."
        pubsub_basic_setup
    fi
}

pubsub_basic_setup() {
    if ! command -v gcloud >/dev/null 2>&1; then
        print_error "gcloud CLI required for topic setup"
        return 1
    fi
    
    # Create topics
    PUBSUB_EMULATOR_HOST="$PUBSUB_HOST" gcloud pubsub topics create inbound-messages --project=cx-system-469120 >/dev/null 2>&1 || true
    PUBSUB_EMULATOR_HOST="$PUBSUB_HOST" gcloud pubsub topics create outbound-messages --project=cx-system-469120 >/dev/null 2>&1 || true
    
    print_success "Basic PubSub topics created"
}

pubsub_topics() {
    print_section "📬 PubSub Topics and Subscriptions"
    
    if ! check_port "localhost" "$PUBSUB_PORT"; then
        print_error "PubSub emulator not running"
        return 1
    fi
    
    if ! command -v gcloud >/dev/null 2>&1; then
        print_error "gcloud CLI required"
        return 1
    fi
    
    print_info "Topics:"
    PUBSUB_EMULATOR_HOST="$PUBSUB_HOST" gcloud pubsub topics list --project=cx-system-469120 2>/dev/null || print_warning "Could not list topics"
    
    print_info "Subscriptions:"
    PUBSUB_EMULATOR_HOST="$PUBSUB_HOST" gcloud pubsub subscriptions list --project=cx-system-469120 2>/dev/null || print_warning "Could not list subscriptions"
}

# =============================================================================
# REDIS FUNCTIONS
# =============================================================================

redis_status() {
    print_section "🔴 Redis Status"
    
    check_docker
    
    if docker ps --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        print_success "Redis container running"
        
        # Get detailed info
        docker ps --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        # Redis info
        print_info "Redis info:"
        docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" INFO server 2>/dev/null | grep -E "(redis_version|uptime_in_seconds|connected_clients)" | sed 's/^/  /' || print_warning "Could not get Redis info"
        
        # Memory usage
        docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" INFO memory 2>/dev/null | grep -E "(used_memory_human|used_memory_peak_human)" | sed 's/^/  /' || print_warning "Could not get memory info"
        
    elif docker ps -a --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        print_warning "Redis container exists but is stopped"
        docker ps -a --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        print_error "Redis container does not exist"
    fi
}

redis_start() {
    print_section "🔴 Starting Redis"
    
    check_docker
    
    if docker ps --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        print_warning "Redis already running"
        return 0
    fi
    
    if docker ps -a --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        print_info "Starting existing Redis container..."
        docker start "$REDIS_CONTAINER_NAME" >/dev/null
    else
        print_info "Creating new Redis container..."
        docker run -d \
            --name "$REDIS_CONTAINER_NAME" \
            -p "$REDIS_PORT:6379" \
            --restart unless-stopped \
            redis:$REDIS_VERSION \
            redis-server --requirepass "$REDIS_PASSWORD" >/dev/null
    fi
    
    # Wait for Redis to be ready
    echo -n "Waiting for Redis to be ready"
    for i in {1..10}; do
        if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; then
            break
        fi
        echo -n "."
        sleep 1
    done
    echo
    
    if docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; then
        print_success "Redis started successfully on port $REDIS_PORT"
    else
        print_error "Redis failed to start properly"
        return 1
    fi
}

redis_stop() {
    print_section "🔴 Stopping Redis"
    
    check_docker
    
    if ! docker ps --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        print_warning "Redis is not running"
        return 0
    fi
    
    print_info "Stopping Redis container..."
    docker stop "$REDIS_CONTAINER_NAME" >/dev/null
    print_success "Redis stopped"
}

redis_logs() {
    local lines=${1:-50}
    print_section "🔴 Redis Logs (last $lines lines)"
    
    check_docker
    
    if docker ps --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        docker logs --tail "$lines" "$REDIS_CONTAINER_NAME"
    else
        print_error "Redis container not running"
    fi
}

redis_data() {
    print_section "🔴 Redis Data Overview"
    
    check_docker
    
    if ! docker ps --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        print_error "Redis is not running"
        return 1
    fi
    
    print_info "Database info:"
    docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" INFO keyspace 2>/dev/null | sed 's/^/  /' || print_info "No data found"
    
    print_info "Conversation states (sample):"
    CONV_KEYS=$(docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" KEYS "conversation:*" 2>/dev/null)
    
    if [ -n "$CONV_KEYS" ]; then
        echo "$CONV_KEYS" | head -5 | while read -r key; do
            if [ -n "$key" ]; then
                VALUE=$(docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" GET "$key" 2>/dev/null)
                echo "  $key: $VALUE"
            fi
        done
        
        TOTAL_KEYS=$(echo "$CONV_KEYS" | wc -l)
        if [ "$TOTAL_KEYS" -gt 5 ]; then
            print_info "... and $((TOTAL_KEYS - 5)) more conversation states"
        fi
    else
        print_info "No conversation states found"
    fi
}

redis_clean() {
    print_section "🔴 Redis Data Cleanup"
    
    check_docker
    
    if ! docker ps --filter "name=$REDIS_CONTAINER_NAME" --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"; then
        print_error "Redis is not running"
        return 1
    fi
    
    print_warning "This will delete ALL Redis data"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "Cleaning Redis data..."
        docker exec "$REDIS_CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" FLUSHALL >/dev/null
        print_success "Redis data cleaned"
    else
        print_info "Operation cancelled"
    fi
}

# =============================================================================
# COMBINED FUNCTIONS
# =============================================================================

all_status() {
    print_header
    firebase_status
    pubsub_status
    redis_status
    
    print_section "🎯 Development URLs"
    echo "  🔥 Firebase Database: http://127.0.0.1:$FIREBASE_DB_PORT"
    echo "  🔥 Firebase UI: http://127.0.0.1:$FIREBASE_UI_PORT/database"
    echo "  📬 PubSub Emulator: $PUBSUB_HOST"
    echo "  🔴 Redis: localhost:$REDIS_PORT"
}

all_start() {
    print_header
    print_info "Starting all emulators for CX System development..."
    
    # Start in order of dependencies
    redis_start
    sleep 1
    
    firebase_start
    sleep 1
    
    pubsub_start
    
    print_section "🎉 All Emulators Started Successfully"
    echo
    all_status
    
    print_section "🚀 Next Steps"
    echo "  1. Create test data: node scripts/create_complete_data.js --all"
    echo "  2. Start services: ./scripts/manage-services.sh dev"
    echo "  3. Test the system end-to-end"
}

all_stop() {
    print_header
    print_info "Stopping all emulators..."
    
    # Stop in reverse order
    pubsub_stop
    firebase_stop  # This exports data automatically
    redis_stop
    
    print_success "All emulators stopped"
}

all_restart() {
    all_stop
    sleep 3
    all_start
}

all_logs() {
    local lines=${1:-20}
    print_header
    firebase_logs "$lines"
    pubsub_logs "$lines"
    redis_logs "$lines"
}

# =============================================================================
# HELP FUNCTION
# =============================================================================

show_help() {
    print_header
    echo
    echo -e "${YELLOW}Usage: $0 [SERVICE] [COMMAND] [OPTIONS]${NC}"
    echo
    echo -e "${CYAN}Services:${NC}"
    echo "  all         All emulators (Firebase + PubSub + Redis)"
    echo "  firebase    Firebase Realtime Database + UI"
    echo "  pubsub      Google Cloud PubSub emulator"
    echo "  redis       Redis container for session storage"
    echo
    echo -e "${CYAN}Commands:${NC}"
    echo "  start       Start the service(s)"
    echo "  stop        Stop the service(s)"
    echo "  restart     Restart the service(s)"
    echo "  status      Show service status and info"
    echo "  logs        Show recent logs (default: 50 lines)"
    echo
    echo -e "${CYAN}Data Management:${NC}"
    echo "  data        Show data overview"
    echo "  topics      Show PubSub topics (pubsub only)"
    echo "  clean       Clean data (redis only, requires confirmation)"
    echo
    echo -e "${CYAN}Examples:${NC}"
    echo "  $0 all start                 # Start all emulators"
    echo "  $0 firebase status           # Check Firebase status"
    echo "  $0 redis logs 100           # Show last 100 Redis log lines"
    echo "  $0 pubsub topics            # List PubSub topics"
    echo "  $0 all logs                 # Show logs from all services"
    echo
    echo -e "${CYAN}Configuration:${NC}"
    echo "  Firebase DB: 127.0.0.1:$FIREBASE_DB_PORT"
    echo "  Firebase UI: 127.0.0.1:$FIREBASE_UI_PORT"
    echo "  PubSub: $PUBSUB_HOST"
    echo "  Redis: localhost:$REDIS_PORT"
    echo "  Data dir: $FIREBASE_DATA_DIR"
    echo
    echo -e "${YELLOW}💡 Tips:${NC}"
    echo "  • Firebase data is automatically exported on stop"
    echo "  • Redis requires Docker to be running"
    echo "  • Use 'all start' for complete development environment"
    echo "  • Logs are saved to $FIREBASE_LOGS_DIR"
}

# =============================================================================
# MAIN SCRIPT LOGIC
# =============================================================================

SERVICE=${1:-help}
COMMAND=${2:-help}
OPTION=${3:-50}

case "$SERVICE" in
    all)
        case "$COMMAND" in
            start) all_start ;;
            stop) all_stop ;;
            restart) all_restart ;;
            status) all_status ;;
            logs) all_logs "$OPTION" ;;
            *) echo "Available commands for 'all': start, stop, restart, status, logs"; exit 1 ;;
        esac
        ;;
    firebase)
        case "$COMMAND" in
            start) firebase_start ;;
            stop) firebase_stop ;;
            restart) firebase_stop && sleep 2 && firebase_start ;;
            status) firebase_status ;;
            logs) firebase_logs "$OPTION" ;;
            data) firebase_data ;;
            *) echo "Available commands for 'firebase': start, stop, restart, status, logs, data"; exit 1 ;;
        esac
        ;;
    pubsub)
        case "$COMMAND" in
            start) pubsub_start ;;
            stop) pubsub_stop ;;
            restart) pubsub_stop && sleep 2 && pubsub_start ;;
            status) pubsub_status ;;
            logs) pubsub_logs "$OPTION" ;;
            topics) pubsub_topics ;;
            *) echo "Available commands for 'pubsub': start, stop, restart, status, logs, topics"; exit 1 ;;
        esac
        ;;
    redis)
        case "$COMMAND" in
            start) redis_start ;;
            stop) redis_stop ;;
            restart) redis_stop && sleep 2 && redis_start ;;
            status) redis_status ;;
            logs) redis_logs "$OPTION" ;;
            data) redis_data ;;
            clean) redis_clean ;;
            *) echo "Available commands for 'redis': start, stop, restart, status, logs, data, clean"; exit 1 ;;
        esac
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown service: $SERVICE"
        echo
        show_help
        exit 1
        ;;
esac