#!/bin/bash

# CX Debug System Demo
echo "🎯 CX Debug System - Complete Demo"
echo "=================================="

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Function to check if service is running
check_service() {
    local port=$1
    local name=$2
    
    if nc -z localhost $port 2>/dev/null; then
        echo -e "${GREEN}✅ $name (port $port)${NC}"
        return 0
    else
        echo -e "${RED}❌ $name (port $port)${NC}"
        return 1
    fi
}

echo ""
echo -e "${BLUE}📋 Step 1: Starting CX Logs WebSocket Proxy...${NC}"
./scripts/start-logs-proxy.sh &
PROXY_SCRIPT_PID=$!
sleep 3

echo ""
echo -e "${BLUE}📋 Step 2: Checking Services Status...${NC}"
echo "Required services for full demo:"
check_service 3008 "Logs WebSocket Proxy"
check_service 3003 "Chat Realtime API" 
check_service 3007 "Chat UI"
check_service 9000 "Firebase Emulator"

echo ""
echo -e "${YELLOW}🛠️  Step 3: CX Debug Tools Available${NC}"
echo ""
echo "Once you open http://localhost:3007 in your browser, you'll have access to:"
echo ""
echo -e "${GREEN}📱 Browser Console Commands:${NC}"
echo "   window.cxDebug.help()           - Show all available commands"
echo "   window.cxDebug.clearCache()     - Clear all cached data"
echo "   window.cxDebug.showCacheState() - Show current cache state"
echo "   window.cxDebug.simulateOffline() - Test offline behavior"
echo "   window.cxDebug.forceSync()       - Force Firebase sync"
echo "   window.cxDebug.runDiagnostics()  - Run system diagnostics"
echo ""
echo -e "${GREEN}📁 Log Files:${NC}"
echo "   Browser logs: logs/browser/browser-console.log"  
echo "   Service logs: logs/services/[service-name]/"
echo "   Proxy logs:   logs/services/logs-proxy/logs-proxy.log"
echo ""
echo -e "${GREEN}🔍 Monitoring Commands:${NC}"
echo "   tail -f logs/browser/browser-console.log          # Watch browser logs"
echo "   tail -f logs/services/chat-realtime/*.log         # Watch API logs"
echo "   tail -f logs/services/logs-proxy/logs-proxy.log   # Watch proxy logs"
echo ""

if check_service 3007 "Chat UI" >/dev/null; then
    echo -e "${GREEN}🎉 Ready for Testing!${NC}"
    echo ""
    echo "1. Open: http://localhost:3007/agent"
    echo "2. Open Browser DevTools (F12)"
    echo "3. Try: cxDebug.help()"
    echo "4. Watch logs: tail -f logs/browser/browser-console.log"
    echo ""
    echo -e "${YELLOW}💡 Test Scenarios:${NC}"
    echo "   • Use cxDebug.simulateOffline() to test offline behavior"
    echo "   • Use cxDebug.clearCache() to reset data"
    echo "   • Use cxDebug.seedTestData() to load test conversations"
    echo "   • Check console logs streaming to file in real-time"
else
    echo -e "${YELLOW}⚠️  Chat UI not running${NC}"
    echo "To start Chat UI:"
    echo "   cd services/chat-ui && npm run dev -- --port 3007"
fi

echo ""
echo -e "${BLUE}📊 Log File Locations:${NC}"
echo "   📁 Browser Console: $(pwd)/logs/browser/"
echo "   📁 Services:        $(pwd)/logs/services/"
echo "   📁 Emulators:       $(pwd)/logs/emulators/"
echo ""

echo -e "${YELLOW}🛑 To Stop Everything:${NC}"
echo "   ./scripts/stop-logs-proxy.sh"
echo "   Ctrl+C to stop this demo"
echo ""

# Wait for user interrupt
echo -e "${BLUE}Press Ctrl+C to stop demo...${NC}"
trap "echo -e '\n🛑 Stopping demo...'; ./scripts/stop-logs-proxy.sh; exit 0" INT

# Keep script running
while true; do
    sleep 1
done