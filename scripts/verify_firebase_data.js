#!/usr/bin/env node

/**
 * Verify Firebase data - check if conversations exist and are properly assigned
 * Uses centralized Firebase configuration for consistency
 */

const { initializeFirebase, getFirebaseDatabase, CONSTANTS } = require('../services/chat-realtime/firebase-config');

// Initialize Firebase using centralized configuration
initializeFirebase();
const db = getFirebaseDatabase();

const JUAN_PEREZ_ID = CONSTANTS.JUAN_PEREZ_ID;

async function verifyFirebaseData() {
  try {
    console.log('🔍 VERIFYING FIREBASE DATA');
    console.log('=' .repeat(50));
    
    // Database already initialized at top of file
    
    // Check all conversations
    console.log('📋 Checking all conversations...');
    const conversationsRef = db.ref('conversations');
    const conversationsSnapshot = await conversationsRef.get();
    
    if (conversationsSnapshot.exists()) {
      const conversations = conversationsSnapshot.val();
      const conversationIds = Object.keys(conversations);
      console.log(`✅ Found ${conversationIds.length} total conversations`);
      
      // Filter conversations assigned to <PERSON>
      const juanConversations = conversationIds.filter(id => {
        const conv = conversations[id];
        return conv.agentId === JUAN_PEREZ_ID || conv.assignedTo === JUAN_PEREZ_ID;
      });
      
      console.log(`👤 Conversations assigned to Juan Pérez: ${juanConversations.length}`);
      
      if (juanConversations.length > 0) {
        console.log('\n📝 Juan\'s conversations:');
        juanConversations.forEach(id => {
          const conv = conversations[id];
          console.log(`  - ${id}: ${conv.metadata?.customerName || 'Unknown'} (${conv.status})`);
        });
      } else {
        console.log('❌ No conversations found for Juan Pérez');
        console.log('\n🔍 Checking assignment patterns...');
        conversationIds.slice(0, 3).forEach(id => {
          const conv = conversations[id];
          console.log(`  - ${id}:`);
          console.log(`    agentId: ${conv.agentId}`);
          console.log(`    assignedTo: ${conv.assignedTo}`);
          console.log(`    status: ${conv.status}`);
        });
      }
      
    } else {
      console.log('❌ No conversations found in Firebase');
    }
    
    // Check agent data
    console.log('\n👤 Checking agent data...');
    const agentRef = db.ref(`agents/${JUAN_PEREZ_ID}`);
    const agentSnapshot = await agentRef.get();
    
    if (agentSnapshot.exists()) {
      const agent = agentSnapshot.val();
      console.log(`✅ Agent found: ${agent.name}`);
      console.log(`   Status: ${agent.status?.current || 'unknown'}`);
      console.log(`   Capacity: ${JSON.stringify(agent.capacity || {})}`);
    } else {
      console.log('❌ Agent not found in Firebase');
    }
    
    // Test direct Firebase query like the API does
    console.log('\n🔍 Testing API-like query...');
    const apiQueryRef = db.ref('conversations');
    const apiSnapshot = await apiQueryRef.orderByChild('assignedTo').equalTo(JUAN_PEREZ_ID).get();
    
    if (apiSnapshot.exists()) {
      const apiResults = apiSnapshot.val();
      console.log(`✅ API query found ${Object.keys(apiResults).length} conversations`);
    } else {
      console.log('❌ API query returned no results');
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

if (require.main === module) {
  verifyFirebaseData()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}