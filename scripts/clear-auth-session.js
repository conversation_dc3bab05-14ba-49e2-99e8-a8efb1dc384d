/**
 * <PERSON><PERSON>t para limpiar completamente la sesión de autenticación
 * Copia y pega este código en la consola del navegador para resolver problemas de auth
 */

// 🧹 LIMPIADOR COMPLETO DE SESIÓN - COPY & PASTE
(function clearAuthSession() {
  console.log('🧹 INICIANDO LIMPIEZA COMPLETA DE SESIÓN...');
  
  // 1. Limpiar localStorage
  console.log('📦 Limpiando localStorage...');
  const localStorageKeys = Object.keys(localStorage);
  const removedLocalKeys = [];
  
  localStorageKeys.forEach(key => {
    if (key.includes('supabase') || key.includes('auth') || key.includes('sb-')) {
      localStorage.removeItem(key);
      removedLocalKeys.push(key);
    }
  });
  
  console.log(`   ✅ Removed ${removedLocalKeys.length} localStorage keys:`, removedLocalKeys);
  
  // 2. Limpiar sessionStorage
  console.log('🗂️ Limpiando sessionStorage...');
  const sessionStorageKeys = Object.keys(sessionStorage);
  const removedSessionKeys = [];
  
  sessionStorageKeys.forEach(key => {
    if (key.includes('supabase') || key.includes('auth') || key.includes('sb-')) {
      sessionStorage.removeItem(key);
      removedSessionKeys.push(key);
    }
  });
  
  console.log(`   ✅ Removed ${removedSessionKeys.length} sessionStorage keys:`, removedSessionKeys);
  
  // 3. Limpiar cookies relacionadas con auth
  console.log('🍪 Limpiando cookies de autenticación...');
  const cookies = document.cookie.split(';');
  const removedCookies = [];
  
  cookies.forEach(cookie => {
    const [name] = cookie.split('=');
    const trimmedName = name.trim();
    if (trimmedName.includes('supabase') || trimmedName.includes('auth') || trimmedName.includes('sb-')) {
      document.cookie = `${trimmedName}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
      removedCookies.push(trimmedName);
    }
  });
  
  console.log(`   ✅ Removed ${removedCookies.length} cookies:`, removedCookies);
  
  // 4. Limpiar IndexedDB (usado por algunos browsers para Supabase)
  console.log('💾 Limpiando IndexedDB...');
  if (typeof indexedDB !== 'undefined') {
    indexedDB.databases().then(databases => {
      databases.forEach(db => {
        if (db.name && (db.name.includes('supabase') || db.name.includes('auth'))) {
          console.log(`   🗑️ Eliminando base de datos: ${db.name}`);
          indexedDB.deleteDatabase(db.name);
        }
      });
    }).catch(() => {
      console.log('   ⚠️ No se pudo acceder a IndexedDB (normal en algunos browsers)');
    });
  }
  
  // 5. Intentar hacer sign out programáticamente si hay acceso al cliente Supabase
  console.log('🚪 Intentando sign out programático...');
  try {
    // Buscar cliente Supabase en window
    if (window.supabase || window._supabase) {
      const client = window.supabase || window._supabase;
      client.auth.signOut().then(() => {
        console.log('   ✅ Sign out programático exitoso');
      }).catch(err => {
        console.log('   ⚠️ Error en sign out programático:', err.message);
      });
    } else {
      console.log('   ⚠️ Cliente Supabase no encontrado en window');
    }
  } catch (error) {
    console.log('   ⚠️ Error intentando sign out:', error.message);
  }
  
  // 6. Resumen final
  console.log('');
  console.log('✨ LIMPIEZA COMPLETA FINALIZADA ✨');
  console.log('='.repeat(50));
  console.log(`📦 localStorage: ${removedLocalKeys.length} keys eliminadas`);
  console.log(`🗂️ sessionStorage: ${removedSessionKeys.length} keys eliminadas`);
  console.log(`🍪 Cookies: ${removedCookies.length} eliminadas`);
  console.log('💾 IndexedDB: Limpieza iniciada');
  console.log('');
  console.log('🔄 SIGUIENTE PASO: Recarga la página (F5 o Cmd+R)');
  console.log('📱 O cierra y abre el navegador para una limpieza total');
  
  return {
    localStorage: removedLocalKeys,
    sessionStorage: removedSessionKeys,
    cookies: removedCookies,
    status: 'completed'
  };
})();