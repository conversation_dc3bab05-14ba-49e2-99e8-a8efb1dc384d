# Development Scripts

Scripts esenciales para el desarrollo del CX System.

## 🧹 Authentication

### `clear-auth-session.js`
**Browser Console Script** - Limpia problemas de autenticación cuando hay loops.

**Usage:**
1. Open DevTools Console (F12)
2. Copy-paste el contenido completo del archivo
3. Press Enter → Reload page

---

## 🔥 Firebase Data

### `create_complete_data.js` ⭐ **PRINCIPAL**
Script maestro que crea toda la data de desarrollo de forma completa.

```bash
cd /Users/<USER>/Dev/cx-system/scripts
node create_complete_data.js
```

**Crea:**
- 15 conversaciones completas con mensajes realistas
- <PERSON><PERSON><PERSON><PERSON> de sistema (welcome, agent assigned, etc.)  
- Datos del agente <PERSON>
- Queues por departamentos
- Contadores y metadata correctos

### `verify_firebase_data.js`
Verifica la integridad de los datos en Firebase.

```bash
node verify_firebase_data.js
```

### `debug_firebase_structure.js`
Debug utility para inspeccionar estructura de Firebase.

```bash
node debug_firebase_structure.js
```

---

## 🚀 Emulators con Persistencia de Datos

### Shell Scripts (.sh)
- `start-all-emulators.sh` - Inicia todos los emulators
- `start-firebase-emulator.sh` - Solo Firebase **con import automático**
- `start-firebase-ui-only.sh` - **Solo UI** de Firebase (sin tocar database)
- `start-pubsub-emulator.sh` - Solo PubSub  
- `stop-firebase-emulator.sh` - Para Firebase **con export automático**
- `export-firebase-data.sh` - Exporta datos manualmente
- `manage-services.sh` ⭐ **GESTOR DE SERVICIOS** - Control inteligente de servicios

### Node Scripts
- `setup-pubsub-topics.js` - Configura topics de PubSub

### 💾 Persistencia de Datos
Los scripts ahora incluyen **persistencia automática**:
- **Al iniciar**: Importa datos de `firebase-data/` (raíz del proyecto) si existen
- **Al parar**: Exporta datos automáticamente a `firebase-data/` (raíz del proyecto)
- **Manual**: Usa `./export-firebase-data.sh` para exportar en cualquier momento
- **📁 Ubicación**: Todos los datos se almacenan en `/firebase-data/` (raíz del proyecto)

---

## 🎯 Workflow Recomendado

**Desarrollo diario con persistencia:**
```bash
# 1. Start emulators (importa datos automáticamente si existen)
./start-all-emulators.sh

# 2. Solo si necesitas datos frescos:
node create_complete_data.js

# 3. Start services
cd ../services/chat-realtime && npm start
cd ../services/chat-ui && npm run dev

# 4. Access UI
# http://localhost:3007/agent

# 5. Al terminar (exporta datos automáticamente)
./stop-all-emulators.sh
```

**Primera vez (setup inicial):**
```bash
# 1. Crear datos iniciales
node create_complete_data.js

# 2. Exportar para persistencia
./export-firebase-data.sh

# 3. Ahora tienes persistencia automática
```

**Si hay problemas de auth:**
1. Use `clear-auth-session.js` en browser console
2. Reload page

**Si los datos se ven raros:**
1. `node verify_firebase_data.js` 
2. `node create_complete_data.js` (recrear)

**Si solo necesitas la UI de Firebase (database ya corriendo):**
```bash
./start-firebase-ui-only.sh
# Visita http://localhost:4000
```

---

## 🎛️ Gestor de Servicios

### `manage-services.sh` - Control Inteligente de Servicios

Script completo para gestión de servicios backend y frontend:

**Comandos principales:**
```bash
./manage-services.sh status                # Estado de todos los servicios
./manage-services.sh start chat-ui        # Iniciar Chat UI
./manage-services.sh restart chat-realtime # Reiniciar API
./manage-services.sh stop                 # Parar todos los servicios
./manage-services.sh clean                # Parar todos + limpiar caches
./manage-services.sh dev                  # Entorno de desarrollo (UI + API)
./manage-services.sh full                 # Sistema completo
./manage-services.sh logs chat-realtime   # Ver logs de servicio
```

**Servicios gestionados:**
- `session-manager` (puerto 3001)
- `channel-router` (puerto 3002) 
- `chat-realtime` (puerto 3003)
- `bot-human-router` (puerto 3004)
- `twilio` (puerto 3005)
- `chat-ui` (puerto 3007)

**Funcionalidades:**
- 🔍 Detección automática de servicios corriendo
- 🚀 Inicio inteligente (solo si no está corriendo)
- 🛑 Parada graceful con timeout y force-kill
- 🧹 Limpieza de caches (.next, dist, .cache)
- 📋 Logs organizados en `/logs/services/`
- 🎯 Modo desarrollo (solo esenciales: API + UI)
- 📊 Estado completo con emuladores incluidos

---

## ⚙️ Environment

- Firebase Emulator: 127.0.0.1:9000  
- PubSub Emulator: localhost:8085
- Config: `../services/chat-realtime/firebase-config.js`