#!/bin/bash

# Redis Management Script for CX System
# Manages Redis instance for conversation state storage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Redis configuration
REDIS_CONTAINER_NAME="cx-redis"
REDIS_PORT="6379"
REDIS_PASSWORD="cx-system-dev"
REDIS_VERSION="7-alpine"

# Function to print colored output
print_status() {
    echo -e "${BLUE}📊 Redis Status${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if Redis container exists
redis_exists() {
    docker ps -a --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"
}

# Function to check if Redis is running
redis_running() {
    docker ps --format "table {{.Names}}" | grep -q "^${REDIS_CONTAINER_NAME}$"
}

# Function to start Redis
start_redis() {
    print_info "Starting Redis for CX System..."
    
    if redis_running; then
        print_warning "Redis is already running"
        return 0
    fi
    
    if redis_exists; then
        print_info "Starting existing Redis container..."
        docker start ${REDIS_CONTAINER_NAME}
    else
        print_info "Creating new Redis container..."
        docker run -d \
            --name ${REDIS_CONTAINER_NAME} \
            -p ${REDIS_PORT}:6379 \
            --restart unless-stopped \
            redis:${REDIS_VERSION} \
            redis-server --requirepass ${REDIS_PASSWORD}
    fi
    
    # Wait for Redis to be ready
    echo -n "Waiting for Redis to be ready"
    for i in {1..10}; do
        if docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} ping > /dev/null 2>&1; then
            break
        fi
        echo -n "."
        sleep 1
    done
    echo
    
    if docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} ping > /dev/null 2>&1; then
        print_success "Redis started successfully on port ${REDIS_PORT}"
    else
        print_error "Redis failed to start properly"
        return 1
    fi
}

# Function to stop Redis
stop_redis() {
    print_info "Stopping Redis..."
    
    if ! redis_running; then
        print_warning "Redis is not running"
        return 0
    fi
    
    docker stop ${REDIS_CONTAINER_NAME}
    print_success "Redis stopped"
}

# Function to restart Redis
restart_redis() {
    print_info "Restarting Redis..."
    stop_redis
    sleep 2
    start_redis
}

# Function to show Redis status
status_redis() {
    print_status
    echo
    
    if redis_running; then
        print_success "Redis is running"
        
        # Get container info
        echo -e "${CYAN}Container Info:${NC}"
        docker ps --filter "name=${REDIS_CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo
        
        # Get Redis info
        echo -e "${CYAN}Redis Info:${NC}"
        docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} INFO server | grep -E "(redis_version|uptime_in_seconds|connected_clients)" 2>/dev/null || print_warning "Could not fetch Redis info"
        echo
        
        # Get memory usage
        echo -e "${CYAN}Memory Usage:${NC}"
        docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} INFO memory | grep -E "(used_memory_human|used_memory_peak_human)" 2>/dev/null || print_warning "Could not fetch memory info"
        echo
        
    elif redis_exists; then
        print_warning "Redis container exists but is not running"
        docker ps -a --filter "name=${REDIS_CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        print_error "Redis container does not exist"
    fi
}

# Function to show Redis data
data_redis() {
    print_info "Redis Data Overview"
    echo
    
    if ! redis_running; then
        print_error "Redis is not running"
        return 1
    fi
    
    echo -e "${CYAN}Database Info:${NC}"
    docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} INFO keyspace 2>/dev/null || print_info "No data found"
    echo
    
    echo -e "${CYAN}Conversation States (sample):${NC}"
    # Get conversation state keys
    CONV_KEYS=$(docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} KEYS "conversation:*" 2>/dev/null)
    
    if [ -n "$CONV_KEYS" ]; then
        echo "$CONV_KEYS" | head -5 | while read -r key; do
            if [ -n "$key" ]; then
                VALUE=$(docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} GET "$key" 2>/dev/null)
                echo "  $key: $VALUE"
            fi
        done
        
        TOTAL_KEYS=$(echo "$CONV_KEYS" | wc -l)
        if [ "$TOTAL_KEYS" -gt 5 ]; then
            print_info "... and $((TOTAL_KEYS - 5)) more conversation states"
        fi
    else
        print_info "No conversation states found"
    fi
    echo
}

# Function to clean Redis data
clean_redis() {
    print_warning "This will delete ALL Redis data"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if redis_running; then
            print_info "Cleaning Redis data..."
            docker exec ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD} FLUSHALL
            print_success "Redis data cleaned"
        else
            print_error "Redis is not running"
            return 1
        fi
    else
        print_info "Operation cancelled"
    fi
}

# Function to connect to Redis CLI
cli_redis() {
    if ! redis_running; then
        print_error "Redis is not running"
        return 1
    fi
    
    print_info "Connecting to Redis CLI..."
    print_info "Password: ${REDIS_PASSWORD}"
    print_info "Type 'exit' to quit"
    echo
    
    docker exec -it ${REDIS_CONTAINER_NAME} redis-cli -a ${REDIS_PASSWORD}
}

# Function to show help
show_help() {
    echo -e "${BLUE}Redis Management Script for CX System${NC}"
    echo
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  start     Start Redis container"
    echo "  stop      Stop Redis container"
    echo "  restart   Restart Redis container"
    echo "  status    Show Redis status and info"
    echo "  data      Show Redis data overview"
    echo "  clean     Clean all Redis data (requires confirmation)"
    echo "  cli       Connect to Redis CLI"
    echo "  help      Show this help message"
    echo
    echo "Configuration:"
    echo "  Container: ${REDIS_CONTAINER_NAME}"
    echo "  Port: ${REDIS_PORT}"
    echo "  Version: ${REDIS_VERSION}"
    echo
}

# Main script logic
case "${1:-help}" in
    start)
        start_redis
        ;;
    stop)
        stop_redis
        ;;
    restart)
        restart_redis
        ;;
    status)
        status_redis
        ;;
    data)
        data_redis
        ;;
    clean)
        clean_redis
        ;;
    cli)
        cli_redis
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo
        show_help
        exit 1
        ;;
esac