#!/usr/bin/env node

/**
 * Test script para verificar el WebSocket proxy de logs
 * Simula un cliente que envía logs como lo haría el browser
 */

const WebSocket = require('ws');

const WS_URL = 'ws://localhost:3008';

console.log('🧪 Testing CX Logs WebSocket Proxy...');
console.log(`📡 Connecting to: ${WS_URL}`);

const ws = new WebSocket(WS_URL);

ws.on('open', () => {
    console.log('✅ Connected to logs proxy');
    
    // Simular diferentes tipos de logs
    const testLogs = [
        {
            level: 'log',
            args: ['Test log message from Node.js test script'],
            timestamp: new Date().toISOString(),
            url: 'http://localhost:3007/test',
            userAgent: 'Node.js Test Script'
        },
        {
            level: 'warn',
            args: ['⚠️ Test warning message'],
            timestamp: new Date().toISOString(),
            url: 'http://localhost:3007/test',
            userAgent: 'Node.js Test Script'
        },
        {
            level: 'error',
            args: ['❌ Test error message', { errorCode: 500 }],
            timestamp: new Date().toISOString(),
            url: 'http://localhost:3007/test',
            userAgent: 'Node.js Test Script'
        },
        {
            level: 'info',
            args: ['ℹ️ Test info message with data', { userId: 123, action: 'test' }],
            timestamp: new Date().toISOString(),
            url: 'http://localhost:3007/test',
            userAgent: 'Node.js Test Script'
        }
    ];
    
    // Enviar logs con intervalos
    let index = 0;
    const sendInterval = setInterval(() => {
        if (index >= testLogs.length) {
            clearInterval(sendInterval);
            console.log('🎉 All test logs sent successfully');
            console.log('💡 Check the log file: tail -f logs/browser/browser-console.log');
            ws.close();
            return;
        }
        
        const logData = testLogs[index];
        console.log(`📤 Sending ${logData.level} message...`);
        ws.send(JSON.stringify(logData));
        index++;
    }, 1000);
});

ws.on('message', (data) => {
    try {
        const response = JSON.parse(data.toString());
        console.log('📥 Received from proxy:', response.type, response.message || '');
    } catch (err) {
        console.log('📥 Received raw:', data.toString());
    }
});

ws.on('close', (code, reason) => {
    console.log(`🔌 Connection closed (${code}: ${reason})`);
    console.log('✅ Test completed successfully!');
    process.exit(0);
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error.message);
    console.log('💡 Make sure the logs proxy is running:');
    console.log('   ./scripts/manage-services.sh start logs-proxy');
    process.exit(1);
});

// Timeout fallback
setTimeout(() => {
    console.log('⏰ Test timeout - closing connection');
    ws.close();
}, 10000);