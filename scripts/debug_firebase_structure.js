#!/usr/bin/env node

/**
 * Debug Firebase structure to understand where conversations are stored
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from project root
require('dotenv').config({ path: path.resolve(__dirname, '../../../.env') });

// Initialize Firebase Admin
if (!admin.apps.length) {
  const projectId = process.env.GOOGLE_CLOUD_PROJECT_ID || 'cx-system-469120';
  const emulatorHost = '127.0.0.1:9000';
  
  process.env.FIREBASE_AUTH_EMULATOR_HOST = '127.0.0.1:9099';
  process.env.FIRESTORE_EMULATOR_HOST = '127.0.0.1:8080';
  process.env.FIREBASE_DATABASE_EMULATOR_HOST = emulatorHost;
  
  admin.initializeApp({
    projectId,
    databaseURL: `http://${emulatorHost}/?ns=${projectId}`
  });
  
  console.log(`🔥 Firebase initialized with project: ${projectId}`);
}

async function debugFirebaseStructure() {
  try {
    console.log('🔍 DEBUGGING FIREBASE STRUCTURE');
    console.log('=' .repeat(50));
    
    const db = admin.database();
    
    // Check root structure
    console.log('📁 Checking root structure...');
    const rootRef = db.ref('/');
    const rootSnapshot = await rootRef.get();
    
    if (rootSnapshot.exists()) {
      const rootData = rootSnapshot.val();
      const rootKeys = Object.keys(rootData);
      console.log('✅ Root keys found:', rootKeys);
      
      // Check conversations specifically
      if (rootKeys.includes('conversations')) {
        console.log('\n📋 Checking conversations structure...');
        const conversationsRef = db.ref('/conversations');
        const conversationsSnapshot = await conversationsRef.get();
        
        if (conversationsSnapshot.exists()) {
          const conversationsData = conversationsSnapshot.val();
          const conversationIds = Object.keys(conversationsData);
          
          console.log(`✅ Found ${conversationIds.length} conversations:`);
          conversationIds.forEach((id, index) => {
            console.log(`   ${index + 1}. ${id}`);
          });
          
          // Check if our target conversations exist
          const targetConversations = [
            'twilio_wa_1234567890_+default',
            'twilio_wa_1234567891_+default', 
            'twilio_wa_1234567892_+default',
            'twilio_wa_1234567893_+default'
          ];
          
          console.log('\n🎯 Checking target conversations:');
          for (const targetId of targetConversations) {
            const exists = conversationIds.includes(targetId);
            console.log(`   ${exists ? '✅' : '❌'} ${targetId}`);
            
            if (exists) {
              const conversation = conversationsData[targetId];
              console.log(`      Customer: ${conversation.customer?.name || 'N/A'} (${conversation.customer?.phone || 'N/A'})`);
              console.log(`      Status: ${conversation.status || 'N/A'}`);
              console.log(`      Agent: ${conversation.agentId || conversation.assignedTo || 'None'}`);
            }
          }
          
          // Show a sample conversation structure
          if (conversationIds.length > 0) {
            const sampleId = conversationIds[0];
            console.log(`\n📄 Sample conversation structure (${sampleId}):`);
            const sampleConversation = conversationsData[sampleId];
            console.log(JSON.stringify(sampleConversation, null, 2));
          }
          
        } else {
          console.log('❌ No conversations found in /conversations path');
        }
      } else {
        console.log('❌ No conversations key found in root');
      }
      
      // Check agents structure
      if (rootKeys.includes('agents')) {
        console.log('\n👥 Checking agents structure...');
        const agentsRef = db.ref('/agents');
        const agentsSnapshot = await agentsRef.get();
        
        if (agentsSnapshot.exists()) {
          const agentsData = agentsSnapshot.val();
          const agentIds = Object.keys(agentsData);
          
          console.log(`✅ Found ${agentIds.length} agents:`);
          agentIds.forEach((id, index) => {
            const agent = agentsData[id];
            console.log(`   ${index + 1}. ${id}`);
            console.log(`      Status: ${agent.status?.availability || 'N/A'}`);
            console.log(`      Current chats: ${agent.capacity?.currentChatCount || 0}`);
          });
        }
      }
      
    } else {
      console.log('❌ Root snapshot is empty');
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

if (require.main === module) {
  debugFirebaseStructure()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}