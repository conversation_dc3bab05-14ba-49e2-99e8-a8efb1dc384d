#!/bin/bash

# Clean and rotate logs script
echo "🧹 Cleaning CX System logs..."

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Create logs directory if it doesn't exist
mkdir -p logs/{services/{twilio,channel-router,session-manager,bot-human-router,chat-realtime,chat-ui,analytics},emulators,testing}

# Function to clean logs older than X days
clean_old_logs() {
    local dir=$1
    local days=${2:-7}
    
    if [ -d "$dir" ]; then
        echo -e "${BLUE}Cleaning logs older than $days days in $dir...${NC}"
        find "$dir" -name "*.log" -type f -mtime +$days -exec rm -f {} \;
        echo -e "${GREEN}✅ Old logs cleaned in $dir${NC}"
    fi
}

# Function to compress large logs
compress_large_logs() {
    local dir=$1
    local size=${2:-10M}
    
    if [ -d "$dir" ]; then
        echo -e "${BLUE}Compressing logs larger than $size in $dir...${NC}"
        find "$dir" -name "*.log" -type f -size +$size ! -name "*.gz" -exec gzip {} \;
        echo -e "${GREEN}✅ Large logs compressed in $dir${NC}"
    fi
}

# Function to show log sizes
show_log_sizes() {
    echo -e "${YELLOW}📊 Current log sizes:${NC}"
    if [ -d "logs" ]; then
        du -sh logs/* 2>/dev/null | sort -hr
    else
        echo "No logs directory found"
    fi
}

# Parse command line arguments
CLEAN_OLD=false
COMPRESS_LARGE=false
SHOW_SIZES=false
DAYS=7
SIZE="10M"

while [[ $# -gt 0 ]]; do
    case $1 in
        --old)
            CLEAN_OLD=true
            shift
            ;;
        --compress)
            COMPRESS_LARGE=true
            shift
            ;;
        --sizes)
            SHOW_SIZES=true
            shift
            ;;
        --days)
            DAYS="$2"
            shift 2
            ;;
        --size)
            SIZE="$2"
            shift 2
            ;;
        --all)
            CLEAN_OLD=true
            COMPRESS_LARGE=true
            SHOW_SIZES=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --old              Clean logs older than specified days (default: 7)"
            echo "  --compress         Compress logs larger than specified size (default: 10M)"
            echo "  --sizes           Show current log directory sizes"
            echo "  --days DAYS       Set days for old log cleanup (default: 7)"
            echo "  --size SIZE       Set size threshold for compression (default: 10M)"
            echo "  --all             Run all cleanup operations"
            echo "  --help, -h        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 --all                    # Run all cleanup operations"
            echo "  $0 --old --days 3          # Clean logs older than 3 days"
            echo "  $0 --compress --size 5M    # Compress logs larger than 5MB"
            echo "  $0 --sizes                 # Show log sizes only"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# If no options provided, show help
if [ "$CLEAN_OLD" = false ] && [ "$COMPRESS_LARGE" = false ] && [ "$SHOW_SIZES" = false ]; then
    echo "No operations specified. Use --help for usage information"
    echo ""
    echo "Quick commands:"
    echo "  $0 --all     # Run all cleanup operations"
    echo "  $0 --sizes   # Show current log sizes"
    exit 0
fi

echo -e "${BLUE}🚀 Starting log cleanup operations...${NC}"

# Show sizes if requested
if [ "$SHOW_SIZES" = true ]; then
    show_log_sizes
    echo ""
fi

# Clean old logs
if [ "$CLEAN_OLD" = true ]; then
    echo -e "${YELLOW}🗑️  Cleaning logs older than $DAYS days...${NC}"
    clean_old_logs "logs/services" $DAYS
    clean_old_logs "logs/emulators" $DAYS
    clean_old_logs "logs/testing" $DAYS
    echo ""
fi

# Compress large logs
if [ "$COMPRESS_LARGE" = true ]; then
    echo -e "${YELLOW}📦 Compressing logs larger than $SIZE...${NC}"
    compress_large_logs "logs/services" $SIZE
    compress_large_logs "logs/emulators" $SIZE
    compress_large_logs "logs/testing" $SIZE
    echo ""
fi

# Final status
echo -e "${GREEN}🎉 Log cleanup completed!${NC}"

# Show final sizes
if [ "$SHOW_SIZES" = true ]; then
    echo ""
    echo -e "${YELLOW}📊 Final log sizes:${NC}"
    show_log_sizes
fi

echo ""
echo -e "${BLUE}💡 Tip: Add this to your crontab for automatic cleanup:${NC}"
echo "   0 2 * * * /path/to/cx-system/scripts/clean-logs.sh --all"