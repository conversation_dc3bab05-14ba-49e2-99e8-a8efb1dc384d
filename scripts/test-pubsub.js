#!/usr/bin/env node

/**
 * Test PubSub Message Flow
 * This script tests publishing messages to PubSub topics and verifies the emulator is working
 */

const { PubSub } = require('@google-cloud/pubsub');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

async function testPubSub() {
  console.log(`${colors.blue}🧪 Testing PubSub Emulator...${colors.reset}\n`);

  // Ensure we're using the emulator
  if (!process.env.PUBSUB_EMULATOR_HOST) {
    process.env.PUBSUB_EMULATOR_HOST = 'localhost:8085';
  }

  const pubSubClient = new PubSub({
    projectId: 'cx-system-469120'
  });

  try {
    console.log(`${colors.yellow}📡 Connecting to PubSub emulator at: ${process.env.PUBSUB_EMULATOR_HOST}${colors.reset}`);
    
    // Test 1: List topics
    console.log(`\n${colors.blue}Test 1: Listing topics...${colors.reset}`);
    const [topics] = await pubSubClient.getTopics();
    
    if (topics.length === 0) {
      console.log(`${colors.red}❌ No topics found. Run setup script first.${colors.reset}`);
      process.exit(1);
    }
    
    console.log(`${colors.green}✅ Found ${topics.length} topics:${colors.reset}`);
    topics.forEach(topic => {
      console.log(`   • ${topic.name.split('/').pop()}`);
    });

    // Test 2: List subscriptions
    console.log(`\n${colors.blue}Test 2: Listing subscriptions...${colors.reset}`);
    const [subscriptions] = await pubSubClient.getSubscriptions();
    
    console.log(`${colors.green}✅ Found ${subscriptions.length} subscriptions:${colors.reset}`);
    subscriptions.forEach(sub => {
      console.log(`   • ${sub.name.split('/').pop()}`);
    });

    // Test 3: Publish test message to inbound topic
    console.log(`\n${colors.blue}Test 3: Publishing test message to inbound-messages topic...${colors.reset}`);
    
    const inboundTopic = pubSubClient.topic('inbound-messages');
    const testMessage = {
      sessionId: 'test-session-123',
      from: 'whatsapp:+1234567890',
      to: 'whatsapp:+0987654321',
      body: 'Test message from PubSub test script',
      channel: 'whatsapp',
      timestamp: new Date().toISOString(),
      metadata: {
        test: true,
        source: 'test-script'
      }
    };

    const messageBuffer = Buffer.from(JSON.stringify(testMessage));
    const [messageId] = await inboundTopic.publish(messageBuffer);
    
    console.log(`${colors.green}✅ Message published successfully!${colors.reset}`);
    console.log(`   Message ID: ${messageId}`);
    console.log(`   Message content: ${JSON.stringify(testMessage, null, 2)}`);

    // Test 4: Publish test message to outbound topic
    console.log(`\n${colors.blue}Test 4: Publishing test message to outbound-messages topic...${colors.reset}`);
    
    const outboundTopic = pubSubClient.topic('outbound-messages');
    const outboundMessage = {
      to: 'whatsapp:+1234567890',
      body: 'Test response from PubSub test script',
      channel: 'whatsapp',
      sessionId: 'test-session-123',
      timestamp: new Date().toISOString()
    };

    const outboundBuffer = Buffer.from(JSON.stringify(outboundMessage));
    const [outboundMessageId] = await outboundTopic.publish(outboundBuffer);
    
    console.log(`${colors.green}✅ Message published successfully!${colors.reset}`);
    console.log(`   Message ID: ${outboundMessageId}`);

    // Test 5: Check subscription details
    console.log(`\n${colors.blue}Test 5: Checking subscription configurations...${colors.reset}`);
    
    for (const subscription of subscriptions) {
      const [metadata] = await subscription.getMetadata();
      const subName = subscription.name.split('/').pop();
      
      console.log(`\n   ${colors.yellow}Subscription: ${subName}${colors.reset}`);
      console.log(`   • Topic: ${metadata.topic.split('/').pop()}`);
      
      if (metadata.pushConfig && metadata.pushConfig.pushEndpoint) {
        console.log(`   • Push endpoint: ${metadata.pushConfig.pushEndpoint}`);
        
        // Check if the endpoint is reachable
        const url = new URL(metadata.pushConfig.pushEndpoint);
        const port = url.port || (url.protocol === 'https:' ? 443 : 80);
        
        const net = require('net');
        const isReachable = await new Promise(resolve => {
          const socket = new net.Socket();
          socket.setTimeout(1000);
          
          socket.on('connect', () => {
            socket.destroy();
            resolve(true);
          });
          
          socket.on('timeout', () => {
            socket.destroy();
            resolve(false);
          });
          
          socket.on('error', () => {
            resolve(false);
          });
          
          socket.connect(port, url.hostname);
        });
        
        if (isReachable) {
          console.log(`   • Endpoint status: ${colors.green}✅ Reachable${colors.reset}`);
        } else {
          console.log(`   • Endpoint status: ${colors.red}❌ Not reachable (service might not be running)${colors.reset}`);
        }
      } else {
        console.log(`   • Type: Pull subscription`);
      }
    }

    // Summary
    console.log(`\n${colors.green}═══════════════════════════════════════════════════════${colors.reset}`);
    console.log(`${colors.green}✅ All PubSub tests passed!${colors.reset}`);
    console.log(`${colors.green}═══════════════════════════════════════════════════════${colors.reset}`);
    
    console.log(`\n${colors.yellow}📝 Notes:${colors.reset}`);
    console.log(`• Messages were published to both topics successfully`);
    console.log(`• If services are running, they should receive these messages`);
    console.log(`• Check service logs to verify message reception`);
    console.log(`• Push endpoints that are not reachable will queue messages until the service starts`);

  } catch (error) {
    console.error(`\n${colors.red}❌ Test failed:${colors.reset}`, error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.error(`${colors.red}PubSub emulator is not running!${colors.reset}`);
      console.error(`Run: ./scripts/start-pubsub-emulator.sh`);
    } else if (error.code === 5) {
      console.error(`${colors.red}Topic or subscription not found!${colors.reset}`);
      console.error(`Run: node scripts/setup-pubsub-topics.js`);
    }
    
    process.exit(1);
  }
}

// Run the test
testPubSub().catch(console.error);
