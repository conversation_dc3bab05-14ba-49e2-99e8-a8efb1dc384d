#!/bin/bash

# Export current Firebase emulator data
echo "💾 Exporting Firebase Emulator Data..."

# Check if emulator is running
if ! nc -z 127.0.0.1 9000; then
    echo "❌ Firebase emulator is not running on port 9000"
    echo "   Start the emulator first with: ./start-firebase-emulator.sh"
    exit 1
fi

# Ensure export directory exists
mkdir -p firebase-data

# Export data from running emulator
echo "📤 Exporting data to firebase-data/ directory..."

firebase emulators:export firebase-data --project cx-system-469120 --force

if [ $? -eq 0 ]; then
    echo "✅ Firebase data exported successfully to firebase-data/"
    echo ""
    echo "📁 Exported files:"
    ls -la firebase-data/
    echo ""
    echo "💡 This data will be automatically imported next time you start the emulator"
else
    echo "❌ Failed to export Firebase data"
    exit 1
fi