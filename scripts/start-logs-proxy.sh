#!/bin/bash

# Start CX Logs WebSocket Proxy
echo "🚀 Starting CX Logs WebSocket Proxy..."

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Ensure logs directory exists
mkdir -p logs/browser

# Kill any existing proxy on port 3008
echo "🧹 Checking for existing processes on port 3008..."
if lsof -ti:3008 >/dev/null 2>&1; then
    echo "⚠️  Killing existing process on port 3008..."
    lsof -ti:3008 | xargs kill -9
    sleep 2
fi

# Start the WebSocket proxy
echo -e "${BLUE}📡 Starting WebSocket proxy on port 3008...${NC}"
# Get the project root directory (where this script is located)
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
node "$PROJECT_ROOT/scripts/logs-websocket-proxy.js" &
PROXY_PID=$!

# Wait a moment for startup
sleep 2

# Check if proxy is running
if ps -p $PROXY_PID > /dev/null; then
    echo -e "${GREEN}✅ Logs WebSocket Proxy started successfully!${NC}"
    echo ""
    echo -e "${YELLOW}📋 Proxy Information:${NC}"
    echo "   • WebSocket: ws://localhost:3008"
    echo "   • Log File: logs/browser/browser-console.log"
    echo "   • Process ID: $PROXY_PID"
    echo ""
    echo -e "${YELLOW}🔧 Usage:${NC}"
    echo "   1. Start your Chat UI: cd services/chat-ui && npm run dev"
    echo "   2. Open browser to http://localhost:3007"
    echo "   3. All console logs will be streamed to logs/browser/browser-console.log"
    echo ""
    echo -e "${YELLOW}📊 Monitoring:${NC}"
    echo "   • Watch logs: tail -f logs/browser/browser-console.log"
    echo "   • Stop proxy: ./scripts/stop-logs-proxy.sh"
    echo "   • Or kill PID: kill $PROXY_PID"
    echo ""
    echo -e "${BLUE}✨ Ready to receive browser logs!${NC}"
    
    # Save PID for stop script
    echo $PROXY_PID > /tmp/cx-logs-proxy.pid
    
    # Exit successfully - proxy is running in background
else
    echo -e "${RED}❌ Failed to start WebSocket proxy${NC}"
    echo "Check if port 3008 is available"
    exit 1
fi