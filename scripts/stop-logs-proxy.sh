#!/bin/bash

# Stop CX Logs WebSocket Proxy
echo "🛑 Stopping CX Logs WebSocket Proxy..."

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Function to kill process
kill_proxy() {
    local pid=$1
    if ps -p $pid > /dev/null; then
        echo "🔪 Killing proxy process (PID: $pid)..."
        kill -TERM $pid 2>/dev/null
        sleep 2
        
        # Force kill if still running
        if ps -p $pid > /dev/null; then
            echo "⚡ Force killing proxy process..."
            kill -KILL $pid 2>/dev/null
        fi
        
        if ! ps -p $pid > /dev/null; then
            echo -e "${GREEN}✅ Proxy stopped successfully${NC}"
            return 0
        else
            echo -e "${RED}❌ Failed to stop proxy process${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}ℹ️  Process $pid not found (already stopped)${NC}"
        return 0
    fi
}

# Try to stop from saved PID
if [ -f /tmp/cx-logs-proxy.pid ]; then
    PROXY_PID=$(cat /tmp/cx-logs-proxy.pid)
    echo "📋 Found saved PID: $PROXY_PID"
    
    if kill_proxy $PROXY_PID; then
        rm -f /tmp/cx-logs-proxy.pid
    fi
else
    echo "📋 No saved PID found"
fi

# Also kill any remaining processes on port 3008
echo "🧹 Cleaning up any remaining processes on port 3008..."
if lsof -ti:3008 >/dev/null 2>&1; then
    echo "🔍 Found processes on port 3008, killing..."
    lsof -ti:3008 | xargs kill -9
    echo -e "${GREEN}✅ Port 3008 cleaned up${NC}"
else
    echo -e "${GREEN}✅ Port 3008 is clear${NC}"
fi

# Kill by process name as fallback
pkill -f "logs-websocket-proxy.js" 2>/dev/null && echo "🔪 Killed proxy by process name"

echo ""
echo -e "${GREEN}🎉 CX Logs Proxy cleanup complete!${NC}"
echo -e "${YELLOW}💡 Logs are preserved in: logs/browser/browser-console.log${NC}"