#!/bin/bash

# Start All Development Emulators
echo "🚀 Starting all development emulators for CX System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if port is in use
check_port() {
    if nc -z $1 $2 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to check if topic exists
check_topic_exists() {
    PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics list --project=cx-system-469120 2>/dev/null | grep -q "projects/cx-system-469120/topics/$1"
    return $?
}

# Check what's already running
echo -e "${BLUE}🔍 Checking existing emulators...${NC}"
FIREBASE_RUNNING=false
PUBSUB_RUNNING=false

if check_port 127.0.0.1 9000; then
    FIREBASE_RUNNING=true
    echo -e "${YELLOW}⚠️  Firebase Database emulator already running on port 9000${NC}"
fi

if check_port localhost 8085; then
    PUBSUB_RUNNING=true
    echo -e "${YELLOW}⚠️  PubSub emulator already running on port 8085${NC}"
fi

# Ask user what to do if emulators are already running
if [ "$FIREBASE_RUNNING" = true ] || [ "$PUBSUB_RUNNING" = true ]; then
    echo "Do you want to restart all emulators? (y/n)"
    read -r response
    if [[ "$response" = "y" ]]; then
        echo -e "${BLUE}🧹 Cleaning up existing processes...${NC}"
        pkill -f "firebase emulators" > /dev/null 2>&1
        pkill -f pubsub > /dev/null 2>&1
        sleep 2
        FIREBASE_RUNNING=false
        PUBSUB_RUNNING=false
    else
        echo -e "${GREEN}✅ Keeping existing emulators${NC}"
    fi
fi

# Clean temporary Firebase export directories if restarting
if [ "$FIREBASE_RUNNING" = false ]; then
    if [ -f "./scripts/cleanup-firebase-temp.sh" ]; then
        ./scripts/cleanup-firebase-temp.sh
    fi
fi

# Ensure logs and firebase-data directories exist
mkdir -p logs/emulators
mkdir -p firebase-data

# Start Firebase emulator if not running
if [ "$FIREBASE_RUNNING" = false ]; then
    echo -e "${BLUE}🔥 Starting Firebase Realtime Database + UI emulator with data persistence...${NC}"
    firebase emulators:start --only database,ui --project cx-system-469120 --import=./firebase-data --export-on-exit=./firebase-data > logs/emulators/firebase-emulator.log 2>&1 &
    FIREBASE_PID=$!
else
    echo -e "${GREEN}✅ Using existing Firebase emulators${NC}"
fi

# Set environment variables
export PUBSUB_EMULATOR_HOST=localhost:8085
export GOOGLE_CLOUD_PROJECT=cx-system-469120

# Start PubSub emulator if not running
if [ "$PUBSUB_RUNNING" = false ]; then
    echo -e "${BLUE}📬 Starting PubSub emulator...${NC}"
    gcloud beta emulators pubsub start --host-port=localhost:8085 > logs/emulators/pubsub-emulator.log 2>&1 &
    PUBSUB_PID=$!
else
    echo -e "${GREEN}✅ Using existing PubSub emulator${NC}"
fi

# Wait for emulators to start (only if we started them)
if [ "$FIREBASE_RUNNING" = false ] || [ "$PUBSUB_RUNNING" = false ]; then
    echo -e "${YELLOW}⏳ Waiting for emulators to start...${NC}"
    
    # Wait with timeout for Firebase
    if [ "$FIREBASE_RUNNING" = false ]; then
        for i in {1..15}; do
            if check_port 127.0.0.1 9000 && check_port 127.0.0.1 4000; then
                echo -e "${GREEN}✅ Firebase emulators started${NC}"
                break
            fi
            if [ $i -eq 15 ]; then
                echo -e "${RED}❌ Firebase emulators failed to start${NC}"
                echo "Last 10 lines of log:"
                tail -10 logs/emulators/firebase-emulator.log
            fi
            sleep 1
        done
    fi
    
    # Wait with timeout for PubSub
    if [ "$PUBSUB_RUNNING" = false ]; then
        for i in {1..10}; do
            if check_port localhost 8085; then
                echo -e "${GREEN}✅ PubSub emulator started${NC}"
                break
            fi
            if [ $i -eq 10 ]; then
                echo -e "${RED}❌ PubSub emulator failed to start${NC}"
                echo "Last 10 lines of log:"
                tail -10 logs/emulators/pubsub-emulator.log
            fi
            sleep 1
        done
    fi
fi

# Verify all emulators are running
echo ""
echo -e "${BLUE}🔍 Verifying emulator status...${NC}"

if check_port 127.0.0.1 9000; then
    echo -e "${GREEN}✅ Firebase Database emulator: 127.0.0.1:9000${NC}"
else
    echo -e "${RED}❌ Firebase Database emulator not running${NC}"
fi

if check_port 127.0.0.1 4000; then
    echo -e "${GREEN}✅ Firebase UI: 127.0.0.1:4000${NC}"
else
    echo -e "${RED}❌ Firebase UI not running${NC}"
fi

if check_port localhost 8085; then
    echo -e "${GREEN}✅ PubSub emulator: localhost:8085${NC}"
else
    echo -e "${RED}❌ PubSub emulator not running${NC}"
fi

# Setup PubSub topics and subscriptions
if check_port localhost 8085; then
    echo ""
    echo -e "${BLUE}🔗 Setting up PubSub topics and subscriptions...${NC}"
    
    # Use Node.js script if it exists (it handles checking for existing topics)
    if [ -f "scripts/setup-pubsub-topics.js" ]; then
        PUBSUB_EMULATOR_HOST=localhost:8085 node scripts/setup-pubsub-topics.js
    else
        # Fallback to gcloud commands with existence checks
        echo "Using gcloud to setup topics..."
        
        # Create topics only if they don't exist
        if ! check_topic_exists "inbound-messages"; then
            PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics create inbound-messages --project=cx-system-469120
            echo -e "${GREEN}✅ Created topic: inbound-messages${NC}"
        else
            echo -e "${YELLOW}📋 Topic already exists: inbound-messages${NC}"
        fi
        
        if ! check_topic_exists "outbound-messages"; then
            PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics create outbound-messages --project=cx-system-469120
            echo -e "${GREEN}✅ Created topic: outbound-messages${NC}"
        else
            echo -e "${YELLOW}📋 Topic already exists: outbound-messages${NC}"
        fi
    fi
fi

# Check for existing Firebase data
if check_port 127.0.0.1 9000; then
    if [ -d "firebase-data" ] && [ "$(ls -A firebase-data 2>/dev/null)" ]; then
        echo -e "${GREEN}💾 Previous Firebase data imported from firebase-data/${NC}"
    else
        echo -e "${YELLOW}💡 No previous Firebase data found${NC}"
    fi
fi

# Display summary
echo ""
echo -e "${GREEN}═══════════════════════════════════════════════════════${NC}"
echo -e "${GREEN}🎉 Emulator Status Summary${NC}"
echo -e "${GREEN}═══════════════════════════════════════════════════════${NC}"
echo ""
echo -e "${YELLOW}🔥 Firebase Emulators:${NC}"
echo "  • Database: http://127.0.0.1:9000"
echo "  • UI: http://127.0.0.1:4000/database"
echo "  • Data persistence: ${GREEN}Enabled${NC}"
echo ""
echo -e "${YELLOW}📬 PubSub Emulator:${NC}"
echo "  • Endpoint: localhost:8085"
echo "  • Topics: inbound-messages, outbound-messages"
echo "  • Push subscriptions configured"
echo ""
echo -e "${BLUE}📝 Management Commands:${NC}"
echo "  • Stop all: ./scripts/stop-all-emulators.sh"
echo "  • View Firebase logs: tail -f logs/emulators/firebase-emulator.log"
echo "  • View PubSub logs: tail -f logs/emulators/pubsub-emulator.log"
echo "  • Export Firebase data: firebase emulators:export firebase-data"
echo "  • List PubSub topics: PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics list"
echo ""
echo -e "${BLUE}🚀 Next Steps:${NC}"
echo "  1. Create Firebase data (if needed): node scripts/create_complete_data.js --all"
echo "  2. Start your services in separate terminals:"
echo "     • cd services/channel-router && npm run dev"
echo "     • cd services/bot-human-router && npm run dev"
echo "     • cd services/chat-realtime && npm run dev"
echo "  3. Test message flow end-to-end"
echo ""
echo -e "${GREEN}═══════════════════════════════════════════════════════${NC}"
