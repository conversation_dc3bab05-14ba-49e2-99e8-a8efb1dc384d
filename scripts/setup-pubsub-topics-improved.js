#!/usr/bin/env node

/**
 * Setup PubSub Topics and Subscriptions for Development
 * This script creates the necessary PubSub topics and subscriptions using the emulator
 */

const { PubSub } = require('@google-cloud/pubsub');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

async function setupPubSub() {
  console.log(`${colors.blue}🚀 Setting up PubSub topics and subscriptions...${colors.reset}`);

  // Check if PUBSUB_EMULATOR_HOST is set
  if (!process.env.PUBSUB_EMULATOR_HOST) {
    console.log(`${colors.yellow}🔧 Setting PUBSUB_EMULATOR_HOST=localhost:8085${colors.reset}`);
    process.env.PUBSUB_EMULATOR_HOST = 'localhost:8085';
  }

  // Set up PubSub client for emulator
  const pubSubClient = new PubSub({
    projectId: 'cx-system-469120',
    // PUBSUB_EMULATOR_HOST environment variable should be set to localhost:8085
  });

  // Test connection first
  try {
    await pubSubClient.getTopics();
  } catch (error) {
    console.error(`${colors.red}❌ Cannot connect to PubSub emulator at ${process.env.PUBSUB_EMULATOR_HOST}${colors.reset}`);
    console.error(`${colors.yellow}💡 Make sure the PubSub emulator is running${colors.reset}`);
    console.error(`${colors.yellow}   Run: gcloud beta emulators pubsub start --host-port=localhost:8085${colors.reset}`);
    process.exit(1);
  }

  try {
    // Create topics
    console.log(`\n${colors.blue}📝 Creating topics...${colors.reset}`);
    
    const inboundTopicName = 'inbound-messages';
    const outboundTopicName = 'outbound-messages';
    
    // Check if topics exist, create if not
    try {
      const [inboundExists] = await pubSubClient.topic(inboundTopicName).exists();
      if (!inboundExists) {
        await pubSubClient.createTopic(inboundTopicName);
        console.log(`${colors.green}✅ Created topic: ${inboundTopicName}${colors.reset}`);
      } else {
        console.log(`${colors.yellow}📋 Topic already exists: ${inboundTopicName}${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.red}❌ Error with topic ${inboundTopicName}: ${error.message}${colors.reset}`);
    }

    try {
      const [outboundExists] = await pubSubClient.topic(outboundTopicName).exists();
      if (!outboundExists) {
        await pubSubClient.createTopic(outboundTopicName);
        console.log(`${colors.green}✅ Created topic: ${outboundTopicName}${colors.reset}`);
      } else {
        console.log(`${colors.yellow}📋 Topic already exists: ${outboundTopicName}${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.red}❌ Error with topic ${outboundTopicName}: ${error.message}${colors.reset}`);
    }

    // Create subscriptions
    console.log(`\n${colors.blue}🔗 Creating subscriptions...${colors.reset}`);
    
    const inboundTopic = pubSubClient.topic(inboundTopicName);
    const outboundTopic = pubSubClient.topic(outboundTopicName);

    // Bot Human Router subscription (receives INBOUND messages)
    const botHumanSubscriptionName = 'bot-human-router-inbound';
    try {
      const [botHumanExists] = await inboundTopic.subscription(botHumanSubscriptionName).exists();
      
      if (!botHumanExists) {
        // Create subscription with push config
        // Note: The emulator is more lenient with localhost URLs
        await inboundTopic.createSubscription(botHumanSubscriptionName, {
          pushConfig: {
            pushEndpoint: 'http://localhost:3004/api/pubsub/inbound',
            attributes: {
              'x-goog-version': 'v1'
            }
          },
          ackDeadlineSeconds: 60
        });
        console.log(`${colors.green}✅ Created subscription: ${botHumanSubscriptionName}${colors.reset}`);
        console.log(`   → Push endpoint: http://localhost:3004/api/pubsub/inbound`);
      } else {
        console.log(`${colors.yellow}📋 Subscription already exists: ${botHumanSubscriptionName}${colors.reset}`);
        
        // Get and display current configuration
        const subscription = inboundTopic.subscription(botHumanSubscriptionName);
        const [metadata] = await subscription.getMetadata();
        if (metadata.pushConfig && metadata.pushConfig.pushEndpoint) {
          console.log(`   → Current endpoint: ${metadata.pushConfig.pushEndpoint}`);
        }
      }
    } catch (error) {
      console.error(`${colors.red}❌ Error creating subscription ${botHumanSubscriptionName}: ${error.message}${colors.reset}`);
      
      // Try to create as pull subscription if push fails
      if (error.message.includes('Invalid push endpoint')) {
        console.log(`${colors.yellow}⚠️  Creating as pull subscription instead...${colors.reset}`);
        try {
          await inboundTopic.createSubscription(botHumanSubscriptionName, {
            ackDeadlineSeconds: 60
          });
          console.log(`${colors.green}✅ Created pull subscription: ${botHumanSubscriptionName}${colors.reset}`);
          console.log(`${colors.yellow}   Note: This is a pull subscription. Push endpoints are not supported by gcloud CLI.${colors.reset}`);
        } catch (pullError) {
          console.error(`${colors.red}❌ Also failed to create pull subscription: ${pullError.message}${colors.reset}`);
        }
      }
    }

    // Channel Router subscription (receives OUTBOUND messages)
    const channelRouterSubscriptionName = 'channel-router-outbound';
    try {
      const [channelRouterExists] = await outboundTopic.subscription(channelRouterSubscriptionName).exists();
      
      if (!channelRouterExists) {
        await outboundTopic.createSubscription(channelRouterSubscriptionName, {
          pushConfig: {
            pushEndpoint: 'http://localhost:3002/api/pubsub/outbound',
            attributes: {
              'x-goog-version': 'v1'
            }
          },
          ackDeadlineSeconds: 60
        });
        console.log(`${colors.green}✅ Created subscription: ${channelRouterSubscriptionName}${colors.reset}`);
        console.log(`   → Push endpoint: http://localhost:3002/api/pubsub/outbound`);
      } else {
        console.log(`${colors.yellow}📋 Subscription already exists: ${channelRouterSubscriptionName}${colors.reset}`);
        
        // Get and display current configuration
        const subscription = outboundTopic.subscription(channelRouterSubscriptionName);
        const [metadata] = await subscription.getMetadata();
        if (metadata.pushConfig && metadata.pushConfig.pushEndpoint) {
          console.log(`   → Current endpoint: ${metadata.pushConfig.pushEndpoint}`);
        }
      }
    } catch (error) {
      console.error(`${colors.red}❌ Error creating subscription ${channelRouterSubscriptionName}: ${error.message}${colors.reset}`);
      
      // Try to create as pull subscription if push fails
      if (error.message.includes('Invalid push endpoint')) {
        console.log(`${colors.yellow}⚠️  Creating as pull subscription instead...${colors.reset}`);
        try {
          await outboundTopic.createSubscription(channelRouterSubscriptionName, {
            ackDeadlineSeconds: 60
          });
          console.log(`${colors.green}✅ Created pull subscription: ${channelRouterSubscriptionName}${colors.reset}`);
          console.log(`${colors.yellow}   Note: This is a pull subscription. Push endpoints are not supported by gcloud CLI.${colors.reset}`);
        } catch (pullError) {
          console.error(`${colors.red}❌ Also failed to create pull subscription: ${pullError.message}${colors.reset}`);
        }
      }
    }

    console.log('');
    console.log(`${colors.green}🎉 PubSub setup complete!${colors.reset}`);
    console.log(`${colors.blue}📥 Inbound Topic:${colors.reset} inbound-messages → Bot Human Router`);
    console.log(`${colors.blue}📤 Outbound Topic:${colors.reset} outbound-messages → Channel Router`);
    console.log('');
    
    // List all subscriptions to verify
    console.log(`${colors.blue}📋 Current subscriptions:${colors.reset}`);
    const [allSubscriptions] = await pubSubClient.getSubscriptions();
    for (const sub of allSubscriptions) {
      const [metadata] = await sub.getMetadata();
      const subName = sub.name.split('/').pop();
      const topicName = metadata.topic.split('/').pop();
      
      console.log(`  • ${subName} → ${topicName}`);
      if (metadata.pushConfig && metadata.pushConfig.pushEndpoint) {
        console.log(`    Push: ${metadata.pushConfig.pushEndpoint}`);
      } else {
        console.log(`    Type: Pull subscription`);
      }
    }
    
    console.log('');
    console.log(`${colors.yellow}💡 Notes:${colors.reset}`);
    console.log('• If push subscriptions failed, pull subscriptions were created instead');
    console.log('• The Node.js SDK handles localhost push endpoints better than gcloud CLI');
    console.log('• Make sure both services are running to receive Push messages:');
    console.log('  - Bot Human Router: http://localhost:3004');
    console.log('  - Channel Router: http://localhost:3002');

  } catch (error) {
    console.error(`${colors.red}❌ Error setting up PubSub: ${error.message}${colors.reset}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run setup
setupPubSub().catch(error => {
  console.error(`${colors.red}❌ Fatal error: ${error.message}${colors.reset}`);
  process.exit(1);
});
