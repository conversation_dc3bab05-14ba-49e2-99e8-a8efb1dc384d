#!/usr/bin/env node

/**
 * CX System - Logs WebSocket Proxy
 * 
 * Servidor WebSocket que recibe logs del browser y los guarda en archivos
 * Permite streaming de console logs desde aplicaciones web a Claude Code
 */

const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// Configuración
const WS_PORT = 3008;
const LOG_DIR = path.resolve(__dirname, '../logs/browser');
const PROXY_LOG_DIR = path.resolve(__dirname, '../logs/services/logs-proxy');
const LOG_FILE = path.join(LOG_DIR, 'browser-console.log');
const PROXY_LOG_FILE = path.join(PROXY_LOG_DIR, 'logs-proxy.log');

// Asegurar que los directorios existen
if (!fs.existsSync(LOG_DIR)) {
    fs.mkdirSync(LOG_DIR, { recursive: true });
}
if (!fs.existsSync(PROXY_LOG_DIR)) {
    fs.mkdirSync(PROXY_LOG_DIR, { recursive: true });
}

// Crear servidor WebSocket
const wss = new WebSocket.Server({ 
    port: WS_PORT,
    perMessageDeflate: false
});

// Función para logging del proxy mismo
function logProxy(level, message, data = {}) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        level,
        service: 'logs-proxy',
        message,
        ...data
    };
    
    // Log a consola
    console.log(`[${logEntry.timestamp}] [${level.toUpperCase()}] ${message}`);
    
    // Log a archivo del proxy
    const logLine = JSON.stringify(logEntry) + '\n';
    fs.appendFileSync(PROXY_LOG_FILE, logLine);
}

logProxy('info', `CX Logs WebSocket Proxy started on ws://localhost:${WS_PORT}`);
logProxy('info', `Browser logs will be written to: ${LOG_FILE}`);
logProxy('info', `Proxy logs will be written to: ${PROXY_LOG_FILE}`);

// Función para escribir log con timestamp
function writeLog(logData) {
    const timestamp = new Date().toISOString();
    const logEntry = {
        timestamp,
        ...logData
    };
    
    const logLine = JSON.stringify(logEntry) + '\n';
    
    // Escribir a archivo
    fs.appendFileSync(LOG_FILE, logLine);
    
    // También mostrar en consola del servidor
    console.log(`[${timestamp}] [${logData.level?.toUpperCase() || 'LOG'}] ${logData.message || JSON.stringify(logData.args)}`);
}

// Función para limpiar logs antiguos
function cleanOldLogs() {
    if (fs.existsSync(LOG_FILE)) {
        const stats = fs.statSync(LOG_FILE);
        const fileSizeInMB = stats.size / (1024 * 1024);
        
        if (fileSizeInMB > 10) { // Si el archivo supera 10MB
            const backupFile = LOG_FILE.replace('.log', `-backup-${Date.now()}.log`);
            fs.renameSync(LOG_FILE, backupFile);
            console.log(`📦 Log file rotated to: ${path.basename(backupFile)}`);
        }
    }
}

// Limpiar logs al iniciar
cleanOldLogs();

// Manejar conexiones WebSocket
wss.on('connection', (ws, req) => {
    const clientIP = req.socket.remoteAddress;
    logProxy('info', `New client connected from ${clientIP}`, { clientIP });
    
    // Enviar mensaje de bienvenida
    ws.send(JSON.stringify({
        type: 'connection',
        message: 'Connected to CX Logs WebSocket Proxy',
        timestamp: new Date().toISOString()
    }));

    // Manejar mensajes del cliente
    ws.on('message', (data) => {
        try {
            const logData = JSON.parse(data.toString());
            
            // Validar estructura del log
            if (logData && (logData.level || logData.message || logData.args)) {
                // Agregar metadata del cliente
                logData.client = {
                    ip: clientIP,
                    userAgent: req.headers['user-agent']
                };
                
                // Escribir log
                writeLog(logData);
                
                // Confirmar recepción al cliente
                ws.send(JSON.stringify({
                    type: 'ack',
                    received: true,
                    timestamp: new Date().toISOString()
                }));
            } else {
                logProxy('warn', 'Invalid log data received', { logData });
            }
        } catch (error) {
            logProxy('error', 'Error processing log message', { error: error.message });
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid JSON format',
                timestamp: new Date().toISOString()
            }));
        }
    });

    // Manejar desconexión
    ws.on('close', (code, reason) => {
        console.log(`🔌 Client ${clientIP} disconnected (${code}: ${reason})`);
    });

    // Manejar errores
    ws.on('error', (error) => {
        console.error(`❌ WebSocket error from ${clientIP}:`, error);
    });
});

// Manejar señales del sistema
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down WebSocket proxy...');
    
    // Cerrar todas las conexiones
    wss.clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
            client.close(1000, 'Server shutting down');
        }
    });
    
    wss.close(() => {
        console.log('✅ WebSocket server closed');
        process.exit(0);
    });
});

// Información adicional
console.log('\n📋 Usage Instructions:');
console.log('1. Include the client script in your web app');
console.log('2. All console.log, console.warn, console.error will be proxied');
console.log(`3. Logs are saved to: ${path.relative(process.cwd(), LOG_FILE)}`);
console.log('4. Press Ctrl+C to stop the proxy');

console.log('\n🔍 Monitoring:');
console.log(`   tail -f ${path.relative(process.cwd(), LOG_FILE)}`);
console.log(`   curl -I http://localhost:${WS_PORT} (should return WebSocket upgrade)`);

console.log('\n✨ Ready to receive logs from web applications!');