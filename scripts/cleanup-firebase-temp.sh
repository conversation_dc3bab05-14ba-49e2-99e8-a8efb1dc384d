#!/bin/bash

# Clean up temporary Firebase export directories
# These are created automatically by Firebase emulators sometimes

echo "🧹 Cleaning up temporary Firebase export directories..."

# Count existing temp directories
TEMP_COUNT=$(find . -maxdepth 1 -name "firebase-export-*" -type d | wc -l)

if [ $TEMP_COUNT -gt 0 ]; then
    echo "🗑️  Found $TEMP_COUNT temporary export directories to clean..."
    
    # Remove all firebase-export-* directories
    find . -maxdepth 1 -name "firebase-export-*" -type d -exec rm -rf {} +
    
    echo "✅ Cleaned up $TEMP_COUNT temporary directories"
    echo "💾 Data is safely preserved in firebase-data/"
else
    echo "✅ No temporary export directories found"
fi

echo ""