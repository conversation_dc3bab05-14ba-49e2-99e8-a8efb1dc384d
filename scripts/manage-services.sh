#!/bin/bash

# Intelligent Services Manager for CX System
# Manages Chat UI and backend services with smart operations

# Service configuration (bash 4+ associative arrays)
if ((BASH_VERSINFO[0] >= 4)); then
    declare -A SERVICES
    SERVICES[session-manager]="3001"
    SERVICES[channel-router]="3002" 
    SERVICES[chat-realtime]="3003"
    SERVICES[bot-human-router]="3004"
    SERVICES[twilio]="3005"
    SERVICES[chat-ui]="3007"
    SERVICES[logs-proxy]="3008"
else
    # Fallback for older bash versions
    SERVICES_LIST="session-manager:3001 channel-router:3002 chat-realtime:3003 bot-human-router:3004 twilio:3005 chat-ui:3007 logs-proxy:3008"
fi

PROJECT_ROOT="/Users/<USER>/Dev/cx-system"
LOG_DIR="$PROJECT_ROOT/logs/services"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Helper functions for service management
get_service_port() {
    local service=$1
    if ((BASH_VERSINFO[0] >= 4)); then
        echo "${SERVICES[$service]}"
    else
        for item in $SERVICES_LIST; do
            if [[ "$item" == "$service:"* ]]; then
                echo "${item#*:}"
                return
            fi
        done
    fi
}

get_all_services() {
    if ((BASH_VERSINFO[0] >= 4)); then
        echo "${!SERVICES[@]}"
    else
        for item in $SERVICES_LIST; do
            echo "${item%:*}"
        done
    fi
}

show_usage() {
    echo -e "${CYAN}🔧 CX System Services Manager${NC}"
    echo ""
    echo "Usage: $0 [COMMAND] [SERVICE]"
    echo ""
    echo -e "${YELLOW}Commands:${NC}"
    echo "  status              Show status of all services"
    echo "  build-status        Show build status of all services or specific service"
    echo "  start [service]     Start specific service or all services"
    echo "  stop [service]      Stop specific service or all services" 
    echo "  restart [service]   Restart specific service or all services"
    echo "  build [service]     Build specific service or all services (compile TypeScript)"
    echo "  clean [service]     Clean specific service or all services (remove node_modules, dist, etc.)"
    echo "  logs [service]      Show logs for service"
    echo "  dev                 Start development environment (UI + realtime + logs-proxy)"
    echo "  full                Start full system (all services + logs-proxy)"
    echo ""
    echo -e "${YELLOW}Available Services:${NC}"
    for service in $(get_all_services); do
        echo "  $service (port $(get_service_port $service))"
    done
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  $0 status                    # Show all services status"
    echo "  $0 build-status             # Show build status of all services"
    echo "  $0 build-status chat-realtime # Show build status of specific service"
    echo "  $0 start chat-ui            # Start only Chat UI"  
    echo "  $0 start logs-proxy         # Start browser logs WebSocket proxy"
    echo "  $0 restart chat-realtime    # Restart Chat Realtime API"
    echo "  $0 build channel-router     # Build specific service (TypeScript compilation)"
    echo "  $0 build                    # Build all services with TypeScript compilation"
    echo "  $0 logs logs-proxy          # Show browser console logs in real-time"
    echo "  $0 clean                    # Stop all + clean caches"
    echo "  $0 clean chat-realtime     # Clean specific service (node_modules, dist, etc.)"
    echo "  $0 dev                      # Start development setup"
}

check_port() {
    local port=$1
    if lsof -ti:$port >/dev/null 2>&1; then
        return 0 # Port is in use
    else
        return 1 # Port is free
    fi
}

get_service_pid() {
    local service=$1
    local port=$(get_service_port $service)
    
    # Special handling for logs-proxy
    if [[ "$service" == "logs-proxy" ]]; then
        # Check for saved PID first
        if [ -f "/tmp/cx-logs-proxy.pid" ]; then
            local saved_pid=$(cat /tmp/cx-logs-proxy.pid)
            if ps -p $saved_pid > /dev/null 2>&1; then
                echo $saved_pid
                return
            fi
        fi
        # Fallback to process name
        pgrep -f "logs-websocket-proxy.js" 2>/dev/null
    else
        lsof -ti:$port 2>/dev/null
    fi
}

service_status() {
    local service=$1
    local port=$(get_service_port $service)
    local pid=$(get_service_pid $service)
    
    if [ -n "$pid" ]; then
        echo -e "  ${GREEN}✅ $service${NC} (port $port, pid $pid)"
        return 0
    else
        echo -e "  ${RED}❌ $service${NC} (port $port)"
        return 1
    fi
}

show_status() {
    echo -e "${CYAN}📊 Services Status${NC}"
    echo ""
    
    local running=0
    local total=0
    
    for service in $(get_all_services); do
        total=$((total + 1))
        if service_status $service; then
            running=$((running + 1))
        fi
    done
    
    echo ""
    echo -e "${BLUE}Summary: $running/$total services running${NC}"
    
    # Check emulators
    echo ""
    echo -e "${CYAN}📡 Emulators Status${NC}"
    if nc -z 127.0.0.1 9000 2>/dev/null; then
        echo -e "  ${GREEN}✅ Firebase Database${NC} (port 9000)"
    else
        echo -e "  ${RED}❌ Firebase Database${NC} (port 9000)"
    fi
    
    if nc -z 127.0.0.1 4000 2>/dev/null; then
        echo -e "  ${GREEN}✅ Firebase UI${NC} (port 4000)"
    else
        echo -e "  ${RED}❌ Firebase UI${NC} (port 4000)"
    fi
    
    if nc -z localhost 8085 2>/dev/null; then
        echo -e "  ${GREEN}✅ PubSub Emulator${NC} (port 8085)"
    else
        echo -e "  ${RED}❌ PubSub Emulator${NC} (port 8085)"
    fi
    
    # Check additional debugging tools
    echo ""
    echo -e "${CYAN}🛠️  Debug Tools${NC}"
    if nc -z localhost 3008 2>/dev/null; then
        echo -e "  ${GREEN}✅ Logs WebSocket Proxy${NC} (port 3008)"
    else
        echo -e "  ${RED}❌ Logs WebSocket Proxy${NC} (port 3008)"
    fi
}

check_typescript_errors() {
    local service_dir=$1
    local temp_output=$(mktemp)
    
    cd "$service_dir"
    
    # Check if tsconfig.json exists
    if [ ! -f "tsconfig.json" ]; then
        echo "no-tsconfig"
        rm -f "$temp_output"
        return
    fi
    
    # Try to compile without emitting files (dry run)
    if command -v npx >/dev/null 2>&1 && npx tsc --version >/dev/null 2>&1; then
        npx tsc --noEmit --project . 2>"$temp_output" >/dev/null
    elif [ -f "node_modules/.bin/tsc" ]; then
        ./node_modules/.bin/tsc --noEmit --project . 2>"$temp_output" >/dev/null
    else
        echo "no-tsc"
        rm -f "$temp_output"
        return
    fi
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo "ok"
    else
        # Count errors (lines that contain "error TS")
        local error_count=$(grep -c "error TS" "$temp_output" 2>/dev/null || echo "0")
        # Clean up error count - remove any whitespace or newlines
        error_count=$(echo "$error_count" | tr -d '\n\r ')
        echo "errors:$error_count"
    fi
    
    rm -f "$temp_output"
}

check_source_freshness() {
    local service_dir=$1
    local dist_dir="$service_dir/dist"
    
    # Check for Next.js project and adjust build directory
    if [ -f "$service_dir/next.config.js" ] || [ -f "$service_dir/next.config.ts" ]; then
        dist_dir="$service_dir/.next"
    fi
    
    # If no build directory, can't check freshness
    if [ ! -d "$dist_dir" ]; then
        echo "no-dist"
        return
    fi
    
    # Find the newest file in src/ (if exists)
    local src_dir="$service_dir/src"
    if [ ! -d "$src_dir" ]; then
        # No src directory, assume current directory contains source
        src_dir="$service_dir"
    fi
    
    # Find newest source file (excluding node_modules, dist, .next)
    local newest_src=$(find "$src_dir" -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | \
        grep -v node_modules | grep -v dist | grep -v .next | \
        xargs ls -t 2>/dev/null | head -n1)
    
    # Find newest built file in build directory
    local newest_built=$(find "$dist_dir" -type f | xargs ls -t 2>/dev/null | head -n1)
    
    if [ -z "$newest_src" ]; then
        echo "no-source"
        return
    fi
    
    if [ -z "$newest_built" ]; then
        echo "no-built"
        return
    fi
    
    # Compare modification times
    if [ "$newest_src" -nt "$newest_built" ]; then
        echo "stale"
    else
        echo "fresh"
    fi
}

show_service_build_status() {
    local service=$1
    local service_dir="$PROJECT_ROOT/services/$service"
    
    # Special handling for logs-proxy (not a buildable service)
    if [[ "$service" == "logs-proxy" ]]; then
        echo -e "  ${BLUE}🔵 $service${NC} - No build needed (utility service)"
        return 0
    fi
    
    if [ ! -d "$service_dir" ]; then
        echo -e "  ${RED}❌ $service${NC} - Service directory not found"
        return 1
    fi
    
    # Check if package.json exists
    if [ ! -f "$service_dir/package.json" ]; then
        echo -e "  ${RED}❌ $service${NC} - No package.json found"
        return 1
    fi
    
    # Check if build script exists
    local has_build_script=$(grep -q '"build"' "$service_dir/package.json" && echo "yes" || echo "no")
    
    if [ "$has_build_script" = "no" ]; then
        echo -e "  ${BLUE}🔵 $service${NC} - No build needed (no build script)"
        return 0
    fi
    
    # Check node_modules
    local node_modules_status="❌"
    local node_modules_msg=""
    if [ -d "$service_dir/node_modules" ]; then
        node_modules_status="✅"
    else
        node_modules_msg=" - Missing dependencies"
    fi
    
    # Check dist folder (or .next for Next.js projects)
    local dist_status="❌"
    local dist_msg=""
    local build_dir="$service_dir/dist"
    
    # Check for Next.js project (.next directory)
    if [ -f "$service_dir/next.config.js" ] || [ -f "$service_dir/next.config.ts" ]; then
        build_dir="$service_dir/.next"
    fi
    
    if [ -d "$build_dir" ]; then
        dist_status="✅"
        
        # Check if source is newer than build
        local freshness=$(check_source_freshness "$service_dir")
        case $freshness in
            "stale")
                dist_status="🟡"
                dist_msg=" - Source newer than build"
                ;;
            "fresh")
                dist_status="✅"
                dist_msg=" - Build up-to-date"
                ;;
            "no-source"|"no-built"|"no-dist")
                dist_status="🟡"
                dist_msg=" - Cannot verify freshness"
                ;;
        esac
    else
        if [ -f "$service_dir/next.config.js" ] || [ -f "$service_dir/next.config.ts" ]; then
            dist_msg=" - No Next.js build output (.next missing)"
        else
            dist_msg=" - No build output"
        fi
    fi
    
    # Check TypeScript errors
    local ts_status="❓"
    local ts_msg=""
    if [ -d "$service_dir/node_modules" ]; then
        local ts_result=$(check_typescript_errors "$service_dir")
        case $ts_result in
            "ok")
                ts_status="✅"
                ts_msg=" - No TS errors"
                ;;
            "errors:"*)
                local error_count="${ts_result#errors:}"
                # Clean up error count and validate it's a number
                error_count=$(echo "$error_count" | tr -d '\n\r ')
                if [[ "$error_count" =~ ^[0-9]+$ ]]; then
                    if [ "$error_count" -eq 0 ]; then
                        ts_status="✅"
                        ts_msg=" - No TS errors"
                    elif [ "$error_count" -eq 1 ]; then
                        ts_status="🔴"
                        ts_msg=" - $error_count TypeScript error"
                    else
                        ts_status="🔴"
                        ts_msg=" - $error_count TypeScript errors"
                    fi
                else
                    ts_status="❓"
                    ts_msg=" - Cannot parse error count"
                fi
                ;;
            "no-tsconfig")
                ts_status="🔵"
                ts_msg=" - No TypeScript config"
                ;;
            "no-tsc")
                ts_status="❓"
                ts_msg=" - TypeScript compiler not found"
                ;;
        esac
    else
        ts_msg=" - Cannot check (no dependencies)"
    fi
    
    # Determine overall status
    local overall_status="🔴"
    local overall_msg=""
    
    if [ "$node_modules_status" = "✅" ] && [ "$dist_status" = "✅" ] && [[ "$ts_status" =~ ^(✅|🔵)$ ]]; then
        overall_status="🟢"
        overall_msg="Ready"
    elif [ "$node_modules_status" = "✅" ] && [[ "$dist_status" =~ ^(✅|🟡)$ ]] && [ "$ts_status" != "🔴" ]; then
        overall_status="🟡"
        if [ "$dist_status" = "🟡" ]; then
            overall_msg="Build may need refresh"
        else
            overall_msg="Build exists but check needed"
        fi
    elif [ "$node_modules_status" = "❌" ]; then
        overall_status="🔴"
        overall_msg="Missing dependencies - run npm install"
    elif [ "$dist_status" = "❌" ]; then
        overall_status="🔴"
        overall_msg="Build needed - run npm run build"
    elif [ "$ts_status" = "🔴" ]; then
        overall_status="🔴"
        overall_msg="TypeScript errors detected"
    else
        overall_status="🟡"
        overall_msg="Build status unclear"
    fi
    
    # Display the service status
    echo -e "  ${overall_status} $service${NC} - $overall_msg"
    
    # Show details if there are issues
    if [[ "$overall_status" =~ ^(🔴|🟡)$ ]]; then
        [ -n "$node_modules_msg" ] && echo -e "    ${YELLOW}📦 Dependencies:${NC}$node_modules_msg"
        [ -n "$dist_msg" ] && echo -e "    ${YELLOW}📁 Build Output:${NC}$dist_msg"
        [ -n "$ts_msg" ] && echo -e "    ${YELLOW}🔧 TypeScript:${NC}$ts_msg"
    fi
    
    # Return success if green or blue status
    if [[ "$overall_status" =~ ^(🟢|🔵)$ ]]; then
        return 0
    else
        return 1
    fi
}

show_build_status() {
    echo -e "${CYAN}🔨 Build Status${NC}"
    echo ""
    
    local ready=0
    local needs_attention=0
    local total=0
    
    for service in $(get_all_services); do
        total=$((total + 1))
        if show_service_build_status $service; then
            ready=$((ready + 1))
        else
            needs_attention=$((needs_attention + 1))
        fi
    done
    
    echo ""
    echo -e "${BLUE}Build Summary: $ready/$total services ready${NC}"
    
    if [ $needs_attention -gt 0 ]; then
        echo -e "${YELLOW}$needs_attention services need attention${NC}"
        echo ""
        echo -e "${YELLOW}Common fixes:${NC}"
        echo -e "  ${BLUE}Missing dependencies:${NC} ./manage-services.sh clean [service] && npm install"
        echo -e "  ${BLUE}Build needed:${NC} ./manage-services.sh build [service]"
        echo -e "  ${BLUE}TypeScript errors:${NC} Check service directory for compilation issues"
        echo -e "  ${BLUE}Stale build:${NC} ./manage-services.sh build [service] (rebuild needed)"
    else
        echo -e "${GREEN}All services are build-ready!${NC}"
    fi
}

stop_service() {
    local service=$1
    local port=$(get_service_port $service)
    local pid=$(get_service_pid $service)
    
    # Special handling for logs-proxy
    if [[ "$service" == "logs-proxy" ]]; then
        if [ -n "$pid" ]; then
            echo -e "${YELLOW}🛑 Stopping logs WebSocket proxy (pid $pid)...${NC}"
            # Use dedicated stop script for clean shutdown
            "$PROJECT_ROOT/scripts/stop-logs-proxy.sh" >/dev/null 2>&1
            echo -e "${GREEN}✅ logs-proxy stopped${NC}"
        else
            echo -e "${BLUE}ℹ️  logs-proxy is not running${NC}"
        fi
        return
    fi
    
    if [ -n "$pid" ]; then
        echo -e "${YELLOW}🛑 Stopping $service (pid $pid)...${NC}"
        kill $pid 2>/dev/null
        
        # Wait up to 5 seconds for graceful shutdown
        for i in {1..5}; do
            if ! kill -0 $pid 2>/dev/null; then
                echo -e "${GREEN}✅ $service stopped gracefully${NC}"
                return 0
            fi
            sleep 1
        done
        
        # Force kill if still running
        if kill -0 $pid 2>/dev/null; then
            echo -e "${RED}⚠️  Force killing $service...${NC}"
            kill -9 $pid 2>/dev/null
        fi
        
        echo -e "${GREEN}✅ $service stopped${NC}"
    else
        echo -e "${BLUE}ℹ️  $service is not running${NC}"
    fi
}

start_service() {
    local service=$1
    local port=$(get_service_port $service)
    
    # Check if already running
    if check_port $port; then
        local pid=$(get_service_pid $service)
        echo -e "${YELLOW}⚠️  $service is already running (pid $pid)${NC}"
        return 0
    fi
    
    # Special handling for logs-proxy
    if [[ "$service" == "logs-proxy" ]]; then
        echo -e "${GREEN}🚀 Starting logs WebSocket proxy on port $port...${NC}"
        # Use dedicated start script for logs proxy
        "$PROJECT_ROOT/scripts/start-logs-proxy.sh" &
        
        # Wait for proxy to start and verify multiple times
        echo -e "${BLUE}⏳ Waiting for logs-proxy to start...${NC}"
        
        local attempts=0
        local max_attempts=10
        
        while [ $attempts -lt $max_attempts ]; do
            sleep 1
            attempts=$((attempts + 1))
            
            if check_port $port; then
                echo -e "${GREEN}✅ logs-proxy started successfully (attempt $attempts)${NC}"
                echo -e "${BLUE}📋 Logs: logs/browser/browser-console.log${NC}"
                echo -e "${BLUE}💡 Use: tail -f logs/browser/browser-console.log${NC}"
                return 0
            fi
            
            echo -e "${YELLOW}⏳ Still waiting... (attempt $attempts/$max_attempts)${NC}"
        done
        
        # If we get here, it failed after max attempts
        echo -e "${RED}❌ Failed to start logs-proxy after $max_attempts attempts${NC}"
        return 1
    fi
    
    echo -e "${GREEN}🚀 Starting $service on port $port...${NC}"
    
    local service_dir="$PROJECT_ROOT/services/$service"
    local service_log_dir="$LOG_DIR/$service"
    local log_file="$service_log_dir/$service.log"
    
    # Ensure service log directory exists
    mkdir -p "$service_log_dir"
    
    if [ ! -d "$service_dir" ]; then
        echo -e "${RED}❌ Service directory not found: $service_dir${NC}"
        return 1
    fi
    
    cd "$service_dir"
    
    # Start service based on type
    case $service in
        chat-ui)
            nohup npm run dev -- --port $port > "$log_file" 2>&1 &
            ;;
        channel-router)
            # Channel router needs PubSub emulator connection
            nohup env PUBSUB_EMULATOR_HOST=localhost:8085 npm start > "$log_file" 2>&1 &
            ;;
        *)
            # Backend services
            nohup npm start > "$log_file" 2>&1 &
            ;;
    esac
    
    local service_pid=$!
    
    # Wait for service to start
    echo -e "${BLUE}⏳ Waiting for $service to start...${NC}"
    sleep 3
    
    # Check if service started successfully
    if check_port $port; then
        echo -e "${GREEN}✅ $service started successfully (pid $service_pid)${NC}"
        echo -e "${BLUE}📋 Logs: $log_file${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to start $service${NC}"
        echo -e "${BLUE}📋 Check logs: $log_file${NC}"
        return 1
    fi
}

restart_service() {
    local service=$1
    echo -e "${PURPLE}🔄 Restarting $service...${NC}"
    stop_service $service
    sleep 2
    start_service $service
}

build_service() {
    local service=$1
    local service_dir="$PROJECT_ROOT/services/$service"
    
    if [ ! -d "$service_dir" ]; then
        echo -e "${RED}❌ Service directory not found: $service_dir${NC}"
        return 1
    fi
    
    # Check if package.json exists
    if [ ! -f "$service_dir/package.json" ]; then
        echo -e "${RED}❌ No package.json found for $service${NC}"
        return 1
    fi
    
    # Check if build script exists
    if ! grep -q '"build"' "$service_dir/package.json"; then
        echo -e "${YELLOW}⚠️  No build script found for $service (skipping)${NC}"
        return 0
    fi
    
    echo -e "${GREEN}🔨 Building $service...${NC}"
    
    cd "$service_dir"
    
    # Check if node_modules exists, if not install dependencies
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 node_modules not found, installing dependencies...${NC}"
        if npm install; then
            echo -e "${GREEN}✅ Dependencies installed for $service${NC}"
        else
            echo -e "${RED}❌ Failed to install dependencies for $service${NC}"
            return 1
        fi
    fi
    
    # Check if TypeScript compiler is available
    if ! command -v npx >/dev/null 2>&1 || ! npx tsc --version >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  TypeScript compiler not found, trying to use project dependencies...${NC}"
        if [ ! -f "node_modules/.bin/tsc" ]; then
            echo -e "${RED}❌ TypeScript not found in project dependencies${NC}"
            return 1
        fi
    fi
    
    # Run npm run build
    if npm run build; then
        echo -e "${GREEN}✅ $service built successfully${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed to build $service${NC}"
        echo -e "${BLUE}💡 Try: $0 clean $service && $0 build $service${NC}"
        return 1
    fi
}

clean_service() {
    local service=$1
    echo -e "${YELLOW}🧹 Deep cleaning $service...${NC}"
    
    local service_path="$PROJECT_ROOT/services/$service"
    
    if [ ! -d "$service_path" ]; then
        echo -e "${RED}❌ Service '$service' not found${NC}"
        return 1
    fi
    
    # Clean service-specific files
    echo -e "  ${RED}🗑️  Cleaning $service dependencies and build files...${NC}"
    
    # Remove node_modules
    if [ -d "$service_path/node_modules" ]; then
        echo -e "    ${BLUE}- Removing node_modules${NC}"
        rm -rf "$service_path/node_modules"
    fi
    
    # Remove package-lock.json
    if [ -f "$service_path/package-lock.json" ]; then
        echo -e "    ${BLUE}- Removing package-lock.json${NC}"
        rm -f "$service_path/package-lock.json"
    fi
    
    # Remove dist folder (TypeScript builds)
    if [ -d "$service_path/dist" ]; then
        echo -e "    ${BLUE}- Removing dist folder${NC}"
        rm -rf "$service_path/dist"
    fi
    
    # Remove .next folder (Next.js cache for chat-ui)
    if [ -d "$service_path/.next" ]; then
        echo -e "    ${BLUE}- Removing .next cache${NC}"
        rm -rf "$service_path/.next"
    fi
    
    # Remove other caches
    [ -d "$service_path/.cache" ] && rm -rf "$service_path/.cache"
    [ -d "$service_path/.nyc_output" ] && rm -rf "$service_path/.nyc_output"
    [ -d "$service_path/coverage" ] && rm -rf "$service_path/coverage"
    
    echo -e "${GREEN}✅ $service cleaned successfully${NC}"
}

clean_all_services() {
    echo -e "${YELLOW}🧹 Deep cleaning ALL services...${NC}"
    
    # Clean Next.js cache (chat-ui)
    if [ -d "$PROJECT_ROOT/services/chat-ui/.next" ]; then
        echo -e "  ${BLUE}🗑️  Clearing Next.js cache...${NC}"
        rm -rf "$PROJECT_ROOT/services/chat-ui/.next"
    fi
    
    # Clean ALL node_modules in services
    echo -e "  ${RED}🗑️  Removing ALL node_modules...${NC}"
    find "$PROJECT_ROOT/services" -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null
    
    # Clean ALL package-lock.json files
    echo -e "  ${RED}🗑️  Removing ALL package-lock.json...${NC}"
    find "$PROJECT_ROOT/services" -name "package-lock.json" -type f -delete 2>/dev/null
    
    # Clean ALL TypeScript build outputs
    echo -e "  ${BLUE}🗑️  Removing ALL dist folders...${NC}"
    find "$PROJECT_ROOT/services" -name "dist" -type d -exec rm -rf {} + 2>/dev/null
    
    # Clean miscellaneous caches
    find "$PROJECT_ROOT/services" -name ".cache" -type d -exec rm -rf {} + 2>/dev/null
    find "$PROJECT_ROOT/services" -name ".nyc_output" -type d -exec rm -rf {} + 2>/dev/null
    find "$PROJECT_ROOT/services" -name "coverage" -type d -exec rm -rf {} + 2>/dev/null
    
    echo -e "${GREEN}✅ Deep clean completed - All dependencies and build files removed${NC}"
    echo -e "${YELLOW}⚠️  You'll need to run 'npm install' or restart services to reinstall dependencies${NC}"
}

show_logs() {
    local service=$1
    
    # Special handling for logs-proxy
    if [[ "$service" == "logs-proxy" ]]; then
        local browser_log_file="$PROJECT_ROOT/logs/browser/browser-console.log"
        local proxy_log_file="$PROJECT_ROOT/logs/services/logs-proxy/logs-proxy.log"
        if [ ! -f "$browser_log_file" ]; then
            echo -e "${RED}❌ No browser console log file found${NC}"
            echo -e "${BLUE}💡 Start logs-proxy first: ./manage-services.sh start logs-proxy${NC}"
            return 1
        fi
        
        echo -e "${CYAN}📋 Showing browser console logs via WebSocket proxy${NC}"
        echo -e "${BLUE}Browser logs: $browser_log_file${NC}"
        if [ -f "$proxy_log_file" ]; then
            echo -e "${BLUE}Proxy logs: $proxy_log_file${NC}"
        fi
        echo -e "${YELLOW}💡 These are logs from browser console.log/warn/error${NC}"
        echo ""
        tail -f "$browser_log_file"
        return
    fi
    
    local service_log_dir="$LOG_DIR/$service"
    local log_file="$service_log_dir/$service.log"
    
    if [ ! -f "$log_file" ]; then
        echo -e "${RED}❌ No log file found for $service${NC}"
        return 1
    fi
    
    echo -e "${CYAN}📋 Showing logs for $service${NC}"
    echo -e "${BLUE}File: $log_file${NC}"
    echo ""
    tail -f "$log_file"
}

start_dev_environment() {
    echo -e "${CYAN}🏗️  Starting Development Environment...${NC}"
    echo ""
    
    # Check if emulators are running
    if ! nc -z 127.0.0.1 9000 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Firebase emulator not running. Start with: ./start-all-emulators.sh${NC}"
    fi
    
    # Start essential services for development
    start_service "logs-proxy"
    sleep 2
    start_service "chat-realtime"
    sleep 2
    start_service "chat-ui"
    
    echo ""
    echo -e "${GREEN}🎉 Development environment ready!${NC}"
    echo -e "${BLUE}📱 Chat UI: http://localhost:3007/agent${NC}"
    echo -e "${BLUE}🔗 API: http://localhost:3003/api/health${NC}"
    echo -e "${BLUE}🛠️  Debug Tools: window.cxDebug (browser console)${NC}"
    echo -e "${BLUE}📊 Browser Logs: tail -f logs/browser/browser-console.log${NC}"
}

start_full_system() {
    echo -e "${CYAN}🚀 Starting Full CX System...${NC}"
    echo ""
    
    # Check if emulators are running
    if ! nc -z 127.0.0.1 9000 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Starting emulators first...${NC}"
        "$PROJECT_ROOT/scripts/start-all-emulators.sh" &
        sleep 10
    fi
    
    # Start all services in dependency order
    echo -e "${BLUE}Starting services in dependency order...${NC}"
    
    start_service "logs-proxy"
    sleep 2
    
    start_service "session-manager"
    sleep 2
    
    start_service "chat-realtime" 
    sleep 2
    
    start_service "channel-router"
    sleep 2
    
    start_service "bot-human-router"
    sleep 2
    
    start_service "twilio"
    sleep 2
    
    start_service "chat-ui"
    
    echo ""
    echo -e "${GREEN}🎉 Full system started!${NC}"
    show_status
}

# Main script logic
case "${1:-status}" in
    "help"|"-h"|"--help")
        show_usage
        ;;
    "status")
        show_status
        ;;
    "build-status")
        if [ -n "$2" ]; then
            if [ -n "$(get_service_port $2)" ]; then
                echo -e "${CYAN}🔨 Build Status for $2${NC}"
                echo ""
                show_service_build_status "$2"
            else
                echo -e "${RED}❌ Unknown service: $2${NC}"
                exit 1
            fi
        else
            show_build_status
        fi
        ;;
    "start")
        if [ -n "$2" ]; then
            if [ -n "$(get_service_port $2)" ]; then
                start_service "$2"
            else
                echo -e "${RED}❌ Unknown service: $2${NC}"
                exit 1
            fi
        else
            echo -e "${YELLOW}🚀 Starting all services (except logs-proxy)...${NC}"
            for service in $(get_all_services); do
                # Skip logs-proxy when starting all services - start manually when needed
                if [[ "$service" == "logs-proxy" ]]; then
                    echo -e "${BLUE}ℹ️  Skipping logs-proxy - start manually: $0 start logs-proxy${NC}"
                    continue
                fi
                start_service "$service"
                sleep 1
            done
        fi
        ;;
    "stop")
        if [ -n "$2" ]; then
            if [ -n "$(get_service_port $2)" ]; then
                stop_service "$2"
            else
                echo -e "${RED}❌ Unknown service: $2${NC}"
                exit 1
            fi
        else
            echo -e "${YELLOW}🛑 Stopping all services...${NC}"
            for service in $(get_all_services); do
                stop_service "$service"
            done
        fi
        ;;
    "restart")
        if [ -n "$2" ]; then
            if [ -n "$(get_service_port $2)" ]; then
                restart_service "$2"
            else
                echo -e "${RED}❌ Unknown service: $2${NC}"
                exit 1
            fi
        else
            echo -e "${PURPLE}🔄 Restarting all services...${NC}"
            for service in $(get_all_services); do
                restart_service "$service"
                sleep 1
            done
        fi
        ;;
    "build")
        if [ -n "$2" ]; then
            if [ -n "$(get_service_port $2)" ]; then
                build_service "$2"
            else
                echo -e "${RED}❌ Unknown service: $2${NC}"
                exit 1
            fi
        else
            echo -e "${BLUE}🔨 Building all services...${NC}"
            build_success=0
            build_total=0
            
            for service in $(get_all_services); do
                # Skip logs-proxy as it's not a buildable service
                if [[ "$service" == "logs-proxy" ]]; then
                    continue
                fi
                
                build_total=$((build_total + 1))
                if build_service "$service"; then
                    build_success=$((build_success + 1))
                fi
                echo ""
            done
            
            echo -e "${BLUE}📊 Build Summary: $build_success/$build_total services built successfully${NC}"
            
            if [ $build_success -eq $build_total ]; then
                echo -e "${GREEN}🎉 All services built successfully!${NC}"
            else
                echo -e "${YELLOW}⚠️  Some services failed to build${NC}"
                exit 1
            fi
        fi
        ;;
    "clean")
        if [ -n "$2" ]; then
            # Clean specific service
            if [ -n "$(get_service_port $2)" ]; then
                echo -e "${YELLOW}🧹 Cleaning service: $2${NC}"
                # Stop the specific service first
                stop_service "$2"
                # Clean the specific service
                clean_service "$2"
                echo -e "${GREEN}✅ Service $2 cleaned${NC}"
            else
                echo -e "${RED}❌ Unknown service: $2${NC}"
                exit 1
            fi
        else
            # Clean all services
            echo -e "${YELLOW}🧹 Cleaning all services...${NC}"
            # Stop all services
            for service in $(get_all_services); do
                stop_service "$service"
            done
            # Clean all caches and dependencies
            clean_all_services
            echo -e "${GREEN}✅ All services cleaned${NC}"
        fi
        ;;
    "logs")
        if [ -n "$2" ]; then
            if [ -n "$(get_service_port $2)" ]; then
                show_logs "$2"
            else
                echo -e "${RED}❌ Unknown service: $2${NC}"
                exit 1
            fi
        else
            echo -e "${RED}❌ Please specify a service for logs${NC}"
            exit 1
        fi
        ;;
    "dev")
        start_dev_environment
        ;;
    "full")
        start_full_system
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        echo ""
        show_usage
        exit 1
        ;;
esac