#!/bin/bash

# Script to validate that Chat UI works with or without logs proxy
# This demonstrates that logs proxy is completely optional

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 CX System - Logs Proxy Optional Validation${NC}"
echo "=================================================="

# Function to check if Chat UI is responding
check_chat_ui() {
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3007/ || echo "000")
    if [ "$response_code" = "200" ]; then
        echo -e "${GREEN}✅ Chat UI responding (HTTP $response_code)${NC}"
        return 0
    else
        echo -e "${RED}❌ Chat UI not responding (HTTP $response_code)${NC}"
        return 1
    fi
}

# Function to check logs proxy status
check_logs_proxy() {
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3008/ 2>/dev/null || echo "000")
    if [ "$response_code" = "200" ] || [ "$response_code" = "101" ]; then
        echo -e "${GREEN}✅ Logs proxy available (HTTP $response_code)${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  Logs proxy not available (HTTP $response_code)${NC}"
        return 1
    fi
}

echo "Step 1: Check current Chat UI status"
echo "-----------------------------------"
if check_chat_ui; then
    echo -e "${GREEN}Chat UI is currently running${NC}"
else
    echo -e "${RED}Chat UI is not running - please start it first${NC}"
    exit 1
fi

echo ""
echo "Step 2: Check logs proxy status"
echo "------------------------------"
LOGS_PROXY_AVAILABLE=false
if check_logs_proxy; then
    LOGS_PROXY_AVAILABLE=true
    echo -e "${GREEN}Logs proxy is currently available${NC}"
else
    echo -e "${YELLOW}Logs proxy is not available (this is OK!)${NC}"
fi

echo ""
echo "Step 3: Test Chat UI without logs proxy"
echo "--------------------------------------"

# Stop logs proxy if it's running
if [ "$LOGS_PROXY_AVAILABLE" = true ]; then
    echo -e "${YELLOW}Stopping logs proxy to test optional functionality...${NC}"
    ./scripts/manage-services.sh stop logs-proxy >/dev/null 2>&1 || true
    sleep 2
fi

# Verify logs proxy is stopped
if check_logs_proxy; then
    echo -e "${RED}❌ Logs proxy still running after stop attempt${NC}"
else
    echo -e "${GREEN}✅ Logs proxy successfully stopped${NC}"
fi

echo ""
echo "Step 4: Verify Chat UI still works without logs proxy"
echo "----------------------------------------------------"
if check_chat_ui; then
    echo -e "${GREEN}✅ SUCCESS: Chat UI works perfectly without logs proxy!${NC}"
else
    echo -e "${RED}❌ FAILURE: Chat UI not working without logs proxy${NC}"
    exit 1
fi

echo ""
echo "Step 5: Test browser console (manual verification needed)"
echo "-------------------------------------------------------"
echo -e "${BLUE}📋 Manual Test Instructions:${NC}"
echo "1. Open browser to http://localhost:3007"
echo "2. Open Developer Tools (F12)"
echo "3. Check console for any logs proxy errors"
echo "4. Navigate through the application"
echo ""
echo -e "${GREEN}✅ Expected Result: No errors, application works normally${NC}"
echo -e "${YELLOW}⚠️  Note: You may see a warning about logs proxy not being available - this is OK!${NC}"

echo ""
echo "Step 6: Optional - Restart logs proxy"
echo "------------------------------------"
read -p "Do you want to restart the logs proxy? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}Starting logs proxy...${NC}"
    ./scripts/manage-services.sh start logs-proxy
    sleep 3
    if check_logs_proxy; then
        echo -e "${GREEN}✅ Logs proxy restarted successfully${NC}"
    else
        echo -e "${YELLOW}⚠️  Logs proxy failed to start (this doesn't affect Chat UI)${NC}"
    fi
else
    echo -e "${YELLOW}Logs proxy remains stopped (Chat UI continues to work normally)${NC}"
fi

echo ""
echo -e "${GREEN}🎉 VALIDATION COMPLETE${NC}"
echo "======================"
echo -e "${GREEN}✅ Logs proxy is completely OPTIONAL${NC}"
echo -e "${GREEN}✅ Chat UI works with or without logs proxy${NC}"
echo -e "${GREEN}✅ No errors or crashes when logs proxy is unavailable${NC}"
echo -e "${GREEN}✅ Application is resilient and gracefully handles proxy absence${NC}"
echo ""
echo -e "${BLUE}💡 Summary: The logs proxy is now properly implemented as an optional${NC}"
echo -e "${BLUE}   debugging tool that doesn't affect core application functionality.${NC}"