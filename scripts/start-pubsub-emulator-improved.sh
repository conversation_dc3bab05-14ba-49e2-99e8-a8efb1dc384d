#!/bin/bash

# Start PubSub Emulator and Configure Topics
# This script starts the Google Cloud PubSub emulator and creates the necessary topics and subscriptions

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting PubSub Emulator...${NC}"

# Function to check if port is in use
check_port() {
    if nc -z $1 $2 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# Check if emulator is already running
if check_port localhost 8085; then
    echo -e "${YELLOW}⚠️  PubSub emulator appears to be already running on port 8085${NC}"
    echo "Do you want to restart it? (y/n)"
    read -r response
    if [[ "$response" != "y" ]]; then
        echo -e "${GREEN}✅ Using existing PubSub emulator${NC}"
    else
        echo "Stopping existing emulator..."
        pkill -f "pubsub" > /dev/null 2>&1
        sleep 2
    fi
fi

# Set environment variables for emulator
export PUBSUB_EMULATOR_HOST=localhost:8085
export GOOGLE_CLOUD_PROJECT=cx-system-469120

# Only start if not already running
if ! check_port localhost 8085; then
    # Create logs directory if it doesn't exist
    mkdir -p logs/emulators
    
    # Start the emulator in the background
    echo -e "${BLUE}📡 Starting PubSub emulator on localhost:8085...${NC}"
    gcloud beta emulators pubsub start --host-port=localhost:8085 > logs/emulators/pubsub-emulator.log 2>&1 &
    PUBSUB_PID=$!
    
    # Wait for emulator to start with timeout
    echo -e "${YELLOW}⏳ Waiting for PubSub emulator to start...${NC}"
    for i in {1..10}; do
        if check_port localhost 8085; then
            echo -e "${GREEN}✅ PubSub emulator started successfully (PID: $PUBSUB_PID)${NC}"
            break
        fi
        if [ $i -eq 10 ]; then
            echo -e "${RED}❌ PubSub emulator failed to start after 10 seconds${NC}"
            echo "Last 10 lines of log:"
            tail -10 logs/emulators/pubsub-emulator.log
            exit 1
        fi
        sleep 1
    done
fi

# Configure gcloud to use emulator
echo -e "${BLUE}🔧 Configuring gcloud for emulator...${NC}"
$(gcloud beta emulators pubsub env-init)

# Function to check if topic exists
check_topic_exists() {
    PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics list --project=cx-system-469120 2>/dev/null | grep -q "projects/cx-system-469120/topics/$1"
    return $?
}

# Function to check if subscription exists
check_subscription_exists() {
    PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub subscriptions list --project=cx-system-469120 2>/dev/null | grep -q "projects/cx-system-469120/subscriptions/$1"
    return $?
}

# Create topics (only if they don't exist)
echo -e "${BLUE}📝 Checking/Creating PubSub topics...${NC}"

if ! check_topic_exists "inbound-messages"; then
    PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics create inbound-messages --project=cx-system-469120
    echo -e "${GREEN}✅ Created topic: inbound-messages${NC}"
else
    echo -e "${YELLOW}📋 Topic already exists: inbound-messages${NC}"
fi

if ! check_topic_exists "outbound-messages"; then
    PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics create outbound-messages --project=cx-system-469120
    echo -e "${GREEN}✅ Created topic: outbound-messages${NC}"
else
    echo -e "${YELLOW}📋 Topic already exists: outbound-messages${NC}"
fi

# Create subscriptions for Push endpoints (only if they don't exist)
echo -e "${BLUE}🔗 Checking/Creating PubSub subscriptions...${NC}"

# Bot Human Router subscription (receives INBOUND messages)
if ! check_subscription_exists "bot-human-router-inbound"; then
    PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub subscriptions create bot-human-router-inbound \
        --topic=inbound-messages \
        --push-endpoint=http://localhost:3004/api/pubsub/inbound \
        --project=cx-system-469120
    echo -e "${GREEN}✅ Created subscription: bot-human-router-inbound${NC}"
else
    echo -e "${YELLOW}📋 Subscription already exists: bot-human-router-inbound${NC}"
fi

# Channel Router subscription (receives OUTBOUND messages)
if ! check_subscription_exists "channel-router-outbound"; then
    PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub subscriptions create channel-router-outbound \
        --topic=outbound-messages \
        --push-endpoint=http://localhost:3002/api/pubsub/outbound \
        --project=cx-system-469120
    echo -e "${GREEN}✅ Created subscription: channel-router-outbound${NC}"
else
    echo -e "${YELLOW}📋 Subscription already exists: channel-router-outbound${NC}"
fi

echo ""
echo -e "${GREEN}🎉 PubSub emulator setup complete!${NC}"
echo ""
echo -e "${YELLOW}📥 Inbound Topic:${NC} inbound-messages"
echo -e "${YELLOW}📤 Outbound Topic:${NC} outbound-messages"
echo -e "${YELLOW}🔄 Bot Human Router subscription:${NC} bot-human-router-inbound"
echo -e "${YELLOW}🔄 Channel Router subscription:${NC} channel-router-outbound"
echo ""
echo -e "${BLUE}💡 Management commands:${NC}"
echo "  • Stop emulator: pkill -f pubsub"
echo "  • View logs: tail -f logs/emulators/pubsub-emulator.log"
echo "  • List topics: PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub topics list"
echo "  • List subscriptions: PUBSUB_EMULATOR_HOST=localhost:8085 gcloud pubsub subscriptions list"
echo ""
echo -e "${GREEN}✅ Emulator running on: localhost:8085${NC}"
