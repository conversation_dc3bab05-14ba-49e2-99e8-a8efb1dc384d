# CX System

Sistema de Customer Experience basado en microservicios para Cloud Run, especializado en conversaciones multicanal con énfasis en WhatsApp, automatización inteligente y analytics avanzados.

## 🏗️ Arquitectura

### Servicios Core
```
services/
├── twilio/              # Integración WhatsApp via Twilio
├── channel-router/      # Ruteo de mensajes + AI department assignment  
├── session-manager/     # Gestión de sesiones (Redis)
├── bot-human-router/    # Ruteo inteligente bot/humano + n8n
├── chat-realtime/       # Interface Firebase + lógica de asignación
├── chat-ui/            # Frontend Next.js + Firebase real-time híbrido
└── analytics/          # Métricas y KPIs (Supabase)
```

### Flujo de Mensajes (Arquitectura Actualizada 2025-08-22)
```
WhatsApp → Twilio → Channel Router → N8N Department Analysis
    ↓                     ↓                    ↓
    ↓                     ↓            (loop until department != more_info)
    ↓                     ↓                    ↓
    ↓                     ↓ → Chat Realtime (create conversation)
    ↓                     ↓                    ↓
Session Manager → PubSub Inbound ← Channel Router
    ↓ (Redis)      ↓
    ↓              ↓ → Bot Human Router (Decision Engine con ESTADO)
    ↓              ↓           ↓
    ↓              ↓   Check conversation state:
    ↓              ↓   ├── unassigned → try N8N first
    ↓              ↓   ├── n8n assigned → N8N passthrough
    ↓              ↓   └── human assigned → Human passthrough
    ↓              ↓                    ↓
    └──────────────┴──────→ Firebase Realtime Database ← N8N direct calls
                                   ↓
                           Chat UI + Analytics
```

## 📊 Estado de Servicios

| Servicio | Estado | Puerto | Descripción |
|----------|--------|---------|-------------|
| **chat-realtime** | ✅ PRODUCTION-READY | 3003 | Interface Firebase + conversation management completo |
| **chat-ui** | ✅ PRODUCTION-READY | 3007 | **Hybrid TanStack + Optimistic UI** + conversation actions |
| **channel-router** | ✅ FUNCIONAL | 3002 | Ruteo + AI dept analysis (mock n8n) |
| **session-manager** | ✅ COMPLETO | 3001 | Gestión sesiones Redis |
| **bot-human-router** | ✅ COMPLETO | 3004 | Decision engine + ConversationStateService funcional |
| **twilio** | 📋 PENDIENTE | 3000 | Estructura básica |
| **analytics** | 📋 PENDIENTE | 3006 | Diseño completo |

## 🚀 Quick Start

### Prerrequisitos
- Node.js 18+
- Docker (Redis, n8n)
- Firebase CLI
- Google Cloud SDK (para emuladores)

### Setup de Desarrollo
```bash
# Clonar proyecto
git clone github-n1co:h4b-dev/cx-system.git
cd cx-system

# Instalar dependencias de servicios
cd services/chat-realtime && npm install && cd ../..
cd services/channel-router && npm install && cd ../..
cd services/session-manager && npm install && cd ../..
cd services/bot-human-router && npm install && cd ../..

# Iniciar emuladores y Redis
./scripts/start-all-emulators.sh
./scripts/manage-redis.sh start

# Iniciar servicios (en terminales separadas)
cd services/chat-realtime && npm run dev     # Puerto 3003
cd services/channel-router && npm run dev    # Puerto 3002  
cd services/session-manager && npm run dev   # Puerto 3001
cd services/bot-human-router && npm run dev  # Puerto 3004
```

### Variables de Entorno
Copiar y configurar el archivo `.env` en la raíz del proyecto con las credenciales necesarias de:
- Supabase (dev instance)
- Firebase Realtime Database  
- Redis local (Docker container)
- PubSub emulator
- Twilio (para producción)

## 🔧 Stack Tecnológico

### Backend
- **TypeScript**: 100% del código
- **Node.js**: Runtime de servicios
- **Express**: Framework web
- **Winston**: Logging estructurado

### Bases de Datos
- **Firebase Realtime Database**: Datos operacionales en tiempo real
- **Redis**: Gestión de sesiones (TTL 2h) + Estado de conversaciones compartido
- **Supabase PostgreSQL**: Analytics, configuración, datos históricos

### Comunicación
- **Google Cloud PubSub**: Mensajería entre servicios  
- **REST APIs**: Comunicación HTTP
- **Firebase WebSockets**: Tiempo real híbrido con fallback automático a polling
- **Exponential Backoff**: Reconexión inteligente (1s-30s intervals)

### Desarrollo Local
- **Emuladores**: Firebase, PubSub
- **Redis**: Docker container para estado compartido
- **Mock Services**: n8n department analysis
- **Hot Reload**: Desarrollo en tiempo real

## 📁 Documentación

### Documentación Técnica
- **`docs-locales/`**: Documentación organizada por servicio
- **`docs-locales/servicios/{service}/README.md`**: Estado y guías específicas
- **`docs-locales/arquitectura/`**: Documentos de arquitectura general

### Reportes de Testing
- **Chat Realtime**: E2E testing con 50% pass rate (producción-ready)
- **Channel Router**: 24 tests funcionales
- **Session Manager**: Testing completo

### Arquitectura y Diseño
- **CLAUDE.md**: Contexto completo para desarrollo con Claude
- **Hybrid Database Strategy**: Firebase (real-time) + Supabase (analytics)
- **Offline Resilience**: Cache local + sync automático

## 🧪 Testing

### Ejecutar Tests por Servicio
```bash
# Chat Realtime (E2E + Unit)
cd services/chat-realtime && npm test
cd services/chat-realtime/testing && npm test

# Channel Router
cd services/channel-router && npm test

# Session Manager  
cd services/session-manager && npm test

# Bot Human Router
cd services/bot-human-router && npm test

# Chat UI Real-time
cd services/chat-ui && npm test
```

### Testing Firebase Real-time
```bash
# Test connection fallback
./scripts/start-all-emulators.sh
cd services/chat-ui && npm run dev -- --port 3007

# Simulate offline: Kill Firebase emulator
# UI should automatically switch to "Syncing" mode

# Debug tools in browser console:
window.cxDebug.clearCache()        # Clear cached data
window.cxDebug.showCacheState()    # Inspect cache
window.cxDebug.forceSync()         # Force data sync
```

### Coverage Reports
Los reportes de cobertura se generan en `services/{service}/coverage/`

## 📊 Logging y Monitoring

### Logs Organizados
```
logs/
├── servicios/           # Logs por servicio
│   ├── chat-realtime/  # Firebase operations + API calls
│   ├── channel-router/ # Twilio webhooks + AI analysis  
│   ├── session-manager/# Redis operations
│   └── bot-human-router/# Decision engine logs
├── emulators/          # Firebase, PubSub emulator logs
└── testing/            # Test execution logs
```

### Structured Logging
- **Winston**: JSON structured logs con rotación automática
- **Log Levels**: debug (dev) → info (prod)  
- **Context**: sessionId, conversationId, agentId automático

## 🎯 Próximos Pasos de Desarrollo

### Inmediatos
1. **Implementar funcionalidades restantes del dropdown**: Transfer, Supervisión, Escalación, Historia, Notas, Recordatorios
2. **Decision Engine** en Bot Human Router
3. **Twilio Integration** completa para WhatsApp production

### Medio Plazo  
4. **Setup usuarios de prueba** para 100% test coverage en Chat Realtime
5. **n8n Real Integration** (reemplazar mock)
6. **Analytics Service** implementación
7. **WhatsApp Typing Indicators** via Meta Graph API

### Deploy
7. **Cloud Run** deployment
8. **Production Firebase** + **Supabase**
9. **Real PubSub** topics

## 🔒 Principios de Desarrollo

- **MVP Focus**: Solo funcionalidad core hasta que se solicite
- **100% Testable**: Todo código diseñado para testing fácil
- **Simple y Legible**: No código complejo innecesario
- **TypeScript Obligatorio**: Type safety en todo el proyecto
- **Do what has been asked; nothing more, nothing less**

## 📝 Contribución

### Flujo de Trabajo
- Branches por feature/sesión de desarrollo
- Direct merge a main (sin PRs por ahora)
- Testing obligatorio antes de merge
- Documentación actualizada en cada cambio

### Convenciones
- Commits descriptivos en español
- README.md actualizado por servicio
- Logging estructurado siempre
- Variables de entorno centralizadas

---

## ✨ Funcionalidades Implementadas

### 🎯 Gestión de Conversaciones (PRODUCTION-READY)
- **Dropdown de Acciones Completo**: Menú de 7 opciones (⋮) con Transfer, Supervisión, Escalación, Cierre, Historia, Notas, Recordatorios
- **Sistema de Cierre**: Modal con razones localizadas, mensajes automáticos, marcado para export (`readyForExport: true`)
- **UI Read-Only**: Conversaciones cerradas automáticamente deshabilitan input y muestran estado de finalización
- **Cambio Dinámico**: Switch automático entre conversaciones activas/cerradas con estados visuales apropiados
- **Integración End-to-End**: Frontend → Backend → Firebase → System Messages completamente funcional

### 🎨 Chat UI Avanzado
- **Firebase Real-time Híbrido**: Tiempo real con fallback automático a polling
- **Optimistic UI**: Mensajes instantáneos con resolución de conflictos
- **Connection States**: 🟢 Real-time | 🟡 Syncing | 🔴 Offline con indicadores visuales honestos
- **Typing Indicators**: Real-time "user is typing" feedback (web chat)
- **Offline Resilience**: Cache local + sync automático al reconectar

### 🔧 Backend Robusto
- **Chat Realtime API**: 50+ endpoints completamente funcionales con autenticación JWT
- **Error Handling**: Manejo robusto de Firebase structure, undefined/NaN, timestamp parsing
- **System Messages**: Generación automática de mensajes contextuales basados en acciones
- **Data Validation**: Type safety y validación completa de datos en toda la arquitectura

### 👥 Agent Status Management (NEW - PRODUCTION-READY)
- **Load Balancing Algorithm**: Asignación por menor utilización (sessions/max_sessions)
- **Real-time Status Sync**: Supabase ↔ Firebase bidireccional automático
- **Department Filtering**: Agentes asignados por departamentos específicos
- **Session Tracking**: Updates automáticos al asignar/cerrar conversaciones
- **Status States**: available/busy/away con filtrado inteligente

---

## 🔥 Firebase Real-time Híbrido

### Arquitectura Inteligente
- **Smart Switching**: Real-time cuando disponible, polling como fallback
- **Zero Breaking Changes**: APIs existentes mantenidas
- **Visual Indicators**: Status honest del tipo de conexión actual
- **Connection States**: 🟢 Real-time | 🟡 Syncing | 🔴 Offline

### Arquitectura Híbrida Post-Refactor
```typescript
// SUPERVISOR MODE - TanStack Query (memory-safe)
useSupervisorQueries()      // Dashboard data con polling 30s
useEscalationsQuery()       // Crisis interventions
useNavigationCountsQuery()  // Navigation counts

// AGENT MODE - Custom Optimistic UI (real-time performance)  
useOptimisticConversationAnimations()  // Conversation animations
useConversationMutations()  // Optimistic conversation updates
useMessageMutations()       // Optimistic message handling
```

### Fallback Automático
1. **Firebase WebSocket**: Actualizaciones < 100ms
2. **Network Issues**: Automático switch a REST polling  
3. **Reconnection**: Exponential backoff (1s → 30s)
4. **Recovery**: Automático return a real-time

---

## 🚀 Estado Actual del Sistema

### ✅ Servicios Production-Ready
- **Chat Realtime** (Puerto 3003): API completa + Load Balancing + Session Management
- **Channel Router** (Puerto 3002): Message routing operacional  
- **Bot Human Router** (Puerto 3004): Decision engine funcional
- **Chat UI** (Puerto 3007): Interface completa con real-time + conversation management
- **Session Manager**: Compilación y dependencies limpias

### 🎯 Funcionalidades Core Implementadas
- **Conversation Management**: Crear, asignar, cerrar conversaciones
- **Agent Assignment**: Load balancing automático por utilización
- **Real-time Messaging**: Firebase hybrid system < 100ms
- **Status Management**: available/busy/away con sync automático
- **Department Routing**: Filtrado por departamentos específicos

---

**Organización**: n1co  
**Última actualización**: 2025-08-26  
**Estado**: HYBRID ARCHITECTURE IMPLEMENTED - TanStack Query (Supervisor) + Optimistic UI (Agent) - Memory-Safe & Performance Optimized